[tools]
opentofu  = "1.8.3"
java      = "17"
liquibase = "4.29.2"
node      = "20"

[settings]
experimental = true

[tasks.copy_liquibase_libs]
description = "copy liquibase libs for bigquery drivers"
run =  """
# copying liquibase drivers 
# LQBASE_LIBS is defined in the gitlab runner docker image
if [ -z "$LQBASE_LIBS" ]; then
    echo "LQBASE_LIBS is not set"
    exit 1
fi

echo "copying driver jars from $LQBASE_LIBS"

LIQ_BIN=$(mise which liquibase)
LIQ_DIR=$(dirname $LIQ_BIN)
LIQ_LIB_DIR=${LIQ_DIR}/lib
cp $LQBASE_LIBS/*.jar $LIQ_LIB_DIR
"""
 