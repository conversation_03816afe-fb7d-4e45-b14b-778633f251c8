#!/bin/bash
set -e

# This script is used to ensure that the required Tableau sites are created for organizations that have the tableauSite metadata property set

TF_CONFIG_FILE="./infrastructure/Configs/${1}"
TABLEAU_URL=($(grep "^[[:space:]]*tableau_server_url" $TF_CONFIG_FILE | awk '{print $3}' | tr -d '"'))
TABLEAU_API_TOKEN=""
CURRENT_TABLEAU_SITES=""

function login() {
    echo "Logging into Tableau Server"

    # login to the default site to be able to query all sites
    # parse the token out of the json response
    TABLEAU_API_TOKEN=($(curl -s --location "$TABLEAU_URL/api/3.6/auth/signin" \
    --header 'content-type: application/json' \
    --header 'Accept: application/json' \
    --data '{
        "credentials": {
            "personalAccessTokenName": "'$TABLEAU_PAT_NAME'",
            "personalAccessTokenSecret": "'$TABLEAU_PAT_SECRET'",
            "site": {}
        }
    }' | jq '.credentials.token' | tr -d '"'))
    if [[ -z "$TABLEAU_API_TOKEN" ]]; then
        echo "Unable to find token in API login response"
        exit 1
    fi
}

function getSites() {
    CURRENT_TABLEAU_SITES=($(curl -s --location "$TABLEAU_URL/api/3.6/sites?pageSize=1000&pageNumber=1" \
    --header 'content-type: application/json' \
    --header 'Accept: application/json' \
    --header "X-Tableau-Auth: $TABLEAU_API_TOKEN" | jq ".sites.site[].contentUrl" | tr -d '" ' | tr '\n' ' ' | xargs echo -n))
    echo "Current Tableau Sites: ${CURRENT_TABLEAU_SITES[@]}"
}

function createSite() {
    echo "Creating Tableau Site for $1"
    SITE_NAME=$1
    create_response_code=($(curl --location "$TABLEAU_URL/api/3.6/sites" \
    --header 'content-type: application/json' \
    --header "X-Tableau-Auth: $TABLEAU_API_TOKEN" \
    --header 'Accept: application/json' \
    --data '{
        "site": {
            "name": "'"${SITE_NAME}"'",
            "contentUrl": "'"${SITE_NAME}"'"
        }
    }' \
    -s -o /dev/null -w "%{http_code}"))
    if [[ $create_response_code != 201 ]]; then
        echo "Failed to create site $SITE_NAME"
        exit 1
    else
        echo "Successfully created site $SITE_NAME"
    fi
}

WANTED_TABLEAU_SITES=($(grep "^[[:space:]]*tableauSite" $TF_CONFIG_FILE | awk '{print $3}' ORS=' ' | tr -d '"'))

echo "-----------------------------------"
echo "TF Config File: $TF_CONFIG_FILE"
echo "Wanted Tableau Sites: ${WANTED_TABLEAU_SITES[@]}"
echo "-----------------------------------"

login
getSites

for site_name in "${WANTED_TABLEAU_SITES[@]}"; do
    echo "Ensuring Tableau Site for ${site_name}"
    if [[ " ${CURRENT_TABLEAU_SITES[*]} " =~ [[:space:]]${site_name}[[:space:]] ]]; then
        echo "Tableau Site $site_name already exists"
    else
        createSite $site_name
    fi
done
