# Labels constraints: Can only contain lowercase letters, numeric characters, underscores and dashes.  Must be less than 63 characters.

locals {
  env       = var.env == "test" ? "dev" : var.env
  createdon = formatdate("YYYY-MM-DD", timestamp())
  lifetime  = formatdate("YYYY-MM-DD", timeadd(timestamp(), "15778463s")) # = 6 months

  # https://cloud.google.com/resource-manager/docs/tags/tags-overview
  labels = {
    "lifetime"        = "perpetual"
    "application"     = var.application
    "business-region" = var.region
    "costcenter"      = var.costcenter
    "customer"        = var.customer
    "environment"     = local.env
    "owner"           = var.owner
    "project"         = var.project
    "role"            = var.role
    "createdby"       = var.createdby
    "contact"         = var.contact
  }
  description_metadata = " - CONTACT: ${var.contact}@kiongroup.com - OWNER: ${var.owner}"

  callback_urls = [format("%s/vizportal/api/web/v1/auth/openIdLogin", var.tableau_server_url), "http://localhost:8080/vizportal/api/web/v1/auth/openIdLogin"]

  # Mapping of tenant to facilities for easy reference
  tenant_facilities_map = {
    for tenant in var.tenants : tenant.name => tenant.facilities
    if tenant.exclude_from_auth0_organization != true
  }

  # Precomputed default roles for each tenant as CSV strings (regular facility roles only)
  tenant_default_roles_map = {
    for tenant in var.tenants : tenant.name => join(",", [
      for facility in tenant.facilities : "${tenant.name}#${facility}"
    ])
    if tenant.exclude_from_auth0_organization != true
  }

  # Regular facility roles (tenant#facility)
  tenant_facility_roles = flatten([
    for tenant in var.tenants : [
      for facility in tenant.facilities : {
        tenant    = tenant.name
        facility  = facility
        role_name = "${tenant.name}#${facility}"
      }
    ]
    if tenant.exclude_from_auth0_organization != true
  ])

  # Facility admin roles (tenant#facility_facility_admin)
  tenant_facility_admin_roles = flatten([
    for tenant in var.tenants : [
      for facility in tenant.facilities : {
        tenant    = tenant.name
        facility  = facility
        role_name = "${tenant.name}#${facility}_facility_admin"
      }
    ]
    if tenant.exclude_from_auth0_organization != true
  ])

}
