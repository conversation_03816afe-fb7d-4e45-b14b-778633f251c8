locals {
  ict_artifact_registry = "us-east1-docker.pkg.dev/${var.project_id}/${var.project_id}-registry" # To be removed once the ict-ops registry is in place
}


## NO AUTH
data "google_iam_policy" "noauth" {
  binding {
    role = "roles/run.invoker"
    members = [
      "allUsers",
    ]
  }
}


# Members!

data "google_iam_policy" "iap" {
  binding {
    role = "roles/iap.httpsResourceAccessor"
    members = [
      "group:<EMAIL>", // a google group
      // TODO: Add in Appigee Service Account! (Once it's created)
      // "allAuthenticatedUsers"          // anyone with a Google account (not recommended)
    ]
  }
}



data "external" "git" {
  program = [
    "git",
    "log",
    "--pretty=format:{ \"sha\": \"%H\" }",
    "-1",
    "HEAD"
  ]
}

output "git_sha" {
  value = data.external.git.result.sha
}

data "google_project" "project" {
  project_id = var.project_id
}

module "security_policy" {
  source  = "GoogleCloudPlatform/cloud-armor/google"
  version = "1.2.0"

  project_id                           = var.project_id
  name                                 = "${var.project_id}-${var.env}-policy"
  description                          = "Cloud Armor security policy with preconfigured rules, security rules, and custom rules"
  default_rule_action                  = "allow"
  type                                 = "CLOUD_ARMOR"
  layer_7_ddos_defense_enable          = true
  layer_7_ddos_defense_rule_visibility = "STANDARD"
  json_parsing                         = "STANDARD"
  log_level                            = "NORMAL"

  # Define or reference rules as needed
  pre_configured_rules      = {}
  security_rules            = {}
  custom_rules              = {}
  threat_intelligence_rules = {}
}

# outputs.tf - Contains the output definitions - ICT-1307 - CloudArmor
output "cloud_armor_policy" {
  value       = module.security_policy.policy
  description = "The name of the Cloud Armor security policy."
}
