
data "terraform_remote_state" "networks" {
  backend = "gcs"
  config = {
    prefix = "${var.env}/${var.gitlab_project_shortname}/Network"
    bucket = "ict-p-statefiles"
  }
}

data "terraform_remote_state" "memorystore" {
  # We are getting the remote state from API - TODO: SHOULD UPDATE THE BLOCK TO SPECIFY THAT
  backend = "gcs"
  config = {
    prefix = "${var.env}/api/Memorystore"
    bucket = "ict-p-statefiles"
  }
}

