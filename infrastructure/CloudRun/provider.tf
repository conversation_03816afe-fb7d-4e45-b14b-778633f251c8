terraform {
  backend "gcs" {
    bucket = "ict-p-statefiles"
    prefix = "${var.env}/${var.gitlab_project_shortname}/${basename(abspath(path.root))}"
  }


  required_providers {
    google = {
      version = "~> 4.67"
      source  = "hashicorp/google"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}
