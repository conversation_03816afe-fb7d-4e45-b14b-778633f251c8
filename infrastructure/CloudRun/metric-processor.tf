/*
resource "null_resource" "metric_processor_sub_filters" {
  provisioner "local-exec" {
    command = "python3 ./metric_processor_subscription_filter.py"
  }

  triggers = {
    always_run = timestamp()
  }
}
data "external" "metric_processor_filter" {
  program = ["python3", "./metric_processor_subscription_filter.py"]  # Correct path or absolute path is crucial
}

resource "local_file" "metric_processor_filters" {
 content = data.external.metric_processor_filter.result.filter # Access from script output
 filename = "metric_processor_subscription_filter_output.txt"
 depends_on = [data.external.metric_processor_filter]

}
*/

module "service_account_metric_processor" {
  source     = "terraform-google-modules/service-accounts/google"
  version    = "~> 4.1.1"
  project_id = var.project_id
  prefix     = "${var.env}-${var.service_name}"
  names      = ["metric-processor"]
  project_roles = [
    "${var.project_id}=>roles/aiplatform.user",
    "${var.project_id}=>roles/cloudsql.client",
    "${var.project_id}=>roles/compute.networkUser",
    "${var.project_id}=>roles/redis.editor",
    "${var.project_id}=>roles/secretmanager.secretAccessor",
    "${var.project_id}=>roles/vpcaccess.user",
  ]
}

# Add cross-project IAM bindings for API project access
resource "google_project_iam_member" "metric_processor_api_secret_accessor" {
  project = var.api_project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${module.service_account_metric_processor.email}"
}

resource "google_project_iam_member" "metric_processor_api_cloudsql_client" {
  project = var.api_project_id
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:${module.service_account_metric_processor.email}"
}

module "cloud_run_metric_processor" {
  source  = "GoogleCloudPlatform/cloud-run/google"
  version = "~> 0.9.1"

  service_name          = "${var.env}-${var.project_name}-metric-processor"
  service_labels        = local.labels
  project_id            = var.project_id
  location              = var.region
  image                 = "${var.ict_artifact_registry}/ict-metric-processor:${data.external.git.result.sha}"
  service_account_email = module.service_account_metric_processor.email

  limits = {
    cpu    = "1000m"
    memory = "1024Mi"
  }

  env_vars = [
    { name = "ENVIRONMENT", value = var.env },
    { name = "COST_CENTER", value = "550164" },
    { name = "CREATED_BY", value = "terraform" },
    { name = "PROJECT_ID", value = var.project_id },
    { name = "BIGQUERY_PROJECT_ID", value = var.bigquery_project_id },
    { name = "REDIS_HOST", value = var.redis_host_ip },
    { name = "REDIS_PORT", value = "6379" },
    { name = "REDIS_AUTH_STRING", value = data.terraform_remote_state.memorystore.outputs.output_auth_string },
    { name = "SHA", value = data.external.git.result.sha },
    { name = "POSTGRES_SOURCE_PROJECT_ID", value = var.api_project_id },
    { name = "POSTGRES_SOURCE_SECRET_NAME", value = var.postgres_secret_name }
    #{ name = "GCP_POSTGRES_SECRET_NAME", value = data.terraform_remote_state.cloudsql.outputs.secret_version_name },
    #{ name = "GCP_REDIS_SECRET_NAME", value = data.terraform_remote_state.memorystore.outputs.redis_secret_name },
    #{ name = "AUTH_DOMAIN", value = var.auth_domain },
    # { name = "AUTH_AUDIENCE", value = var.auth_audience },
  ]

  # https://cloud.google.com/run/docs/securing/ingress
  service_annotations = {
    # "run.googleapis.com/ingress" : "internal-and-cloud-load-balancing" # <<-- goal setting
    "run.googleapis.com/ingress" : "all"
    "run.googleapis.com/binary-authorization" : "default"
  }

  template_annotations = {
    "autoscaling.knative.dev/maxScale" = var.default_cloudrun_max
    "autoscaling.knative.dev/minScale" = var.default_cloudrun_min
    #"run.googleapis.com/vpc-access-connector" = "projects/${var.project_id}/locations/${var.region}/connectors/${var.project_name}-${var.env}-${var.region}"
    "run.googleapis.com/vpc-access-connector" = "projects/${var.api_project_id}/locations/${var.region}/connectors/${var.env}-${var.api_project_id}"
    "run.googleapis.com/vpc-access-egress"    = "all-traffic"
  }
}

resource "google_cloud_run_service_iam_member" "run_cloudrun_metric_processor" {
  service  = module.cloud_run_metric_processor.service_name
  location = var.region
  role     = "roles/run.invoker"
  member   = "serviceAccount:service-${data.google_project.project.number}@gcp-sa-iap.iam.gserviceaccount.com"
}

resource "google_cloud_run_service_iam_policy" "noauth_metric_processor" {
  location    = var.region
  project     = var.project_id
  service     = module.cloud_run_metric_processor.service_name
  policy_data = data.google_iam_policy.noauth.policy_data
}

resource "google_project_iam_member" "metric_processor_pubsub_publisher" {
  project = var.project_id
  role    = "roles/pubsub.publisher"
  member  = "serviceAccount:${module.service_account_metric_processor.email}"
}

output "service_name_metric_processor" {
  value       = module.cloud_run_metric_processor.service_name
  description = "Name of the created service"
}

output "revision_metric_processor" {
  value       = module.cloud_run_metric_processor.revision
  description = "Deployed revision for the service"
}

output "service_url_metric_processor" {
  value       = module.cloud_run_metric_processor.service_url
  description = "The URL on which the deployed service is available"
}

output "service_id_metric_processor" {
  value       = module.cloud_run_metric_processor.service_id
  description = "Unique Identifier for the created service"
}

output "service_status_metric_processor" {
  value       = module.cloud_run_metric_processor.service_status
  description = "Status of the created service"
}

output "service_location_metric_processor" {
  value       = module.cloud_run_metric_processor.location
  description = "Location in which the Cloud Run service was created"
}


resource "google_compute_region_network_endpoint_group" "metric_processor_neg" {
  provider              = google-beta
  name                  = "${var.env}-${var.project_name}-metric-processor-neg"
  network_endpoint_type = "SERVERLESS"
  region                = var.region
  cloud_run {
    service = module.cloud_run_metric_processor.service_name
  }
}

resource "google_compute_backend_service" "metric_processor_default" {
  name = "${var.env}-${var.project_name}-metric-processor-backend"

  protocol        = "HTTP"
  port_name       = "http"
  timeout_sec     = 30
  security_policy = module.security_policy.policy.id

  log_config {
    enable      = true
    sample_rate = 1.0
  }

  backend {
    group = google_compute_region_network_endpoint_group.metric_processor_neg.id
  }
}

resource "google_pubsub_subscription" "silver_pubsub_to_metric_processor_subscription" {
  ack_deadline_seconds = 10
  expiration_policy {
    ttl = "2678400s"
  }
  message_retention_duration = "604800s"
  name                       = "${var.project_id}-silver-pubsub-to-metric-processor-subscription"
  project                    = var.project_id
  labels                     = local.labels

  push_config {
    push_endpoint = module.cloud_run_metric_processor.service_url
    oidc_token {
      service_account_email = module.service_account_metric_processor.email
      audience              = "allUsers"
    }
  }

  retry_policy {
    minimum_backoff = "10s"
  }

  topic = "projects/${var.edp_project_id}/topics/silver_adi"
  /*
  filter = data.external.metric_processor_filter.result.filter #Get filter from data source output
  depends_on = [data.external.metric_processor_filter]

*/
}
