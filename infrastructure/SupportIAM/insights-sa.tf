module "service_account_insights_etl" {
  source     = "terraform-google-modules/service-accounts/google"
  version    = "~> 4.1.1"
  project_id = var.project_id
  prefix     = "${var.env}-${var.project_name}-insights-etl"
  names      = ["gen"]
  project_roles = [
    "${var.project_id}=>roles/storage.objectViewer",
    "${var.project_id}=>roles/bigquery.user",
    "${var.project_id}=>roles/bigquery.dataViewer",
    "${var.project_id}=>roles/compute.networkUser",
    "${var.project_id}=>roles/vpcaccess.user",
    "${var.project_id}=>roles/pubsub.editor",
  ]
}
