#!/bin/bash

echo "This script builds and pushes docker images up to Google Cloud Artifact Repository for local apps."
echo "It is intended to utilize these images as seeds for 2-stage docker builds of all needed functions."
echo "This should be in a shared repo"

set -ex
Common_Docker_Images=( "ict-test-etl" "ict-c14n" "ict-metric-processor") 

# TODO: Implement careful versioning
PROJECT_ID="ict-o-registry" 
REGISTRY_URL="us-docker.pkg.dev/ict-o-registry/ict-o-registry"
REGION="us-east1"

GIT_SHA=$(git rev-parse HEAD)

echo "SHA IS $GIT_SHA"

if [ $# -eq 0 ] ; then
  ACTION="build"
else 
  ACTION="push"  
fi

# Not yet intended to be used until we are off Azure... 
#NPM_LOCAL_ARTIFACTORY_URL=$NPM_LOCAL_ARTIFACTORY_URL
# DEV_BLD_ARTIFACTORY_PWD=$DEV_BLD_ARTIFACTORY_PWD
# DEV_BLD_USERNAME=$DEV_BLD_USERNAME

TAG=$GIT_SHA
printf "Preparing to build and tag images with $TAG" 

# To login with docker:
gcloud auth list
gcloud auth configure-docker us-docker.pkg.dev
docker-credential-gcloud gcr-login


for docker in "${Common_Docker_Images[@]}"
do
   :
   echo "docker to build is ${docker} tagged ${TAG}"
 
   cd ..
   cd ./cloudrun/${docker}/
   pwd

  if [ $ACTION == "build" ] ;
   then
      echo "Building Docker image for ${docker}"
      docker build -t ${REGISTRY_URL}/${docker}:${TAG} -f ./Dockerfile  --build-arg DEV_BLD_USERNAME=$DEV_BLD_USERNAME --build-arg DEV_BLD_ARTIFACTORY_PWD=$DEV_BLD_ARTIFACTORY_PWD --build-arg docker=${docker} .
   else
      echo "Tagging Existing Images"
      docker images list
      docker tag  ${OLD_REGISTRY_URL}/${docker}:${TAG} ${REGISTRY_URL}/${docker}:${TAG}

   fi

   docker tag ${REGISTRY_URL}/${docker}:${TAG} ${REGISTRY_URL}/${docker}:latest
   # To push image:
   docker push ${REGISTRY_URL}/${docker}:${TAG}
   docker push ${REGISTRY_URL}/${docker}:latest


   cd ../../infrastructure
   pwd
   # debug for one run only
   # exit 0
done
