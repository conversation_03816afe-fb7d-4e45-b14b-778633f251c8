variable "gitlab_project_shortname" {
  type        = string
  description = "The shortname for the application, or gitlab repository that this repository is cloned from.  Used in the statefile path"
}

variable "project_id" {
  type        = string
  description = "project id"
}

variable "project_number" {
  type        = string
  description = "project number"
  default     = null
}

variable "zone" {
  type        = string
  description = "The GCP zone to deploy the infra into."
  default     = "us-east1-b"
}

variable "alternate_zone" {
  type        = string
  description = "The Secondary GCP zone to deploy the infra into."
  default     = "us-east1-d"
}

variable "region" {
  type        = string
  description = "GCP Region"
  default     = "us-east1"
}

variable "project_name" {
  type        = string
  default     = "ict"
  description = "Project Tag Name"
}

variable "service_name" {
  type        = string
  default     = "testing"
  description = "Name of the thing we are working on"
}

variable "env" {
  type        = string
  default     = "sandbox"
  description = "Environment Variable"
}

variable "ict_artifact_registry" {
  type        = string
  description = "Artifact Registry for ICT"
  default     = "us-docker.pkg.dev/ict-o-registry/ict-o-registry"
}

# RESOURCE TAGGING
variable "tags" {
  type        = list(string)
  description = "Tags are key:value pairs used to provide fine-grained control over policies, such as network access."
  default     = []
}
variable "costcenter" {
  type        = string
  description = "Identifies which group is responsible for the cost of this resource."
  default     = "550164"
}

variable "application" {
  type        = string
  description = "Identifies which application this resource is used for. Use only lowercase, numbers, underscores and dashes."
  default     = "control-tower"
}

variable "customer" {
  type        = string
  description = "Identifies which group/department is using this resource. Can be the 'customer', 'demo', or 'internal'."
  default     = "customer"
}

variable "owner" {
  type        = string
  description = "Identifies who is responsible for the cost of the department. Format: first-last."
  default     = "carl-parkin"
}

variable "role" {
  type        = string
  description = "Identifies what role this resource is responsible for."
  default     = "application"
}

variable "createdby" {
  type        = string
  description = "Identifies who/what created this resource."
  default     = "terraform"
}

variable "lifetime" {
  type        = string
  description = "Identifies the approximate date (YYYY-MM-DD | perpetual) indicating when the resource will no longer be needed.  This does NOT delete the resource on the date. If you don't know how long it'll be needed, put a date no further away than 6 months from now max."
  default     = ""
}

variable "contact" {
  type        = string
  description = "Identifies the Dematic employee/group who can be contact if there are questions about the resource/group or other issues, such as to coordinate downtime.  Typically the lead user of the resource/group."
  default     = "dl-global-digitalsolutions-dev"
}

variable "project" {
  type        = string
  description = "Identifies this resource if it is used for a particular project. The value of this tag is alpha-numeric, but can be omitted."
  default     = null
}

variable "host_project_id" {
  type        = string
  description = "Host Project ID where Networking will be created"
  default     = null
}

variable "api_project_id" {
  type        = string
  description = "API Project ID where API will need access"
  default     = null
}

variable "network_name" {
  type        = string
  description = "Name of Network for use with associating any subnetworks or bindings"
  default     = null
}

variable "subnet_01_pub" {
  type        = string
  description = "CIDR for pub if needed"
  default     = null
}

variable "subnet_02_priv" {
  type        = string
  description = "CIDR for priv if needed"
  default     = null
}

variable "vpc_serverless_connector_cidr" {
  type        = string
  description = "CIDR block for private VPC Network Serverless Connector"
  default     = null
}

variable "external_allocation_range" {
  type        = string
  description = "Allocated IP Ranges for Services. See VPC --> Private Service Connection. Add prefix. Suggestion is a /16 in 10. space. but we are creating /24."
  default     = null
}

variable "dr_bucket_retention_days" {
  type        = string
  description = "Disaster Recovery Bucket Retention Days. Generic to all DR at this tier."
  default     = 31
}

variable "auth0_domain" {
  type        = string
  description = "Auth0 Domain"
  default     = null
}

variable "auth0_client_id" {
  type        = string
  description = "Auth0 Client ID"
  default     = null
}

variable "auth0_mgmt_client_id" {
  type        = string
  description = "Auth0 Management Client ID"
  default     = null
}

variable "auth0_mgmt_client_secret" {
  type        = string
  description = "Auth0 Management Client Secret"
  default     = null
  sensitive   = true
}

variable "auth0_audience" {
  type        = string
  description = "Auth0 Audience"
  default     = null
}

variable "tenants" {
  description = "Tenants to be created in Auth0 and to be used for creating BigQuery Datasets and Tables."
  type = list(object({
    name                            = string
    display_name                    = optional(string)
    exclude_from_auth0_organization = optional(bool, false)
    logo_url                        = optional(string)
    branding_primary_color          = optional(string, "#0059d6")
    branding_page_background_color  = optional(string, "#000000")
    metadata                        = optional(map(string))
    facilities                      = optional(list(string), [])
    connections = optional(
      object({
        google = optional(
          object({
            enabled                    = bool
            assign_membership_on_login = optional(bool, false)
          }),
          {
            enabled                    = false
            assign_membership_on_login = null
        })
        auth0 = optional(
          object({
            enabled                    = bool
            assign_membership_on_login = optional(bool, false)
          }),
          {
            enabled                    = false
            assign_membership_on_login = null
          }
        )
      }),
      {
        google = { enabled = false }
        auth0  = { enabled = false }
      }
    )
  }))
  default = null
}

variable "destroy_datasets" {
  type        = bool
  description = "Destroy BigQuery Datasets"
  default     = false
}

variable "insights_publisher_sa" {
  type        = string
  description = "Insights Cloud Function SA with right publish permissions."
  default     = null
}

variable "edp_subscriber_sa" {
  type        = string
  description = "EDP SA with right subscriber permissions."
  default     = null
}

variable "default_cloudrun_min" {
  type        = number
  description = "Default per environment number of minimum CloudRun services to scale in knative."
  default     = 1
}

variable "default_cloudrun_max" {
  type        = number
  description = "Default per environment number of maximum CloudRun services to scale in knative."
  default     = 12
}

variable "devops_teams_channel" {
  type        = string
  default     = "<EMAIL>"
  description = "System Notifications - SCS Dematic R&D Digital Solutions <<EMAIL>>"
}

variable "bigquery_project_id" {
  type        = string
  description = "Project ID to use to connect the BigQuery client to."
  default     = "ict-b-prototype-etlt"
}

variable "edp_project_id" {
  type        = string
  default     = "edp-d-us-east2-etl"
  description = "Project ID in corresponding EDP environment"
}

variable "redis_host_ip" {
  type        = string
  description = "Host IP Address of the Redis Cluster in API"
  default     = null
}

variable "tableau_PAT_name" {
  type        = string
  description = "Tableau Personal Access Token Name"
  default     = null
  sensitive   = true
}

variable "tableau_server_url" {
  type        = string
  description = "Tableau Server URL"
  default     = null
}

variable "tableau_PAT_secret" {
  type        = string
  description = "Tableau Personal Access Token Secret"
  default     = null
  sensitive   = true
}

variable "MR_IID" {
  type        = string
  description = "Merge Request IID. Used to make unique bigquery dataset names for liquibase testing."
  default     = null
}

variable "ops_management_project_id" {
  type        = string
  description = "Ops Management Project ID"
  default     = "ict-ops-management"
}

variable "fluid_topics_callback_urls" {
  type        = list(string)
  description = "Fluid Topics Callback URLs"
  default     = []
}

variable "postgres_secret_name" {
  type        = string
  description = "Name of the PostgreSQL secret in the API project"
  default     = null
}
