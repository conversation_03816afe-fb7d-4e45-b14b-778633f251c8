module "vpc" {
  source  = "terraform-google-modules/network/google"
  version = "~> 7.1"

  project_id   = var.project_id
  network_name = "${var.env}-${var.project_id}-private"
  routing_mode = "GLOBAL"

  subnets = [
    {
      subnet_name               = "${var.env}-ict-01"
      subnet_ip                 = var.subnet_01_pub
      subnet_region             = "us-east1"
      description               = "Public Subnet 01"
      subnet_flow_logs_interval = "INTERVAL_10_MIN"
      subnet_flow_logs_sampling = 0.7
      subnet_flow_logs_metadata = "INCLUDE_ALL_METADATA"
    },
    {
      subnet_name               = "${var.env}-ict-02-priv"
      subnet_ip                 = var.subnet_02_priv
      subnet_region             = "us-east1"
      subnet_private_access     = "true"
      subnet_flow_logs          = "true"
      description               = "Private Subnet 02 PRIV"
      subnet_flow_logs_interval = "INTERVAL_10_MIN"
      subnet_flow_logs_sampling = 0.7
      subnet_flow_logs_metadata = "INCLUDE_ALL_METADATA"
    },
    {
      subnet_name               = "${var.env}-ict-03-serverless-connector"
      subnet_ip                 = var.vpc_serverless_connector_cidr
      subnet_region             = "us-east1"
      subnet_private_access     = "true"
      subnet_flow_logs          = "true"
      description               = "VPC Serverless Connector 03"
      subnet_flow_logs_interval = "INTERVAL_10_MIN"
      subnet_flow_logs_sampling = 0.7
      subnet_flow_logs_metadata = "INCLUDE_ALL_METADATA"
    }
  ]

  secondary_ranges = {
    services_allocation = [
    ]
  }

  routes = [
    {
      name              = "egress-internet"
      description       = "route through IGW to access internet"
      destination_range = "0.0.0.0/0"
      tags              = "egress-inet"

      next_hop_internet = "true"
    },
  ]
}


resource "google_compute_firewall" "default" {
  name    = "iap-ssh"
  network = module.vpc.network_name

  allow {
    protocol = "icmp"

  }

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = ["************/20"]
}
