# All of the services listed here must be enabled for ALL projects participating in this advanture

locals {
  # Service Projects need love too!
  service_services_to_enable = [
    "compute.googleapis.com",
    "container.googleapis.com",
    "servicenetworking.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "vpcaccess.googleapis.com"
  ]
}



resource "google_project_service" "service_services" {
  for_each                   = toset(local.service_services_to_enable)
  project                    = var.project_id
  service                    = each.value
  disable_dependent_services = false
  disable_on_destroy         = false

}