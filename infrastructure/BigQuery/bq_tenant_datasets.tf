# locals {
#   tenants = {
#     for tenant in var.tenants :
#     tenant.metadata.dataset => tenant
#     if lookup(tenant, "metadata", null) != null &&
#     lookup(tenant.metadata, "dataset", null) != null
#   }
# }

# module "bigquery_landing" {
#   source   = "terraform-google-modules/bigquery/google"
#   version  = "~> 6.1"
#   for_each = local.tenants

#   # Dataset IDs must be alphanumeric (plus underscores) and must be at most 1024 characters long.
#   dataset_id   = replace("${each.value.metadata.dataset}_landing", "-", "_")
#   dataset_name = "${each.value.metadata.dataset}_landing"
#   description  = "${local.env}_${var.project_id} dataset ${each.value.metadata.dataset} Landing"
#   project_id   = var.project_id
#   location     = "US"
#   # default_table_expiration_ms = 3600000
#   dataset_labels = local.labels
#   access = [
#     {
#       "role" : "roles/bigquery.dataOwner",
#       "special_group" : "projectOwners"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-mock-data-gen@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-operators-simple@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-orders-simple@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-inventory-simple@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-equipment-gen@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "roles/bigquery.admin",
#       "user_by_email" : "${local.env}-ict-config-gen@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#   ]
#   delete_contents_on_destroy = var.destroy_datasets
# }

# output "bigquery_landing" {
#   value       = module.bigquery_landing
#   description = "Tenant landing Bigquery dataset resource."
# }

# module "bigquery_oa_curated" {
#   source   = "terraform-google-modules/bigquery/google"
#   version  = "~> 6.1"
#   for_each = local.tenants

#   # Dataset IDs must be alphanumeric (plus underscores) and must be at most 1024 characters long.
#   dataset_id   = replace("${each.value.metadata.dataset}_oa_curated", "-", "_")
#   dataset_name = "${each.value.metadata.dataset}_oa_curated"
#   description  = "${local.env}_${var.project_id} dataset ${each.value.metadata.dataset} oa_curated"
#   project_id   = var.project_id
#   location     = "US"
#   # default_table_expiration_ms = 3600000
#   dataset_labels = local.labels

#   # https://cloud.google.com/iam/docs/understanding-roles#predefined_roles
#   # NOTE: Legacy roles are used below and should be changed.
#   access = [
#     {
#       "role" : "roles/bigquery.dataOwner",
#       "special_group" : "projectOwners"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-mock-data-gen@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-operators-simple@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-orders-simple@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-inventory-simple@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-equipment-gen@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "roles/bigquery.admin",
#       "user_by_email" : "${local.env}-ict-config-gen@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#   ]
#   delete_contents_on_destroy = var.destroy_datasets
# }

# output "bigquery_oa_curated" {
#   value       = module.bigquery_oa_curated
#   description = "Tenant oa_curated Bigquery dataset resource."
# }
