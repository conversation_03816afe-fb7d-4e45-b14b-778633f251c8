# locals {
#   # BigQuery Datasets to Make
#   big_query_datasets = [
#     var.env == "test" ? "test_${var.MR_IID}_liquibase_changelogs" : "liquibase_changelogs"
#   ]
# }

# module "bigquery" {
#   source   = "terraform-google-modules/bigquery/google"
#   version  = "~> 6.1"
#   for_each = toset(local.big_query_datasets)

#   # Dataset IDs must be alphanumeric (plus underscores) and must be at most 1024 characters long.
#   dataset_id   = replace(each.key, "-", "_")
#   dataset_name = each.key
#   description  = "${local.env}_${var.project_id} dataset ${each.key}"
#   project_id   = var.project_id
#   location     = "US"
#   # default_table_expiration_ms = 3600000
#   dataset_labels = local.labels
#   access = [
#     {
#       "role" : "roles/bigquery.dataOwner",
#       "special_group" : "projectOwners"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-mock-data-gen@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-operators-simple@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-orders-simple@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-inventory-simple@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#     {
#       "role" : "WRITER",
#       "user_by_email" : "${local.env}-ict-equipment-gen@${var.api_project_id}.iam.gserviceaccount.com"
#     },
#   ]
#   delete_contents_on_destroy = var.destroy_datasets
# }

# output "bigquery" {
#   value       = module.bigquery
#   description = "Bigquery dataset resource."
# }
