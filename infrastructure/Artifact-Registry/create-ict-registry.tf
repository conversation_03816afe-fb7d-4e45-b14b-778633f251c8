# This TF function run will:
# Create T

locals {
  dev_tf_deployer_service_account   = "<EMAIL>"
  stage_tf_deployer_service_account = "<EMAIL>"
  prod_tf_deployer_service_account  = "<EMAIL>"
}



# Some things can't do this
# Some can. 
resource "google_project_service" "artifactregistry" {
  service            = "artifactregistry.googleapis.com"
  project            = var.project_id
  disable_on_destroy = false

}


module "ict_artifact_registry" {
  source  = "github.com/dapperlabs-platform/terraform-google-artifact-registry"
  version = "0.9.3"

  project_id = var.project_id
  location   = "us-east1"
  format     = "DOCKER"
  id         = "${var.project_id}-registry"

  depends_on = [google_project_service.artifactregistry]

  iam = {
    "roles/artifactregistry.admin" = [
      "serviceAccount:${local.dev_tf_deployer_service_account}",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "serviceAccount:<EMAIL>"

    ]

    "roles/artifactregistry.writer" = [
      "serviceAccount:${local.dev_tf_deployer_service_account}",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "serviceAccount:<EMAIL>"
    ]

    "roles/artifactregistry.reader" = [
      "serviceAccount:${local.dev_tf_deployer_service_account}",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>"
    ]


  }
}
