terraform {
  backend "gcs" {
    bucket = "ict-p-statefiles"
    prefix = "${var.env}/${var.gitlab_project_shortname}/${basename(abspath(path.root))}"
  }

  required_providers {
    google = {
      version = "~> 4.67"
      source  = "hashicorp/google"
    }
    auth0 = {
      source  = "auth0/auth0"
      version = "1.13.1"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

provider "auth0" {
  domain    = var.auth0_domain
  client_id = var.auth0_client_id
  audience  = var.auth0_audience
}
