locals {
  tenant_map = {
    for tenant in var.tenants : tenant.name => tenant
    if tenant.exclude_from_auth0_organization != true
  }
  tenant_connections = {
    for tenant in var.tenants : tenant.name => tenant
    if tenant.exclude_from_auth0_organization != true && (tenant.connections.google.enabled != false || tenant.connections.auth0.enabled != false)
  }
}

# Create the Auth0 Organization
resource "auth0_organization" "org" {
  for_each = local.tenant_map

  name         = each.value.name
  display_name = each.value.display_name != null ? each.value.display_name : title(replace(each.value.name, "-", " "))

  branding {
    logo_url = each.value.logo_url
    colors = {
      primary         = each.value.branding_primary_color
      page_background = each.value.branding_page_background_color
    }
  }
  metadata = merge(each.value.metadata, { managed_by = "terraform" }, { default_roles = local.tenant_default_roles_map[each.value.name] })
}

# Create regular facility roles for each tenant
resource "auth0_role" "facility_roles" {
  for_each = {
    for idx, item in local.tenant_facility_roles : item.role_name => item
  }

  name        = each.value.role_name
  description = "Facility access role for ${each.value.facility} in tenant ${each.value.tenant}"
}

# Create facility admin roles for each tenant
resource "auth0_role" "facility_admin_roles" {
  for_each = {
    for idx, item in local.tenant_facility_admin_roles : item.role_name => item
  }

  name        = each.value.role_name
  description = "Facility administrator role for ${each.value.facility} in tenant ${each.value.tenant}"
}

resource "auth0_connection" "google_oauth2" {
  name     = "google-oauth2"
  strategy = "google-oauth2"
}

resource "auth0_connection" "username_password_authentication" {
  name     = "Username-Password-Authentication"
  strategy = "auth0"
}

resource "auth0_organization_connections" "connections" {
  for_each = local.tenant_connections

  organization_id = auth0_organization.org[each.value.name].id

  dynamic "enabled_connections" {
    for_each = each.value.connections.auth0.enabled == true ? [1] : []
    content {
      connection_id              = auth0_connection.username_password_authentication.id
      assign_membership_on_login = each.value.connections.auth0.assign_membership_on_login
    }
  }

  dynamic "enabled_connections" {
    for_each = each.value.connections.google.enabled == true ? [1] : []
    content {
      connection_id              = auth0_connection.google_oauth2.id
      assign_membership_on_login = each.value.connections.google.assign_membership_on_login
    }
  }
}

# Define custom auth0 actions
resource "auth0_action" "add_organization_info" {
  name    = "Add Organization Info"
  runtime = "node18"
  deploy  = true
  code    = file("../../auth0/actions/add_organization_info.js")

  supported_triggers {
    id      = "post-login"
    version = "v3"
  }
}

# Define custom auth0 actions
resource "auth0_action" "add_user_email_info" {
  name    = "Add User Email Info"
  runtime = "node18"
  deploy  = true
  code    = file("../../auth0/actions/add_user_email_info.js")

  supported_triggers {
    id      = "post-login"
    version = "v3"
  }
}

resource "auth0_action" "add_user_roles" {
  name    = "Add User Roles"
  runtime = "node18"
  deploy  = true
  code    = file("../../auth0/actions/add_user_roles.js")

  supported_triggers {
    id      = "post-login"
    version = "v3"
  }
  secrets {
    name  = "domain"
    value = var.auth0_domain
  }
  secrets {
    name  = "clientId"
    value = var.auth0_mgmt_client_id
  }
  secrets {
    name  = "clientSecret"
    value = var.auth0_mgmt_client_secret
  }
  dependencies {
    name    = "auth0"
    version = "4.4.0"
  }
}

resource "auth0_action" "add_organization_info_to_cc_flow" {
  name    = "Add Organization Info to CC flow"
  runtime = "node18"
  deploy  = true
  code    = file("../../auth0/actions/add_organization_info_to_cc_flow.js")

  supported_triggers {
    id      = "credentials-exchange"
    version = "v2"
  }
}

resource "auth0_action" "auto_enroll_by_domain" {
  name    = "Auto Enroll By Domain"
  runtime = "node18"
  deploy  = true
  code    = file("../../auth0/actions/auto_enroll_by_domain.js")
  supported_triggers {
    id      = "post-user-registration"
    version = "v2"
  }
  secrets {
    name  = "domain"
    value = var.auth0_domain
  }
  secrets {
    name  = "clientId"
    value = var.auth0_mgmt_client_id
  }
  ## Need to figure out how to load the secret from env.
  secrets {
    name  = "clientSecret"
    value = var.auth0_mgmt_client_secret
  }
  dependencies {
    name    = "auth0"
    version = "4.4.0"
  }
}

resource "auth0_action" "tableau" {
  name    = "Tableau Account Management"
  runtime = "node18"
  deploy  = "true"
  code    = file("../../auth0/actions/tableau.js")
  supported_triggers {
    id      = "post-login"
    version = "v3"
  }
  secrets {
    name  = "adminPATName"
    value = var.tableau_PAT_name
  }
  secrets {
    name  = "adminPATSecret"
    value = var.tableau_PAT_secret
  }
  secrets {
    name  = "tableauServerURL"
    value = var.tableau_server_url
  }
  dependencies {
    name    = "axios"
    version = "1.7.2"
  }
}

# Attach the needed actions to the post-login flow
resource "auth0_trigger_actions" "post_login" {
  trigger = "post-login"

  actions {
    id           = auth0_action.add_organization_info.id
    display_name = auth0_action.add_organization_info.name
  }
  actions {
    id           = auth0_action.add_user_email_info.id
    display_name = auth0_action.add_user_email_info.name
  }
  actions {
    id           = auth0_action.add_user_roles.id
    display_name = auth0_action.add_user_roles.name
  }
  actions {
    id           = auth0_action.tableau.id
    display_name = auth0_action.tableau.name
  }
}

# Attach the needed actions to the M2M/Client-credentials flow
resource "auth0_trigger_actions" "credentials_exchange" {
  trigger = "credentials-exchange"

  actions {
    id           = auth0_action.add_organization_info_to_cc_flow.id
    display_name = auth0_action.add_organization_info_to_cc_flow.name
  }
}

# Attach the post user registration flow
resource "auth0_trigger_actions" "post_user_registration" {
  trigger = "post-user-registration"

  actions {
    id           = auth0_action.auto_enroll_by_domain.id
    display_name = auth0_action.auto_enroll_by_domain.name
  }
}

# --TABLEAU COMPONENTS--

resource "auth0_role" "ct_tableau_viewer" {
  description = "Tableau viewer."
  name        = "ct_tableau_viewer"
}

resource "auth0_role" "ct_tableau_admin" {
  description = "Tableau admin."
  name        = "ct_tableau_admin"
}

resource "auth0_role" "ct_tableau_explorer" {
  description = "Tableau Explorer."
  name        = "ct_tableau_explorer"
}

resource "auth0_client" "tableau" {
  allowed_clients                       = []
  allowed_logout_urls                   = []
  allowed_origins                       = []
  app_type                              = "regular_web"
  callbacks                             = local.callback_urls
  client_aliases                        = []
  client_metadata                       = {}
  cross_origin_auth                     = false
  cross_origin_loc                      = null
  custom_login_page                     = null
  custom_login_page_on                  = true
  description                           = null
  form_template                         = null
  grant_types                           = ["authorization_code", "implicit", "refresh_token"]
  initiate_login_uri                    = null
  is_first_party                        = true
  is_token_endpoint_ip_header_trusted   = false
  logo_uri                              = null
  name                                  = "Tableau"
  oidc_backchannel_logout_urls          = []
  oidc_conformant                       = true
  organization_require_behavior         = null
  organization_usage                    = null
  require_pushed_authorization_requests = false
  sso                                   = false
  sso_disabled                          = false
  web_origins                           = []
  jwt_configuration {
    alg                 = "RS256"
    lifetime_in_seconds = 36000
    scopes              = {}
    secret_encoded      = false
  }
  native_social_login {
    apple {
      enabled = false
    }
    facebook {
      enabled = false
    }
  }
  refresh_token {
    expiration_type              = "non-expiring"
    idle_token_lifetime          = 1296000
    infinite_idle_token_lifetime = true
    infinite_token_lifetime      = true
    leeway                       = 0
    rotation_type                = "non-rotating"
    token_lifetime               = 2592000
  }
}

resource "random_password" "auth0_client_secret" {
  length           = 64
  special          = true
  override_special = "_-+=."
}

resource "random_password" "auth0_tableau_admin_password" {
  length           = 16
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"
}

resource "auth0_client_credentials" "tableau_client_credentials" {
  client_id             = auth0_client.tableau.client_id
  client_secret         = random_password.auth0_client_secret.result
  authentication_method = "client_secret_post"
}

resource "auth0_client_grant" "tableau_client" {
  audience  = "tableau"
  client_id = auth0_client.tableau.client_id
  scopes    = ["tableau:content:read", "tableau:data_driven_alerts:read"]
}

resource "auth0_resource_server" "tableau" {
  allow_offline_access                            = false
  enforce_policies                                = true
  identifier                                      = "tableau"
  name                                            = "Tableau"
  signing_alg                                     = "RS256"
  signing_secret                                  = null
  skip_consent_for_verifiable_first_party_clients = true
  token_dialect                                   = "access_token_authz"
  token_lifetime                                  = 86400
  token_lifetime_for_web                          = 7200
  verification_location                           = null
}

resource "auth0_resource_server_scopes" "tableau_resource_server_scopes" {
  resource_server_identifier = auth0_resource_server.tableau.identifier

  scopes {
    name = "tableau:content:read"
  }

  scopes {
    name = "tableau:data_driven_alerts:read"
  }
}

resource "auth0_role_permissions" "ct_tableau_viewer" {
  role_id = auth0_role.ct_tableau_viewer.id
  dynamic "permissions" {
    for_each = auth0_resource_server_scopes.tableau_resource_server_scopes.scopes
    content {
      name                       = permissions.value.name
      resource_server_identifier = auth0_resource_server.tableau.identifier
    }
  }
}

resource "auth0_role_permissions" "ct_tableau_admin" {
  role_id = auth0_role.ct_tableau_admin.id
  dynamic "permissions" {
    for_each = auth0_resource_server_scopes.tableau_resource_server_scopes.scopes
    content {
      name                       = permissions.value.name
      resource_server_identifier = auth0_resource_server.tableau.identifier
    }
  }
}

resource "auth0_role_permissions" "ct_tableau_explorer" {
  role_id = auth0_role.ct_tableau_explorer.id
  dynamic "permissions" {
    for_each = auth0_resource_server_scopes.tableau_resource_server_scopes.scopes
    content {
      name                       = permissions.value.name
      resource_server_identifier = auth0_resource_server.tableau.identifier
    }
  }
}

resource "auth0_user" "auth0_tableau_server_admin" {
  email           = var.devops_teams_channel
  email_verified  = true
  name            = "Tableau Server Admin"
  connection_name = "Username-Password-Authentication"
  password        = random_password.auth0_tableau_admin_password.result
}

resource "google_secret_manager_secret" "auth0_tableau_client_secret" {
  secret_id = "${var.project_name}-${var.env}-auth0-tableau-client-secret"
  project   = var.ops_management_project_id

  replication {
    auto {

    }
  }
}

resource "google_secret_manager_secret_version" "auth0_tableau_client_secret" {
  secret      = google_secret_manager_secret.auth0_tableau_client_secret.id
  secret_data = random_password.auth0_client_secret.result
}

resource "google_secret_manager_secret" "auth0_tableau_server_admin_creds" {
  secret_id = "${var.project_name}-${var.env}-tableau-server-admin-creds"
  project   = var.ops_management_project_id

  labels = local.labels

  replication {
    auto {

    }
  }
}

resource "google_secret_manager_secret_version" "auth0_tableau_server_admin_creds" {
  secret = google_secret_manager_secret.auth0_tableau_server_admin_creds.id
  secret_data = jsonencode({
    username = auth0_user.auth0_tableau_server_admin.email
    password = random_password.auth0_tableau_admin_password.result
  })
}

resource "auth0_client" "fluid_topics" {
  allowed_clients                       = []
  allowed_logout_urls                   = []
  allowed_origins                       = []
  app_type                              = "spa"
  callbacks                             = var.fluid_topics_callback_urls
  client_aliases                        = []
  client_metadata                       = {}
  compliance_level                      = null
  cross_origin_auth                     = false
  cross_origin_loc                      = null
  custom_login_page                     = null
  custom_login_page_on                  = true
  description                           = null
  encryption_key                        = null
  form_template                         = null
  grant_types                           = ["authorization_code", "implicit", "refresh_token"]
  initiate_login_uri                    = null
  is_first_party                        = true
  is_token_endpoint_ip_header_trusted   = false
  logo_uri                              = null
  name                                  = "Fluid Topics"
  oidc_conformant                       = true
  organization_require_behavior         = "post_login_prompt"
  organization_usage                    = "require"
  require_proof_of_possession           = false
  require_pushed_authorization_requests = false
  sso                                   = false
  sso_disabled                          = false
  web_origins                           = []
  default_organization {
    disable         = true
    flows           = []
    organization_id = null
  }
  jwt_configuration {
    alg                 = "RS256"
    lifetime_in_seconds = 36000
    scopes              = {}
    secret_encoded      = false
  }
  native_social_login {
    apple {
      enabled = false
    }
    facebook {
      enabled = false
    }
    google {
      enabled = false
    }
  }
  refresh_token {
    expiration_type              = "expiring"
    idle_token_lifetime          = 1296000
    infinite_idle_token_lifetime = false
    infinite_token_lifetime      = false
    leeway                       = 0
    rotation_type                = "rotating"
    token_lifetime               = 2592000
  }
}
