# locals {
#   composer_service_account_id    = "cloudcomposer"
#   composer_service_account_email = "${local.composer_service_account_id}@${var.project_id}.iam.gserviceaccount.com"
#   data_transforms_folder         = replace(module.composer.gcs_bucket, "dags", "data/transforms")
# }

# resource "google_project_service" "composer_api" {
#   provider           = google-beta
#   project            = var.project_id
#   service            = "composer.googleapis.com"
#   disable_on_destroy = false
# }

# resource "google_service_account" "composer_service_account" {
#   provider     = google-beta
#   project      = var.project_id
#   account_id   = local.composer_service_account_id
#   display_name = "Composer Service Account"
# }

# module "composer" {
#   source                   = "terraform-google-modules/composer/google//modules/create_environment_v2"
#   version                  = "~> 3.4"
#   image_version            = "composer-2.4.5-airflow-2.5.3"
#   composer_service_account = "${local.composer_service_account_id}@${var.project_id}.iam.gserviceaccount.com"

#   project_id              = var.project_id
#   region                  = var.region
#   composer_env_name       = "cloud-composer"
#   network                 = var.network_name
#   subnetwork              = google_compute_subnetwork.cloud_composer_subnetwork.name
#   enable_private_endpoint = true
#   labels                  = local.labels

#   pod_ip_allocation_range_name     = "ict-composer-pod-range"
#   service_ip_allocation_range_name = "ict-composer-service-range"
#   use_private_environment          = true

#   depends_on = [
#     google_project_service.composer_api,
#     google_service_account.composer_service_account,
#     google_compute_subnetwork.cloud_composer_subnetwork
#   ]
# }

# module "composer_iam" {
#   source   = "terraform-google-modules/iam/google//modules/projects_iam"
#   version  = "~> 7.4.0"
#   projects = [var.project_id]
#   mode     = "additive"

#   bindings = {
#     "roles/compute.instanceAdmin.v1" = [
#       "serviceAccount:${local.composer_service_account_email}"
#     ]
#     "roles/compute.networkAdmin" = [
#       "serviceAccount:${local.composer_service_account_email}"
#     ]
#     "roles/iam.serviceAccountUser" = [
#       "serviceAccount:${local.composer_service_account_email}"
#     ]
#     "roles/composer.worker" = [
#       "serviceAccount:${local.composer_service_account_email}"
#     ]
#     "roles/bigquery.jobUser" = [
#       "serviceAccount:${local.composer_service_account_email}"
#     ]
#     "roles/bigquery.user" = [
#       "serviceAccount:${local.composer_service_account_email}"
#     ]
#     "roles/bigquery.dataViewer" = [
#       "serviceAccount:${local.composer_service_account_email}"
#     ]
#     "roles/bigquery.dataEditor" = [
#       "serviceAccount:${local.composer_service_account_email}"
#     ]
#   }
# }

# resource "null_resource" "airflow_dags" {
#   triggers = { always_runs = timestamp() }

#   provisioner "local-exec" {
#     command = <<EOT
#       gcloud storage rsync --delete-unmatched-destination-objects --recursive ../../airflow/dags/ ${module.composer.gcs_bucket}
#     EOT
#   }
# }

# resource "null_resource" "airflow_transforms" {
#   triggers = { always_runs = timestamp() }

#   provisioner "local-exec" {
#     command = <<EOT
#       gcloud storage rsync --delete-unmatched-destination-objects --recursive ../../airflow/transforms/ ${local.data_transforms_folder}
#     EOT
#   }
# }

# resource "google_compute_subnetwork" "cloud_composer_subnetwork" {
#   name                     = "${var.env}-cloud-composer-01"
#   ip_cidr_range            = "************/20"
#   region                   = var.region
#   network                  = var.network_name
#   private_ip_google_access = true

#   secondary_ip_range {
#     range_name    = "ict-composer-pod-range"
#     ip_cidr_range = "************/20"
#   }
#   secondary_ip_range {
#     range_name    = "ict-composer-service-range"
#     ip_cidr_range = "************/20"
#   }
# }

# # TODO: Delete this resource after we ensure that our DAGs are being synced to the Composer environment
# resource "google_storage_bucket" "airflow_source_bucket" {
#   name          = "${var.project_id}-bq-queries"
#   location      = "US"
#   force_destroy = true

#   storage_class            = "STANDARD"
#   public_access_prevention = "enforced"

#   labels = local.labels
# }
