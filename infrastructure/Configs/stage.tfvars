# SERVICEPROJECT_CRAFTED_NETWORK
gitlab_project_shortname = "etl"
zone                     = "us-east1-b"
region                   = "us-east1"
env                      = "stage"
project_name             = "ict-etl"
service_name             = "etl"
project_id               = "ict-s-us-east1-etl"
host_project_id          = "ict-s-us-east1-etl"
api_project_id           = "ict-s-us-east1-api"
subnet_01_pub            = "***********/24"
subnet_02_priv           = "***********/24"
# Spare space 34, 35, 36
vpc_serverless_connector_cidr = "***********/28" # This will create a /28 for association.. <<< MOVED >>> To Infra
# network_name = "${var.env}-${var.project_id}-private"
network_name = "stage-ict-s-us-east1-etl-private"
# https://www.davidc.net/sites/default/subnets/subnets.html
# PLAN  = "192.168.{16,32}.0/20"    # ROOT + Prefix . 
# This is for NON_REAL Google IP's which will be used for things like REDIS/CloudSQL
external_allocation_range = "************" # ROOT + Prefix . This is for NON_REAL Google IP's which will be used for things like REDIS/CloudSQL
dr_bucket_retention_days  = 31             # Default is 31. Go with 365 in prod? 

auth0_domain         = "control-tower-stage.us.auth0.com"
auth0_client_id      = "av75tfywc9JUfadhGsYt6kNByw0mInuJ"
auth0_mgmt_client_id = "6onztUMpTGD5DmvIdk2elko4Ee7lgeYw"
auth0_audience       = "https://control-tower-stage.us.auth0.com/api/v2/"

tableau_server_url = "https://stage.bi.ict.dematic.dev"
fluid_topics_callback_urls = [
  "https://dematic-staging.fluidtopics.net/api/authentication/sso/Auth0/callback",
  "https://staging-kb.dematic.com/api/authentication/sso/Auth0/callback"
]

tenants = [
  {
    name         = "dematic_solution_center"
    display_name = "Dematic Solution Center"
    metadata = {
      dataset     = "dematic"
      tableauSite = "DematicSolutionCenter"
    }
    facilities = ["grandrapidsmi"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "drt_automation"
    display_name = "DRT Automation"
    metadata = {
      dataset = "drt_automation"
    }
    facilities = ["drt_automation"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "ict_development"
    display_name = "ICT Development"
    metadata = {
      dataset = "ict_development"
    }
    facilities = ["ict_development"]
    connections = {
      auth0  = { enabled = true }
      google = { enabled = true }
    }
  },
  {
    name         = "integration_test"
    display_name = "Integration Test"
    metadata = {
      dataset = "integration_test"
    }
    facilities = ["integration_test"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "qa_automation"
    display_name = "QA Automation"
    metadata = {
      dataset = "qa_automation"
    }
    facilities = ["qa_automation"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "qa_manual"
    display_name = "QA Manual"
    metadata = {
      dataset = "qa_manual"
    }
    facilities = ["qa_automation"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "qa_manual_1"
    display_name = "QA Manual 1"
    metadata = {
      dataset = "qa_manual_1"
      domains = "dematic.com,kiongroup.com"
    }
    facilities = ["qa_automation"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "stark_industries"
    display_name = "Stark Industries"
    metadata = {
      tableauSite = "StarkIndustries"
      dataset     = "stark_industries"
    }
    facilities = ["stark_industries"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "superior_uniform"
    display_name = "Superior Uniform"
    metadata = {
      dataset     = "superior_uniform"
      tableauSite = "SuperiorUniform"
    }
    facilities = ["eudoraar"]
    connections = {
      auth0  = { enabled = true }
      google = { enabled = true }
    }
  },
  {
    name         = "tti"
    display_name = "Techtronic Industries"
    metadata = {
      dataset     = "tti"
      tableauSite = "TechtronicIndustries"
    }
    facilities = ["tti"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "verification_and_validation"
    display_name = "V&V"
    metadata = {
      dataset = "verification_and_validation"
      domains = "dematic.com,kiongroup.com"
    }
    facilities = ["qa_automation"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "acehardware"
    display_name = "Ace Hardware"
    metadata = {
      dataset     = "acehardware"
      tableauSite = "AceHardware"
    }
    facilities = ["jeffersonga", "plantcityfl", "visaliaca", "wilmertx"]
    connections = {
      auth0  = { enabled = true }
      google = { enabled = true }
    }
  },
]

insights_publisher_sa = "<EMAIL>"
edp_subscriber_sa     = "<EMAIL>"
edp_project_id        = "edp-s-us-east1-etl"
redis_host_ip         = "************"

postgres_secret_name = "ict-stage-postgres-ict-api"

#! This is to ensure that the BigQuery datasets are deleted when the infra is destroyed.
destroy_datasets = true
