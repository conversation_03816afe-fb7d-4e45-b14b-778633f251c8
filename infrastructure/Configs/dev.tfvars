# SERVICEPROJECT_CRAFTED_NETWORK
gitlab_project_shortname = "etl"
zone                     = "us-east1-b"
region                   = "us-east1"
env                      = "dev"
project_name             = "ict-etl"
service_name             = "etl"
project_id               = "ict-d-etl"
host_project_id          = "ict-d-etl"
api_project_id           = "ict-d-api"
subnet_01_pub            = "***********/24"
subnet_02_priv           = "***********/24"
# Spare space 34, 35, 36
vpc_serverless_connector_cidr = "***********/28" # This will create a /28 for association.. <<< MOVED >>> To Infra
# network_name = "${var.env}-${var.project_id}-private"
network_name = "dev-ict-d-etl-private"
# https://www.davidc.net/sites/default/subnets/subnets.html
# PLAN  = "192.168.{16,32}.0/20"    # ROOT + Prefix . 
# This is for NON_REAL Google IP's which will be used for things like REDIS/CloudSQL
external_allocation_range = "***********" # ROOT + Prefix . This is for NON_REAL Google IP's which will be used for things like REDIS/CloudSQL
dr_bucket_retention_days  = 31            # Default is 31. Go with 365 in prod? 

auth0_domain         = "dev-zmogq9vd.us.auth0.com"
auth0_client_id      = "pAR4uxAVlQHX4NjtbLfJbQOdv9V3R5Ty"
auth0_mgmt_client_id = "z0gTESjc70ZPHxn7zhIFuuFBqeYDA2Ip"
auth0_audience       = "https://dev-zmogq9vd.us.auth0.com/api/v2/"

tableau_server_url = "https://dev.bi.ict.dematic.dev"
fluid_topics_callback_urls = [
  "https://dematic-staging.fluidtopics.net/api/authentication/sso/Auth0/callback",
  "https://staging-kb.dematic.com/api/authentication/sso/Auth0/callback"
]

tenants = [
  {
    name         = "dematic_solution_center"
    display_name = "Dematic Solution Center"
    metadata = {
      domains     = "dematic.com,kiongroup.com"
      dataset     = "dematic"
      tableauSite = "DematicSolutionCenter"
    }
    facilities = ["grandrapidsmi"]
    connections = {
      auth0  = { enabled = true }
      google = { enabled = true }
    }
  },
  {
    name         = "drt_automation"
    display_name = "DRT Automation"
    metadata = {
      dataset = "drt_automation"
    }
    facilities = ["drt_automation"]
    connections = {
      auth0  = { enabled = true }
      google = { enabled = true }
    }
  },
  {
    name         = "ict_development"
    display_name = "ICT Development"
    metadata = {
      dataset = "ict_development"
    }
    facilities = ["ict_development"]
    connections = {
      auth0  = { enabled = true }
      google = { enabled = true }
    }
  },
  {
    name         = "qa_automation"
    display_name = "QA Automation"
    metadata = {
      dataset = "qa_automation"
    }
    facilities = ["qa_automation", "testingtx"]
    connections = {
      auth0  = { enabled = true }
      google = { enabled = true }
    }
  },
  {
    name         = "superior_uniform"
    display_name = "Superior Uniform"
    metadata = {
      dataset     = "superior_uniform"
      tableauSite = "SuperiorUniform"
    }
    facilities = ["eudoraar"]
    connections = {
      auth0  = { enabled = true }
      google = { enabled = true }
    }
  },
  {
    name         = "tti"
    display_name = "Techtronic Industries"
    metadata = {
      dataset     = "tti"
      tableauSite = "TechtronicIndustries"
    }
    facilities = ["tti"]
    connections = {
      auth0  = { enabled = true }
      google = { enabled = true }
    }
  },
  {
    name         = "acehardware"
    display_name = "Ace Hardware"
    metadata = {
      dataset     = "acehardware"
      tableauSite = "AceHardware"
    }
    facilities = ["jeffersonga", "plantcityfl", "visaliaca", "wilmertx"]
    connections = {
      auth0  = { enabled = true }
      google = { enabled = true }
    }
  },
]

insights_publisher_sa = "<EMAIL>"
edp_subscriber_sa     = "<EMAIL>"
edp_project_id        = "edp-d-us-east2-etl"
redis_host_ip         = "***********"

postgres_secret_name = "ict-dev-postgres-ict-api"

#! This is to ensure that the BigQuery datasets are deleted when the infra is destroyed.
destroy_datasets = true
