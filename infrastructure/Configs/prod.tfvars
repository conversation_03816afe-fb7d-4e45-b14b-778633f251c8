# SERVICEPROJECT_CRAFTED_NETWORK
gitlab_project_shortname = "etl"
zone                     = "us-east1-b"
region                   = "us-east1"
env                      = "prod"
project_name             = "ict-etl"
service_name             = "etl"
project_id               = "ict-p-us-east1-etl"
host_project_id          = "ict-p-us-east1-etl"
api_project_id           = "ict-p-us-east1-api"
subnet_01_pub            = "************/24"
subnet_02_priv           = "************/24"
# Spare space 34, 35, 36
vpc_serverless_connector_cidr = "************/28" # This will create a /28 for association.. <<< MOVED >>> To Infra
# network_name = "${var.env}-${var.project_id}-private"
network_name = "prod-ict-p-us-east1-etl-private"
# https://www.davidc.net/sites/default/subnets/subnets.html
# PLAN  = "192.168.{16,32}.0/20"    # ROOT + Prefix . 
# This is for NON_REAL Google IP's which will be used for things like REDIS/CloudSQL
external_allocation_range = "************" # ROOT + Prefix . This is for NON_REAL Google IP's which will be used for things like REDIS/CloudSQL
dr_bucket_retention_days  = 31             # Default is 31. Go with 365 in prod? 

auth0_domain         = "control-tower-prod.us.auth0.com"
auth0_client_id      = "KYfShvEtHRpEspdMMoFvvqhrHdjMwejn"
auth0_mgmt_client_id = "osrydY7TVp90r4Cx59bkCMrqkluPV3h2"
auth0_audience       = "https://control-tower-prod.us.auth0.com/api/v2/"

tableau_server_url = "https://bi.ict.dematic.cloud"
fluid_topics_callback_urls = [
  "https://info.dematic.com/api/authentication/sso/Auth0/callback",
  "https://kb.dematic.com/api/authentication/sso/Auth0/callback"
]

tenants = [
  {
    name         = "dematic_solution_center"
    display_name = "Dematic Solution Center"
    metadata = {
      dataset     = "dematic"
      tableauSite = "DematicSolutionCenter"
    }
    facilities = ["grandrapidsmi"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "ict_development"
    display_name = "ICT Development"
    metadata = {
      dataset = "ict_development"
    }
    facilities = ["ict_development"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "superior_uniform"
    display_name = "Superior Uniform"
    metadata = {
      dataset     = "superior_uniform"
      tableauSite = "SuperiorUniform"
    }
    facilities = ["eudoraar"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "tti"
    display_name = "Techtronic Industries"
    metadata = {
      dataset     = "tti"
      tableauSite = "TechtronicIndustries"
    }
    facilities = ["tti"]
    connections = {
      auth0 = { enabled = true }
    }
  },
  {
    name         = "acehardware"
    display_name = "Ace Hardware"
    metadata = {
      dataset     = "acehardware"
      tableauSite = "AceHardware"
    }
    facilities = ["jeffersonga", "plantcityfl", "visaliaca", "wilmertx"]
    connections = {
      auth0 = { enabled = true }
    }
  },
]

insights_publisher_sa = "<EMAIL>"
edp_subscriber_sa     = "<EMAIL>"
edp_project_id        = "edp-p-us-east1-etl"
redis_host_ip         = "************"

postgres_secret_name = "ict-p-us-east1-api"

#! This is to ensure that the BigQuery datasets are deleted when the infra is destroyed.
destroy_datasets = true
