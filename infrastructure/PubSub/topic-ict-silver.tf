module "pubsub_silver_topic" {
  source  = "terraform-google-modules/pubsub/google"
  version = "~> 5.0"

  project_id                       = var.project_id
  topic                            = "${var.env}-adi-silver"
  topic_labels                     = local.labels
  topic_message_retention_duration = "604800s" # 7 days
}

output "pubsub_silver_topic_name" {
  value       = module.pubsub_silver_topic.topic
  description = "Pub/Sub topic of 'silver'."
}
