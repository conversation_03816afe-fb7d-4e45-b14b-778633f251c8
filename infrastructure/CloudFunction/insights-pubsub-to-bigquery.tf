# resource "null_resource" "from_insights_pubsub_to_bigquery_build" {

#   provisioner "local-exec" {
#     working_dir = "../../cloudfunctions/ict-etl-insights-pubsub-to-bigquery/src"
#     command     = <<-EOT
#         pip install -r requirements.txt
#         du . 
#         pwd
#        EOT
#   }
# }

# data "archive_file" "pubsub_to_bigquery" {
#   type        = "zip"
#   output_path = "../../cloudfunctions/ict-etl-insights-pubsub-to-bigquery.zip"
#   source_dir  = "../../cloudfunctions/ict-etl-insights-pubsub-to-bigquery/src"

#   depends_on = [
#     null_resource.from_insights_pubsub_to_bigquery_build
#   ]
# }

# resource "google_storage_bucket_object" "pubsub_to_bigquery" {
#   name   = "ict-etl-insights-pubsub-to-bigquery.zip"
#   bucket = google_storage_bucket.cf_storage_bucket.name
#   source = "../../cloudfunctions/ict-etl-insights-pubsub-to-bigquery.zip" # Add path to the zipped function source code
#   depends_on = [
#     data.archive_file.pubsub_to_bigquery,
#     google_storage_bucket.cf_storage_bucket
#   ]
# }

# resource "google_cloudfunctions2_function" "pubsub_to_bigquery" {
#   name        = "${var.env}-${var.project_id}-insights_pubsub_to_bigquery"
#   location    = var.region
#   description = "Control Tower ETL from Insights PubSub to BigQuery"
#   labels      = local.labels

#   build_config {
#     runtime     = "python311"
#     entry_point = "handler" # Set the entry point 
#     environment_variables = {
#       ENVIRONMENT           = var.env
#       COST_CENTER           = "550164"
#       CREATED_BY            = "terraform"
#       BIGQUERY_PROJECT      = var.project_id
#       DEFAULT_TENANT        = "ict_development"
#       DEFAULT_FACILITY      = "ict_development"
#       DEFAULT_SOURCE_SYSTEM = "ict_development"
#     }
#     source {
#       storage_source {
#         bucket = google_storage_bucket.cf_storage_bucket.name
#         object = google_storage_bucket_object.pubsub_to_bigquery.name
#       }
#     }
#   }

#   service_config {
#     service_account_email            = module.cloud_function_service_account.email
#     min_instance_count               = 2
#     max_instance_count               = 200
#     max_instance_request_concurrency = 10
#     available_cpu                    = "2"
#     available_memory                 = "1024M"
#     timeout_seconds                  = 240
#     environment_variables = {
#       BIGQUERY_PROJECT = var.project_id
#     }
#   }

#   lifecycle {
#     replace_triggered_by = [google_storage_bucket_object.pubsub_to_bigquery]
#   }

#   #event_trigger {
#   #  trigger_region = var.region
#   #  event_type     = "google.cloud.pubsub.topic.v1.messagePublished"
#   #  pubsub_topic   = "projects/${var.project_id}/topics/${var.env}-from-insights"
#   #  retry_policy   = "RETRY_POLICY_RETRY"
#   #}

#   depends_on = [
#     google_storage_bucket_object.pubsub_to_bigquery,
#     module.cloud_function_service_account
#   ]

# }

# # IAM entry for all users to invoke the function
# resource "google_cloudfunctions2_function_iam_member" "invoker" {
#   project        = var.project_id
#   location       = var.region
#   cloud_function = google_cloudfunctions2_function.pubsub_to_bigquery.name

#   role   = "roles/cloudfunctions.invoker"
#   member = "allUsers" # This likely needs to be the snap-in for pubsub. 
# }

# resource "google_cloud_run_service_iam_member" "invoker" {
#   project  = var.project_id
#   location = var.region
#   service  = google_cloudfunctions2_function.pubsub_to_bigquery.service_config[0].service
#   role     = "roles/run.invoker"
#   member   = "allUsers"
# }

# module "cloud_function_service_account" {
#   source     = "terraform-google-modules/service-accounts/google"
#   version    = "~> 4.2.2"
#   project_id = var.project_id
#   prefix     = "${var.env}-${var.project_name}"
#   names      = ["cloudfunction"]
#   project_roles = [
#     "${var.project_id}=>roles/bigquery.dataViewer",
#     "${var.project_id}=>roles/bigquery.dataEditor",
#     "${var.project_id}=>roles/storage.admin",
#     "${var.project_id}=>roles/cloudfunctions.admin",
#     "${var.project_id}=>roles/run.admin",
#     "${var.project_id}=>roles/pubsub.admin",
#   ]
# }