# resource "google_pubsub_subscription" "insights_pubsub_to_bigquery_subscription" {
#   ack_deadline_seconds = 10
#   expiration_policy {
#     ttl = "2678400s"
#   }
#   message_retention_duration = "604800s"
#   name                       = "${var.project_id}-insights-pubsub-to-bigquery-subscription"
#   project                    = var.project_id
#   labels                     = local.labels
#   push_config {
#     push_endpoint = google_cloudfunctions2_function.pubsub_to_bigquery.service_config[0].uri
#     oidc_token {
#       service_account_email = module.cloud_function_service_account.email
#       audience              = "allUsers"
#     }
#   }
#   topic = "projects/${var.project_id}/topics/${var.env}-adi-to-bq"
# }


# # https://wiki.dematic.com/display/ICTI/Control+Tower+MQTT+Integration
# resource "google_pubsub_subscription" "adi_pubsub_to_bigquery_subscription" {
#   ack_deadline_seconds = 10
#   expiration_policy {
#     ttl = "2678400s"
#   }
#   message_retention_duration = "604800s"
#   name                       = "${var.project_id}-adi-mqtt-formatter-subscription"
#   project                    = var.project_id
#   labels                     = local.labels
#   push_config {
#     push_endpoint = google_cloudfunctions2_function.adi_formatter_to_pubsub.service_config[0].uri
#     oidc_token {
#       service_account_email = module.cloud_function_adi_formatter_to_pubsub_service_account.email
#       audience              = "allUsers"
#     }
#   }
#   topic = "projects/${var.project_id}/topics/${var.env}-adi-mqtt"
# }

