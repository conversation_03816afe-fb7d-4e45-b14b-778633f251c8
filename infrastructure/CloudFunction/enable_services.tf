# All of the services listed here must be enabled for ALL projects participating in this advanture

locals {
  services_to_enable = [
    "compute.googleapis.com",
    "servicenetworking.googleapis.com",
    "vpcaccess.googleapis.com",
    "iap.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "secretmanager.googleapis.com",
    "cloudbuild.googleapis.com",
  ]
}

resource "google_project_service" "services" {
  for_each = toset(local.services_to_enable)

  service                    = each.value
  disable_dependent_services = false
  disable_on_destroy         = false

}