# resource "null_resource" "adi_formatter_to_pubsub_build" {

#   provisioner "local-exec" {
#     working_dir = "../../cloudfunctions/ict-etl-adi-formatter-to-pubsub/src"
#     command     = <<-EOT
#         pip install -r requirements.txt
#         du . 
#         pwd
#        EOT
#   }
# }

# data "archive_file" "adi_formatter_to_pubsub" {
#   type        = "zip"
#   output_path = "../../cloudfunctions/ict-etl-adi-formatter-to-pubsub.zip"
#   source_dir  = "../../cloudfunctions/ict-etl-adi-formatter-to-pubsub/src"

#   depends_on = [
#     null_resource.adi_formatter_to_pubsub_build
#   ]
# }


# resource "google_storage_bucket_object" "adi_formatter_to_pubsub" {
#   name   = "ict-etl-adi-formatter-to-pubsub.zip"
#   bucket = google_storage_bucket.cf_storage_bucket.name
#   source = "../../cloudfunctions/ict-etl-adi-formatter-to-pubsub.zip" # Add path to the zipped function source code
#   depends_on = [
#     data.archive_file.adi_formatter_to_pubsub,
#     google_storage_bucket.cf_storage_bucket
#   ]
# }

# resource "google_cloudfunctions2_function" "adi_formatter_to_pubsub" {
#   name        = "${var.env}-${var.project_id}-adi_formatter_to_pubsub"
#   location    = var.region
#   description = "Control Tower ETL from ADI MQTT to PubSub adi-formatter"
#   labels      = local.labels

#   build_config {
#     runtime     = "python311"
#     entry_point = "handler" # Set the entry point 
#     environment_variables = {
#       ENVIRONMENT = var.env
#       COST_CENTER = "550164"
#       CREATED_BY  = "terraform"
#       PROJECT_ID  = var.project_id
#       TOPIC_NAME  = data.terraform_remote_state.adi_formatter_to_pubsub.outputs.pubsub_adi_topic.topic
#     }
#     source {
#       storage_source {
#         bucket = google_storage_bucket.cf_storage_bucket.name
#         object = google_storage_bucket_object.adi_formatter_to_pubsub.name
#       }
#     }
#   }

#   service_config {
#     service_account_email            = module.cloud_function_service_account.email
#     min_instance_count               = 2
#     max_instance_count               = 200
#     max_instance_request_concurrency = 10
#     available_cpu                    = "2"
#     available_memory                 = "1024M"
#     timeout_seconds                  = 240
#     environment_variables = {
#       PROJECT_ID = var.project_id
#       TOPIC_NAME = data.terraform_remote_state.adi_formatter_to_pubsub.outputs.pubsub_adi_topic.topic
#     }
#   }

#   lifecycle {
#     replace_triggered_by = [google_storage_bucket_object.adi_formatter_to_pubsub]
#   }

#   #event_trigger {
#   #  trigger_region = var.region
#   #  event_type     = "google.cloud.pubsub.topic.v1.messagePublished"
#   #  pubsub_topic   = "projects/${var.project_id}/topics/${var.env}-from-insights"
#   #  retry_policy   = "RETRY_POLICY_RETRY"
#   #}

#   depends_on = [
#     google_storage_bucket_object.adi_formatter_to_pubsub,
#     module.cloud_function_service_account,
#     data.terraform_remote_state.adi_formatter_to_pubsub
#   ]

# }

# # IAM entry for all users to invoke the function
# resource "google_cloudfunctions2_function_iam_member" "invoker_adi" {
#   project        = var.project_id
#   location       = var.region
#   cloud_function = google_cloudfunctions2_function.adi_formatter_to_pubsub.name

#   role   = "roles/cloudfunctions.invoker"
#   member = "allUsers" # This likely needs to be the snap-in for pubsub. 
# }

# resource "google_cloud_run_service_iam_member" "invoker_adi" {
#   project  = var.project_id
#   location = var.region
#   service  = google_cloudfunctions2_function.adi_formatter_to_pubsub.service_config[0].service
#   role     = "roles/run.invoker"
#   member   = "allUsers"
# }

# module "cloud_function_adi_formatter_to_pubsub_service_account" {
#   source     = "terraform-google-modules/service-accounts/google"
#   version    = "~> 4.2.2"
#   project_id = var.project_id
#   prefix     = "${var.env}-${var.project_name}-adi-"
#   names      = ["cf"]
#   project_roles = [
#     "${var.project_id}=>roles/storage.admin",
#     "${var.project_id}=>roles/cloudfunctions.admin",
#     "${var.project_id}=>roles/run.admin",
#     "${var.project_id}=>roles/pubsub.admin",
#   ]
# }