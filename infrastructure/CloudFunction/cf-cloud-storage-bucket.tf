
# resource "google_storage_bucket" "cf_storage_bucket" {
#   # ict-d-api-configapidata | ict-s-us-east1-api-configapidata
#   name                        = "${var.project_id}-cf-storage"
#   location                    = var.region
#   uniform_bucket_level_access = true
#   storage_class               = "STANDARD"
#   // delete bucket and contents on destroy.
#   force_destroy = true

#   public_access_prevention = "enforced"

#   labels = local.labels

# }

# output "cf_storage_bucket_name" {
#   value = google_storage_bucket.cf_storage_bucket.name
# }
