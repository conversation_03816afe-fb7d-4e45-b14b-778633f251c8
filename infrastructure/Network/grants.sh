HOST_PROJECT_ID="ict-management-d-host"
SERVICE_PROJECT_NUMBER="************"

gcloud projects add-iam-policy-binding ${HOST_PROJECT_ID} \
--role "roles/compute.networkUser" \
--member "serviceAccount:service-${SERVICE_PROJECT_NUMBER}@gcp-sa-vpcaccess.iam.gserviceaccount.com"

gcloud projects add-iam-policy-binding ${HOST_PROJECT_ID} \
--role "roles/compute.networkUser" \
--member "serviceAccount:${SERVICE_PROJECT_NUMBER}@cloudservices.gserviceaccount.com"
