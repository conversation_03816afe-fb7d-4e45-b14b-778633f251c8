
# https://cloud.google.com/vpc/docs/configure-private-services-access#terraform_1

# For reference: 
# Doc https://github.com/terraform-google-modules/terraform-docs-samples/blob/main/cloud_sql/mysql_instance_private_ip/main.tf

resource "google_compute_global_address" "private_range" {
  # This isn't real.. It's just a range external to VPC essentially
  name         = "${var.project_name}-${var.env}-private-range"
  project      = var.host_project_id
  provider     = google-beta
  purpose      = "VPC_PEERING"
  address_type = "INTERNAL"
  labels       = local.labels
  # a /24 is sufficient ONLY for a single service. 
  # EG, SQL consumes a /24, Redis consumes a /24, etc
  address       = var.external_allocation_range
  prefix_length = 20 # 16 is suggested.
  network       = var.network_name
}

resource "google_service_networking_connection" "vpc_private_service_connection" {
  network                 = var.network_name
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.private_range.name]
  depends_on              = [google_compute_global_address.private_range]
}


# Pending new google-cloud-beta provider release Estimated Release 03/22
# https://github.com/hashicorp/terraform-provider-google/issues/8475
resource "google_vpc_access_connector" "connector_beta" {
  name    = "${var.project_name}-${var.env}-${var.region}" #ict-etl-stage-us-east1
  project = var.project_id
  region  = var.region
  #ip_cidr_range = var.vpc_serverless_connector_cidr
  #network       = var.network_name
  subnet {
    name = "${var.env}-ict-03-serverless-connector"
  }
  machine_type   = "e2-micro"
  min_instances  = 2
  max_instances  = 7
  max_throughput = 700
  min_throughput = 200
  # Estimated bandwidth to cost
  # 200 Mbps at minimum instances ($12.23)
  # 700 Mbps at maximum instances ($42.82)
}
