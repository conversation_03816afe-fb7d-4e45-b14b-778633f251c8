## Create Cloud Router

resource "google_compute_router" "router" {
  project = var.host_project_id
  name    = "${var.env}-${var.project_name}"
  network = var.network_name
  region  = var.region
}

## Create Nat Gateway

resource "google_compute_router_nat" "nat" {
  project                            = var.host_project_id
  name                               = "${var.env}-${var.project_name}-nat"
  router                             = google_compute_router.router.name
  region                             = var.region
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }
}