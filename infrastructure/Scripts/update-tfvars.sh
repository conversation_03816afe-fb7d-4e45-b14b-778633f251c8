#!/bin/bash

# Find the root directory of the repository
find_repo_root() {
    local current_dir="$PWD"
    while [[ ! -d "$current_dir/.git" && "$current_dir" != "/" ]]; do
        current_dir="$(dirname "$current_dir")"
    done
    if [[ "$current_dir" == "/" ]]; then
        echo "Error: Not inside a git repository" >&2
        exit 1
    fi
    echo "$current_dir"
}

REPO_ROOT=$(find_repo_root)

# Check if CI_MERGE_REQUEST_IID is set, if not, use the current user's name for local testing
if [ -z "$CI_MERGE_REQUEST_IID" ]; then
    CI_MERGE_REQUEST_IID=$(whoami)
fi

# Function to update tfvars file
create_mr_tfvars() {
    local input_file=$1
    local output_file=$2
    local mr_id=$3

    sed -E "
        /^tenants = \[/,/^\]/ {
            s/(name = )\"([^\"]+)\"/\1\"\2_$mr_id\"/g
            s/(dataset = )\"([^\"]+)\"/\1\"\2_$mr_id\"/g
        }
    " "$input_file" >"$output_file"
}

# Set the input file to always be test.tfvars
input_file="$REPO_ROOT/infrastructure/Configs/test.tfvars"
output_file="$REPO_ROOT/infrastructure/Configs/test_${CI_MERGE_REQUEST_IID}.tfvars"

if [ -f "$input_file" ]; then
    echo "Creating MR-specific tfvars file from $input_file"
    create_mr_tfvars "$input_file" "$output_file" "$CI_MERGE_REQUEST_IID"
    echo "Created $output_file:"
    cat "$output_file"
else
    echo "Input file not found: $input_file"
    exit 1
fi

# Echo the name of the created file so it can be captured in CI/CD pipeline
echo "CREATED_TFVARS_FILE=$output_file"
