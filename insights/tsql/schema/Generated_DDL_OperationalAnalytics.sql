
CREATE TABLE [OA].[AGG_Equipment_Availability]
( 
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Uptime_Idle_Seconds] real  NULL ,
	[Downtime_Fault_Seconds] real  NULL ,
	[Downtime_Maintenance_Seconds] real  NULL ,
	[Uptime_Busy_Seconds] real  NULL ,
	[Total_Uptime_Seconds] real  NULL ,
	[Total_Downtime_Seconds] real  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAGG_Equipment_Availability] ON [OA].[AGG_Equipment_Availability]
( 
	[Hour_Quarter_ID]     ASC,
	[Device_ID]           ASC
)
go

CREATE TABLE [OA].[AGG_FCT_Inventory]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Facility_ID]        integer  NOT NULL ,
	[Load_Unit_Count]    integer  NULL ,
	[Base_Load_Unit_Count] integer  NULL ,
	[Total_SKU_Count]    integer  NULL ,
	[Unassigned_SKU_Count] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAGG_FCT_Inventory] ON [OA].[AGG_FCT_Inventory]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Item_ID]             ASC,
	[Facility_ID]         ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1AGG_FCT_Inventory_ETL_Batch] ON [OA].[AGG_FCT_Inventory]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[AGG_FCT_OEE_Sort]
( 
	[Scanner_ID]         integer  NOT NULL ,
	[Hour_ID]            integer  NOT NULL ,
	[Hour_Start_Date_Time] datetime  NOT NULL ,
	[Actual_Induct_Count] integer  NULL ,
	[Outstanding_Orders] integer  NULL ,
	[Induct_Order_Ratio] real  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAGG_FCT_OEE_Sort] ON [OA].[AGG_FCT_OEE_Sort]
( 
	[Scanner_ID]          ASC,
	[Hour_ID]             ASC,
	[Hour_Start_Date_Time]  ASC
)
go

CREATE TABLE [OA].[AGG_FCT_OEE_Throughput]
( 
	[Hour_ID]            integer  NOT NULL ,
	[Hour_Start_Date_Time] datetime  NOT NULL ,
	[Type_Code]          varchar(50)  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Actual_Throughput]  integer  NULL ,
	[Target_Throughput]  integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAGG_FCT_OEE_Throughput] ON [OA].[AGG_FCT_OEE_Throughput]
( 
	[Hour_ID]             ASC,
	[Hour_Start_Date_Time]  ASC,
	[Type_Code]           ASC,
	[Device_ID]           ASC
)
go

CREATE TABLE [OA].[AGG_FCT_Order_Completion]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Remaining_Order_Count] integer  NULL ,
	[Avg_Cycle_Time_Minutes] real  NULL ,
	[Max_Order_Completion_Date_Time] Datetime2(7)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAGG_FCT_Order_Completion] ON [OA].[AGG_FCT_Order_Completion]
( 
	[Record_Timestamp]    ASC
)
go

CREATE TABLE [OA].[AGG_FCT_Sort_Volume]
( 
	[Hour_ID]            integer  NOT NULL ,
	[Scanner_ID]         integer  NOT NULL ,
	[Scanner_Code]       varchar(50)  NULL ,
	[Scanner_Name]       varchar(100)  NULL ,
	[Scanner_Engineered_Rate] integer  NULL ,
	[Total_Induct_Count] integer  NULL ,
	[Total_Recirc_Count] integer  NULL ,
	[Total_Reject_Count] integer  NULL ,
	[Total_Divert_Count] integer  NULL ,
	[Total_Anomaly_Count] integer  NULL ,
	[Total_Induct_Historical_Hourly_Avg] real  NULL ,
	[Total_Recirc_Historical_Hourly_Avg] real  NULL ,
	[Total_Reject_Historical_Hourly_Avg] real  NULL ,
	[Total_Divert_Historical_Hourly_Avg] real  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAGG_FCT_Sort_Volume] ON [OA].[AGG_FCT_Sort_Volume]
( 
	[Hour_ID]             ASC,
	[Scanner_ID]          ASC
)
go

CREATE TABLE [OA].[AGG_Operator_Performance]
( 
	[Calendar_Month_ID]  integer  NOT NULL ,
	[Day_ID]             integer  NOT NULL ,
	[Facility_ID]        integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Average_Pick_Rate]  real  NULL ,
	[Peak_Rate]          real  NULL ,
	[Low_Rate]           real  NULL ,
	[Total_Picks]        integer  NULL ,
	[Total_Omits]        integer  NULL ,
	[Total_Mispicks]     integer  NULL ,
	[Hours_Worked]       real  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAGG_Operator_Performance] ON [OA].[AGG_Operator_Performance]
( 
	[Calendar_Month_ID]   ASC,
	[Day_ID]              ASC,
	[Facility_ID]         ASC,
	[Operator_ID]         ASC
)
go

CREATE TABLE [OA].[AGG_Operator_Target_Day]
( 
	[Day_ID]             integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Zone_ID]            integer  NOT NULL ,
	[Technology_ID]      integer  NOT NULL ,
	[Actual_Operator_Hourly_Rate_Qty] real  NULL ,
	[Actual_Operator_Hourly_Rate_Line_Count] real  NULL ,
	[Actual_Operator_Hourly_Rate_Induct_Count] real  NULL ,
	[Target_Operator_Hourly_Rate_Qty] real  NULL ,
	[Logged_In_Duration_Seconds] real  NULL ,
	[Target_Operator_Hourly_Rate_Line_Count] real  NULL ,
	[Target_Operator_Hourly_Rate_Induct_Count] real  NULL ,
	[Actual_Active_Duration_Seconds] real  NULL ,
	[Actual_Gap_Duration_Seconds] real  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAGG_Operator_Target_Day] ON [OA].[AGG_Operator_Target_Day]
( 
	[Day_ID]              ASC,
	[Operator_ID]         ASC,
	[Zone_ID]             ASC,
	[Technology_ID]       ASC
)
go

CREATE TABLE [OA].[AGG_Robot_Activity_Summary]
( 
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Robot_Device_ID]    integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Module_ID]          integer  NOT NULL ,
	[Event]              varchar(50)  NOT NULL ,
	[Interval_Start_Time] datetime  NOT NULL ,
	[Robot_Available_Ind] integer  NULL ,
	[Total_Time_In_Seconds] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAGG_Robot_Activity_Summary] ON [OA].[AGG_Robot_Activity_Summary]
( 
	[Hour_Quarter_ID]     ASC,
	[Robot_Device_ID]     ASC,
	[Status_ID]           ASC,
	[Module_ID]           ASC,
	[Event]               ASC,
	[Interval_Start_Time]  ASC
)
go

CREATE TABLE [OA].[AGG_Travel_Time]
( 
	[Hour_ID]            integer  NOT NULL ,
	[Source_ID]          integer  NOT NULL ,
	[Source_Type_Code]   varchar(50)  NOT NULL ,
	[Destination_ID]     integer  NOT NULL ,
	[Destination_Type_Code] varchar(50)  NOT NULL ,
	[Source_Code]        varchar(50)  NULL ,
	[Source_Name]        varchar(100)  NULL ,
	[Destination_Code]   varchar(50)  NULL ,
	[Destination_Name]   varchar(100)  NULL ,
	[Route_Count]        integer  NULL ,
	[Average_Travel_Time_Seconds] real  NULL ,
	[Total_Induct_Count] integer  NULL ,
	[Total_Arrival_Count] integer  NULL ,
	[Travel_Time_Seconds_Historical_Hourly_Avg] real  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAGG_Travel_Time] ON [OA].[AGG_Travel_Time]
( 
	[Hour_ID]             ASC,
	[Source_ID]           ASC,
	[Source_Type_Code]    ASC,
	[Destination_ID]      ASC,
	[Destination_Type_Code]  ASC
)
go

CREATE TABLE [dbo].[API_Xref]
( 
	[xref_id]            integer  NOT NULL  IDENTITY ,
	[src_natural_key_column] varchar(50)  NULL ,
	[dst_natural_key_column] varchar(50)  NULL ,
	[dst_surrogate_key_column] varchar(50)  NULL ,
	[dst_dimension_table] varchar(50)  NULL ,
	[target_surrogate_key_column] varchar(50)  NULL ,
	[target_table]       varchar(50)  NULL ,
	[sort_key]           integer  NULL 
	CONSTRAINT [Zero_956106462]
		 DEFAULT  0,
	[added_lookup]       varchar(50)  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAPI_Xref] ON [dbo].[API_Xref]
( 
	[xref_id]             ASC
)
go

CREATE TABLE [dbo].[API_Xref_Maps]
( 
	[ref_att_id]         integer  NOT NULL  IDENTITY ,
	[src_staging_table]  varchar(100)  NULL ,
	[dst_dim_table_name] varchar(100)  NULL ,
	[src_surrogate_key_col] varchar(50)  NULL ,
	[dst_surrogate_key_col] varchar(50)  NULL ,
	[src_column_name]    varchar(100)  NULL ,
	[dst_column_name]    varchar(100)  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAPI_Xref_Maps] ON [dbo].[API_Xref_Maps]
( 
	[ref_att_id]          ASC
)
go

CREATE TABLE [dbo].[API_Xref_Nodes]
( 
	[xref_node_id]       integer  NOT NULL  IDENTITY ,
	[parent_xref_id]     integer  NOT NULL ,
	[child_xref_id]      integer  NOT NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKAPI_Xref_Nodes] ON [dbo].[API_Xref_Nodes]
( 
	[xref_node_id]        ASC,
	[parent_xref_id]      ASC,
	[child_xref_id]       ASC
)
go

CREATE TABLE [OA].[Configuration]
( 
	[Configuration_ID]   integer  NOT NULL  IDENTITY ,
	[Configuration_Code] varchar(50)  NULL ,
	[Configuration_Name] varchar(100)  NULL ,
	[Configuration_Type] varchar(50)  NULL ,
	[Value]              varchar(50)  NULL ,
	[Subsystem_Category] varchar(50)  NULL ,
	[Time_Created]       datetime  NULL ,
	[Time_Modified]      datetime  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(150)  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKConfiguration] ON [OA].[Configuration]
( 
	[Configuration_ID]    ASC
)
go

CREATE TABLE [OA].[DIM_Aisle]
( 
	[Aisle_ID]           integer  NOT NULL  IDENTITY ,
	[Buffer_ID]          integer  NULL ,
	[Aisle_Code]         varchar(50)  NULL ,
	[Aisle_Name]         varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Aisle] ON [OA].[DIM_Aisle]
( 
	[Aisle_ID]            ASC
)
go

CREATE TABLE [OA].[DIM_Bit_Mask]
( 
	[Bit_Mask_Code_ID]   integer  NOT NULL  IDENTITY ,
	[Bit_Mask_Code]      bigint  NULL ,
	[Bit_Mask_Category]  varchar(50)  NULL ,
	[Bit_Mask_Name]      varchar(100)  NULL ,
	[Bit_Mask_Comment]   varchar(255)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Bit_Mask] ON [OA].[DIM_Bit_Mask]
( 
	[Bit_Mask_Code_ID]    ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1DIM_Bit_Mask_Codes_Value_Category] ON [OA].[DIM_Bit_Mask]
( 
	[Bit_Mask_Code]       ASC,
	[Bit_Mask_Category]   ASC
)
go

CREATE TABLE [OA].[DIM_Buffer]
( 
	[Buffer_ID]          integer  NOT NULL  IDENTITY ,
	[Buffer_Code]        varchar(50)  NULL ,
	[Buffer_Name]        varchar(100)  NULL ,
	[Buffer_Description] varchar(100)  NULL ,
	[Buffer_Functional_Type] varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Buffer] ON [OA].[DIM_Buffer]
( 
	[Buffer_ID]           ASC
)
go

CREATE TABLE [OA].[DIM_Calendar_Month]
( 
	[Calendar_Month_ID]  integer  NOT NULL ,
	[Calendar_Quarter_ID] integer  NOT NULL ,
	[Month_Number]       integer  NULL ,
	[Month_Name]         varchar(100)  NULL ,
	[Days_In_Month]      integer  NULL ,
	[Last_Quarter_Month_ID] integer  NULL ,
	[Last_Year_Month_ID] integer  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Calendar_Month] ON [OA].[DIM_Calendar_Month]
( 
	[Calendar_Month_ID]   ASC
)
go

CREATE TABLE [OA].[DIM_Calendar_Quarter]
( 
	[Calendar_Quarter_ID] integer  NOT NULL ,
	[Last_Year_Quarter_ID] integer  NULL ,
	[Quarter_Number]     integer  NULL ,
	[Quarter_Name]       varchar(100)  NULL ,
	[Days_In_Quarter]    integer  NULL ,
	[Calendar_Year_ID]   integer  NOT NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Calendar_Quarter] ON [OA].[DIM_Calendar_Quarter]
( 
	[Calendar_Quarter_ID]  ASC
)
go

CREATE TABLE [OA].[DIM_Calendar_Week]
( 
	[Calendar_Week_ID]   integer  NOT NULL ,
	[Week_Name]          varchar(50)  NULL ,
	[Week_Of_Year]       integer  NULL ,
	[Week_Of_Month]      integer  NULL ,
	[Week_Start_Date]    datetime  NULL ,
	[Week_End_Date]      datetime  NULL ,
	[Days_In_Week]       integer  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Calendar_Week] ON [OA].[DIM_Calendar_Week]
( 
	[Calendar_Week_ID]    ASC
)
go

CREATE TABLE [OA].[DIM_Calendar_Year]
( 
	[Calendar_Year_ID]   integer  NOT NULL ,
	[Year_Number]        integer  NULL ,
	[Days_In_Year]       integer  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Calendar_Year] ON [OA].[DIM_Calendar_Year]
( 
	[Calendar_Year_ID]    ASC
)
go

CREATE TABLE [OA].[DIM_Cart_Instance]
( 
	[Cart_Instance_ID]   integer  NOT NULL  IDENTITY ,
	[Cart_Instance_Code] varchar(50)  NULL ,
	[Cart_Instance_Name] varchar(100)  NULL ,
	[Start_DateTime]     Datetime2(7)  NULL ,
	[End_DateTime]       Datetime2(7)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Cart_Instance] ON [OA].[DIM_Cart_Instance]
( 
	[Cart_Instance_ID]    ASC
)
go

CREATE TABLE [OA].[DIM_Container_Train]
( 
	[Container_Train_ID] integer  NOT NULL  IDENTITY ,
	[Container_Train_Code] varchar(50)  NULL ,
	[Container_Train_Name] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Container_Train] ON [OA].[DIM_Container_Train]
( 
	[Container_Train_ID]  ASC
)
go

CREATE TABLE [OA].[DIM_Container_Type]
( 
	[Container_Type_ID]  integer  NOT NULL  IDENTITY ,
	[Container_Type_Code] varchar(50)  NULL ,
	[Container_Type_Name] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Container_Type] ON [OA].[DIM_Container_Type]
( 
	[Container_Type_ID]   ASC
)
go

CREATE TABLE [OA].[DIM_Customer]
( 
	[Customer_ID]        integer  NOT NULL  IDENTITY ,
	[Customer_Code]      varchar(50)  NULL ,
	[Customer_Name]      varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Customer] ON [OA].[DIM_Customer]
( 
	[Customer_ID]         ASC
)
go

CREATE TABLE [OA].[DIM_Customer_Order]
( 
	[Customer_Order_ID]  integer  NOT NULL  IDENTITY ,
	[Customer_Order_Code] varchar(50)  NULL ,
	[Customer_Order_Name] varchar(100)  NULL ,
	[Customer_Order_Type] varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Customer_Order] ON [OA].[DIM_Customer_Order]
( 
	[Customer_Order_ID]   ASC
)
go

CREATE TABLE [OA].[DIM_Data_Point]
( 
	[Data_Point_ID]      integer  NOT NULL  IDENTITY ,
	[Data_Point_Code]    varchar(50)  NULL ,
	[Data_Point_Description] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Data_Point] ON [OA].[DIM_Data_Point]
( 
	[Data_Point_ID]       ASC
)
go

CREATE TABLE [OA].[DIM_Day]
( 
	[Day_ID]             integer  NOT NULL ,
	[Calendar_Month_ID]  integer  NOT NULL ,
	[Calendar_Week_ID]   integer  NOT NULL ,
	[Calendar_Date]      datetime  NULL ,
	[Day_Short_Name]     varchar(100)  NULL ,
	[Day_Long_Name]      varchar(100)  NULL ,
	[Year_Day_Number]    integer  NULL ,
	[Week_Day_Number]    integer  NULL ,
	[Last_Year_Day_ID]   integer  NULL ,
	[Last_Quarter_Day_ID] integer  NULL ,
	[Last_Month_Day_ID]  integer  NULL ,
	[Last_Week_Day_ID]   integer  NULL ,
	[Month_Day_Number]   integer  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Day] ON [OA].[DIM_Day]
( 
	[Day_ID]              ASC
)
go

CREATE TABLE [OA].[DIM_Device]
( 
	[Device_ID]          integer  NOT NULL  IDENTITY ,
	[Device_Code]        varchar(50)  NULL ,
	[Subsystem_ID]       integer  NULL ,
	[Device_Name]        varchar(100)  NULL ,
	[Device_Weight_Factor] integer  NULL ,
	[Device_Functional_Type] varchar(50)  NULL ,
	[Device_Hardware_Type] varchar(50)  NULL ,
	[Device_Manufacturer] varchar(50)  NULL ,
	[Device_Engineered_Rate] integer  NULL ,
	[Device_Area]        varchar(50)  NULL ,
	[DC_Tenant_Name]     varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Device] ON [OA].[DIM_Device]
( 
	[Device_ID]           ASC
)
go

CREATE TABLE [OA].[DIM_Facility]
( 
	[Facility_ID]        integer  NOT NULL  IDENTITY ,
	[Customer_ID]        integer  NOT NULL ,
	[Facility_Code]      varchar(50)  NULL ,
	[Facility_Name]      varchar(100)  NULL ,
	[Address_Line_1]     varchar(100)  NULL ,
	[Address_Line_2]     varchar(100)  NULL ,
	[City]               varchar(100)  NULL ,
	[Postal_Code]        varchar(50)  NULL ,
	[State_Province]     varchar(50)  NULL ,
	[Country]            varchar(50)  NULL ,
	[Lattitude]          real  NULL ,
	[Longitude]          real  NULL ,
	[Time_Zone]          varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Facility] ON [OA].[DIM_Facility]
( 
	[Facility_ID]         ASC
)
go

CREATE TABLE [OA].[DIM_Fault_Type]
( 
	[Fault_Type_ID]      integer  NOT NULL  IDENTITY ,
	[Fault_Type_Group_ID] integer  NULL ,
	[Fault_Type_Code]    varchar(50)  NULL ,
	[Fault_Type_Name]    varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Fault_Type] ON [OA].[DIM_Fault_Type]
( 
	[Fault_Type_ID]       ASC
)
go

CREATE TABLE [OA].[DIM_Fault_Type_Group]
( 
	[Fault_Type_Group_ID] integer  NOT NULL  IDENTITY ,
	[Fault_Type_Group_Code] varchar(50)  NULL ,
	[Fault_Type_Group_Name] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Fault_Type_Group] ON [OA].[DIM_Fault_Type_Group]
( 
	[Fault_Type_Group_ID]  ASC
)
go

CREATE TABLE [OA].[DIM_FlexSort_Component]
( 
	[FlexSort_Component_ID] integer  NOT NULL  IDENTITY ,
	[FlexSort_Component_Code] varchar(255)  NULL ,
	[FlexSort_System]    varchar(50)  NULL ,
	[FlexSort_System_Name] varchar(100)  NULL ,
	[FlexSort_Functional_Subsystem] varchar(100)  NULL ,
	[FlexSort_Conveyor_SubSystem] varchar(100)  NULL ,
	[FlexSort_Conveyor_VFD] varchar(100)  NULL ,
	[FlexSort_Function_Type] varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_FlexSort_Component] ON [OA].[DIM_FlexSort_Component]
( 
	[FlexSort_Component_ID]  ASC
)
go

CREATE TABLE [OA].[DIM_Hour]
( 
	[Hour_ID]            integer  NOT NULL ,
	[Day_ID]             bigint  NOT NULL ,
	[Hour_of_Day]        integer  NULL ,
	[Hour_Start_Time]    datetime  NULL ,
	[Hour_End_Time]      datetime  NULL ,
	[Last_Year_Hour_ID]  integer  NULL ,
	[Last_Quarter_Hour_ID] integer  NULL ,
	[Last_Month_Hour_ID] integer  NULL ,
	[Last_Week_Hour_ID]  integer  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Hour] ON [OA].[DIM_Hour]
( 
	[Hour_ID]             ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1DIM_Hour_Day_ID] ON [OA].[DIM_Hour]
( 
	[Day_ID]              ASC
)
INCLUDE( [Hour_Start_Time],[Hour_End_Time] )
go

CREATE TABLE [OA].[DIM_Hour_Quarter]
( 
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Hour_ID]            integer  NOT NULL ,
	[Hour_Quarter_Start_Time] datetime  NULL ,
	[Hour_Quarter_End_Time] datetime  NULL ,
	[Last_Year_Hour_Quarter_ID] integer  NULL ,
	[Last_Quarter_Hour_Quarter_ID] integer  NULL ,
	[Last_Month_Hour_Quarter_ID] integer  NULL ,
	[Last_Week_Hour_Quarter_ID] integer  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Hour_Quarter] ON [OA].[DIM_Hour_Quarter]
( 
	[Hour_Quarter_ID]     ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1DIM_Hour_Quarter_Hour_ID] ON [OA].[DIM_Hour_Quarter]
( 
	[Hour_ID]             ASC
)
INCLUDE( [Hour_Quarter_ID] )
go

CREATE TABLE [OA].[DIM_Item]
( 
	[Item_ID]            integer  NOT NULL  IDENTITY ,
	[Item_Category_ID]   integer  NULL ,
	[Item_Code]          varchar(100)  NULL ,
	[Item_Name]          varchar(255)  NULL ,
	[Item_SKU]           varchar(100)  NULL ,
	[Item_Product_Code]  varchar(50)  NULL ,
	[UOM]                varchar(50)  NULL ,
	[Client]             varchar(50)  NULL ,
	[Total_Weight]       real  NULL ,
	[Total_Volume]       real  NULL ,
	[Length]             real  NULL ,
	[Width]              real  NULL ,
	[Height]             real  NULL ,
	[Palletizing_Mode]   varchar(50)  NULL ,
	[Max_Units_Per_Pick] integer  NULL ,
	[Item_Shelf_Life_In_Days] integer  NULL ,
	[Preferred_Container_Type] varchar(50)  NULL ,
	[Velocity_Classification] varchar(50)  NULL ,
	[Hazard_Classification] varchar(50)  NULL ,
	[Supplier_Code]      varchar(50)  NULL ,
	[Base_UOM]           varchar(100)  NULL ,
	[Default_Pick_UOM]   integer  NULL ,
	[Preferred_Fulfillment_Handling] varchar(50)  NULL ,
	[AMCAP_Qualification_Ind] varchar(50)  NULL ,
	[Toppling_Rate]      integer  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Item] ON [OA].[DIM_Item]
( 
	[Item_ID]             ASC
)
go

CREATE TABLE [OA].[DIM_Item_Category]
( 
	[Item_Category_ID]   integer  NOT NULL  IDENTITY ,
	[Item_Category_Code] varchar(50)  NULL ,
	[Item_Category_Desc] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Item_Category] ON [OA].[DIM_Item_Category]
( 
	[Item_Category_ID]    ASC
)
go

CREATE TABLE [OA].[DIM_Level]
( 
	[Level_ID]           integer  NOT NULL  IDENTITY ,
	[Level_Code]         varchar(50)  NULL ,
	[Level_Name]         varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Level] ON [OA].[DIM_Level]
( 
	[Level_ID]            ASC
)
go

CREATE TABLE [OA].[DIM_Location]
( 
	[Location_ID]        integer  NOT NULL  IDENTITY ,
	[Location_Type_ID]   integer  NULL ,
	[Put_Wall_Cubby_ID]  integer  NULL ,
	[Module_ID]          integer  NULL ,
	[Level_ID]           integer  NULL ,
	[Aisle_ID]           integer  NULL ,
	[Workstation_ID]     integer  NULL ,
	[Location_Code]      varchar(255)  NULL ,
	[Location_Name]      varchar(255)  NULL ,
	[Relative_X_Axis]    varchar(50)  NULL ,
	[Relative_Y_Axis]    varchar(50)  NULL ,
	[Relative_Z_Axis]    varchar(50)  NULL ,
	[Side_Position]      varchar(50)  NULL ,
	[Slot_Position]      varchar(50)  NULL ,
	[Location_Group]     varchar(50)  NULL ,
	[Bay]                varchar(50)  NULL ,
	[Weight_Limit]       integer  NULL ,
	[Location_Size]      varchar(50)  NULL ,
	[Hazardous_Material_Allowed] integer  NULL ,
	[Bay_Width]          integer  NULL ,
	[Effective_Begin_Date] datetime  NULL ,
	[Effective_End_Date] datetime  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Location] ON [OA].[DIM_Location]
( 
	[Location_ID]         ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1DIM_Location_Put_Wall_Cubby_ID] ON [OA].[DIM_Location]
( 
	[Put_Wall_Cubby_ID]   ASC
)
INCLUDE( [Location_ID] )
go

CREATE NONCLUSTERED INDEX [XIE4DIM_Location_Module] ON [OA].[DIM_Location]
( 
	[Module_ID]           ASC,
	[Source_System]       ASC
)
INCLUDE( [Location_Code] )
go

CREATE NONCLUSTERED INDEX [XIE2DIM_Location_Source] ON [OA].[DIM_Location]
( 
	[Location_Code]       ASC
)
INCLUDE( [Source_System],[Source_System_Key] )
go

CREATE TABLE [OA].[DIM_Location_Type]
( 
	[Location_Type_ID]   integer  NOT NULL  IDENTITY ,
	[Location_Type_Code] varchar(50)  NULL ,
	[Location_Type_Name] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Location_Type] ON [OA].[DIM_Location_Type]
( 
	[Location_Type_ID]    ASC
)
go

CREATE TABLE [OA].[DIM_Logical_Destination]
( 
	[Logical_Destination_ID] integer  NOT NULL  IDENTITY ,
	[Scanner_ID]         integer  NULL ,
	[Logical_Destination_Code] varchar(50)  NULL ,
	[Logical_Destination_Name] varchar(100)  NULL ,
	[Logical_Destination_Type] varchar(50)  NULL ,
	[DC_Tenant_Name]     varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Logical_Destination] ON [OA].[DIM_Logical_Destination]
( 
	[Logical_Destination_ID]  ASC
)
go

CREATE TABLE [OA].[DIM_Measurement]
( 
	[Measurement_ID]     integer  NOT NULL  IDENTITY ,
	[Measurement_Code]   varchar(50)  NULL ,
	[Measurement_Description] varchar(100)  NULL ,
	[Unit_of_Measure]    varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Measurement] ON [OA].[DIM_Measurement]
( 
	[Measurement_ID]      ASC
)
go

CREATE TABLE [OA].[DIM_Module]
( 
	[Module_ID]          integer  NOT NULL  IDENTITY ,
	[Work_Area_ID]       integer  NULL ,
	[Module_Code]        varchar(50)  NULL ,
	[Module_Name]        varchar(100)  NULL ,
	[Module_Type]        varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Module] ON [OA].[DIM_Module]
( 
	[Module_ID]           ASC
)
go

CREATE TABLE [OA].[DIM_Monitor_Device]
( 
	[Monitor_Device_ID]  integer  NOT NULL  IDENTITY ,
	[Monitor_Device_Code] varchar(50)  NULL ,
	[Monitor_Device_Description] varchar(100)  NULL ,
	[Monitor_Device_Type] varchar(50)  NULL ,
	[Battery_Percent]    real  NULL ,
	[Voltage]            real  NULL ,
	[Signal_Strength]    real  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Monitor_Device] ON [OA].[DIM_Monitor_Device]
( 
	[Monitor_Device_ID]   ASC
)
go

CREATE TABLE [OA].[DIM_Movement_Type]
( 
	[Movement_Type_ID]   integer  NOT NULL  IDENTITY ,
	[Movement_Type_Code] varchar(50)  NULL ,
	[Movement_Type_Name] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Movement_Type] ON [OA].[DIM_Movement_Type]
( 
	[Movement_Type_ID]    ASC
)
go

CREATE TABLE [OA].[DIM_OPC_Tag]
( 
	[OPC_Tag_ID]         integer  NOT NULL  IDENTITY ,
	[OPC_Tag_Code]       varchar(255)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_OPC_Tag] ON [OA].[DIM_OPC_Tag]
( 
	[OPC_Tag_ID]          ASC
)
go

CREATE TABLE [OA].[DIM_Operator]
( 
	[Operator_ID]        integer  NOT NULL  IDENTITY ,
	[Operator_Code]      varchar(50)  NULL ,
	[Operator_Name]      varchar(100)  NULL ,
	[Operator_Full_Name] varchar(100)  NULL ,
	[Operator_Description] varchar(100)  NULL ,
	[Start_Date]         datetime  NULL ,
	[End_Date]           datetime  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Operator] ON [OA].[DIM_Operator]
( 
	[Operator_ID]         ASC
)
go

CREATE TABLE [OA].[DIM_Overhead_Destination]
( 
	[Overhead_Destination_ID] integer  NOT NULL  IDENTITY ,
	[Overhead_Destination_Code] varchar(50)  NULL ,
	[Overhead_Destination_Name] varchar(100)  NULL ,
	[Overhead_Destination_Area] varchar(50)  NULL ,
	[Overhead_Destination_Group] varchar(50)  NULL ,
	[Overhead_Destination_SubGroup] varchar(50)  NULL ,
	[Overhead_Destination_Type] varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Overhead_Destination] ON [OA].[DIM_Overhead_Destination]
( 
	[Overhead_Destination_ID]  ASC
)
go

CREATE TABLE [OA].[DIM_Overhead_Loop]
( 
	[Overhead_Loop_ID]   integer  NOT NULL  IDENTITY ,
	[Overhead_Loop_Code] varchar(50)  NULL ,
	[Overhead_Loop_Name] varchar(100)  NULL ,
	[Overhead_Loop_Type] varchar(50)  NULL ,
	[Overhead_Loop_Area] varchar(50)  NULL ,
	[Overhead_Loop_Group] varchar(50)  NULL ,
	[Overhead_Loop_SubGroup] varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Overhead_Loop] ON [OA].[DIM_Overhead_Loop]
( 
	[Overhead_Loop_ID]    ASC
)
go

CREATE TABLE [OA].[DIM_Overhead_Order]
( 
	[Overhead_Order_ID]  integer  NOT NULL  IDENTITY ,
	[Overhead_Order_Code] varchar(50)  NULL ,
	[Overhead_Order_Description] varchar(100)  NULL ,
	[Overhead_Order_Capability] varchar(50)  NULL ,
	[Overhead_Order_Sort_Mode] varchar(50)  NULL ,
	[Overhead_Order_Run] varchar(50)  NULL ,
	[Overhead_Order_Loop] varchar(50)  NULL ,
	[Overhead_Order_Index] integer  NULL ,
	[Overhead_Order_Width_Millimeters] integer  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Overhead_Order] ON [OA].[DIM_Overhead_Order]
( 
	[Overhead_Order_ID]   ASC
)
go

CREATE TABLE [OA].[DIM_Overhead_Station_Induct_Mode]
( 
	[Overhead_Station_Induct_Mode_ID] integer  NOT NULL  IDENTITY ,
	[Overhead_Station_Induct_Mode_Code] varchar(50)  NULL ,
	[Overhead_Station_Induct_Mode_Name] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Overhead_Station_Induct_Mode] ON [OA].[DIM_Overhead_Station_Induct_Mode]
( 
	[Overhead_Station_Induct_Mode_ID]  ASC
)
go

CREATE TABLE [OA].[DIM_Physical_Destination]
( 
	[Physical_Destination_ID] integer  NOT NULL  IDENTITY ,
	[Scanner_ID]         integer  NULL ,
	[Physical_Destination_Code] varchar(50)  NULL ,
	[Physical_Destination_Name] varchar(100)  NULL ,
	[Physical_Destination_Type] varchar(50)  NULL ,
	[DC_Tenant_Name]     varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Physical_Destination] ON [OA].[DIM_Physical_Destination]
( 
	[Physical_Destination_ID]  ASC
)
go

CREATE TABLE [OA].[DIM_Pick_Order]
( 
	[Pick_Order_ID]      integer  NOT NULL  IDENTITY ,
	[Customer_Order_ID]  integer  NULL ,
	[Pick_Order_Code]    varchar(50)  NULL ,
	[Pick_Order_Name]    varchar(100)  NULL ,
	[Put_Wall_Flg]       integer  NULL ,
	[Pick_Order_Type]    varchar(50)  NULL ,
	[Pick_Order_Category] varchar(50)  NULL ,
	[Pick_Order_Fulfillment_Handling_Type] varchar(50)  NULL ,
	[Pick_Order_Value_Added_Service] varchar(50)  NULL ,
	[Pick_Order_Priority] bigint  NULL ,
	[Pick_Order_Packing_Requirement] varchar(50)  NULL ,
	[Pick_Order_Latest_Staging_Date_Time] Datetime2(7)  NULL ,
	[Pick_Order_Order_Channel] varchar(50)  NULL ,
	[Pick_Order_Line_Count_Expected] real  NULL ,
	[Pick_Order_Total_Qty_Expected] real  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Pick_Order] ON [OA].[DIM_Pick_Order]
( 
	[Pick_Order_ID]       ASC
)
go

CREATE NONCLUSTERED INDEX [XIE2DIM_Pick_Order_ETL_Batch_Timestamp] ON [OA].[DIM_Pick_Order]
( 
	[ETL_Update_Timestamp]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1DIM_Pick_Order_Put_Wall_Flg] ON [OA].[DIM_Pick_Order]
( 
	[Put_Wall_Flg]        ASC
)
INCLUDE( [Pick_Order_ID] )
go

CREATE TABLE [OA].[DIM_Process_Type]
( 
	[Process_Type_ID]    integer  NOT NULL  IDENTITY ,
	[Process_Type_Code]  varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Process_Type] ON [OA].[DIM_Process_Type]
( 
	[Process_Type_ID]     ASC
)
go

CREATE TABLE [OA].[DIM_Put_Wall]
( 
	[Put_Wall_ID]        integer  NOT NULL  IDENTITY ,
	[Put_Wall_Code]      varchar(50)  NULL ,
	[Put_Wall_Name]      varchar(100)  NULL ,
	[Put_Wall_Description] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Put_Wall] ON [OA].[DIM_Put_Wall]
( 
	[Put_Wall_ID]         ASC
)
go

CREATE TABLE [OA].[DIM_Put_Wall_Cubby]
( 
	[Put_Wall_Cubby_ID]  integer  NOT NULL  IDENTITY ,
	[Put_Wall_ID]        integer  NULL ,
	[Put_Wall_Cubby_Code] varchar(50)  NULL ,
	[Put_Wall_Cubby_Name] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Put_Wall_Cubby] ON [OA].[DIM_Put_Wall_Cubby]
( 
	[Put_Wall_Cubby_ID]   ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1DIM_Put_Wall_Cubby_Put_Wall_ID] ON [OA].[DIM_Put_Wall_Cubby]
( 
	[Put_Wall_ID]         ASC
)
INCLUDE( [Put_Wall_Cubby_Code] )
go

CREATE TABLE [OA].[DIM_Reason]
( 
	[Reason_ID]          integer  NOT NULL  IDENTITY ,
	[Reason_Code]        varchar(100)  NULL ,
	[Reason_Name]        varchar(255)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Reason] ON [OA].[DIM_Reason]
( 
	[Reason_ID]           ASC
)
go

CREATE TABLE [OA].[DIM_Scanner]
( 
	[Scanner_ID]         integer  NOT NULL  IDENTITY ,
	[Scanner_Code]       varchar(50)  NULL ,
	[Device_ID]          integer  NULL ,
	[Scanner_Name]       varchar(100)  NULL ,
	[Engineered_Rate]    integer  NULL ,
	[Scanner_Functional_Type] varchar(50)  NULL ,
	[Scanner_Manufacturer] varchar(50)  NULL ,
	[Scanner_Hardware_Type] varchar(50)  NULL ,
	[DC_Tenant_Name]     varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Scanner] ON [OA].[DIM_Scanner]
( 
	[Scanner_ID]          ASC
)
go

CREATE TABLE [OA].[DIM_Status]
( 
	[Status_ID]          integer  NOT NULL  IDENTITY ,
	[Status_Code]        varchar(100)  NULL ,
	[Subsystem_ID]       integer  NOT NULL ,
	[Status_Category_ID] integer  NULL ,
	[Status_Type]        varchar(50)  NULL ,
	[DC_Tenant_Name]     varchar(50)  NULL ,
	[Status_Relevancy_Ind] integer  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Status] ON [OA].[DIM_Status]
( 
	[Status_ID]           ASC
)
go

CREATE TABLE [OA].[DIM_Status_Category]
( 
	[Status_Category_ID] integer  NOT NULL  IDENTITY ,
	[Status_Category_Code] varchar(50)  NULL ,
	[Status_Category_Name] varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Status_Category] ON [OA].[DIM_Status_Category]
( 
	[Status_Category_ID]  ASC
)
go

CREATE TABLE [OA].[DIM_Status_Locale]
( 
	[Status_Locale_ID]   integer  NOT NULL  IDENTITY ,
	[Status_ID]          integer  NOT NULL ,
	[Status_Locale_Code] varchar(50)  NULL ,
	[Status_Locale_Name] varchar(255)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Status_Locale] ON [OA].[DIM_Status_Locale]
( 
	[Status_Locale_ID]    ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1DIM_Status_Locale_Status_Locale] ON [OA].[DIM_Status_Locale]
( 
	[Status_ID]           ASC,
	[Status_Locale_Code]  ASC
)
go

CREATE TABLE [OA].[DIM_Subsystem]
( 
	[Subsystem_ID]       integer  NOT NULL  IDENTITY ,
	[Subsystem_Code]     varchar(50)  NULL ,
	[Subsystem_Name]     varchar(100)  NULL ,
	[Subsystem_Category] varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Subsystem] ON [OA].[DIM_Subsystem]
( 
	[Subsystem_ID]        ASC
)
go

CREATE TABLE [OA].[DIM_Technology]
( 
	[Technology_ID]      integer  NOT NULL  IDENTITY ,
	[Technology_Code]    varchar(50)  NULL ,
	[Technology_Name]    varchar(100)  NULL ,
	[Technology_Vendor]  varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Technology] ON [OA].[DIM_Technology]
( 
	[Technology_ID]       ASC
)
go

CREATE TABLE [OA].[DIM_Wave]
( 
	[Wave_ID]            integer  NOT NULL  IDENTITY ,
	[Wave_Code]          varchar(50)  NULL ,
	[Wave_Name]          varchar(100)  NULL ,
	[Wave_Status]        varchar(50)  NULL ,
	[Wave_Priority]      varchar(50)  NULL ,
	[Wave_Created_Date_Time] Datetime2(7)  NULL ,
	[Wave_Released_Date_Time] Datetime2(7)  NULL ,
	[Wave_Active_Date_Time] Datetime2(7)  NULL ,
	[Wave_Closed_Date_Time] Datetime2(7)  NULL ,
	[Wave_Item_Count]    integer  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Wave] ON [OA].[DIM_Wave]
( 
	[Wave_ID]             ASC
)
go

CREATE TABLE [OA].[DIM_Work_Area]
( 
	[Work_Area_ID]       integer  NOT NULL  IDENTITY ,
	[Work_Area_Code]     varchar(50)  NULL ,
	[Work_Area_Name]     varchar(100)  NULL ,
	[Work_Area_Type]     varchar(50)  NULL ,
	[Work_Area_Description] varchar(100)  NULL ,
	[Work_Area_Operation_Mode] varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Work_Area] ON [OA].[DIM_Work_Area]
( 
	[Work_Area_ID]        ASC
)
go

CREATE TABLE [OA].[DIM_Work_Type]
( 
	[Work_Type_ID]       integer  NOT NULL  IDENTITY ,
	[Work_Type_Code]     varchar(50)  NULL ,
	[Work_Type_Desc]     varchar(100)  NULL ,
	[Work_Type_Style]    varchar(100)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Work_Type] ON [OA].[DIM_Work_Type]
( 
	[Work_Type_ID]        ASC
)
go

CREATE TABLE [OA].[DIM_Workstation]
( 
	[Workstation_ID]     integer  NOT NULL  IDENTITY ,
	[Workstation_Code]   varchar(50)  NULL ,
	[Workstation_Name]   varchar(100)  NULL ,
	[Workstation_Type]   varchar(50)  NULL ,
	[Terminal_Code]      varchar(50)  NULL ,
	[Operation_Mode]     varchar(50)  NULL ,
	[Workstation_Area]   varchar(50)  NULL ,
	[Workstation_Group]  varchar(50)  NULL ,
	[Workstation_SubGroup] varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Workstation] ON [OA].[DIM_Workstation]
( 
	[Workstation_ID]      ASC
)
go

CREATE TABLE [OA].[DIM_Zone]
( 
	[Zone_ID]            integer  NOT NULL  IDENTITY ,
	[Facility_ID]        integer  NULL ,
	[Zone_Code]          varchar(100)  NULL ,
	[Zone_Name]          varchar(100)  NULL ,
	[Logical_Destination_ID] integer  NULL ,
	[Zone_Type]          varchar(50)  NULL ,
	[Qty_Picked_Target]  integer  NULL ,
	[Line_Picked_Target] integer  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Zone] ON [OA].[DIM_Zone]
( 
	[Zone_ID]             ASC
)
go

CREATE TABLE [Stage].[Equipment_Log]
( 
	[Equipment_Log_ID]   integer  NOT NULL  IDENTITY ,
	[Record_Timestamp]   Datetime2(7)  NULL ,
	[Metric]             varchar(255)  NULL ,
	[Dimension]          varchar(500)  NULL ,
	[Value]              varchar(50)  NULL ,
	[Date_Time_Created]  datetime  NULL 
	CONSTRAINT [GetDate_1430697749]
		 DEFAULT  getdate()
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKEquipment_Log] ON [Stage].[Equipment_Log]
( 
	[Equipment_Log_ID]    ASC
)
go

CREATE TABLE [OA].[ETL_Package_Exception]
( 
	[Exception_ID]       integer  NOT NULL  IDENTITY ,
	[Source_System]      varchar(50)  NULL ,
	[Package_Name]       varchar(50)  NULL ,
	[Start_Time]         datetime  NULL ,
	[Machine_Name]       varchar(50)  NULL ,
	[User_Name]          varchar(50)  NULL ,
	[Source_Name]        varchar(50)  NULL ,
	[Source_Description] varchar(100)  NULL ,
	[Event_Handler_Start_Time] datetime  NULL ,
	[Error_Code]         varchar(50)  NULL ,
	[Error_Description]  varchar(5000)  NULL ,
	[ETL_Batch_ID]       integer  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKETL_Package_Exception] ON [OA].[ETL_Package_Exception]
( 
	[Exception_ID]        ASC
)
go

CREATE TABLE [OA].[ETL_Row_Rejection]
( 
	[Rejection_ID]       integer  NOT NULL  IDENTITY ,
	[Rejection_Row_Time_Stamp] Datetime2(7)  NULL ,
	[Data_Row_ID]        varchar(100)  NULL ,
	[Data_Row]           varchar(5000)  NULL ,
	[Package_Name]       varchar(50)  NULL ,
	[Source_System]      varchar(50)  NULL ,
	[Rejection_Threshold] integer  NULL ,
	[Principal_Dimension] varchar(50)  NULL ,
	[Load_File_Row_Count] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL ,
	[ETL_Created_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKETL_Row_Rejection] ON [OA].[ETL_Row_Rejection]
( 
	[Rejection_ID]        ASC
)
go

CREATE TABLE [OA].[FCT_Container_Mode]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Container_Code]     varchar(50)  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Module_ID]          integer  NOT NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Container_Mode] ON [OA].[FCT_Container_Mode]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Container_Code]      ASC,
	[Status_ID]           ASC,
	[Module_ID]           ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Container_Mode_ETL_Batch_ID] ON [OA].[FCT_Container_Mode]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Customer_Order]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Customer_Order_ID]  integer  NOT NULL ,
	[Expected_Order_Line_Count] integer  NULL ,
	[Actual_Order_Line_Count] integer  NULL ,
	[Actual_Container_Count] integer  NULL ,
	[Undelivered_Line_Count] integer  NULL ,
	[Overdelivered_Line_Count] integer  NULL ,
	[Underdelivered_Line_Count] integer  NULL ,
	[Order_Complete_Date_Time] Datetime2(7)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Customer_Order] ON [OA].[FCT_Customer_Order]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Customer_Order_ID]   ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Customer_Order_ETL_Batch_ID] ON [OA].[FCT_Customer_Order]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Device_Statistics_Fault_Detail]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Fault_ID]           integer  NOT NULL ,
	[Fault_Count]        integer  NULL ,
	[Fault_Rank]         integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Device_Statistics_Fault_Detail] ON [OA].[FCT_Device_Statistics_Fault_Detail]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Device_ID]           ASC,
	[Fault_ID]            ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Device_Statistics_Fault_Detail_ETL_Batch_ID] ON [OA].[FCT_Device_Statistics_Fault_Detail]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Dimension_Check]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Source_Pallet_Code] varchar(50)  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Aisle_ID]           integer  NOT NULL ,
	[Wave_ID]            integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Load_Unit_Code]     varchar(50)  NULL ,
	[Transport_Request_ID] varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Dimension_Check] ON [OA].[FCT_Dimension_Check]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Source_Pallet_Code]  ASC,
	[Device_ID]           ASC,
	[Aisle_ID]            ASC,
	[Wave_ID]             ASC,
	[Item_ID]             ASC,
	[Operator_ID]         ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Dimension_Check_ETL_Batch_ID] ON [OA].[FCT_Dimension_Check]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Equipment]
( 
	[Record_Timestamp]   datetime  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Monitor_Device_ID]  integer  NOT NULL ,
	[Measurement_ID]     integer  NOT NULL ,
	[OPC_Tag_ID]         integer  NOT NULL ,
	[Value]              real  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Equipment] ON [OA].[FCT_Equipment]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Monitor_Device_ID]   ASC,
	[Measurement_ID]      ASC,
	[OPC_Tag_ID]          ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Equipment_ETL_Batch_ID] ON [OA].[FCT_Equipment]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Equipment_Comm]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Measurement_ID]     integer  NOT NULL ,
	[Receive_Overrun_Count] integer  NULL ,
	[Receive_Packet_Count] integer  NULL ,
	[Receive_Packet_Error_Count] integer  NULL ,
	[Transmit_Packet_Count] integer  NULL ,
	[Transmit_Packet_Error_Count] integer  NULL ,
	[Missing_IO_Packet_Count] integer  NULL ,
	[Not_Received_Error_Count] integer  NULL ,
	[Not_Found_Error_Count] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Equipment_Comm] ON [OA].[FCT_Equipment_Comm]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Device_ID]           ASC,
	[Measurement_ID]      ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Equipment_Comm_ETL_Batch_ID] ON [OA].[FCT_Equipment_Comm]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Equipment_Efficiency]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Data_Point_ID]      integer  NOT NULL ,
	[Run_Time_Duration_Seconds] real  NULL ,
	[Stop_Time_Duration_Seconds] real  NULL ,
	[Fault_Time_Duration_Seconds] real  NULL ,
	[Idle_Time_Duration_Seconds] real  NULL ,
	[Blocked_Time_Duration_Seconds] real  NULL ,
	[Starved_Time_Duration_Seconds] real  NULL ,
	[Total_Carton_Count] integer  NULL ,
	[Avg_Carton_Length]  real  NULL ,
	[Total_Event_Count]  integer  NULL ,
	[Fault_Count]        integer  NULL ,
	[VFD_Run_Event_Count] integer  NULL ,
	[VFD_Stop_Event_Count] integer  NULL ,
	[VFD_Idle_Event_Count] integer  NULL ,
	[VFD_Accelerate_Event_Count] integer  NULL ,
	[VFD_Decelerate_Event_Count] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Equipment_Efficiency] ON [OA].[FCT_Equipment_Efficiency]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Device_ID]           ASC,
	[Data_Point_ID]       ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Equipment_Efficiency_ETL_Batch_ID] ON [OA].[FCT_Equipment_Efficiency]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Equipment_Fault]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Fault_Status_ID]    integer  NOT NULL ,
	[Device_Status_ID]   integer  NOT NULL ,
	[Fault_Number]       varchar(50)  NOT NULL ,
	[Seq_Num]            varchar(100)  NOT NULL ,
	[Frequency]          real  NULL ,
	[Device_Current]     real  NULL ,
	[Voltage]            real  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Equipment_Fault] ON [OA].[FCT_Equipment_Fault]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Device_ID]           ASC,
	[Fault_Status_ID]     ASC,
	[Device_Status_ID]    ASC,
	[Fault_Number]        ASC,
	[Seq_Num]             ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Equipment_Fault_ETL_Batch_ID] ON [OA].[FCT_Equipment_Fault]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Equipment_Measuring_Point]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Measurement_ID]     integer  NOT NULL ,
	[Data_Point_ID]      integer  NOT NULL ,
	[Phase_Code]         varchar(50)  NOT NULL ,
	[Min_Value]          real  NULL ,
	[Max_Value]          real  NULL ,
	[Avg_Value]          real  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Equipment_Measuring_Point] ON [OA].[FCT_Equipment_Measuring_Point]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Device_ID]           ASC,
	[Measurement_ID]      ASC,
	[Data_Point_ID]       ASC,
	[Phase_Code]          ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Equipment_Measuring_Point_ETL_Batch_ID] ON [OA].[FCT_Equipment_Measuring_Point]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Equipment_PLC]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Catalog_Number]     varchar(50)  NULL ,
	[Serial_Number]      varchar(50)  NULL ,
	[Exec_Modification_Count] integer  NULL ,
	[Audit_Value_1]      varchar(100)  NULL ,
	[Audit_Value_2]      varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Equipment_PLC] ON [OA].[FCT_Equipment_PLC]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Device_ID]           ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Equipment_PLC_ETL_Batch_ID] ON [OA].[FCT_Equipment_PLC]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Equipment_Status]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Reason_ID]          integer  NOT NULL ,
	[Label_Print_Line_Code] varchar(50)  NOT NULL ,
	[Pallet_Code]        varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Equipment_Status] ON [OA].[FCT_Equipment_Status]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Device_ID]           ASC,
	[Status_ID]           ASC,
	[Reason_ID]           ASC,
	[Label_Print_Line_Code]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Equipment_Status_ETL_Batch_ID] ON [OA].[FCT_Equipment_Status]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Fault]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Fault_Start_Date_Time] Datetime2(7)  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Physical_Device_ID] integer  NOT NULL ,
	[Location_ID]        integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Fault_Tag_Reason_ID] integer  NOT NULL ,
	[Container_Physical_Code] varchar(50)  NOT NULL ,
	[PLC_Code]           varchar(50)  NOT NULL ,
	[Fault_Duration_Seconds] integer  NULL ,
	[Fault_Repair_Duration_Seconds] integer  NULL ,
	[Fault_Acknowledgement_Date_Time] Datetime2(7)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Fault] ON [OA].[FCT_Fault]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Fault_Start_Date_Time]  ASC,
	[Device_ID]           ASC,
	[Physical_Device_ID]  ASC,
	[Location_ID]         ASC,
	[Status_ID]           ASC,
	[Item_ID]             ASC,
	[Operator_ID]         ASC,
	[Fault_Tag_Reason_ID]  ASC,
	[Container_Physical_Code]  ASC,
	[PLC_Code]            ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Fault_ETL_Batch_ID] ON [OA].[FCT_Fault]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Fault_Detail]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Workstation_ID]     integer  NOT NULL ,
	[Fault_Status_ID]    integer  NOT NULL ,
	[Module_ID]          integer  NOT NULL ,
	[Fault_Flg]          varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Fault_Detail] ON [OA].[FCT_Fault_Detail]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Workstation_ID]      ASC,
	[Fault_Status_ID]     ASC,
	[Module_ID]           ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Fault_Detail_ETL_Batch_ID] ON [OA].[FCT_Fault_Detail]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE NONCLUSTERED INDEX [XIE2FCT_Fault_Detail_RecordTimestamp] ON [OA].[FCT_Fault_Detail]
( 
	[Record_Timestamp]    ASC
)
go

CREATE TABLE [OA].[FCT_FlexSort_Fault_Detail]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[FlexSort_Component_ID] integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Fault_Type_ID]      integer  NOT NULL ,
	[Event]              varchar(50)  NOT NULL ,
	[Message_Sequence]   integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_FlexSort_Fault_Detail] ON [OA].[FCT_FlexSort_Fault_Detail]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[FlexSort_Component_ID]  ASC,
	[Status_ID]           ASC,
	[Fault_Type_ID]       ASC,
	[Event]               ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_FlexSort_Fault_Detail_ETL_Batch_ID] ON [OA].[FCT_FlexSort_Fault_Detail]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_GTP_Operator_Performance]
( 
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Workstation_ID]     integer  NOT NULL ,
	[Zone_ID]            integer  NOT NULL ,
	[Work_Type_ID]       integer  NOT NULL ,
	[Hour_Quarter_Start_Time] datetime  NOT NULL ,
	[Hour_Quarter_End_Time] datetime  NOT NULL ,
	[Logged_In_Time_In_Seconds] integer  NULL ,
	[Departure_Starvation_In_Seconds] integer  NULL ,
	[Arrival_Starvation_In_Seconds] integer  NULL ,
	[Total_Starvation_In_Seconds] integer  NULL ,
	[Qty_Completed]      integer  NULL ,
	[Lines_Completed]    integer  NULL ,
	[Qty_Shorted]        integer  NULL ,
	[Qty_Exception]      integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_GTP_Operator_Performance] ON [OA].[FCT_GTP_Operator_Performance]
( 
	[Hour_Quarter_ID]     ASC,
	[Operator_ID]         ASC,
	[Workstation_ID]      ASC,
	[Zone_ID]             ASC,
	[Work_Type_ID]        ASC,
	[Hour_Quarter_Start_Time]  ASC,
	[Hour_Quarter_End_Time]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_GTP_Operator_Performance_ETL_Batch_ID] ON [OA].[FCT_GTP_Operator_Performance]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Hour_Quarter_ID] )
go

CREATE TABLE [OA].[FCT_Inventory]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Location_ID]        integer  NOT NULL ,
	[Load_Unit_Code]     varchar(50)  NOT NULL ,
	[Inventory_Unit_Code] varchar(50)  NOT NULL ,
	[Inventory_Batch_Code] varchar(50)  NOT NULL ,
	[Base_Load_Unit_Code] varchar(50)  NULL ,
	[Receiving_Processing_Code] varchar(50)  NULL ,
	[Inventory_Qty]      integer  NULL ,
	[Inventory_Unassigned_Qty] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Inventory] ON [OA].[FCT_Inventory]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Item_ID]             ASC,
	[Location_ID]         ASC,
	[Load_Unit_Code]      ASC,
	[Inventory_Unit_Code]  ASC,
	[Inventory_Batch_Code]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Inventory_ETL_Batch] ON [OA].[FCT_Inventory]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Label_Print_Apply]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Label_Print_Line_Code] varchar(50)  NOT NULL ,
	[Induct_Status_ID]   integer  NOT NULL ,
	[Response_Status_ID] integer  NOT NULL ,
	[Induct_Scanned_Code] varchar(50)  NOT NULL ,
	[Response_Event_Time] Datetime2(7)  NOT NULL ,
	[Intended_Destination_Code] varchar(50)  NULL ,
	[Verification_Status_ID] integer  NOT NULL ,
	[Verification_Scanned_Code] varchar(100)  NULL ,
	[Verification_Event_Time] Datetime2(7)  NULL ,
	[Print_Details]      varchar(100)  NULL ,
	[Induct_Request_Count] integer  NULL ,
	[Tracking_Code]      integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Label_Print_Apply] ON [OA].[FCT_Label_Print_Apply]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Label_Print_Line_Code]  ASC,
	[Induct_Status_ID]    ASC,
	[Response_Status_ID]  ASC,
	[Induct_Scanned_Code]  ASC,
	[Response_Event_Time]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Label_Print_Apply_ETL_Batch_ID] ON [OA].[FCT_Label_Print_Apply]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Lane_Status]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Logical_Destination_ID] integer  NOT NULL ,
	[Physical_Destination_ID] integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Reason_ID]          integer  NOT NULL ,
	[Fill_Level_Value]   integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Lane_Status] ON [OA].[FCT_Lane_Status]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Logical_Destination_ID]  ASC,
	[Physical_Destination_ID]  ASC,
	[Status_ID]           ASC,
	[Reason_ID]           ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Lane_Status_ETL_Batch_ID] ON [OA].[FCT_Lane_Status]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Lift_Statistics]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Up_Move_Count]      integer  NULL ,
	[Down_Move_Count]    integer  NULL ,
	[Y_Travel_Distance_Meters] integer  NULL ,
	[Infeed_Count]       integer  NULL ,
	[Outfeed_Count]      integer  NULL ,
	[Down_Time_Minutes]  integer  NULL ,
	[Active_Time_Minutes] integer  NULL ,
	[Run_Time_Minutes]   integer  NULL ,
	[Fault_Count]        integer  NULL ,
	[Warning_Count]      integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Lift_Statistics] ON [OA].[FCT_Lift_Statistics]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Device_ID]           ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Lift_Statistics_ETL_Batch_ID] ON [OA].[FCT_Lift_Statistics]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Merge_Lane]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Merge_Physical_Destination_ID] integer  NOT NULL ,
	[Merge_PLC_Code]     varchar(50)  NOT NULL ,
	[Reason_ID]          integer  NOT NULL ,
	[Container_Count]    integer  NULL ,
	[Event_Code]         varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Merge_Lane] ON [OA].[FCT_Merge_Lane]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Merge_Physical_Destination_ID]  ASC,
	[Merge_PLC_Code]      ASC,
	[Reason_ID]           ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Merge_Lane_ETL_Batch_ID] ON [OA].[FCT_Merge_Lane]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Movement]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Move_Start_Date_Time] Datetime2(7)  NOT NULL ,
	[Load_Unit_Code]     varchar(50)  NOT NULL ,
	[Source_Location_ID] integer  NOT NULL ,
	[Destination_Location_ID] integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Physical_Device_ID] integer  NOT NULL ,
	[Movement_Type_ID]   integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Transport_Reason_ID] integer  NOT NULL ,
	[Container_Type_ID]  integer  NOT NULL ,
	[Transport_Request_ID] varchar(50)  NOT NULL ,
	[Group_Transport_Request_ID] varchar(50)  NOT NULL ,
	[Move_Duration_Seconds] integer  NULL ,
	[Item_Count]         integer  NULL ,
	[X_Distance_Traveled] integer  NULL ,
	[Aisles_Traveled_Count] integer  NULL ,
	[Load_Unit_Content_Code] varchar(50)  NULL ,
	[Move_Arrive_Date_Time] Datetime2(7)  NULL ,
	[Move_Leave_Date_Time] Datetime2(7)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Movement] ON [OA].[FCT_Movement]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Move_Start_Date_Time]  ASC,
	[Load_Unit_Code]      ASC,
	[Source_Location_ID]  ASC,
	[Destination_Location_ID]  ASC,
	[Device_ID]           ASC,
	[Physical_Device_ID]  ASC,
	[Movement_Type_ID]    ASC,
	[Item_ID]             ASC,
	[Status_ID]           ASC,
	[Transport_Reason_ID]  ASC,
	[Container_Type_ID]   ASC,
	[Transport_Request_ID]  ASC,
	[Group_Transport_Request_ID]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Movement_ETL_Batch_ID] ON [OA].[FCT_Movement]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Order_Line]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Location_ID]        integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Zone_ID]            integer  NOT NULL ,
	[Pick_Order_ID]      integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Cart_Instance_ID]   integer  NOT NULL ,
	[Technology_ID]      integer  NOT NULL ,
	[Container_Train_ID] integer  NOT NULL ,
	[Pick_To_Container_Type_ID] integer  NOT NULL ,
	[Line_Item]          varchar(50)  NOT NULL ,
	[Work_Type_ID]       integer  NOT NULL ,
	[Work_Request_Code]  varchar(50)  NOT NULL ,
	[Container_Type_ID]  integer  NOT NULL ,
	[Process_Type_ID]    integer  NOT NULL ,
	[Reason_ID]          integer  NOT NULL ,
	[Pick_Order_Line_Complete_Ind] integer  NULL ,
	[Container_Physical_Code] varchar(50)  NULL ,
	[Pick_To_Container_Physical_Code] varchar(50)  NULL ,
	[Container_Instance_Code] varchar(50)  NULL ,
	[Pick_Batch_Code]    varchar(50)  NULL ,
	[Duration_Seconds]   real  NULL ,
	[Picked_Qty]         real  NULL ,
	[Skipped_Qty]        real  NULL ,
	[Shorted_Qty]        real  NULL ,
	[New_Container_Qty]  real  NULL ,
	[Requested_Qty]      integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Order_Line] ON [OA].[FCT_Order_Line]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Location_ID]         ASC,
	[Operator_ID]         ASC,
	[Zone_ID]             ASC,
	[Pick_Order_ID]       ASC,
	[Item_ID]             ASC,
	[Cart_Instance_ID]    ASC,
	[Technology_ID]       ASC,
	[Container_Train_ID]  ASC,
	[Pick_To_Container_Type_ID]  ASC,
	[Line_Item]           ASC,
	[Work_Type_ID]        ASC,
	[Work_Request_Code]   ASC,
	[Container_Type_ID]   ASC
)
go

CREATE NONCLUSTERED INDEX [XIE4FCT_Order_Line_Shorted_Qty] ON [OA].[FCT_Order_Line]
( 
	[Record_Timestamp]    ASC,
	[Shorted_Qty]         ASC
)
INCLUDE( [Hour_Quarter_ID],[Operator_ID] )
go

CREATE NONCLUSTERED INDEX [XIE5FCT_Order_Line_ETL_Batch_ID] ON [OA].[FCT_Order_Line]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE NONCLUSTERED INDEX [XIE6FCT_Order_Line_Pick_Order_ID] ON [OA].[FCT_Order_Line]
( 
	[Pick_Order_ID]       ASC
)
INCLUDE( [Pick_Order_Line_Complete_Ind],[Picked_Qty] )
go

CREATE NONCLUSTERED INDEX [XIE2FCT_Order_Line_RecordTimestamp] ON [OA].[FCT_Order_Line]
( 
	[Record_Timestamp]    ASC,
	[Location_ID]         ASC
)
INCLUDE( [Operator_ID],[Picked_Qty] )
go

CREATE NONCLUSTERED INDEX [XIE3FCT_Order_Line_Location_ID] ON [OA].[FCT_Order_Line]
( 
	[Location_ID]         ASC,
	[Record_Timestamp]    ASC
)
INCLUDE( [Picked_Qty] )
go

CREATE TABLE [OA].[FCT_Overhead_BufferLoop_Carrier]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Overhead_Order_ID]  integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Overhead_Buffer_Loop_ID] integer  NOT NULL ,
	[Overhead_Carrier_Exit_Status_ID] integer  NOT NULL ,
	[Overhead_Carrier_Code] varchar(50)  NULL ,
	[Overhead_Line_Item_Code] varchar(50)  NULL ,
	[Overhead_Carrier_Entry_Date_Time] Datetime2(7)  NULL ,
	[Overhead_Carrier_Loop_Count] integer  NULL ,
	[Overhead_Carrier_InLoop_Fail_Exit_Count] integer  NULL ,
	[Overhead_Carrier_InLoop_Duration_Seconds] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Overhead_BufferLoop_Carrier] ON [OA].[FCT_Overhead_BufferLoop_Carrier]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Overhead_Order_ID]   ASC,
	[Item_ID]             ASC,
	[Overhead_Buffer_Loop_ID]  ASC,
	[Overhead_Carrier_Exit_Status_ID]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Overhead_BufferLoop_Carrier_ETL_Batch_ID] ON [OA].[FCT_Overhead_BufferLoop_Carrier]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Overhead_BufferLoop_Status]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Overhead_BufferLoop_ID] integer  NOT NULL ,
	[Fault_Status_ID]    integer  NOT NULL ,
	[Lock_Status_ID]     integer  NOT NULL ,
	[Empty_Overhead_Carrier_Count] integer  NULL ,
	[Entered_Overhead_Carrier_Count] integer  NULL ,
	[Exited_Overhead_Carrier_Count] integer  NULL ,
	[Occupied_Overhead_Carrier_Count] integer  NULL ,
	[Overhead_BufferLoop_Fault_Count] integer  NULL ,
	[Overhead_BufferLoop_Open_SKU_Requests] integer  NULL ,
	[Overhead_BufferLoop_Utilization_Percentage] integer  NULL ,
	[Oldest_Overhead_Carrier_In_Loop_Duration_Seconds] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Overhead_BufferLoop_Status] ON [OA].[FCT_Overhead_BufferLoop_Status]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Overhead_BufferLoop_ID]  ASC,
	[Fault_Status_ID]     ASC,
	[Lock_Status_ID]      ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Overhead_BufferLoop_Status_ETL_Batch_ID] ON [OA].[FCT_Overhead_BufferLoop_Status]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Overhead_Carrier]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Location_ID]        integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Reason_ID]          integer  NOT NULL ,
	[Overhead_Carrier_Code] varchar(50)  NOT NULL ,
	[Overhead_Carrier_Insert_Date_Time] Datetime2(7)  NULL ,
	[Overhead_Carrier_Create_Date_Time] Datetime2(7)  NULL ,
	[Overhead_Carrier_Age_In_Seconds] integer  NULL ,
	[Event]              varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Overhead_Carrier] ON [OA].[FCT_Overhead_Carrier]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Location_ID]         ASC,
	[Status_ID]           ASC,
	[Reason_ID]           ASC,
	[Overhead_Carrier_Code]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Overhead_Carrier_ETL_Batch_ID] ON [OA].[FCT_Overhead_Carrier]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Overhead_Empty_BufferLoop]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Overhead_Loop_ID]   integer  NOT NULL ,
	[Empty_Buffer_Utilization_Percentage] integer  NULL ,
	[Empty_Buffer_Inbound_Carrier_Requested_Qty] integer  NULL ,
	[Empty_Upper_Buffer_Available_Outbound_Carrier_Qty] integer  NULL ,
	[Empty_Lower_Buffer_Available_Outbound_Carrier_Qty] integer  NULL ,
	[Empty_Buffer_Overflow_Planned_Carrier_Qty] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Overhead_Empty_BufferLoop] ON [OA].[FCT_Overhead_Empty_BufferLoop]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Overhead_Loop_ID]    ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Overhead_Emtpy_Buffer_Loop_ETL_Batch_ID] ON [OA].[FCT_Overhead_Empty_BufferLoop]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Overhead_Induction]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Workstation_ID]     integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Overhead_Station_Induct_Mode_ID] integer  NOT NULL ,
	[Overhead_Induction_Type] varchar(50)  NULL ,
	[Container_Code]     varchar(50)  NULL ,
	[Container_Scan_Time] Datetime2(7)  NULL ,
	[SKU_Qualifier]      varchar(100)  NULL ,
	[SKU_Confirm_Time]   Datetime2(7)  NULL ,
	[SKU_Scan_Time]      Datetime2(7)  NULL ,
	[Overhead_Carrier_Code] varchar(50)  NULL ,
	[Overhead_Carrier_Scan_Time] Datetime2(7)  NULL ,
	[Induct_Duration_Seconds] real  NULL ,
	[Empty_Buffer_Filling_Ratio] real  NULL ,
	[Operator_Wait_Duration_Seconds] real  NULL ,
	[Overhead_Induction_Exchange_Duration_Seconds] real  NULL ,
	[Previous_Overhead_Item_Release_Time] Datetime2(7)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Overhead_Induction] ON [OA].[FCT_Overhead_Induction]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Workstation_ID]      ASC,
	[Operator_ID]         ASC,
	[Item_ID]             ASC,
	[Status_ID]           ASC,
	[Overhead_Station_Induct_Mode_ID]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Overhead_Induction_ETL_Batch_ID] ON [OA].[FCT_Overhead_Induction]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Overhead_Order_Buffer_Run]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Overhead_Buffer_Loop_ID] integer  NOT NULL ,
	[Overhead_Run]       varchar(50)  NOT NULL ,
	[Order_Buffer_Run_Create_Date_Time] Datetime2(7)  NULL ,
	[First_Carrier_Arrival_Date_Time] Datetime2(7)  NULL ,
	[Last_Carrier_Arrival_Date_Time] Datetime2(7)  NULL ,
	[Order_Buffer_Run_Release_Date_Time] Datetime2(7)  NULL ,
	[Order_Buffer_Run_Qty] integer  NULL ,
	[Order_Buffer_Run_Duration_Seconds] integer  NULL ,
	[Order_Buffer_Run_Width_Millimeters] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Overhead_Order_Buffer_Run] ON [OA].[FCT_Overhead_Order_Buffer_Run]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Overhead_Buffer_Loop_ID]  ASC,
	[Overhead_Run]        ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Overhead_OrderBuffer_Run_ETL_Batch_ID] ON [OA].[FCT_Overhead_Order_Buffer_Run]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Overhead_Order_Complete]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Overhead_Order_ID]  integer  NOT NULL ,
	[Pack_Station_ID]    integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Order_Created_Date_Time] Datetime2(7)  NULL ,
	[Order_Target_Date_Time] Datetime2(7)  NULL ,
	[Order_Active_Date_Time] Datetime2(7)  NULL ,
	[Order_Buffer_First_Unit_Exit_Date_Time] Datetime2(7)  NULL ,
	[Order_Buffer_Last_Unit_Exit_Date_Time] Datetime2(7)  NULL ,
	[Order_Buffer_First_Exit_Unavailable_Date_Time] Datetime2(7)  NULL ,
	[Order_Buffer_Last_Exit_Unavailable_Date_Time] Datetime2(7)  NULL ,
	[Order_PreSort_Completion_Time] Datetime2(7)  NULL ,
	[Order_PreSort_Release_Time] Datetime2(7)  NULL ,
	[Order_Sort_Run_Assemble_Complete_Date_Time] Datetime2(7)  NULL ,
	[Order_Pack_Station_Request_Date_Time] Datetime2(7)  NULL ,
	[Order_Pack_Station_Assignment_Date_Time] Datetime2(7)  NULL ,
	[Order_Pack_Start_Date_Time] Datetime2(7)  NULL ,
	[Order_Pack_Last_Carrier_Date_Time] Datetime2(7)  NULL ,
	[Order_Buffer_Exit_Unavailable_Count] real  NULL ,
	[Order_Misdivert_Count] real  NULL ,
	[Order_Requested_Line_Count] real  NULL ,
	[Order_Requested_Qty] real  NULL ,
	[Order_Short_Ind]    varchar(50)  NULL ,
	[Order_Late_Ind]     integer  NULL ,
	[Order_Pack_Duration_Seconds] real  NULL ,
	[Order_Download_To_Complete_Duration_Seconds] real  NULL ,
	[Order_Active_To_Complete_Duration_Seconds] real  NULL ,
	[Order_Pack_Station_Idle_Duration_Seconds] real  NULL ,
	[Order_Pack_Station_Busy_Duration_Seconds] real  NULL ,
	[Order_Pack_Station_Operator_Duration_Seconds] real  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Overhead_Order_Complete] ON [OA].[FCT_Overhead_Order_Complete]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Overhead_Order_ID]   ASC,
	[Pack_Station_ID]     ASC,
	[Operator_ID]         ASC,
	[Status_ID]           ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Overhead_Order_Complete_ETL_Batch_ID] ON [OA].[FCT_Overhead_Order_Complete]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Overhead_Scanner_Readings]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Scanner_ID]         integer  NOT NULL ,
	[Location_ID]        integer  NOT NULL ,
	[Start_TimeFrame_Date_Time] Datetime2(7)  NULL ,
	[Min_Response_Time_In_Millisecond] real  NULL ,
	[Max_Response_Time_In_Millisecond] real  NULL ,
	[Mean_Response_Time_In_Millisecond] real  NULL ,
	[Scan_Fail_Count]    integer  NULL ,
	[Scan_Success_Count] integer  NULL ,
	[Telegram_Type]      varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Overhead_Scanner_Readings] ON [OA].[FCT_Overhead_Scanner_Readings]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Scanner_ID]          ASC,
	[Location_ID]         ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Overhead_Scanner_Readings_ETL_Batch_ID] ON [OA].[FCT_Overhead_Scanner_Readings]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [ETL_Update_Timestamp] )
go

CREATE TABLE [OA].[FCT_Overhead_Sort_Complete]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Overhead_Sort_Loop_ID] integer  NOT NULL ,
	[Overhead_Run]       integer  NOT NULL ,
	[Run_Qty]            integer  NULL ,
	[Run_Lane_Count]     integer  NULL ,
	[Run_Unit_Assignment_Date_Time] Datetime2(7)  NULL ,
	[Run_PreSort_Completion_Date_Time] Datetime2(7)  NULL ,
	[Run_PreSort_Release_Date_Time] Datetime2(7)  NULL ,
	[Run_Sort_Start_Date_Time] Datetime2(7)  NULL ,
	[Run_PreSort_Empty_Date_Time] Datetime2(7)  NULL ,
	[Overhead_Run_Width_Millimeters] integer  NULL ,
	[Run_Sort_Duration_Seconds] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Overhead_Sort_Complete] ON [OA].[FCT_Overhead_Sort_Complete]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Overhead_Sort_Loop_ID]  ASC,
	[Overhead_Run]        ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Overhead_Sort_Complete_ETL_Batch_ID] ON [OA].[FCT_Overhead_Sort_Complete]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Overhead_Sort_Fault]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Overhead_Carrier_Code] varchar(50)  NOT NULL ,
	[Overhead_Sort_Intended_Destination_ID] integer  NOT NULL ,
	[Overhead_Sort_Actual_Destination_ID] integer  NOT NULL ,
	[Overhead_Order_ID]  integer  NOT NULL ,
	[Fault_Status_ID]    integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Overhead_Scanner_Location] varchar(50)  NULL ,
	[Overhead_Order_Line] varchar(50)  NULL ,
	[Overhead_Order_Run_Position] varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Overhead_Sort_Fault] ON [OA].[FCT_Overhead_Sort_Fault]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Overhead_Carrier_Code]  ASC,
	[Overhead_Sort_Intended_Destination_ID]  ASC,
	[Overhead_Sort_Actual_Destination_ID]  ASC,
	[Overhead_Order_ID]   ASC,
	[Fault_Status_ID]     ASC,
	[Item_ID]             ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Overhead_Sort_Fault_ETL_Batch_ID] ON [OA].[FCT_Overhead_Sort_Fault]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Pallet_Summary]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Pallet_Code]        varchar(50)  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Wave_ID]            integer  NOT NULL ,
	[Container_Type_ID]  integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Order_Code]         varchar(50)  NULL ,
	[Pallet_Start_Date_Time] Datetime2(7)  NULL ,
	[Pallet_Duration_In_Seconds] integer  NULL ,
	[Total_Case_Count]   integer  NULL ,
	[Manual_Case_Count]  integer  NULL ,
	[Layer_Count]        integer  NULL ,
	[Event_Type]         varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Pallet_Summary] ON [OA].[FCT_Pallet_Summary]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Pallet_Code]         ASC,
	[Device_ID]           ASC,
	[Wave_ID]             ASC,
	[Container_Type_ID]   ASC,
	[Operator_ID]         ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Pallet_Summary_ETL_Batch_ID] ON [OA].[FCT_Pallet_Summary]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Palletize_Item]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Pallet_Code]        varchar(50)  NOT NULL ,
	[Load_Unit_Code]     varchar(50)  NOT NULL ,
	[Source_Location_ID] integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Wave_ID]            integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Retrieve_Date_Time] Datetime2(7)  NULL ,
	[Item_Stacked_Flg]   integer  NULL ,
	[Manual_Flg]         integer  NULL ,
	[Stack_Sequence_Number] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Palletize_Item] ON [OA].[FCT_Palletize_Item]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Pallet_Code]         ASC,
	[Load_Unit_Code]      ASC,
	[Source_Location_ID]  ASC,
	[Device_ID]           ASC,
	[Item_ID]             ASC,
	[Wave_ID]             ASC,
	[Operator_ID]         ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Palletize_Item_ETL_Batch_ID] ON [OA].[FCT_Palletize_Item]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Pick_Activity]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Zone_ID]            integer  NOT NULL ,
	[Workstation_ID]     integer  NOT NULL ,
	[Technology_ID]      integer  NOT NULL ,
	[Work_Type_ID]       integer  NOT NULL ,
	[Container_Type_ID]  integer  NOT NULL ,
	[Module_ID]          integer  NOT NULL ,
	[Load_Unit_Code]     varchar(50)  NOT NULL ,
	[Event]              varchar(255)  NOT NULL ,
	[SKU_Code]           varchar(50)  NOT NULL ,
	[Cart_Code]          varchar(50)  NULL ,
	[Container_Instance_Code] varchar(50)  NULL ,
	[Cart_Instance_Code] varchar(50)  NULL ,
	[Induct_Type]        varchar(50)  NULL ,
	[Cluster_Train_Code] varchar(50)  NULL ,
	[Induction_Zone_Code] varchar(50)  NULL ,
	[Load_Unit_Usage_Type_Code] varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Pick_Activity] ON [OA].[FCT_Pick_Activity]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Operator_ID]         ASC,
	[Zone_ID]             ASC,
	[Workstation_ID]      ASC,
	[Technology_ID]       ASC,
	[Work_Type_ID]        ASC,
	[Container_Type_ID]   ASC,
	[Module_ID]           ASC,
	[Load_Unit_Code]      ASC,
	[Event]               ASC,
	[SKU_Code]            ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Pick_Activity_ETL_Batch_ID] ON [OA].[FCT_Pick_Activity]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Pick_Order]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Pick_Order_ID]      integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Location_ID]        integer  NOT NULL ,
	[Zone_ID]            integer  NOT NULL ,
	[Wave_ID]            integer  NOT NULL ,
	[Reason_ID]          integer  NOT NULL ,
	[Pick_Order_Event]   varchar(50)  NOT NULL ,
	[Unique_Event_Identifier] varchar(50)  NULL ,
	[Event_Detail]       varchar(2000)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Pick_Order] ON [OA].[FCT_Pick_Order]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Pick_Order_ID]       ASC,
	[Operator_ID]         ASC,
	[Location_ID]         ASC,
	[Zone_ID]             ASC,
	[Wave_ID]             ASC,
	[Reason_ID]           ASC,
	[Pick_Order_Event]    ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Pick_Order_ETL_Batch_ID] ON [OA].[FCT_Pick_Order]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Pick_Order_Complete]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Pick_Order_ID]      integer  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Location_ID]        integer  NOT NULL ,
	[Wave_ID]            integer  NOT NULL ,
	[Zone_ID]            integer  NOT NULL ,
	[Order_Released_Date_Time] Datetime2(7)  NULL ,
	[Order_Assigned_Date_Time] Datetime2(7)  NULL ,
	[Order_Assigned_Location_ID] integer  NULL ,
	[Order_Put_Start_Date_Time] Datetime2(7)  NULL ,
	[Order_Pack_Start_Date_Time] Datetime2(7)  NULL ,
	[Order_Line_Count]   real  NULL ,
	[Order_Total_Qty]    real  NULL ,
	[Overflow_Location_Count] real  NULL ,
	[Order_Put_Lines_Complete_Date_Time] Datetime2(7)  NULL ,
	[Order_Put_Wall_Pack_Complete_Date_Time] Datetime2(7)  NULL ,
	[Order_Put_Wall_Pack_Operator_ID] integer  NULL ,
	[Order_Put_Wall_Pack_Location_ID] integer  NULL ,
	[Order_Put_Wall_Pack_Zone_ID] integer  NULL ,
	[Order_Lines_Complete_Date_Time] Datetime2(7)  NULL ,
	[Order_Pack_Complete_Date_Time] Datetime2(7)  NULL ,
	[Order_Complete_Date_Time] Datetime2(7)  NULL ,
	[Pick_Order_Complete_Ind] integer  NULL ,
	[Order_Cancelled_Ind] integer  NULL ,
	[Order_Cancelled_Date_Time] Datetime2(7)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Pick_Order_Complete] ON [OA].[FCT_Pick_Order_Complete]
( 
	[Record_Timestamp]    ASC,
	[Pick_Order_ID]       ASC,
	[Hour_Quarter_ID]     ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Pick_Order_Complete_Pick_Order_ID] ON [OA].[FCT_Pick_Order_Complete]
( 
	[Pick_Order_ID]       ASC
)
INCLUDE( [Order_Put_Wall_Pack_Complete_Date_Time],[Order_Assigned_Date_Time],[Order_Put_Start_Date_Time],[Order_Put_Lines_Complete_Date_Time],[Pick_Order_Complete_Ind] )
go

CREATE NONCLUSTERED INDEX [XIE2FCT_Pick_Order_Complete_Order_Pack_Complete_DateTime] ON [OA].[FCT_Pick_Order_Complete]
( 
	[Order_Put_Wall_Pack_Complete_Date_Time]  ASC
)
INCLUDE( [Location_ID],[Wave_ID] )
go

CREATE NONCLUSTERED INDEX [XIE7FCT_Pick_Order_Complete_Order_Complete_Date_Time] ON [OA].[FCT_Pick_Order_Complete]
( 
	[Order_Complete_Date_Time]  ASC
)
INCLUDE( [Record_Timestamp],[Order_Released_Date_Time] )
go

CREATE NONCLUSTERED INDEX [XIE8FCT_Pick_Order_Complete_ETL_Batch_ID] ON [OA].[FCT_Pick_Order_Complete]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE NONCLUSTERED INDEX [XIE3FCT_Pick_Order_Complete_Order_Lines_Complete_DateTime] ON [OA].[FCT_Pick_Order_Complete]
( 
	[Order_Put_Lines_Complete_Date_Time]  ASC
)
INCLUDE( [Order_Assigned_Location_ID],[Wave_ID] )
go

CREATE NONCLUSTERED INDEX [XIE4FCT_Pick_Order_Complete_Overflow] ON [OA].[FCT_Pick_Order_Complete]
( 
	[Overflow_Location_Count]  ASC,
	[Order_Put_Wall_Pack_Complete_Date_Time]  ASC
)
INCLUDE( [Order_Assigned_Location_ID],[Wave_ID] )
go

CREATE NONCLUSTERED INDEX [XIE5FCT_Pick_Order_Complete_Order_Assigned_Location_ID] ON [OA].[FCT_Pick_Order_Complete]
( 
	[Order_Assigned_Location_ID]  ASC,
	[Order_Pack_Start_Date_Time]  ASC,
	[Order_Put_Wall_Pack_Complete_Date_Time]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE6FCT_Pick_Order_Complete_Order_Put_Start_Date_Time] ON [OA].[FCT_Pick_Order_Complete]
( 
	[Order_Put_Start_Date_Time]  ASC,
	[Order_Pack_Start_Date_Time]  ASC
)
INCLUDE( [Pick_Order_ID],[Order_Assigned_Location_ID] )
go

CREATE TABLE [OA].[FCT_Robot_Activity]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Robot_Device_ID]    integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Module_ID]          integer  NOT NULL ,
	[Event]              varchar(50)  NOT NULL ,
	[Initial_Download_Flg] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Robot_Activity] ON [OA].[FCT_Robot_Activity]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Robot_Device_ID]     ASC,
	[Status_ID]           ASC,
	[Module_ID]           ASC,
	[Event]               ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Robot_Activity_RecordTimestamp] ON [OA].[FCT_Robot_Activity]
( 
	[Record_Timestamp]    ASC
)
go

CREATE NONCLUSTERED INDEX [XIE2FCT_Robot_Activity_ETL_Batch_ID] ON [OA].[FCT_Robot_Activity]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Shuttle_Statistics]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Device_ID]          integer  NOT NULL ,
	[Physical_Device_ID] integer  NOT NULL ,
	[Storage_Count]      integer  NULL ,
	[Retrieval_Count]    integer  NULL ,
	[Shuffle_Count]      integer  NULL ,
	[IAT_Count]          integer  NULL ,
	[Bypass_Count]       integer  NULL ,
	[X_Travel_Distance_Meters] integer  NULL ,
	[Z_Travel_Distance_Meters] integer  NULL ,
	[W_Travel_Distance_Meters] integer  NULL ,
	[Finger_Moves_Pair_1] integer  NULL ,
	[Finger_Moves_Pair_2] integer  NULL ,
	[Finger_Moves_Pair_3] integer  NULL ,
	[Finger_Moves_Pair_4] integer  NULL ,
	[Down_Time_Minutes]  integer  NULL ,
	[Active_Time_Minutes] integer  NULL ,
	[Run_Time_Minutes]   integer  NULL ,
	[Fault_Count]        integer  NULL ,
	[Warning_Count]      integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Shuttle_Statistics] ON [OA].[FCT_Shuttle_Statistics]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Device_ID]           ASC,
	[Physical_Device_ID]  ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Shuttle_Statistics_ETL_Batch_ID] ON [OA].[FCT_Shuttle_Statistics]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Sort]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Scanned_Code]       varchar(50)  NOT NULL ,
	[Intended_Logical_Destination_ID] integer  NOT NULL ,
	[Actual_Logical_Destination_ID] integer  NOT NULL ,
	[Intended_Physical_Destination_ID] integer  NOT NULL ,
	[Actual_Physical_Destination_ID] integer  NOT NULL ,
	[Dispatch_Status_ID] integer  NOT NULL ,
	[Disposition_Status_ID] integer  NOT NULL ,
	[Wave_ID]            integer  NOT NULL ,
	[Tracking_Code]      varchar(50)  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[Reason_ID]          integer  NOT NULL ,
	[Container_Type_ID]  integer  NULL ,
	[Disposition_Reason_ID] integer  NULL ,
	[Dispatch_Request_Timestamp] Datetime2(7)  NULL ,
	[Dispatch_Assignment_Timestamp] Datetime2(7)  NULL ,
	[Disposition_Bit_Status] bigint  NULL ,
	[Induction_Lane_Code] varchar(50)  NULL ,
	[Terminal_Code]      varchar(50)  NULL ,
	[Dispatch_Success_Count] real  NULL ,
	[Dispatch_Error_Count] real  NULL ,
	[Dispatch_Total_Count] real  NULL ,
	[Disposition_Success_Count] real  NULL ,
	[Disposition_Error_Count] real  NULL ,
	[Disposition_Total_Count] real  NULL ,
	[Recirc_Destination_Ind] integer  NULL ,
	[Reject_Destination_Ind] integer  NULL ,
	[Recirculation_Count] integer  NULL ,
	[QC_Ind]             integer  NULL ,
	[Unit_Sorter_Carrier_Code] varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Sort] ON [OA].[FCT_Sort]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Scanned_Code]        ASC,
	[Intended_Logical_Destination_ID]  ASC,
	[Actual_Logical_Destination_ID]  ASC,
	[Intended_Physical_Destination_ID]  ASC,
	[Actual_Physical_Destination_ID]  ASC,
	[Dispatch_Status_ID]  ASC,
	[Disposition_Status_ID]  ASC,
	[Wave_ID]             ASC,
	[Tracking_Code]       ASC,
	[Operator_ID]         ASC,
	[Reason_ID]           ASC
)
go

CREATE NONCLUSTERED INDEX [XIE2FCT_Sort_RecordTimestamp] ON [OA].[FCT_Sort]
( 
	[Record_Timestamp]    ASC
)
INCLUDE( [Actual_Physical_Destination_ID],[Scanned_Code] )
go

CREATE NONCLUSTERED INDEX [XIE3FCT_Sort_Physical_Destination_Record_Timestamp] ON [OA].[FCT_Sort]
( 
	[Actual_Physical_Destination_ID]  ASC,
	[Record_Timestamp]    ASC
)
INCLUDE( [Hour_Quarter_ID],[Dispatch_Success_Count],[Disposition_Success_Count],[Recirc_Destination_Ind],[Reject_Destination_Ind] )
go

CREATE NONCLUSTERED INDEX [XIE6FCT_Sort_ETL_Batch_ID] ON [OA].[FCT_Sort]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE NONCLUSTERED INDEX [XIE4FCT_Sort_Container_Physical_Code] ON [OA].[FCT_Sort]
( 
	[Scanned_Code]        ASC,
	[Record_Timestamp]    ASC
)
go

CREATE TABLE [OA].[FCT_Storage_Utilization]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Aisle_ID]           integer  NOT NULL ,
	[Module_ID]          integer  NOT NULL ,
	[Empty_Location_Count] integer  NULL ,
	[Total_Location_Count] integer  NULL ,
	[Occupied_Location_Count] integer  NULL ,
	[Partial_Occupied_Location_Count] integer  NULL ,
	[Location_Utilization_Percentage] real  NULL ,
	[Empty_Location_Position_Count] integer  NULL ,
	[Total_Location_Position_Count] integer  NULL ,
	[Location_Position_Utilization_Percentage] real  NULL ,
	[Fault_Location_Position_Count] integer  NULL ,
	[Unavailable_Location_Position_Count] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Storage_Utilization] ON [OA].[FCT_Storage_Utilization]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Aisle_ID]            ASC,
	[Module_ID]           ASC
)
go

CREATE TABLE [OA].[FCT_System_Status]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Module_ID]          integer  NOT NULL ,
	[Status_ID]          integer  NOT NULL ,
	[Message_Sequence]   integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_System_Status] ON [OA].[FCT_System_Status]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Module_ID]           ASC,
	[Status_ID]           ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_FlexSort_System_Mode_ETL_Batch_ID] ON [OA].[FCT_System_Status]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Record_Timestamp] )
go

CREATE TABLE [OA].[FCT_Target]
( 
	[Day_ID]             integer  NOT NULL ,
	[Zone_ID]            integer  NOT NULL ,
	[Technology_ID]      integer  NOT NULL ,
	[Target_Operator_Hourly_Rate_Qty] real  NULL ,
	[Target_Operator_Hourly_Rate_Line_Count] real  NULL ,
	[Target_Operator_Hourly_Rate_Induct_Count] real  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Target] ON [OA].[FCT_Target]
( 
	[Day_ID]              ASC,
	[Zone_ID]             ASC,
	[Technology_ID]       ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Target_ETL_Batch_ID] ON [OA].[FCT_Target]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [Day_ID] )
go

CREATE TABLE [OA].[FCT_Workstation_Work_Type_Status]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Workstation_ID]     integer  NOT NULL ,
	[Work_Type_ID]       integer  NOT NULL ,
	[Workstation_Status_ID] integer  NOT NULL ,
	[Work_Type_Status_ID] integer  NOT NULL ,
	[Operator_ID]        integer  NOT NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Workstation_Work_Type_Status] ON [OA].[FCT_Workstation_Work_Type_Status]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Workstation_ID]      ASC,
	[Work_Type_ID]        ASC,
	[Workstation_Status_ID]  ASC,
	[Work_Type_Status_ID]  ASC,
	[Operator_ID]         ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1FCT_Workstation_Work_Type_Status_ETL_Batch] ON [OA].[FCT_Workstation_Work_Type_Status]
( 
	[ETL_Batch_ID]        ASC
)
INCLUDE( [ETL_Update_Timestamp] )
go

CREATE TABLE [OA].[KPI_Data]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Dimension]          varchar(255)  NOT NULL ,
	[KPI_Type]           varchar(50)  NOT NULL ,
	[KPI_ID]             integer  NOT NULL  IDENTITY ,
	[KPI_Value]          varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKKPI_Data] ON [OA].[KPI_Data]
( 
	[Record_Timestamp]    ASC,
	[Dimension]           ASC,
	[KPI_Type]            ASC
)
go

CREATE TABLE [OA].[Purge_Log]
( 
	[Purge_ID]           integer  NOT NULL  IDENTITY ,
	[Purge_Start_Date]   datetime  NULL ,
	[Table_Name]         varchar(50)  NULL ,
	[Table_Purge_Start_Date] datetime  NULL ,
	[Batch_Purge_Start_Date] datetime  NULL ,
	[Batch_Purge_End_Date] datetime  NULL ,
	[Batch_Purge_Time_Seconds] integer  NULL ,
	[Batch_Purge_Rows_Deleted] integer  NULL ,
	[Table_Purge_End_Date] datetime  NULL ,
	[Table_Purge_Time_Seconds] integer  NULL ,
	[Table_Purge_Total_Rows_Deleted] integer  NULL ,
	[Purge_End_Date]     datetime  NULL ,
	[Purge_Time_Seconds] integer  NULL ,
	[Purge_Total_Rows_Deleted] integer  NULL ,
	[Created_Date_Time]  datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKPurge_Log] ON [OA].[Purge_Log]
( 
	[Purge_ID]            ASC
)
go

CREATE NONCLUSTERED INDEX [XIE1Purge_Log_Purge_Start_Date] ON [OA].[Purge_Log]
( 
	[Purge_Start_Date]    ASC
)
go
