-----------------
-- Data Models --
-----------------

---------------
-- Receiving --
---------------

CREATE TABLE [OA].[DIM_Receiving_Advice]
( 
	[Receiving_Advice_Id] integer  NOT NULL  IDENTITY ,
	[Receiving_Advice_Code] varchar(50)  NULL ,
	[Receiving_Advice_Owner] varchar(50)  NULL ,
	[Receiving_Advice_Type] varchar(50)  NULL ,
	[Supplier]           varchar(50)  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Receiving_Advice] ON [OA].[DIM_Receiving_Advice]
( 
	[Receiving_Advice_Id]  ASC
)
go

-------------------------------------------------
-- IPointMovmentFact - Stored in FCT_Induction --
-------------------------------------------------


CREATE TABLE [OA].[FCT_Induction]
( 
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Record_Timestamp]   datetime  NOT NULL ,
	[Location_ID]        integer  NOT NULL ,
	[Container_Type_ID]  integer  NOT NULL ,
	[Load_Unit_Code]     varchar(50)  NOT NULL ,
	[Height]             real  NULL ,
	[Weight]             real  NULL ,
	[Contour_Details]    varchar(50)  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Induction] ON [OA].[FCT_Induction]
( 
	[Hour_Quarter_ID]     ASC,
	[Record_Timestamp]    ASC,
	[Location_ID]         ASC,
	[Container_Type_ID]   ASC,
	[Load_Unit_Code]      ASC
)
go

------------------------------------------------------------------------------
-- AdviceLineBasedCreateInventoryFact - Stored in FCT_Receiving_Advice_Line --
------------------------------------------------------------------------------

CREATE TABLE [OA].[FCT_Receiving_Advice_Line]
( 
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Receiving_Advice_Id] integer  NOT NULL ,
	[Receiving_Advice_Line_Code] varchar(50)  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Handling_Unit_Code] varchar(50)  NULL ,
	[Container_Type_ID]  integer  NOT NULL ,
	[Receiving_Advice_Qty] integer  NULL ,
	[Originator]         varchar(50)  NULL ,
	[Packaging_Level_Code] varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Receiving_Advice_Line] ON [OA].[FCT_Receiving_Advice_Line]
( 
	[Hour_Quarter_ID]     ASC,
	[Record_Timestamp]    ASC,
	[Receiving_Advice_Id]  ASC,
	[Receiving_Advice_Line_Code]  ASC,
	[Item_ID]             ASC
)
go

----------------------------------------------------------------------------------------
-- AdviceCreatedFact and AdviceFinishtedFact - Stored in FCT_Receiving_Advice_Summary --
----------------------------------------------------------------------------------------
CREATE TABLE [OA].[FCT_Receiving_Advice_Summary]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Receiving_Advice_Id] integer  NOT NULL ,
	[Receiving_Advice_Complete_Date_Time] Datetime2(7)  NULL ,
	[Receiving_Advice_Completed_Pallet_Count] integer  NULL ,
	[Receiving_Advice_Expected_Line_Count] integer  NULL ,
	[Receiving_Advice_Over_Delivered_Line_Count] integer  NULL ,
	[Receiving_Advice_Completed_Line_Count] integer  NULL ,
	[Receiving_Advice_Under_Delivered_Line_Count] integer  NULL ,
	[Receiving_Advice_Over_Delivered_Pallet_Count] integer  NULL ,
	[Receiving_Advice_Exactly_Delivered_Line_Count] integer  NULL ,
	[Receiving_Advice_Under_Delivered_Pallet_Count] integer  NULL ,
	[Receiving_Advice_Exactly_Delivered_Pallet_Count] integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Receiving_Advice_Summary] ON [OA].[FCT_Receiving_Advice_Summary]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Receiving_Advice_Id]  ASC
)
go

------------------------------------------------------
-- TransportTaskConfirmed - Stored in FCT_Transport --
------------------------------------------------------
CREATE TABLE [OA].[FCT_Transport]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Source_Location_ID] integer  NOT NULL ,
	[Destination_Location_ID] integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Container_Type_ID]  integer  NOT NULL ,
	[Movement_Type_ID]   integer  NOT NULL ,
	[Transport_Request_Code] varchar(50)  NOT NULL ,
	[Handling_Unit_Code] varchar(50)  NOT NULL ,
	[Transport_Request_Type] varchar(50)  NULL ,
	[Facility]           varchar(50)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Transport] ON [OA].[FCT_Transport]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Source_Location_ID]  ASC,
	[Destination_Location_ID]  ASC,
	[Item_ID]             ASC,
	[Container_Type_ID]   ASC,
	[Movement_Type_ID]    ASC,
	[Transport_Request_Code]  ASC,
	[Handling_Unit_Code]  ASC
)
go

-------------------------------------------------------------------------------
-- Facility Order, Order Line                                                -- 
-- Stored in Dim_Facility_Order, FCT_Facility_Order, FCT_Facility_Order_Line --
-------------------------------------------------------------------------------

CREATE TABLE [OA].[DIM_Facility_Order]
( 
	[Facility_Order_ID]  integer  NOT NULL  IDENTITY ,
	[Facility_Order_Code] varchar(50)  NULL ,
	[Facility_Order_Name] varchar(100)  NULL ,
	[Facility_Order_Owner] varchar(50)  NULL ,
	[Facility_Order_Type] varchar(50)  NULL ,
	[Facility_Order_Processing_Type] varchar(50)  NULL ,
	[Facility_Order_Category] varchar(50)  NULL ,
	[Facility_Order_Priority] integer  NULL ,
	[Facility_Order_Urgency] integer  NULL ,
	[Facility_Order_Packing_Requirement] varchar(50)  NULL ,
	[Facility_Order_VAS_Requirememnt] varchar(50)  NULL ,
	[Route]              varchar(50)  NULL ,
	[Facility_Order_Destination_Store] varchar(50)  NULL ,
	[Facility_Order_Customer_Receiving_Method] varchar(50)  NULL ,
	[Initiating_Customer_Order_Reference_ID] varchar(50)  NULL ,
	[Requested_Line_Count] integer  NULL ,
	[Requested_Qty]      integer  NULL ,
	[Requested_SKU_Count] integer  NULL ,
	[Facility_Order_Deadline_Date_Time] datetime  NULL ,
	[Facility_Order_Earliest_Staging_Date_Time] datetime  NULL ,
	[Facility_Order_Staging_Deadline_Date_Time] datetime  NULL ,
	[Facility_Order_Earliest_Start_Date_Time] datetime  NULL ,
	[Facility_Order_Start_Deadline_Date_Time] datetime  NULL ,
	[Facility_Order_Picking_Deadline_Date_Time] datetime  NULL ,
	[Facility_Order_Loading_Start_Deadline_Date_Time] datetime  NULL ,
	[Facility_Order_Ship_Deadline_Date_Time] datetime  NULL ,
	[Facility_Order_Delivery_Deadline_Date_Time] datetime  NULL ,
	[Source_System]      varchar(100)  NULL ,
	[Source_System_Key]  varchar(100)  NULL ,
	[Active_Rec_Ind]     integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKDIM_Facility_Order] ON [OA].[DIM_Facility_Order]
( 
	[Facility_Order_ID]   ASC
)
go

CREATE TABLE [OA].[FCT_Facility_Order]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Facility_Order_ID]  integer  NOT NULL ,
	[Subsystem_ID]       integer  NOT NULL ,
	[Facility_Order_Event] varchar(50)  NOT NULL ,
	[Reason_ID]          integer  NOT NULL ,
	[Wave_ID]            integer  NOT NULL ,
	[Event_Detail]       varchar(2000)  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Facility_Order] ON [OA].[FCT_Facility_Order]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Facility_Order_ID]   ASC,
	[Subsystem_ID]        ASC,
	[Facility_Order_Event]  ASC,
	[Reason_ID]           ASC,
	[Wave_ID]             ASC
)
go

CREATE TABLE [OA].[FCT_Facility_Order_Line]
( 
	[Record_Timestamp]   Datetime2(7)  NOT NULL ,
	[Hour_Quarter_ID]    integer  NOT NULL ,
	[Facility_Order_ID]  integer  NOT NULL ,
	[Facility_Order_Line_Code] varchar(50)  NOT NULL ,
	[Requested_Item_ID]  integer  NOT NULL ,
	[Item_ID]            integer  NOT NULL ,
	[Facility_Order_Line_Event] varchar(50)  NOT NULL ,
	[Reason_ID]          integer  NOT NULL ,
	[Batch_Number]       varchar(50)  NOT NULL ,
	[Completed_Qty]      integer  NOT NULL ,
	[Requested_Qty]      integer  NULL ,
	[ETL_Batch_ID]       integer  NULL ,
	[ETL_Update_Timestamp] datetime  NULL 
)
go

CREATE UNIQUE CLUSTERED INDEX [XPKFCT_Facility_Order_Line] ON [OA].[FCT_Facility_Order_Line]
( 
	[Record_Timestamp]    ASC,
	[Hour_Quarter_ID]     ASC,
	[Facility_Order_ID]   ASC,
	[Facility_Order_Line_Code]  ASC,
	[Requested_Item_ID]   ASC,
	[Item_ID]             ASC,
	[Facility_Order_Line_Event]  ASC,
	[Reason_ID]           ASC,
	[Batch_Number]        ASC,
	[Completed_Qty]       ASC
)
go