
INSERT INTO @lTemp_API_XREF_Fields (
 [src_natural_key_column], [dst_natural_key_column], [dst_surrogate_key_column], [dst_dimension_table], [target_surrogate_key_column], [target_table], [sort_key], added_lookup
)
VALUES
-- DIM_Work_Area
('Work_Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.DIM_Work_Area', 0,null),
-- DIM_Container_Train
('Container_Train_Code', 'Container_Train_Code', 'Container_Train_ID', 'OA.DIM_Container_Train', 'Container_Train_ID', 'OA.DIM_Container_Train', 0,null),
-- Fct_Fault
('Item_Category_Code', 'Item_Category_Code', 'Item_Category_ID', 'OA.DIM_Item_Category', 'Item_Category_ID', 'OA.FCT_Fault', 0,null),
('Work_Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.FCT_Fault', 18, null),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Fault', 5,'Subsystem_Category'),
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.Dim_Operator', 'Operator_ID', 'OA.FCT_Fault', 10,null),
('Item_Code', 'Item_Code', 'Item_ID', 'OA.DIM_Item', 'Item_ID', 'OA.FCT_Fault', 15,null),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.Dim_Module', 'Module_ID', 'OA.FCT_Fault', 20,null),
('Location_Code', 'Location_Code', 'Location_ID', 'OA.Dim_Location', 'Location_ID', 'OA.FCT_Fault', 25,null),
('Physical_Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Physical_Device_ID', 'OA.FCT_Fault', 6, NULL),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Fault', 7, NULL),
('Status_Category_Code', 'Status_Category_Code', 'Status_Category_ID', 'OA.DIM_Status_Category', 'Status_Category_ID', 'OA.FCT_Fault', 28,null),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_Fault', 30,null),
--('Status_Locale_Code','Status_Locale_Code','Status_Locale_ID','OA.DIM_Status_Locale','Status_Locale_ID','OA.FCT_Fault',40,NULL),
('Fault_Tag_Reason_Code', 'Reason_Code', 'Reason_ID', 'OA.DIM_Reason', 'Fault_Tag_Reason_ID', 'OA.FCT_Fault', 45, null),
-- FCT_Movement
('Item_Code', 'Item_Code', 'Item_ID', 'OA.DIM_Item', 'Item_ID', 'OA.FCT_Movement', 110, NULL),
('Item_Category_Code', 'Item_Category_Code', 'Item_Category_ID', 'OA.DIM_Item_Category', 'Item_Category_ID', 'OA.FCT_Movement', 100, NULL),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Movement', 120, 'Subsystem_Category'),
('Transport_Reason_Code', 'Reason_Code', 'Reason_ID', 'OA.DIM_Reason', 'Transport_Reason_ID', 'OA.FCT_Movement', 150, NULL),
('Physical_Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Physical_Device_ID', 'OA.FCT_Movement', 140, NULL),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Movement', 130,  NULL),
('Movement_Type_Code', 'Movement_Type_Code', 'Movement_Type_ID', 'OA.DIM_Movement_Type', 'Movement_Type_ID', 'OA.FCT_Movement', 160, NULL),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_Movement', 170, NULL),
('Container_Type_Code', 'Container_Type_Code', 'Container_Type_ID', 'OA.DIM_Container_Type', 'Container_Type_ID', 'OA.FCT_Movement', 180, NULL),
('Work_Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.FCT_Movement', 190, null),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.FCT_Movement', 200, NULL),
('Source_Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Source_Location_ID', 'OA.FCT_Movement', 220, NULL),
('Destination_Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Destination_Location_ID', 'OA.FCT_Movement', 230, NULL),
('Location_Type_Code', 'Location_Type_Code', 'Location_Type_ID', 'OA.DIM_Location_Type', 'Location_Type_ID', 'OA.FCT_Movement', 210, NULL),
--('Level_Code', 'Level_Code', 'Level_ID', 'OA.DIM_Level', Null, 'OA.FCT_Movement',  126, NULL),
--('Put_Wall_Cubby_Code', 'Put_Wall_Cubby_Code', 'Put_Wall_Cubby_ID', 'OA.DIM_Put_Wall_Cubby', NULL, 'OA.FCT_Movement',  127, NULL),
--('Aisle_Code', 'Aisle_Code', 'Aisle_ID', 'OA.DIM_Aisle', NULL, 'OA.FCT_Movement',  128, NULL),
--('Workstation_Code', 'Workstation_Code', 'Workstation_ID', 'OA.DIM_Workstation', NULL, 'OA.FCT_Movement',  129, NULL),
-- FCT_Sort
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Sort', 10,'Subsystem_Category'),
('Sort_Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Sort', 20, NULL),
('Scanner_Code', 'Scanner_Code', 'Scanner_ID', 'OA.DIM_Scanner', 'Scanner_ID', 'OA.FCT_Sort', 30,null),
('Status_Category_Code', 'Status_Category_Code', 'Status_Category_ID', 'OA.DIM_Status_Category','Status_Category_ID', 'OA.FCT_Sort', 35,null),
('Disposition_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Disposition_Status_ID', 'OA.FCT_Sort', 40,null),
('Dispatch_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Dispatch_Status_ID', 'OA.FCT_Sort', 50,null),
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Sort', 60,null),
('Reason_Code', 'Reason_Code', 'Reason_ID', 'OA.DIM_Reason', 'Reason_ID', 'OA.FCT_Sort', 70,null),
('Disposition_Reason_Code', 'Reason_Code', 'Reason_ID', 'OA.DIM_Reason', 'Disposition_Reason_ID', 'OA.FCT_Sort', 75,null),
('Wave_Code', 'Wave_Code', 'Wave_ID', 'OA.DIM_Wave', 'Wave_ID', 'OA.FCT_Sort', 80,null),
('Container_Type_Code', 'Container_Type_Code', 'Container_Type_ID', 'OA.DIM_Container_Type', 'Container_Type_ID', 'OA.FCT_Sort', 85, null),
('Intended_Logical_Destination_Code', 'Logical_Destination_Code', 'Logical_Destination_ID', 'OA.DIM_Logical_Destination', 'Intended_Logical_Destination_ID', 'OA.FCT_Sort', 99,null),
('Actual_Logical_Destination_Code', 'Logical_Destination_Code', 'Logical_Destination_ID', 'OA.DIM_Logical_Destination', 'Actual_Logical_Destination_ID', 'OA.FCT_Sort', 99,null),
('Intended_Physical_Destination_Code', 'Physical_Destination_Code', 'Physical_Destination_ID', 'OA.DIM_Physical_Destination', 'Intended_Physical_Destination_ID', 'OA.FCT_Sort', 99,null),
('Actual_Physical_Destination_Code', 'Physical_Destination_Code', 'Physical_Destination_ID', 'OA.DIM_Physical_Destination', 'Actual_Physical_Destination_ID', 'OA.FCT_Sort', 99,null),
-- Fct_Lane_Status
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Lane_Status', 10,'Subsystem_Category'),
('Sort_Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Lane_Status', 15, NULL),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_Lane_Status', 20,null),
('Scanner_Code', 'Scanner_Code', 'Scanner_ID', 'OA.DIM_Scanner', 'Scanner_ID', 'OA.FCT_Lane_Status', 25,null),
('Logical_Destination_Code', 'Logical_Destination_Code', 'Logical_Destination_ID', 'OA.DIM_Logical_Destination', 'Logical_Destination_ID', 'OA.FCT_Lane_Status', 30,null),
('Physical_Destination_Code', 'Physical_Destination_Code', 'Physical_Destination_ID', 'OA.DIM_Physical_Destination', 'Physical_Destination_ID', 'OA.FCT_Lane_Status', 35,null),
('Reason_Code', 'Reason_Code', 'Reason_ID', 'OA.DIM_Reason', 'Reason_ID', 'OA.FCT_Lane_Status', 40,null),
-- Fct_Merge_Lane
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Merge_Lane', 10,'Subsystem_Category'),
('Sort_Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Merge_Lane', 15, NULL),
('Scanner_Code', 'Scanner_Code', 'Scanner_ID', 'OA.DIM_Scanner', 'Scanner_ID', 'OA.FCT_Merge_Lane', 20,null),
('Merge_Physical_Destination_Code', 'Physical_Destination_Code', 'Physical_Destination_ID', 'OA.DIM_Physical_Destination', 'Merge_Physical_Destination_ID', 'OA.FCT_Merge_Lane', 25,null),
('Reason_Code', 'Reason_Code', 'Reason_ID', 'OA.DIM_Reason', 'Reason_ID', 'OA.FCT_Merge_Lane', 30,null),
-- Fct_Equipment_Log
('Metric_Code', 'Metric_Code', 'Metric_ID', 'OA.DIM_Metric', 'Metric_ID', 'OA.FCT_Equipment_Log', 10,null),
-- Fct_Palletize_Item
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Palletize_Item', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Palletize_Item', 15, NULL),
('Source_Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Source_Location_ID', 'OA.FCT_Palletize_Item', 20, null),
('Item_Category_Code', 'Item_Category_Code', 'Item_Category_ID', 'OA.DIM_Item_Category', 'Item_Category_ID', 'OA.FCT_Palletize_Item', 25,null),
('Item_Code', 'Item_Code', 'Item_ID', 'OA.DIM_Item', 'Item_ID', 'OA.FCT_Palletize_Item', 30, null),
('Wave_Code', 'Wave_Code', 'Wave_ID', 'OA.DIM_Wave', 'Wave_ID', 'OA.FCT_Palletize_Item', 35, null),
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Palletize_Item', 40, null),
-- Fct_Pallet_Summary
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Pallet_Summary', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Pallet_Summary', 15,  NULL),
('Container_Type_Code', 'Container_Type_Code', 'Container_Type_ID', 'OA.DIM_Container_Type', 'Container_Type_ID', 'OA.FCT_Pallet_Summary', 20, null),
('Wave_Code', 'Wave_Code', 'Wave_ID', 'OA.DIM_Wave', 'Wave_ID', 'OA.FCT_Pallet_Summary', 30, null),
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Pallet_Summary', 35, null),
-- Fct_Dimension_Check
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Dimension_Check', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Dimension_Check', 15, NULL),
('Item_Category_Code', 'Item_Category_Code', 'Item_Category_ID', 'OA.DIM_Item_Category', 'Item_Category_ID', 'OA.FCT_Dimension_Check', 20,null),
('Item_Code', 'Item_Code', 'Item_ID', 'OA.DIM_Item', 'Item_ID', 'OA.FCT_Dimension_Check', 25, null),
('Wave_Code', 'Wave_Code', 'Wave_ID', 'OA.DIM_Wave', 'Wave_ID', 'OA.FCT_Dimension_Check', 30, null),
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Dimension_Check', 35, null),
('Buffer_Code', 'Buffer_Code', 'Buffer_ID', 'OA.DIM_Buffer', 'Buffer_ID', 'OA.FCT_Dimension_Check', 40, null),
('Aisle_Code', 'Aisle_Code', 'Aisle_ID', 'OA.DIM_Aisle', 'Aisle_ID', 'OA.FCT_Dimension_Check', 45, null),
-- FCT_Equipment
('OPC_Tag_Code', 'OPC_Tag_Code', 'OPC_Tag_ID', 'OA.DIM_OPC_Tag', 'OPC_Tag_ID', 'OA.FCT_Equipment', 10, null),
('Monitor_Device_Code', 'Monitor_Device_Code', 'Monitor_Device_ID', 'OA.DIM_Monitor_Device', 'Monitor_Device_ID', 'OA.FCT_Equipment', 20, null),
('Measurement_Code', 'Measurement_Code', 'Measurement_ID', 'OA.DIM_Measurement', 'Measurement_ID', 'OA.FCT_Equipment', 30, 'Unit_Of_Measure'),
-- FCT_Pick_Activity
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Pick_Activity', 10, null),
('Workstation_Code', 'Workstation_Code', 'Workstation_ID', 'OA.DIM_Workstation', 'Workstation_ID', 'OA.FCT_Pick_Activity', 20, null),
('Zone_Code', 'Zone_Code', 'Zone_ID', 'OA.DIM_Zone', 'Zone_ID', 'OA.FCT_Pick_Activity', 30, null),
('Technology_Code', 'Technology_Code', 'Technology_ID', 'OA.DIM_Technology', 'Technology_ID', 'OA.FCT_Pick_Activity', 40, 'Technology_Vendor'),
('Work_Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.FCT_Pick_Activity', 45, null),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.FCT_Pick_Activity', 50, null),
('Work_Type_Code', 'Work_Type_Code', 'Work_Type_ID', 'OA.DIM_Work_Type', 'Work_Type_ID', 'OA.FCT_Pick_Activity', 60, 'Work_Type_Style'),
('Container_Type_Code', 'Container_Type_Code', 'Container_Type_ID', 'OA.DIM_Container_Type', 'Container_Type_ID', 'OA.FCT_Pick_Activity', 70, null),
-- FCT_Equipment_Efficiency
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Equipment_Efficiency', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Equipment_Efficiency', 15, NULL),
('Data_Point_Code', 'Data_Point_Code', 'Data_Point_ID', 'OA.DIM_Data_Point', 'Data_Point_ID', 'OA.FCT_Equipment_Efficiency', 20 ,null),
-- FCT_Equipment_Measuring_Point
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Equipment_Measuring_Point', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Equipment_Measuring_Point', 15, NULL),
('Data_Point_Code', 'Data_Point_Code', 'Data_Point_ID', 'OA.DIM_Data_Point', 'Data_Point_ID', 'OA.FCT_Equipment_Measuring_Point', 20 ,null),
('Measurement_Code', 'Measurement_Code', 'Measurement_ID', 'OA.DIM_Measurement', 'Measurement_ID', 'OA.FCT_Equipment_Measuring_Point', 30 ,null),
-- FCT_Equipment_Comm
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Equipment_Comm', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Equipment_Comm', 15, NULL),
('Measurement_Code', 'Measurement_Code', 'Measurement_ID', 'OA.DIM_Measurement', 'Measurement_ID', 'OA.FCT_Equipment_Comm', 30 ,null),
-- FCT_Equipment_Fault
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Equipment_Fault', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Equipment_Fault', 15, NULL),
('Fault_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Fault_Status_ID', 'OA.FCT_Equipment_Fault', 40,null),
('Device_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Device_Status_ID', 'OA.FCT_Equipment_Fault', 50,null),
-- FCT_Equipment_PLC
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Equipment_PLC', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Equipment_PLC', 15, NULL),
-- FCT_Overhead_Order_Complete
('Pack_Station_Code', 'Workstation_Code', 'Workstation_ID', 'OA.DIM_Workstation', 'Pack_Station_ID', 'OA.FCT_Overhead_Order_Complete', 10, null),
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Overhead_Order_Complete', 20, null),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Overhead_Order_Complete', 30, 'Subsystem_Category'),
('Status_Category_Code', 'Status_Category_Code', 'Status_Category_ID', 'OA.DIM_Status_Category', 'Status_Category_ID', 'OA.FCT_Overhead_Order_Complete', 40,null),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_Overhead_Order_Complete', 50, NULL),
('Overhead_Order_Code', 'Overhead_Order_Code', 'Overhead_Order_ID', 'OA.DIM_Overhead_Order', 'Overhead_Order_ID', 'OA.FCT_Overhead_Order_Complete', 60, NULL),
-- FCT_Overhead_Induction
('Status_Category_Code', 'Status_Category_Code', 'Status_Category_ID', 'OA.DIM_Status_Category', 'Status_Category_ID', 'OA.FCT_Overhead_Induction', 0,null),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Overhead_Induction', 10,'Subsystem_Category'),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_Overhead_Induction', 15,null),
('Workstation_Code', 'Workstation_Code', 'Workstation_ID', 'OA.DIM_Workstation', 'Workstation_ID', 'OA.FCT_Overhead_Induction', 30, null),
('Item_Code', 'Item_Code', 'Item_ID', 'OA.DIM_Item', 'Item_ID', 'OA.FCT_Overhead_Induction', 40, null),
('Overhead_Station_Induct_Mode_Code', 'Overhead_Station_Induct_Mode_Code', 'Overhead_Station_Induct_Mode_ID', 'OA.DIM_Overhead_Station_Induct_Mode', 'Overhead_Station_Induct_Mode_ID', 'OA.FCT_Overhead_Induction', 50, null),
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Overhead_Induction', 60, null), 
-- FCT_Overhead_BufferLoop_Status
('Status_Category_Code', 'Status_Category_Code', 'Status_Category_ID', 'OA.DIM_Status_Category', 'Status_Category_ID', 'OA.FCT_Overhead_BufferLoop_Status', 0,null),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Overhead_BufferLoop_Status', 10,'Subsystem_Category'),
('Lock_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Lock_Status_ID', 'OA.FCT_Overhead_BufferLoop_Status', 20, null),
('Fault_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Fault_Status_ID', 'OA.FCT_Overhead_BufferLoop_Status', 25, null),
('Overhead_Loop_Code', 'Overhead_Loop_Code', 'Overhead_Loop_ID', 'OA.DIM_Overhead_Loop', 'Overhead_BufferLoop_ID', 'OA.FCT_Overhead_BufferLoop_Status', 30, NULL),
-- FCT_Overhead_BufferLoop_Carrier
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Overhead_BufferLoop_Carrier', 10,'Subsystem_Category'),
('Overhead_Carrier_Exit_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Overhead_Carrier_Exit_Status_ID', 'OA.FCT_Overhead_BufferLoop_Carrier', 15, null),
('Item_Code', 'Item_Code', 'Item_ID', 'OA.DIM_Item', 'Item_ID', 'OA.FCT_Overhead_BufferLoop_Carrier', 30, null),
('Overhead_Loop_Code', 'Overhead_Loop_Code', 'Overhead_Loop_ID', 'OA.DIM_Overhead_Loop', 'Overhead_Buffer_Loop_ID', 'OA.FCT_Overhead_BufferLoop_Carrier', 40, NULL),
('Overhead_Order_Code', 'Overhead_Order_Code', 'Overhead_Order_ID', 'OA.DIM_Overhead_Order', 'Overhead_Order_ID', 'OA.FCT_Overhead_BufferLoop_Carrier', 50, NULL),
-- FCT_Overhead_Sort_Fault
('Status_Category_Code', 'Status_Category_Code', 'Status_Category_ID', 'OA.DIM_Status_Category', 'Status_Category_ID', 'OA.FCT_Overhead_Sort_Fault', 0,null),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Overhead_Sort_Fault', 10,'Subsystem_Category'),
('Fault_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Fault_Status_ID', 'OA.FCT_Overhead_Sort_Fault', 20, null),
('Item_Code', 'Item_Code', 'Item_ID', 'OA.DIM_Item', 'Item_ID', 'OA.FCT_Overhead_Sort_Fault', 30, null),
('Overhead_Intended_Destination_Code'  , 'Overhead_Destination_Code'  , 'Overhead_Destination_ID'  , 'OA.DIM_Overhead_Destination'  , 'Overhead_Sort_Intended_Destination_ID'  , 'OA.FCT_Overhead_Sort_Fault', 40, NULL),
('Overhead_Actual_Destination_Code'  , 'Overhead_Destination_Code'  , 'Overhead_Destination_ID'  , 'OA.DIM_Overhead_Destination'  , 'Overhead_Sort_Actual_Destination_ID'  , 'OA.FCT_Overhead_Sort_Fault', 50, NULL),
('Overhead_Order_Code', 'Overhead_Order_Code', 'Overhead_Order_ID', 'OA.DIM_Overhead_Order', 'Overhead_Order_ID', 'OA.FCT_Overhead_Sort_Fault', 60, NULL),
-- FCT_Overhead_Scanner_Readings
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Overhead_Scanner_Readings', 10,'Subsystem_Category'),
('Location_Type_Code', 'Location_Type_Code', 'Location_Type_ID', 'OA.DIM_Location_Type', 'Location_Type_ID', 'OA.FCT_Overhead_Scanner_Readings', 15, null),
('Scanner_Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Location_ID', 'OA.FCT_Overhead_Scanner_Readings', 20, null),
('Scanner_Code', 'Scanner_Code', 'Scanner_ID', 'OA.DIM_Scanner', 'Scanner_ID', 'OA.FCT_Overhead_Scanner_Readings', 25, null),
-- FCT_Overhead_Sort_Complete
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Overhead_Sort_Complete', 10,'Subsystem_Category'),
('Overhead_Loop_Code', 'Overhead_Loop_Code', 'Overhead_Loop_ID', 'OA.DIM_Overhead_Loop', 'Overhead_Sort_Loop_ID', 'OA.FCT_Overhead_Sort_Complete', 40, NULL),
-- FCT_Overhead_Empty_BufferLoop
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Overhead_Empty_BufferLoop', 10,'Subsystem_Category'),
('Overhead_Loop_Code', 'Overhead_Loop_Code', 'Overhead_Loop_ID', 'OA.DIM_Overhead_Loop', 'Overhead_Loop_ID', 'OA.FCT_Overhead_Empty_BufferLoop', 20, NULL),
-- FCT_Overhead_Carrier
('Status_Category_Code', 'Status_Category_Code', 'Status_Category_ID', 'OA.DIM_Status_Category', 'Status_Category_ID', 'OA.FCT_Overhead_Carrier', 0,null),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Overhead_Carrier', 10,'Subsystem_Category'),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_Overhead_Carrier', 15,null),
('Work_Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.FCT_Overhead_Carrier', 20, null),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.FCT_Overhead_Carrier', 30, null),
('Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Location_ID', 'OA.FCT_Overhead_Carrier', 40,null),
('Reason_Code', 'Reason_Code', 'Reason_ID', 'OA.DIM_Reason', 'Reason_ID', 'OA.FCT_Overhead_Carrier', 50,null),
-- FCT_Overhead_Order_Buffer_Run
('Overhead_Loop_Code', 'Overhead_Loop_Code', 'Overhead_Loop_ID', 'OA.DIM_Overhead_Loop', 'Overhead_Buffer_Loop_ID', 'OA.FCT_Overhead_Order_Buffer_Run', 10, NULL),
-- FCT_Pick_Order
('Work_Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.FCT_Pick_Order', 10, null),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module','Module_ID', 'OA.FCT_Pick_Order', 30, null),
('Workstation_Code', 'Workstation_Code', 'Workstation_ID', 'OA.DIM_Workstation','Workstation_ID', 'OA.FCT_Pick_Order', 35, null),
('Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Location_ID', 'OA.FCT_Pick_Order', 40,null),
('Customer_Order_Code', 'Customer_Order_Code', 'Customer_Order_ID', 'OA.DIM_Customer_Order', 'Customer_Order_ID', 'OA.FCT_Pick_Order', 50, null),
('Pick_Order_Code', 'Pick_Order_Code', 'Pick_Order_ID', 'OA.DIM_Pick_Order', 'Pick_Order_ID', 'OA.FCT_Pick_Order', 60, null),
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Pick_Order', 70, null), 
('Zone_Code', 'Zone_Code', 'Zone_ID', 'OA.DIM_Zone', 'Zone_ID', 'OA.FCT_Pick_Order', 80, null),
('Wave_Code', 'Wave_Code', 'Wave_ID', 'OA.DIM_Wave', 'Wave_ID', 'OA.FCT_Pick_Order', 90,null),
('Reason_Code', 'Reason_Code', 'Reason_ID', 'OA.DIM_Reason', 'Reason_ID', 'OA.FCT_Pick_Order', 100, null),
-- FCT_Customer_Order
('Customer_Order_Code', 'Customer_Order_Code', 'Customer_Order_ID', 'OA.DIM_Customer_Order', 'Customer_Order_ID', 'OA.FCT_Customer_Order', 20, null),
-- FCT_Storage_Utilization
('Work_Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.FCT_Storage_Utilization', 20, null),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.FCT_Storage_Utilization', 30, null),
('Aisle_Code', 'Aisle_Code', 'Aisle_ID', 'OA.DIM_Aisle', 'Aisle_ID', 'OA.FCT_Storage_Utilization', 40, null), 
-- FCT_Inventory
('Work_Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.FCT_Inventory', 10, null),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.FCT_Inventory', 20, null),
('Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Location_ID', 'OA.FCT_Inventory', 30, null),
('Item_Category_Code', 'Item_Category_Code', 'Item_Category_ID', 'OA.DIM_Item_Category', 'Item_Category_ID', 'OA.FCT_Inventory', 40,null),
('Item_Code', 'Item_Code', 'Item_ID', 'OA.DIM_Item', 'Item_ID', 'OA.FCT_Inventory', 50, null),
-- FCT_Workstation_Work_Type_Status
('Workstation_Code', 'Workstation_Code', 'Workstation_ID', 'OA.DIM_Workstation', 'Workstation_ID', 'OA.FCT_Workstation_Work_Type_Status', 10, null),
('Work_Type_Code', 'Work_Type_Code', 'Work_Type_ID', 'OA.DIM_Work_Type', 'Work_Type_ID', 'OA.FCT_Workstation_Work_Type_Status', 20, null),
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Workstation_Work_Type_Status', 30, null),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Workstation_Work_Type_Status', 40,'Subsystem_Category'),
('Workstation_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Workstation_Status_ID', 'OA.FCT_Workstation_Work_Type_Status', 50,null),
('Work_Type_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Work_Type_Status_ID', 'OA.FCT_Workstation_Work_Type_Status', 60,null),
-- FCT_Robot_Activity
('Robot_Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Robot_Device_ID', 'OA.FCT_Robot_Activity', 30, NULL),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_Robot_Activity', 20, NULL),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Robot_Activity', 10, 'Subsystem_Category'),
('Status_Category_Code', 'Status_Category_Code', 'Status_Category_ID', 'OA.DIM_Status_Category', 'Status_Category_ID', 'OA.FCT_Robot_Activity', 5, NULL),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.FCT_Robot_Activity', 40, NULL),
('Work_Area_Code','Work_Area_Code','Work_Area_ID','OA.DIM_Work_Area','Work_Area_ID','OA.FCT_Robot_Activity',35,NULL),
-- FCT_Fault_Detail
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Fault_Status_ID', 'OA.FCT_Fault_Detail', 30, NULL),
('Workstation_Code', 'Workstation_Code', 'Workstation_ID', 'OA.DIM_Workstation', 'Workstation_ID', 'OA.FCT_Fault_Detail', 40, NULL),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Fault_Detail', 20, NULL),
('Status_Category_Code', 'Status_Category_Code', 'Status_Category_ID', 'OA.DIM_Status_Category','Status_Category_ID', 'OA.FCT_Fault_Detail', 10, NULL),
('Work_Area_Code','Work_Area_Code','Work_Area_ID','OA.DIM_Work_Area','Work_Area_ID','OA.FCT_Fault_Detail',45,NULL),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.FCT_Fault_Detail', 50, NULL),
-- FCT_FlexSort_Fault_Detail
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_FlexSort_Fault_Detail', 10, 'Subsystem_Category'),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_FlexSort_Fault_Detail', 20, NULL),
('Status_Locale_Code', 'Status_Locale_Code', 'Status_Locale_ID', 'OA.DIM_Status_Locale', 'Status_Locale_ID', 'OA.FCT_FlexSort_Fault_Detail', 30, NULL),
('Fault_Type_Code', 'Fault_Type_Code', 'Fault_Type_ID', 'OA.DIM_Fault_Type', 'Fault_Type_ID', 'OA.FCT_FlexSort_Fault_Detail', 40, NULL),
('FlexSort_Component_Code', 'FlexSort_Component_Code', 'FlexSort_Component_ID', 'OA.DIM_FlexSort_Component', 'FlexSort_Component_ID', 'OA.FCT_FlexSort_Fault_Detail', 50, NULL),
-- FCT_FlexSort_System_Mode
('FlexSort_Component_Code', 'FlexSort_Component_Code', 'FlexSort_Component_ID', 'OA.DIM_FlexSort_Component', 'FlexSort_Component_ID', 'OA.FCT_FlexSort_System_Mode', 50, NULL),
-- FCT_Shuttle_Statistics
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Shuttle_Statistics', 10, 'Subsystem_Category'),
('Physical_Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Physical_Device_ID', 'OA.FCT_Shuttle_Statistics', 20, NULL),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Shuttle_Statistics', 25, NULL),
-- FCT_Device_Statistics_Fault_Detail
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Device_Statistics_Fault_Detail', 10, 'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Device_Statistics_Fault_Detail', 15, NULL),
('Fault_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Fault_ID', 'OA.FCT_Device_Statistics_Fault_Detail', 20, NULL),
-- FCT_Lift Statistics
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Lift_Statistics', 10, NULL),
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Lift_Statistics', 0, 'Subsystem_Category'),
-- FCT_System_Status
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_System_Status', 10, 'Subsystem_Category'),
('Operation_Status', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_System_Status', 20, NULL),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.FCT_System_Status', 50, NULL),
('Work_Area_Code','Work_Area_Code','Work_Area_ID','OA.DIM_Work_Area','Work_Area_ID','OA.FCT_System_Status',40,NULL),
--FCT_Container_Mode
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Container_Mode', 10, 'Subsystem_Category'),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_Container_Mode', 20, NULL),
('Work_Area_Code','Work_Area_Code','Work_Area_ID','OA.DIM_Work_Area','Work_Area_ID','OA.FCT_Container_Mode',40,NULL),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.FCT_Container_Mode', 50, NULL),
--FCT_Order_Line
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Order_Line', 0, null),
('Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Location_ID', 'OA.FCT_Order_Line', 40, null),
('Item_Code', 'Item_Code', 'Item_ID', 'OA.DIM_Item', 'Item_ID', 'OA.FCT_Order_Line', 0, null),
('Work_Type_Code', 'Work_Type_Code', 'Work_Type_ID', 'OA.DIM_Work_Type', 'Work_Type_ID', 'OA.FCT_Order_Line', 0, 'Work_Type_Style'),
('Reason_Code', 'Reason_Code', 'Reason_ID', 'OA.DIM_Reason', 'Reason_ID', 'OA.FCT_Order_Line', 0, null),
('Container_Type_Code', 'Container_Type_Code', 'Container_Type_ID', 'OA.DIM_Container_Type', 'Container_Type_ID', 'OA.FCT_Order_Line', 0, NULL),
('Zone_Code', 'Zone_Code', 'Zone_ID', 'OA.DIM_Zone', 'Zone_ID', 'OA.FCT_Order_Line', 0,null),
('Container_Train_Code', 'Container_Train_Code', 'Container_Train_ID', 'OA.DIM_Container_Train', 'Container_Train_ID', 'OA.FCT_Order_Line', 0,null),
('Cart_Instance_Code', 'Cart_Instance_Code', 'Cart_Instance_ID', 'OA.DIM_Cart_Instance', 'Cart_Instance_ID', 'OA.FCT_Order_Line', 0,null),
('Process_Type_Code', 'Process_Type_Code', 'Process_Type_ID', 'OA.DIM_Process_Type', 'Process_Type_ID', 'OA.FCT_Order_Line', 0,null),
('Technology_Code', 'Technology_Code', 'Technology_ID', 'OA.DIM_Technology', 'Technology_ID', 'OA.FCT_Order_Line', 20, 'Technology_Vendor'),
('Work_Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.FCT_Order_Line', 20, null),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.FCT_Order_Line', 30, null),
('Workstation_Code', 'Workstation_Code', 'Workstation_ID', 'OA.DIM_Workstation', 'Workstation_ID', 'OA.FCT_Order_Line', 35, null),
('Pick_To_Container_Type_Code', 'Container_Type_Code', 'Container_Type_ID', 'OA.DIM_Container_Type', 'Pick_To_Container_Type_ID', 'OA.FCT_Order_Line', 0, NULL),
('Pick_Order_Code', 'Pick_Order_Code', 'Pick_Order_ID', 'OA.Dim_Pick_Order', 'Pick_Order_ID', 'OA.FCT_Order_Line', 50,null),
('Customer_Order_Code', 'Customer_Order_Code', 'Customer_Order_ID', 'OA.DIM_Customer_Order', 'Customer_Order_ID', 'OA.FCT_Order_Line', 45, null),
-- FCT_Pick_Order_Complete
('Pick_Order_Code', 'Pick_Order_Code', 'Pick_Order_ID', 'OA.Dim_Pick_Order', 'Pick_Order_ID', 'OA.FCT_Pick_Order_Complete', 20,null),
('Customer_Order_Code', 'Customer_Order_Code', 'Customer_Order_ID', 'OA.Dim_Customer_Order', 'Customer_Order_ID', 'OA.FCT_Pick_Order_Complete', 10,null),
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.FCT_Pick_Order_Complete', 0,null),
('Wave_Code', 'Wave_Code', 'Wave_ID', 'OA.DIM_Wave', 'Wave_ID', 'OA.FCT_Pick_Order_Complete', 0,null),
('Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Location_ID', 'OA.FCT_Pick_Order_Complete', 0,null),
('Zone_Code', 'Zone_Code', 'Zone_ID', 'OA.DIM_Zone', 'Zone_ID', 'OA.FCT_Pick_Order_Complete', 0,null),
('Order_Assigned_Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Order_Assigned_Location_ID', 'OA.FCT_Pick_Order_Complete', 10,null),
('Order_Put_Wall_Pack_Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Order_Put_Wall_Pack_Operator_ID', 'OA.FCT_Pick_Order_Complete', 20,null),
('Order_Put_Wall_Pack_Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Order_Put_Wall_Pack_Location_ID', 'OA.FCT_Pick_Order_Complete', 25,null),
('Order_Put_Wall_Pack_Zone_Code', 'Zone_Code', 'Zone_ID', 'OA.DIM_Zone', 'Order_Put_Wall_Pack_Zone_ID', 'OA.FCT_Pick_Order_Complete', 30,null),
-- Fct_Equipment_Status
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Equipment_Status', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.FCT_Equipment_Status', 15, NULL),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.FCT_Equipment_Status', 20,null),
('Reason_Code', 'Reason_Code', 'Reason_ID', 'OA.DIM_Reason', 'Reason_ID', 'OA.FCT_Equipment_Status', 30, null),
-- DIM Item
('Item_Category_Code', 'Item_Category_Code', 'Item_Category_ID', 'OA.DIM_Item_Category', 'Item_Category_ID', 'OA.DIM_Item', 10, NULL),
('Item_Code', 'Item_Code', 'Item_ID', 'OA.DIM_Item', 'Item_ID', 'OA.DIM_Item', '20', NULL),
-- DIM_Operator
('Operator_Code', 'Operator_Code', 'Operator_ID', 'OA.DIM_Operator', 'Operator_ID', 'OA.DIM_Operator', 20, NULL),
-- DIM_Wave
('Wave_Code', 'Wave_Code', 'Wave_ID', 'OA.DIM_Wave', 'Wave_ID', 'OA.DIM_Wave', 20, NULL),
-- DIM_Workstation
('Workstation_Code', 'Workstation_Code', 'Workstation_ID', 'OA.DIM_Workstation', 'Workstation_ID', 'OA.DIM_Workstation', 20, NULL),
-- DIM_Status_Locale
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.DIM_Status_Locale', 10,'Subsystem_Category'),
('Status_Category_Code', 'Status_Category_Code', 'Status_Category_ID', 'OA.DIM_Status_Category', 'Status_Category_ID', 'OA.DIM_Status_Locale', 20, null),
('Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Status_ID', 'OA.DIM_Status_Locale', 30, NULL),
('Status_Locale_Code', 'Status_Locale_Code', 'Status_Locale_ID', 'OA.DIM_Status_Locale', 'Status_Locale_ID', 'OA.DIM_Status_Locale', 40, NULL),
-- DIM_Module
('Work_Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.DIM_Module', 10, null),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.DIM_Module', 20, NULL),
-- DIM_Zone
('Customer_Code', 'Customer_Code', 'Customer_ID', 'OA.DIM_Customer', 'Customer_ID', 'OA.DIM_Zone', 10, null),
('Facility_Code', 'Facility_Code', 'Facility_ID', 'OA.DIM_Facility', 'Facility_ID', 'OA.DIM_Zone', 20, null),
('Logical_Destination_Code', 'Logical_Destination_Code', 'Logical_Destination_ID', 'OA.DIM_Logical_Destination', 'Logical_Destination_ID', 'OA.DIM_Zone', 20, NULL),
('Zone_Code', 'Zone_Code', 'Zone_ID', 'OA.DIM_Zone', 'Zone_ID', 'OA.DIM_Zone', 30, NULL),
-- DIM_Scanner
('Subsystem_Code','Subsystem_Code','Subsystem_ID','OA.DIM_Subsystem','Subsystem_ID','OA.DIM_Scanner',10,'Subsystem_Category'),
('Sort_Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.DIM_Scanner', 20, NULL),
('Scanner_Code', 'Scanner_Code', 'Scanner_ID', 'OA.DIM_Scanner', 'Scanner_ID', 'OA.DIM_Scanner', 30,null),
-- DIM_Work_Type
('Work_Type_Code', 'Work_Type_Code', 'Work_Type_ID', 'OA.DIM_Work_Type', 'Work_Type_ID', 'OA.DIM_Work_Type', 30, NULL),
-- DIM_Technology
('Technology_Code', 'Technology_Code', 'Technology_ID', 'OA.DIM_Technology', 'Technology_ID', 'OA.DIM_Technology', 30,null),
-- DIM_Device
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.DIM_Device', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.DIM_Device', 20, NULL),
-- DIM_Put_Wall
('Put_Wall_Code', 'Put_Wall_Code', 'Put_Wall_ID', 'OA.DIM_Put_Wall', 'Put_Wall_ID', 'OA.DIM_Put_Wall', 0,null),
-- DIM_Logical_Destination
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.DIM_Logical_Destination', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.DIM_Logical_Destination', 15, NULL),
('Scanner_Code', 'Scanner_Code', 'Scanner_ID', 'OA.DIM_Scanner', 'Scanner_ID', 'OA.DIM_Logical_Destination', 20,null),
('Logical_Destination_Code', 'Logical_Destination_Code', 'Logical_Destination_ID', 'OA.DIM_Logical_Destination', 'Logical_Destination_ID', 'OA.DIM_Logical_Destination', 30,null),
-- DIM_Physical_Destination
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.DIM_Physical_Destination', 10,'Subsystem_Category'),
('Device_Code', 'Device_Code', 'Device_ID', 'OA.DIM_Device', 'Device_ID', 'OA.DIM_Physical_Destination', 15, NULL),
('Scanner_Code', 'Scanner_Code', 'Scanner_ID', 'OA.DIM_Scanner', 'Scanner_ID', 'OA.DIM_Physical_Destination', 20,null),
('Physical_Destination_Code', 'Physical_Destination_Code', 'Physical_Destination_ID', 'OA.DIM_Physical_Destination', 'Physical_Destination_ID', 'OA.DIM_Physical_Destination', 30, null),
-- DIM_Location
('Location_Type_Code', 'Location_Type_Code', 'Location_Type_ID', 'OA.DIM_Location_Type', 'Location_Type_ID', 'OA.DIM_Location', 10, null),
('Put_Wall_Code', 'Put_Wall_Code', 'Put_Wall_ID', 'OA.DIM_Put_Wall', 'Put_Wall_ID', 'OA.DIM_Location', 20, null),
('Put_Wall_Cubby_Code', 'Put_Wall_Cubby_Code', 'Put_Wall_Cubby_ID', 'OA.DIM_Put_Wall_Cubby', 'Put_Wall_Cubby_ID', 'OA.DIM_Location', 30, null),
('Area_Code', 'Work_Area_Code', 'Work_Area_ID', 'OA.DIM_Work_Area', 'Work_Area_ID', 'OA.DIM_Location', 40, null),
('Module_Code', 'Module_Code', 'Module_ID', 'OA.DIM_Module', 'Module_ID', 'OA.DIM_Location', 50, null),
('Level_Code', 'Level_Code', 'Level_ID', 'OA.DIM_Level', 'Level_ID', 'OA.DIM_Location', 60, null),
('Aisle_Code', 'Aisle_Code', 'Aisle_ID', 'OA.DIM_Aisle', 'AIsle_ID', 'OA.DIM_Location', 70, null),
('Workstation_Code', 'Workstation_Code', 'Workstation_ID', 'OA.DIM_Workstation', 'Workstation_ID', 'OA.DIM_Location', 80, null),
('Location_Code', 'Location_Code', 'Location_ID', 'OA.DIM_Location', 'Location_ID', 'OA.DIM_Location', 90, null), 
-- FCT_Label_Print_Apply
('Subsystem_Code', 'Subsystem_Code', 'Subsystem_ID', 'OA.DIM_Subsystem', 'Subsystem_ID', 'OA.FCT_Label_Print_Apply', 10,'Subsystem_Category'),
('Induct_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Induct_Status_ID', 'OA.FCT_Label_Print_Apply', 20,null),
('Response_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Response_Status_ID', 'OA.FCT_Label_Print_Apply', 30,null),
('Verification_Status_Code', 'Status_Code', 'Status_ID', 'OA.DIM_Status', 'Verification_Status_ID', 'OA.FCT_Label_Print_Apply', 40,null)


DECLARE @table_node_fk TABLE (parent_table varchar(50),target_tb varchar(50), child_table varchar(50))

INSERT INTO @table_node_fk  VALUES  
-- Fct_Fault
('OA.DIM_Device','OA.FCT_Fault','OA.DIM_Subsystem'),
('OA.DIM_Item','OA.FCT_Fault','OA.DIM_Item_Category'),
('OA.DIM_Status','OA.FCT_Fault','OA.DIM_Subsystem'),
('OA.DIM_Status','OA.FCT_Fault','OA.DIM_Status_Category'),
--('OA.DIM_Status_Locale','OA.FCT_Fault','OA.DIM_Status'),
('OA.DIM_Location','OA.FCT_Fault','OA.DIM_Module'),
('OA.DIM_Module','OA.FCT_Fault','OA.DIM_Work_Area'),
-- Fct_Sort
('OA.DIM_Device','OA.FCT_Sort','OA.DIM_Subsystem'),
('OA.DIM_Logical_Destination','OA.FCT_Sort','OA.DIM_Scanner'),
('OA.DIM_Physical_Destination','OA.FCT_Sort','OA.DIM_Scanner'),
('OA.DIM_Scanner','OA.FCT_Sort','OA.DIM_Device'),
('OA.DIM_Status','OA.FCT_Sort','OA.DIM_Status_Category'),
('OA.DIM_Status','OA.FCT_Sort','OA.DIM_Subsystem'),
-- FCT_Lane_Status
('OA.DIM_Device','OA.FCT_Lane_Status','OA.DIM_Subsystem'),
('OA.DIM_Scanner','OA.FCT_Lane_Status','OA.DIM_Device'),
('OA.DIM_Logical_Destination','OA.FCT_Lane_Status','OA.DIM_Scanner'),
('OA.DIM_Physical_Destination','OA.FCT_Lane_Status','OA.DIM_Scanner'),
('OA.DIM_Status','OA.FCT_Lane_Status','OA.DIM_Subsystem'),
-- FCT_Merge_Lane
('OA.DIM_Device','OA.FCT_Merge_Lane','OA.DIM_Subsystem'),
('OA.DIM_Scanner','OA.FCT_Merge_Lane','OA.DIM_Device'),
('OA.DIM_Physical_Destination','OA.FCT_Merge_Lane','OA.DIM_Scanner'),
-- Fct_Palletize_Item
('OA.DIM_Device','OA.Fct_Palletize_Item','OA.DIM_Subsystem'),
('OA.DIM_Item','OA.Fct_Palletize_Item','OA.DIM_Item_Category'),
-- Fct_Pallet_Summary
('OA.DIM_Device','OA.Fct_Pallet_Summary','OA.DIM_Subsystem'),
-- Fct_Dimension_Check
('OA.DIM_Device','OA.Fct_Dimension_Check','OA.DIM_Subsystem'),
('OA.DIM_Item','OA.Fct_Dimension_Check','OA.DIM_Item_Category'),
('OA.DIM_Aisle','OA.Fct_Dimension_Check','OA.DIM_Buffer'),
-- Fct_Movement
('OA.DIM_Item','OA.FCT_Movement','OA.DIM_Item_Category'),
('OA.DIM_Device', 'OA.FCT_Movement', 'OA.DIM_Subsystem'),
('OA.DIM_Status', 'OA.FCT_Movement', 'OA.DIM_Subsystem'),
('OA.DIM_Location','OA.FCT_Movement','OA.DIM_Module'),
('OA.DIM_Module','OA.FCT_Movement','OA.DIM_Work_Area'),
--('OA.DIM_Location','OA.FCT_Movement','OA.DIM_Location_Type'),
--('OA.DIM_Location','OA.FCT_Movement','OA.DIM_Level'),
--('OA.DIM_Location','OA.FCT_Movement','OA.DIM_Put_Wall_Cubby'),
--('OA.DIM_Location','OA.FCT_Movement','OA.DIM_Aisle'),
--('OA.DIM_Location','OA.FCT_Movement','OA.DIM_Workstation'),
-- FCT_Equipment_Efficiency
('OA.DIM_Device', 'OA.FCT_Equipment_Efficiency', 'OA.DIM_Subsystem'),
-- FCT_Equipment_Measuring_Point
('OA.DIM_Device', 'OA.FCT_Equipment_Measuring_Point', 'OA.DIM_Subsystem'),
-- FCT_Equipment_Comm
('OA.DIM_Device', 'OA.FCT_Equipment_Comm', 'OA.DIM_Subsystem'),
-- FCT_Equipment_Fault
('OA.DIM_Device', 'OA.FCT_Equipment_Fault', 'OA.DIM_Subsystem'),
('OA.DIM_Status','OA.FCT_Equipment_Fault','OA.DIM_Subsystem'),
-- FCT_Equipment_PLC
('OA.DIM_Device', 'OA.FCT_Equipment_PLC', 'OA.DIM_Subsystem'),
-- FCT_Overhead_Order_Complete
('OA.DIM_Status','OA.FCT_Overhead_Order_Complete','OA.DIM_Subsystem'),
('OA.DIM_Status','OA.FCT_Overhead_Order_Complete','OA.DIM_Status_Category'),
-- FCT_Overhead_Induction
('OA.DIM_Status','OA.FCT_Overhead_Induction','OA.DIM_Subsystem'),
('OA.DIM_Status','OA.FCT_Overhead_Induction','OA.DIM_Status_Category'),
-- FCT_Overhead_BufferLoop_Status
('OA.DIM_Status','OA.FCT_Overhead_BufferLoop_Status','OA.DIM_Subsystem'),
('OA.DIM_Status','OA.FCT_Overhead_BufferLoop_Status','OA.DIM_Status_Category'),
-- FCT_Overhead_BufferLoop_Carrier
('OA.DIM_Status','OA.FCT_Overhead_BufferLoop_Carrier','OA.DIM_Subsystem'),
-- FCT_Overhead_Sort_Fault
('OA.DIM_Status','OA.FCT_Overhead_Sort_Fault','OA.DIM_Subsystem'),
('OA.DIM_Status','OA.FCT_Overhead_Sort_Fault','OA.DIM_Status_Category'),
-- FCT_Overhead_Scanner_Readings
('OA.DIM_Location','OA.FCT_Overhead_Scanner_Readings','OA.DIM_Location_Type'),
-- Fct_Pick_Activity
('OA.DIM_Module','OA.FCT_Pick_Activity','OA.DIM_Work_Area'),
-- FCT_Overhead_Carrier
('OA.DIM_Status','OA.FCT_Overhead_Carrier','OA.DIM_Subsystem'),
('OA.DIM_Location','OA.FCT_Overhead_Carrier','OA.DIM_Module'),
('OA.DIM_Module','OA.FCT_Overhead_Carrier','OA.DIM_Work_Area'),
('OA.DIM_Status','OA.FCT_Overhead_Carrier','OA.DIM_Status_Category'),
-- Fct_Pick_Order
('OA.DIM_Location','OA.FCT_Pick_Order','OA.DIM_Module'),
('OA.DIM_Module','OA.FCT_Pick_Order','OA.DIM_Work_Area'),
('OA.DIM_Pick_Order','OA.FCT_Pick_Order','OA.DIM_Customer_Order'),
('OA.DIM_Location','OA.FCT_Pick_Order','OA.DIM_Workstation'),
-- Fct_Aisle_Utilization
('OA.DIM_Aisle','OA.FCT_Storage_Utilization','OA.DIM_Buffer'),
('OA.DIM_Module','OA.FCT_Storage_Utilization','OA.DIM_Work_Area'),
-- Fct_Inventory
('OA.DIM_Location','OA.FCT_Inventory','OA.DIM_Module'),
('OA.DIM_Module','OA.FCT_Inventory','OA.DIM_Work_Area'),
('OA.DIM_Item','OA.FCT_Inventory','OA.DIM_Item_Category'),
-- FCT_Workstation_Work_Type_Status
('OA.DIM_Status','OA.FCT_Workstation_Work_Type_Status','OA.DIM_Subsystem'),
-- FCT_Robot_Activity
('OA.DIM_Status','OA.FCT_Robot_Activity','OA.DIM_Subsystem'),
('OA.DIM_Status','OA.FCT_Robot_Activity','OA.DIM_Status_Category'),
('OA.DIM_Module','OA.FCT_Robot_Activity','OA.DIM_Work_Area'),
-- FCT_Fault_Detail
('OA.DIM_Status','OA.FCT_Fault_Detail','OA.DIM_Subsystem'),
('OA.DIM_Status','OA.FCT_Fault_Detail','OA.DIM_Status_Category'),
('OA.DIM_Module','OA.FCT_Fault_Detail','OA.DIM_Work_Area'),
--OA.FCT_FlexSort_Fault_Detail
--(parent_table varchar(50),target_tb varchar(50), child_table varchar(50))
('OA.DIM_Status','OA.FCT_FlexSort_Fault_Detail','OA.DIM_Subsystem'),
('OA.DIM_Status_Locale','OA.FCT_FlexSort_Fault_Detail','OA.DIM_Status'),
-- FCT_Device_Statistics
('OA.DIM_Device', 'OA.FCT_Device_Statistics_Fault_Detail', 'OA.DIM_Subsystem'),
('OA.DIM_Status', 'OA.FCT_Device_Statistics_Fault_Detail', 'OA.DIM_Subsystem'),
-- FCT_Lift_Statistics
('OA.DIM_Device', 'OA.FCT_Lift_Statistics', 'OA.DIM_Subsystem'),
-- FCT_Shuttle_Statistics
('OA.DIM_Device', 'OA.FCT_Shuttle_Statistics', 'OA.DIM_Subsystem'),
('OA.Physical_Device_Code', 'OA.FCT_Shuttle_Statistics', 'OA.DIM_Subsystem'),
-- FCT_System_Status
('OA.DIM_Status','OA.FCT_System_Status','OA.DIM_Subsystem'),
('OA.DIM_Module','OA.FCT_System_Status','OA.DIM_Work_Area'),
--FCT_Container_Mode
('OA.DIM_Status','OA.FCT_Container_Mode','OA.DIM_Subsystem'),
('OA.DIM_Module','OA.FCT_Container_Mode','OA.DIM_Work_Area'),
--FCT_Equipment_Status
('OA.DIM_Device','OA.FCT_Equipment_Status','OA.DIM_Subsystem'),
('OA.DIM_Status','OA.FCT_Equipment_Status','OA.DIM_Subsystem'),
-- FCT_Order_Line
('OA.DIM_Location','OA.FCT_Order_Line','OA.DIM_Module'),
('OA.DIM_Module','OA.FCT_Order_Line','OA.DIM_Work_Area'),
('OA.DIM_Location','OA.FCT_Order_Line','OA.DIM_Workstation'),
('OA.DIM_Pick_Order','OA.FCT_Order_Line','OA.DIM_Customer_Order'),
-- FCT_Pick_Order_Complete
('OA.DIM_Pick_Order','OA.FCT_Pick_Order_Complete','OA.DIM_Customer_Order'),
('OA.DIM_Location','OA.FCT_Pick_Order_Complete','OA.DIM_Module'),
('OA.DIM_Module','OA.FCT_Pick_Order_Complete','OA.DIM_Work_Area'),
('OA.DIM_Location','OA.FCT_Pick_Order_Complete','OA.DIM_Workstation'),
--DIM_Status_Locale
('OA.DIM_Status','OA.DIM_Status_Locale','OA.DIM_Subsystem'),
('OA.DIM_Status','OA.DIM_Status_Locale','OA.DIM_Status_Category'),
('OA.DIM_Status_Locale','OA.DIM_Status_Locale','OA.DIM_Status'),
--DIM_Zone
('OA.DIM_Facility','OA.DIM_Zone','OA.DIM_Customer'),
--DIM_Logical_Destination
('OA.DIM_Device','OA.DIM_Logical_Destination','OA.DIM_Subsystem'),
('OA.DIM_Scanner','OA.DIM_Logical_Destination','OA.DIM_Device'),
('OA.DIM_Logical_Destination','OA.DIM_Logical_Destination','OA.DIM_Scanner'),
--DIM_Physical_Destination
('OA.DIM_Device','OA.DIM_Physical_Destination','OA.DIM_Subsystem'),
('OA.DIM_Scanner','OA.DIM_Physical_Destination','OA.DIM_Device'),
('OA.DIM_Physical_Destination','OA.DIM_Physical_Destination','OA.DIM_Scanner'),
-- DIM_Location
('OA.DIM_Put_Wall_Cubby', 'OA.DIM_Location','OA.DIM_Put_Wall' ),
('OA.DIM_Module', 'OA.DIM_Location', 'OA.DIM_Work_Area'),
-- DIM_Device
('OA.DIM_Device','OA.DIM_Device','OA.DIM_Subsystem'),
-- DIM_Scanner
('OA.DIM_Scanner','OA.DIM_Scanner','OA.DIM_Device'),
('OA.DIM_Device','OA.DIM_Scanner','OA.DIM_Subsystem'),
-- FCT_Label_Print_Apply
('OA.DIM_Status','OA.FCT_Label_Print_Apply','OA.DIM_Subsystem'),
-- DIM_Module
('OA.DIM_Module','OA.DIM_Module','OA.DIM_Work_Area')


