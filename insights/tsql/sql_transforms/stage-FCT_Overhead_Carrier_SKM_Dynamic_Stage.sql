SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
-- DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''Overhead_Carrier_Lifecycle_Event_Fact_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
                   ' SELECT DISTINCT Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),eventTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,eventTime)/15))  ' +
                   '     , Record_Timestamp = eventTime ' +
                   '     , Location_Code = ISNULL(eventLocation,'''') ' +
				   '     , Work_Area_Code = ISNULL(CASE WHEN CHARINDEX(''.'',eventLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(eventLocation),CHARINDEX(''.'',REVERSE(eventLocation)) + 1, LEN(eventLocation))),3) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Module_Code = ISNULL(CASE WHEN CHARINDEX(''.'',eventLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(eventLocation),CHARINDEX(''.'',REVERSE(eventLocation)) + 1, LEN(eventLocation))),2) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
				   '     , Bay = ISNULL(CASE WHEN CHARINDEX(''.'',eventLocation) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(eventLocation),CHARINDEX(''.'',REVERSE(eventLocation)) + 1, LEN(eventLocation))),1) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +				   
				   
                   '     , Location_Name = ISNULL(OA.Location_Name_Generator(eventLocation), '''')  ' +
				   '     , Status_Code = ISNULL(eventCode, '''') ' +
				   '     , Subsystem_Code = ''OVERHEAD_SORT'' ' +
                   '     , Subsystem_Category = ''DEMATIC IQ'' ' +
				   '     , Reason_Code = ISNULL(reasonCode, '''') ' +			   
				   '     , Overhead_Carrier_Code = ISNULL(sortationLoadUnitId, '''') ' +				   
                   '     , Overhead_Carrier_Insert_Date_Time = ISNULL(insertionTime, '''') ' +
				   '     , Overhead_Carrier_Create_Date_Time = ISNULL(creationTime, '''') ' +
				   '     , Overhead_Carrier_Age_In_Seconds = ISNULL(CAST(CAST(CAST(age / 1000 AS DECIMAL(16,4)) AS INT) AS VARCHAR), '''') ' +
				   '     , [Event] = ISNULL(eventType, '''') ' +
                   '     , Source_System = ''' + @lSourceSystem +  '''' +
				   '     , Status_Category_Code = '''' '+
                   ' FROM [STAGE].Overhead_Carrier_Lifecycle_Event_Fact_'+ @lSourceSystem +
                   ' ORDER BY Record_Timestamp, Location_Code ' +
                   ' END '   +  
				   ' IF OBJECT_ID(''[STAGE].Overhead_Carrier_Lifecycle_Event_Fact_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].Overhead_Carrier_Lifecycle_Event_Fact_'+@lSourceSystem;  
				   
EXEC sp_executesql @lSQLStmt

