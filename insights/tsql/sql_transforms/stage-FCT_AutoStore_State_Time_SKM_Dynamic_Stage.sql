--the goal was to make the autostore data a little easier to work with;  
--for aggregates like how long does the robot spend in each activity? charging, active, etc;
--those are not the actual states; Fct_Robot_Activity table;


SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;
DECLARE @dt DATETIME = '1905-01-01';
--DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lStartDate DATETIME = '$(BatchFrom)', @lEndDate DATETIME = '$(BatchTo)', @lBatchID VARCHAR(50) = '$(ETL_Batch_ID)';

--For Testing
DECLARE @lStartDate DATETIME2(7), @lEndDate DATETIME2(7);
DECLARE @lBatchID INT
SELECT TOP 1 @lBatchID = ETL_Batch_ID FROM OA.FCT_Robot_Activity ORDER BY ETL_Update_Timestamp DESC

-- Find the Start and End date based on Batch Run
SELECT @lStartDate = DATEADD(hour,-1,MIN(record_timestamp)) , @lEndDate = DATEADD(hour,1,MAX(Record_Timestamp)) 
FROM OA.FCT_Robot_Activity WITH (NOLOCK)
WHERE ETL_Batch_ID = @lBatchID 

-- Preset the Start Date to the top of the hour of the PREVIOUS batch run
--SELECT @lStartDate = MIN(record_timestamp) 
--FROM OA.FCT_Robot_Activity with(NOLOCK)
--WHERE ETL_Batch_ID = (SELECT top 1 ETL_Batch_ID
--						FROM OA.FCT_Robot_Activity with(NOLOCK)
--						WHERE ETL_Batch_ID != @lBatchID
--						ORDER BY Record_Timestamp DESC)


SET @lStartDate = DATEADD(hour,DATEPART(hour,@lStartDate),CONVERT(VARCHAR(10),@lStartDate,101))
/*** Set the end Date to the last 10 minute bucket ***/
SET @lEndDate = DATEADD(MINUTE, DATEDIFF(MINUTE, 0, @lEndDate) / 15 * 15, 0)

-- Create each ten-minute time bucket in the span of this batch
DECLARE @lBucketDate DATETIME = DATEADD(MINUTE,-30,@lStartDate)
DECLARE @tbl_Bucket TABLE(bucket_date DATETIME, Bucket_end DATETIME)

WHILE @lBucketDate < @lEndDate BEGIN
	INSERT INTO @tbl_Bucket VALUES(@lBucketDate,DATEADD(minute,15,@lBucketDate))
	SET @lBucketDate = DATEADD(minute,15,@lBucketDate);
END

-- Snag lead/lag times and events
;with Robot_Events as (
SELECT 
	Hour_Quarter_ID,
	Robot_Device_Code = Device_Code,
	[Prev_Event] = LAG(Event) OVER (PARTITION BY Robot_Device_ID ORDER BY  Record_Timestamp ASC),
	[Curr_Event] = [Event],  
	[Post_Event] = LEAD(Event) OVER (PARTITION BY Robot_Device_ID ORDER BY  Record_Timestamp ASC),	
	Prev_Time =  LAG(Record_Timestamp) OVER (PARTITION BY Robot_Device_ID ORDER BY  Record_Timestamp ASC),
	Record_Timestamp,
	Post_Time =  LEAD(Record_Timestamp) OVER (PARTITION BY Robot_Device_ID ORDER BY  Record_Timestamp ASC),
	Seconds = DATEDIFF(second, LAG(Record_Timestamp) OVER (PARTITION BY Robot_Device_ID ORDER BY  Record_Timestamp ASC), Record_Timestamp),
	Date_Hour = CONVERT(VARCHAR(10),Record_Timestamp,101)  +' '+ LTRIM(str(DatePart(hour,Record_Timestamp))) +':00',
	Status_ID, 
	Module_ID
FROM OA.FCT_Robot_Activity WITH(NOLOCK)
INNER JOIN OA.DIM_Device D on D.Device_ID = Robot_Device_ID
)
,
  tag_events as (
  SELECT *,
  CASE
	WHEN Prev_Event in ('available','not_charging','not_faulted')  THEN 1
	WHEN Prev_Event in ('charging', 'unavailable','fault')  THEN 0
	ELSE NULL END Available
 FROM Robot_Events
  )
, 

activity_states_segmented as (
SELECT 
	CASE WHEN DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Prev_Time) / 15 * 15 , 0) = DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Record_Timestamp) / 15 * 15 , 0) THEN
		--inter-bucket segment gap
		DATEDIFF_BIG(MILLISECOND, Prev_Time, Record_Timestamp)/1000.00
	  ELSE
	  CASE 
			-- Floor to Segment
			WHEN bucket_date = DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Prev_Time) / 15 * 15 , 0) THEN
				DATEDIFF_BIG(MILLISECOND, Prev_Time, DATEADD(minute,15,bucket_date))/1000.00
			-- Full Inter-Segment Gap - a Blocking incident the starts prior to this time bucket, and extends past its end is set to the full 10-minute frame for this bucket. 
			WHEN bucket_date > DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Prev_Time) / 15 * 15 , 0) and bucket_date < DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Record_Timestamp) / 15 * 15 , 0) THEN
				DATEDIFF_BIG(MILLISECOND, bucket_date, DATEADD(minute,15,bucket_date))/1000.00
	  		-- Segment to Ceiling
			WHEN bucket_date =  DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Record_Timestamp) / 15 * 15 , 0) THEN
			 DATEDIFF_BIG(MILLISECOND, bucket_date, Record_Timestamp)/1000.00
		END  	
	END AS state_times,

	Hour_Quarter_ID,
	Record_Timestamp,
	Robot_Device_Code,
	[Event] = Prev_Event,
	Seconds, 
	Event_Sequence = 'pre:'+prev_event +';cur:'+ curr_event +';pos:'+ post_event,
	Date_Hour,
	Status_Code = ISNULL(Status_Code,''), 
	Module_Code = ISNULL(Module_Code,''),
	bucket_date,
	Available,
	S.Source_System
FROM TAG_EVENTS TE
INNER JOIN OA.DIM_Status S ON S.Status_ID = TE.Status_ID 
INNER JOIN OA.DIM_Module M ON M.Module_ID = TE.Module_ID
JOIN @tbl_Bucket TB ON Record_Timestamp >= bucket_date AND Record_Timestamp < Bucket_end
WHERE [prev_event] IS NOT NULL 
)

SELECT
	Record_Timestamp = MIN(Record_Timestamp), 
	Hour_Quarter_ID,
	Robot_Device_Code,
	Time_Segment = bucket_date,
	[Event],
	Available,
	Event_Sequence,
	Total_State_Time = SUM(CAST(State_Times as INT)),
	Status_Code,
	Module_Code,
	Source_System
FROM activity_states_segmented
GROUP BY
	Hour_Quarter_ID,
	Robot_Device_Code,
	bucket_date,
	[Event],
	Available,
	Status_Code,
	Module_Code,
	Event_Sequence,
	Source_System
ORDER BY Robot_Device_Code, Record_Timestamp