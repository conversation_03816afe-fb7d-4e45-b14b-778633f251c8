SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--SET @lSourceSystem = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX);
DECLAR<PERSON> @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Logical_Destination_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 logicalDestinationId= '''', 	sortDeviceId= '''', logicalDestinationName= '''', logicalDestinationType= '''', tenantName= '''', 
		dimensionModificationTime= '''', scannerId= ''''
		INTO STAGE.DIM_Logical_Destination_' + @lSourceSystem + '
	END;'
EXEC sp_executesql @lSQLStmt


SET @Transform_SQLStmt = '
WITH Latest_Logical_Dest AS (
	SELECT TOP 1 WITH TIES
		Scanner_Code = scannerId,
		Logical_Destination_Code = logicalDestinationId,
		Logical_Destination_Name = logicalDestinationName,
		Logical_Destination_Type = logicalDestinationType,
		DC_Tenant_Name = ISNULL(UPPER(tenantName), ''''),
		Device_Code = ISNULL(sortDeviceId, ''''),
		Source_System = ''' + @lSourceSystem + '''
	FROM [STAGE].[DIM_Logical_Destination_' + @lSourceSystem + ']
	ORDER BY Row_number() over (partition by sortDeviceId, logicalDestinationId, scannerId, tenantName order by dimensionModificationTime desc)
)
SELECT DISTINCT
	Scanner_Code = ISNULL(UPPER(ISNULL(Latest_Logical_Dest.Scanner_Code, s.Scanner_Code)), ''''),
	Logical_Destination_Code = ISNULL(UPPER(Logical_Destination_Code), ''''),
	Logical_Destination_Name = ISNULL(Logical_Destination_Name, ''''),
	Logical_Destination_Type,
	Device_Code = Latest_Logical_Dest.Device_Code,
	DC_Tenant_Name = Latest_Logical_Dest.DC_Tenant_Name,
	Source_System = Latest_Logical_Dest.Source_System,
	Subsystem_Category = ''DEMATIC IQ'',
    Subsystem_Code = ISNULL(Subsystem.Subsystem_Code,''SORTER'')
FROM Latest_Logical_Dest
LEFT OUTER JOIN OA.DIM_Device d WITH(NOLOCK) 
	ON ISNULL(d.DC_Tenant_Name,'''') = ISNULL(Latest_Logical_Dest.DC_Tenant_Name,'''') 
	AND d.Device_Code = Latest_Logical_Dest.Device_Code AND d.Source_System = Latest_Logical_Dest.Source_System
LEFT OUTER JOIN OA.DIM_Scanner s WITH(NOLOCK) 
	ON d.Device_ID = s.Device_ID AND d.Source_System = s.Source_System 
	AND ISNULL(d.DC_Tenant_Name,'''') = ISNULL(s.DC_Tenant_Name, '''')
LEFT OUTER JOIN OA.DIM_Subsystem Subsystem ON Subsystem.Subsystem_ID = D.Subsystem_ID
   AND Subsystem.Source_System = D.Source_System
   AND Subsystem.Subsystem_Category = ''DEMATIC IQ''
'

SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Logical_Destination_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[DIM_Logical_Destination_'+@lSourceSystem +']'

--PRINT(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt