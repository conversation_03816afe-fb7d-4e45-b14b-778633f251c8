SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 
DECLARE @lSQLStmt NVARCHAR(MAX), @Drop_SQLStmt NVARCHAR(MAX);


SET @lSQLStmt = '
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_PssNokIntervals_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
-- Create transform tabke if it does not exists stage table
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Fault_Dynamic_Stage_' + @lSourceSystem + ''' and s.name = ''Stage'')
	BEGIN
		CREATE TABLE [STAGE].[FCT_Fault_Dynamic_Stage_' + @lSourceSystem + '] (
			Hour_Quarter_ID INTEGER NOT NULL, Container_Physical_Code VARCHAR(50) NULL, Device_Code VARCHAR(150) NULL,
			Fault_Acknowledgement_Date_Time Datetime2(7) NULL, Fault_End_Date_Time Datetime2(7)  NOT NULL, Fault_Duration_Seconds INTEGER NULL,
			Fault_Repair_Duration_Seconds INTEGER NULL, Fault_Start_Date_Time Datetime2(7)  NOT NULL, Fault_Tag_Reason_Code VARCHAR(150) NULL,
			Item_Category_Code VARCHAR(150) NULL, Item_Code VARCHAR(150) NULL, Location_Code VARCHAR(150) NULL, Module_Code VARCHAR(150) NULL,
			Operator_Code VARCHAR(150) NULL, Physical_Device_Code VARCHAR(150) NULL, PLC_Code VARCHAR(150) NULL, Reason_Name VARCHAR(150) NULL,
			Record_Timestamp Datetime2(7) NULL, Source_System VARCHAR(150) NULL, Fault_Code VARCHAR(50) NULL, Subsystem_Category VARCHAR(50) NULL,
			Subsystem_Code VARCHAR(50) NULL, Tenant_Name VARCHAR(50) NULL);
    END; 
 	-- Process NOK Events
	WITH PreProcessed_Events as (
	SELECT
		  Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), endTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), endTime,120),15,2) as INT)/15))
		  ,Container_Physical_Code = [tuId]
		  ,Fault_Start_Date_Time = ISNULL(startTime, endTime) 
		  ,Fault_End_Date_Time = [endTime]
		  ,Record_Timestamp = [endTime]
		  ,Fault_Acknowledgement_Date_Time = [AcknowledgeTime]
		  ,Fault_Repair_Duration_Seconds = ROUND(faultRepairTime / 1000,1) 
		  ,Fault_Duration_Seconds = ISNULL(ROUND(faultDuration / 1000,1), DATEDIFF("S",startTime,[endTime]))
		  ,Fault_Code =  CASE WHEN errorCode IS NULL OR errorCode = ''0''  THEN classification ELSE errorCode END 
		  ,Classification = [classification]
		  ,Device_Code = [deviceId]
		  ,Location_Code = [locationId]
		  ,Subsystem_Code = ''OVERHEAD_SORT''
		  ,Tenant_Name = [tenantName]
		  ,Physical_Device_Code = [hardwareIdentifier]
		  ,Item_Code = NULL
	  FROM [STAGE].[FCT_PssNokIntervals_' + @lSourceSystem + ']
	)
	, Processed_Events as (
		SELECT 
			Hour_Quarter_ID
			,Container_Physical_Code
			,Device_Code
			,Fault_Acknowledgement_Date_Time = CASE 
									WHEN UPPER(Classification) = ''PLANNED_DOWNTIME'' OR UPPER(Classification) = ''PLANNED_MAINTENANCE'' THEN Fault_End_Date_Time
									WHEN UPPER(Classification) = ''FAULT'' and Fault_Repair_Duration_Seconds < Fault_Duration_Seconds THEN DATEADD("Second",Fault_Repair_Duration_Seconds * -1,Fault_End_Date_Time)
									WHEN UPPER(Classification) = ''FAULT'' and Fault_Repair_Duration_Seconds = Fault_Duration_Seconds THEN Fault_End_Date_Time
									ELSE Fault_Acknowledgement_Date_Time
								END
			,Fault_End_Date_Time
			,Fault_Duration_Seconds
			,Fault_Repair_Duration_Seconds = CASE
									WHEN UPPER(Classification) = ''PLANNED_DOWNTIME'' or UPPER(Classification) = ''PLANNED_MAINTENANCE'' THEN 0
									WHEN Fault_Acknowledgement_Date_Time IS NULL THEN ISNULL(Fault_Repair_Duration_Seconds,0)
									ELSE DATEDIFF("S", Fault_Acknowledgement_Date_Time, Fault_End_Date_Time)
								END		
			,Fault_Start_Date_Time
			,Fault_Tag_Reason_Code = NULL
			,Item_Category_Code = NULL
			,Item_Code
			,Location_Code
			,Module_Code = NULL
			,Operator_Code = NULL
			,Physical_Device_Code
			,PLC_Code = NULL
			,Reason_Name = NULL
			,Record_Timestamp
			,Source_System = ''' + @lSourceSystem + '''
			,Fault_Code
			,Subsystem_Category = ''DEMATIC IQ''  
			,Subsystem_Code
			,Tenant_Name
		FROM PreProcessed_Events PPE
	)
	--SELECT * FROM Processed_Events;
	INSERT INTO [STAGE].[FCT_Fault_Dynamic_Stage_' + @lSourceSystem + '] SELECT * from Processed_Events
END
'

SET @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_PssNokIntervals_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[FCT_PssNokIntervals_'+@lSourceSystem +']'

--PRINT(@lSQLStmt)
EXEC sp_executesql @lSQLStmt
EXEC sp_executesql @Drop_SQLStmt
