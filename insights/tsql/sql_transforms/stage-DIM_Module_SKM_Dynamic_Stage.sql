SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware';
DECLARE @Tables_SQLStmt NVARCHAR(MAX); 
DECLARE @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drops_SQLStmt NVARCHAR(MAX);


SET @Tables_SQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Module_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 primaryKey= '''', 	area= '''', 	id= '''', configurationStatus= '''', 	groupType= '''', 	tenantName= '''', 	dimensionModificationTime= ''''
		INTO STAGE.DIM_Module_' + @lSourceSystem + '
	END;'
EXEC(@Tables_SQLStmt)

SET @Transform_SQLStmt = '
WITH Modules AS (
	SELECT TOP 1 WITH TIES
		Module_Code = CASE WHEN OA.CHARINDEX2(primaryKey,''#'',2) > 0 THEN REPLACE(SUBSTRING(primaryKey,OA.CHARINDEX2(primaryKey,''#'',2), LEN(primaryKey)), ''#'', '''') ELSE primaryKey END,
		--Module_Name = ISNULL(primaryKey,id),
		Module_Name = id,
		Module_Type = groupType,
		Work_Area_Code = ISNULL(CASE WHEN CHARINDEX(''#'', area) > 0 THEN REPLACE(SUBSTRING(area, CHARINDEX(''#'', area)+ 1, LEN(area)) , ''#'', '''') ELSE area END, ''''),
		Tenant_Name = tenantName
	FROM [STAGE].[DIM_Module_' + @lSourceSystem + ']
	ORDER BY Row_number() over (partition by primaryKey, area order by dimensionModificationTime desc)
)
SELECT DISTINCT
	Module_Code = ISNULL(Module_Code, ''''),
	Module_Name = ISNULL(Module_Name, Module_Code),
	Module_Type,
	Work_Area_Code,
	Modules.Tenant_Name,
	Active_Rec_Ind = 1,
	Source_System = ''' + @lSourceSystem +'''
FROM Modules
'

SELECT @Drops_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Module_' + @lSourceSystem + ']'', ''U'') IS NOT NULL DROP TABLE [STAGE].[DIM_Module_'+@lSourceSystem +']'

--PRINT(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drops_SQLStmt