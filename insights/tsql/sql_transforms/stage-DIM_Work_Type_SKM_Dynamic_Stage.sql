SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--SET @lSourceSystem = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX);
DECLAR<PERSON> @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Workflow_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 workflowPk= '''', 	technology= '''', 	vendor= '''', 	workType= '''',
			workTypeStyle= '''', tenantName= '''', dimensionModificationTime= ''''
		INTO STAGE.DIM_Workflow_' + @lSourceSystem + '
	END;'
EXEC sp_executesql @lSQLStmt

SET @Transform_SQLStmt = '
--DIM_Workflow
WITH Latest_WorkFlow AS (
	SELECT TOP 1 WITH TIES
		Work_Type_Code = ISNULL(workType, ''''),
		Work_Type_Style = workTypeStyle	
	FROM [STAGE].[DIM_Workflow_' + @lSourceSystem + ']
	ORDER BY Row_number() over (partition by workflowPk order by dimensionModificationTime desc)
)
SELECT DISTINCT
	Work_Type_Code,
	Work_Type_Style,
	Work_Type_Desc = '''',
	Source_System = ''' + @lSourceSystem +'''
FROM Latest_WorkFlow
'

-- SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Workflow_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
--		DROP TABLE [STAGE].[DIM_Workflow_'+@lSourceSystem +']'

--PRINT(@Transform_SQLStmt)

EXEC sp_executesql @Transform_SQLStmt
-- Workflow stage table dropped in Dim_Technology Transform 
-- EXEC(@Drop_SQLStmt)