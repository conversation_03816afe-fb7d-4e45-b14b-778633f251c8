SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)'; 
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id' +   
                   ' where t.name = ''POUCH_ORDERFACT_' +  @lSourceSystem + ''' and s.name = ''Stage'') ' + 
                   ' BEGIN ' +
   ' SELECT DISTINCT ' +
   '    Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),packingFinishTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,packingFinishTime)/15)) ' +
   '  , Record_Timestamp = packingFinishTime ' +
   '  , Overhead_Order_Code = ISNULL(CASE WHEN orderid is not null AND clientid is not null AND orderid != ISNULL(clientid,'''') '+
   '                          THEN clientid + ''#'' + orderid ELSE orderid END ,'''') ' +
   '  , Pack_Station_Code = ISNULL(CASE WHEN CHARINDEX(''.'',packingStationLocation) > 0 '+
   '                                    THEN REVERSE(LEFT(REVERSE(packingStationLocation), CHARINDEX(''.'',REVERSE(packingStationLocation)) - 1)) '+
   '                                    ELSE packingStationLocation  END,'''') ' +
   '  , Workstation_Name = ISNULL(packingStationLocation,'''') ' +
   '  , WorkStation_Area = ISNULL(CASE WHEN CHARINDEX(''.'',packingStationLocation) > 0 ' +
   '                                   THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(packingStationLocation),CHARINDEX(''.'',REVERSE(packingStationLocation)) + 1, LEN(packingStationLocation))),3) ' +
   '								   ELSE '''' END,  '''') ' +
   '  , WorkStation_Group = ISNULL(CASE WHEN CHARINDEX(''.'',packingStationLocation) > 0 ' +
   '                                   THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(packingStationLocation),CHARINDEX(''.'',REVERSE(packingStationLocation)) + 1, LEN(packingStationLocation))),2) ' +
   '								   ELSE '''' END,  '''') ' +
   '  , WorkStation_SubGroup = ISNULL(CASE WHEN CHARINDEX(''.'',packingStationLocation) > 0 ' +
   '                                   THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(packingStationLocation),CHARINDEX(''.'',REVERSE(packingStationLocation)) + 1, LEN(packingStationLocation))),1) ' +
   '								   ELSE '''' END,  '''') ' +
   '  , WorkStation_Type = ''PACK STATION'' ' +
   '  , Operator_Code = ISNULL(packingOperator,'''') ' +
   '  , Status_Code = ISNULL(resultCode,'''') ' +
   '  , Subsystem_Code = ISNULL(''OVERHEAD_SORT'','''') ' +
   '  , Subsystem_Category = ISNULL(''DEMATIC IQ'','''') ' +
   '  , Order_Created_Date_Time = ISNULL(orderCreationTime,'''')' +
   '  , Order_Target_Date_Time = ISNULL(cutoffTime,'''') ' +
   '  , Order_Active_Date_Time =  ISNULL(orderActivationTime,'''') ' +
   '  , Order_Buffer_First_Unit_Exit_Date_Time = ISNULL(firstSortationLoadUnitAllocationTime,'''') ' +
   '  , Order_Buffer_Last_Unit_Exit_Date_Time = ISNULL(lastSortationLoadUnitAllocationTime,'''') ' +
   '  , Order_Buffer_First_Exit_Unavailable_Date_Time = ISNULL(firstNoDownstreamCapacityTime,'''') ' +
   '  , Order_Buffer_Last_Exit_Unavailable_Date_Time = ISNULL(lastNoDownstreamCapacityTime,'''') ' +
   '  , Order_PreSort_Completion_Time = ISNULL(presorterCompletionTime,'''') ' +
   '  , Order_PreSort_Release_Time = ISNULL(presorterReleaseTime,'''') ' +
   '  , Order_Sort_Run_Assemble_Complete_Date_Time = ISNULL(runCompletionTime,'''') ' +
   '  , Order_Pack_Station_Request_Date_Time = ISNULL(packingStationSorterReaderTime,'''') ' +
   '  , Order_Pack_Station_Assignment_Date_Time = ISNULL(packingStationAssignmentTime,'''') ' +
   '  , Order_Pack_Start_Date_Time = ISNULL(firstSortationLoadUnitReleaseTime,'''') ' +
   '  , Order_Pack_Last_Carrier_Date_Time = ISNULL(lastSortationLoadUnitReleaseTime,'''') ' +   
   '  , Order_Buffer_Exit_Unavailable_Count = ISNULL(noDownstreamCapacityCounter,'''') ' +
   '  , Order_Misdivert_Count = ISNULL(numberOfMisdiverts,'''') ' +
   '  , Order_Requested_Line_Count = ISNULL(numberOfOrderLines,'''') ' +
   '  , Order_Requested_Qty = ISNULL(numberOfItems,'''') ' +
   '  , Order_Short_Ind = ISNULL(shortIndication,'''') ' +
   '  , Order_Late_Ind = ISNULL(lateIndication,'''') ' +
   '  , Order_Pack_Duration_Seconds = ISNULL(packingDuration,'''') ' +
   '  , Order_Download_To_Complete_Duration_Seconds = ISNULL(orderLeadTime,'''') ' +
   '  , Order_Active_To_Complete_Duration_Seconds = ISNULL(orderCycleTime,'''') ' +
   '  , Order_Pack_Station_Idle_Duration_Seconds = ISNULL(idleWaitDuration,'''') ' +
   '  , Order_Pack_Station_Busy_Duration_Seconds = ISNULL(busyWaitDuration,'''') ' +
   '  , Order_Pack_Station_Operator_Duration_Seconds = ISNULL(operatorDuration,'''') ' +
   '  , Overhead_Order_Sort_Mode = ISNULL(sortationMode,'''') ' +
   '  , Overhead_Order_Run = ISNULL(runId,'''')  ' +
   '  , Overhead_Order_Loop = ISNULL( CASE WHEN CHARINDEX(''.'',loop) > 0 '+
   '                                  THEN REVERSE(LEFT(REVERSE(loop), CHARINDEX(''.'',REVERSE(loop)) - 1))  ' +
   '                                  ELSE loop END ,'''') ' +
   '  , Overhead_Order_Capability = ISNULL(capability,'''')  '  +
   '  , Overhead_Order_Width_Millimeters = ISNULL(widthOfOrder,'''')  '  +
   '  , Status_Category_Code = '''' '+
   '  , Source_System = ''' + @lSourceSystem +  '''' +
   ' FROM STAGE.POUCH_ORDERFACT_' + @lSourceSystem +
   '	ORDER BY Record_Timestamp, Overhead_Order_Code, Pack_Station_Code, Operator_Code, Status_Code, Subsystem_Code, Subsystem_Category ' +  
   ' END ' + 
   
   -- ' DROP TABLE IF EXISTS [STAGE].POUCH_ORDERFACT_' + @lSourceSystem
    ' IF OBJECT_ID(''[STAGE].POUCH_ORDERFACT_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].POUCH_ORDERFACT_'+@lSourceSystem; 
   

EXEC sp_executesql @lSQLStmt
