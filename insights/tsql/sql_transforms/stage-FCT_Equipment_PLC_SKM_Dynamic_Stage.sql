SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)'; 
-- DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' + 
                   ' where t.name = ''PLC_STATUS_DATA_POINT_MSG_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' + 
				   '   WITH FCT_LatestRow AS (  ' +
                   '   SELECT Max_Record_Timestamp = MAX(Record_Timestamp) ' +
                   '    , Device_Code, Catalog_Number , Serial_Number, Exec_Modification_Count ' +
	               '    , Audit_Value_1 , D.Source_System ' +
				   ' FROM OA.FCT_Equipment_PLC PLC ' +
                   ' JOIN OA.DIM_Device D ON PLC.Device_ID = D.Device_ID ' +
                   ' JOIN OA.DIM_Subsystem S ON S.Subsystem_ID = D.Subsystem_ID ' +
                   ' WHERE S.Subsystem_Code = ''Sort'' '+ 
                   ' AND S.Subsystem_Category = ''APE'' '+ 
				   ' AND D.Source_System = '''+ @lSourceSystem + '''' +
                   ' GROUP BY Device_Code, Catalog_Number, Serial_Number, ' +
                   ' Exec_Modification_Count, Audit_Value_1, D.Source_System ), ' +
				   
                   ' Stage_LatestRow AS (SELECT MaxDate = Max([DateTime]), ' +
				   ' DeviceID, CatalogNo, SerialNo, ExeModificationCount, Audit '+
                   ' FROM STAGE.PLC_STATUS_DATA_POINT_MSG_'+ @lSourceSystem + 
                   ' GROUP BY DeviceID, CatalogNo, SerialNo, ExeModificationCount, Audit) ' +

				   ' SELECT DISTINCT Record_Timestamp = SLR.MaxDate  ' +
				   '  , Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),SLR.MaxDate,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,SLR.MaxDate)/15))  ' +
                   '  , Device_Code = SLR.DeviceID ' +
	               '  , Subsystem_Code = ''SORT'' ' +
                   '  , Subsystem_Category = ''APE''  ' +
	               '  , Catalog_Number = ISNULL(SLR.CatalogNo,'''') ' +
	               '  , Serial_Number = ISNULL(SLR.SerialNo,'''') ' +
	               '  , Exec_Modification_Count = ISNULL(SLR.ExeModificationCount,'''') ' +
	               '  , Audit_Value_1 = ISNULL(SLR.[Audit],'''') ' +
			       '  , Audit_Value_2 = '''' ' +
				   '  , Source_System = ''' + @lSourceSystem +  '''' +
                   '  FROM Stage_LatestRow SLR ' +
                   ' LEFT OUTER JOIN FCT_LatestRow FLR ON SLR.DeviceID = FLR.Device_Code ' +
                   ' WHERE (MaxDate >= Max_Record_Timestamp ' +
                   '  AND (SLR.Audit != FLR.Audit_Value_1  ' +
                   '     OR SLR.CatalogNo != FLR.Catalog_Number ' +
                   '     OR SLR.SerialNo != FLR.Serial_Number ' +
                   '     OR SLR.ExeModificationCount != FLR.Exec_Modification_Count)) ' +
				   '     OR FLR.Max_Record_Timestamp IS NULL ' +
				   ' END ' + 
				   
				   ' IF OBJECT_ID(''[STAGE].PLC_STATUS_DATA_POINT_MSG_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].PLC_STATUS_DATA_POINT_MSG_'+@lSourceSystem;  
				   
				   -- ' DROP TABLE IF EXISTS [STAGE].PLC_STATUS_DATA_POINT_MSG_' + @lSourceSystem

EXEC sp_executesql @lSQLStmt;

