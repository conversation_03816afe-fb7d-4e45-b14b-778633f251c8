SET NOCOUNT ON
DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware';

DECLARE @strLocationSQL NVARCHAR(MAX),
        --@strWorkstationSQL VARCHAR(MAX),
		--@strPutWallSQL VARCHAR(MAX),
        @strTableDeclareSQL NVARCHAR(MAX),
		@strDropSQL NVARCHAR(MAX)


/******************************\
 Create tables if missing
\******************************/
SET @strTableDeclareSQL = '
IF NOT (EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
                 WHERE TABLE_SCHEMA = ''STAGE'' 
                 AND  TABLE_NAME = ''DIM_Location_'+ @lSourceSystem +'''))
BEGIN

	CREATE TABLE [STAGE].[DIM_Location_'+ @lSourceSystem +'](
		[primaryKey] [varchar](255) NULL,
		[id] [varchar](255) NULL,
		[displayID] [varchar](255) NULL,
		[side] [varchar](255) NULL,
		[x] [varchar](255) NULL,
		[y] [varchar](255) NULL,
		[coordinate] [varchar](255) NULL,
		[usageType] [varchar](255) NULL,
		[sequenceNumber] [varchar](255) NULL,
		[velocityClassification] [varchar](255) NULL,
		[areHazourdousMaterialsAllowed] [varchar](255) NULL,
		[heightClassification] [varchar](255) NULL,
		[weightLimit] [varchar](255) NULL,
		[bayWidth] [varchar](255) NULL,
		[area] [varchar](255) NULL,
		[group] [varchar](255) NULL,
		[subGroup] [varchar](255) NULL,
		[locationType] [varchar](255) NULL,
		[tenantName] [varchar](255) NULL,
		[dimensionModificationTime] [varchar](255) NULL,
		[Key_ID] [int] IDENTITY(1,1) NOT NULL
	) ON [PRIMARY]

END


IF NOT (EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
                 WHERE TABLE_SCHEMA = ''STAGE'' 
                 AND  TABLE_NAME = ''DIM_Workstation_Location_'+@lSourceSystem  +'''))
BEGIN

	CREATE TABLE [STAGE].[DIM_Workstation_Location_'+ @lSourceSystem +'](
		[workstationPk] [varchar](255) NULL,
		[locationPk] [varchar](255) NULL,
		[workstationlocationtype] [varchar](255) NULL,
		[tenantName] [varchar](255) NULL,
		[dimensionModificationTime] [varchar](255) NULL,
		[autostore_id] [varchar](255) NULL,
		[process_epoch_ns] [varchar](255) NULL,
		[Key_ID] [int] IDENTITY(1,1) NOT NULL
	) ON [PRIMARY]

END

IF NOT (EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
                 WHERE TABLE_SCHEMA = ''STAGE'' 
                 AND  TABLE_NAME = ''DIM_Putwall_Location_'+ @lSourceSystem +'''))
BEGIN

CREATE TABLE [STAGE].[DIM_Putwall_Location_'+ @lSourceSystem +'](
	[primaryKey] [varchar](255) NULL,
	[putWallLocationType] [varchar](255) NULL,
	[location] [varchar](255) NULL,
	[packLocation] [varchar](255) NULL,
	[putWall] [varchar](255) NULL,
	[tenantName] [varchar](255) NULL,
	[dimensionModificationTime] [varchar](255) NULL,
	[Key_ID] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]

END;
';

/*************************\
 Consolidation Query
\*************************/

SET @strLocationSQL = '
;WITH PutWallLocations AS (
(-- Put Locations 
      SELECT primaryKey = ISNULL(PW.Location, PW.primaryKey)
             , PutLocation = ISNULL(PW.Location, PW.primaryKey)
             , PackLocation = packLocation 
             , PutWallLocationType = ISNULL(PW.putWallLocationType, ''Put'')
             , PutWall = ISNULL(PW.putWall,'''')
             , PW.TenantName
             , PW.dimensionModificationTime
      FROM [STAGE].[DIM_Putwall_Location_'+ @lSourceSystem +'] PW
      UNION
      SELECT primaryKey = PackLocation
             , PutLocation = ISNULL(PW.Location, PW.primaryKey)
             , PackLocation = packLocation 
             , PutWallLocationType = ISNULL(PW.putWallLocationType, ''Pack'')
             , PutWall = ISNULL(PW.putWall,'''')
             , TenantName
             , PW.dimensionModificationTime
      FROM [STAGE].[DIM_Putwall_Location_'+ @lSourceSystem +'] PW ))
, Locations as (
-- LOCATON
SELECT TOP 1 WITH TIES
	src=''loc''
	,primaryKey
	,location_code = primaryKey 
	,id
	,displayID
	,side
	,x
	,y
	,coordinate
	, Location_Type_Code = CASE LEFT(RIGHT(primaryKey,4),2)
						WHEN ''PS'' THEN ''Pick Station''
						WHEN ''LU''  THEN ''Load Unit''
						WHEN ''DS''  THEN ''Drop Station''
						ELSE
							usageType
						END
	, sequenceNumber, velocityClassification, areHazourdousMaterialsAllowed, heightClassification, weightLimit, bayWidth
	,area = ISNULL( CASE WHEN CHARINDEX(''.'',primaryKey)>0
					     THEN parseName(RIGHT(primaryKey, LEN(primaryKey) - CHARINDEX(''.'',primaryKey)),4)
					     ELSE ''''
				    END, 	
	CASE WHEN CHARINDEX(''#'',ISNULL(area,''''))>0
	                    THEN REPLACE(REVERSE(LEFT(REVERSE(area), CHARINDEX(''#'',area))),''#'','''') 
				        ELSE area
			       END)
	,[group] = ISNULL(CASE WHEN CHARINDEX(''#'',ISNULL([group],''''))>0
	                          THEN REPLACE(REVERSE(LEFT(REVERSE([group]), CHARINDEX(''#'',REVERSE([group])))),''#'','''')
					       ELSE [group]
				      END,
					  CASE WHEN CHARINDEX(''.'',primaryKey)>0
					       THEN parseName(RIGHT(primaryKey, LEN(primaryKey) - CHARINDEX(''.'',primaryKey)),3)
					       ELSE ''''
					  END) 
	, subgroup = ISNULL(CASE WHEN CHARINDEX(''#'',ISNULL([subGroup],''''))>0
	                         THEN REPLACE(REVERSE(LEFT(REVERSE([subGroup]), CHARINDEX(''#'',REVERSE([subGroup])))),''#'','''')
		 		        ELSE [subGroup]
				        END,
					    CASE WHEN CHARINDEX(''.'',primaryKey)>0
					         THEN parseName(RIGHT(primaryKey, LEN(primaryKey) - CHARINDEX(''.'',primaryKey)),2)
					         ELSE ''''
					    END) 
	,Location_Size = locationType 
	,putwall_cubby_code = ''''
	,putlocation = ''''
	,packLocation = ''''
	,level_code = ''''
	,aisle_code =  CASE WHEN SUBSTRING(primaryKey,3,2) = ''AI'' THEN SUBSTRING(primaryKey,5,2) ELSE '''' END
	,tenantName
	,dimensionModificationTime
	, Workstation_Code = ''''
	, Workstation_Type = ''''
	, Workstation_Area = ''''
	, Workstation_Group = ''''
	, Workstation_Subgroup = ''''
	, Put_Wall_Code = ''''
FROM [STAGE].[DIM_Location_'+ @lSourceSystem +']
ORDER BY Row_number() over (partition by primaryKey, tenantName order by dimensionModificationTime desc)
UNION 
' 

SET @strLocationSQL = @strLocationSQL + '
-- WORKSTATION
SELECT TOP 1 WITH TIES
	 src=''wst''
	,primaryKey = CASE WHEN CHARINDEX('','',ws.locationPk) > 0 THEN ISNULL(autostore_id,'''') + ''-'' + REPLACE(ws.locationPk,'','',''-'') ELSE ws.locationPk END
	,Location_Code = CASE WHEN CHARINDEX('','',ws.locationPk) > 0 THEN ISNULL(autostore_id,'''') + ''-'' + REPLACE(ws.locationPk,'','',''-'') ELSE ws.locationPk END
	,id = workstationPk
	,displayID='''',side=''''
	,x  = CASE WHEN CHARINDEX('','',ws.locationPk) > 0 THEN LEFT(ws.locationPk,CHARINDEX('','',ws.locationPk)) ELSE '''' END
    ,y = CASE WHEN CHARINDEX('','',ws.locationPk) > 0 THEN REVERSE(LEFT(REVERSE(ws.locationPk),CHARINDEX('','',REVERSE(ws.locationPk)))) ELSE '''' END
	,coordinate = ''''
	,usageType = '''', sequenceNumber ='''', velocityClassification = '''', areHazourdousMaterialsAllowed ='''', heightClassification =''''
	,weightLimit ='''', bayWidth=''''
	,area
	,[group]
	,subGroup
	,locationType = ''''
	,putwall_cubby_code = '''', putlocation = '''', packLocation = ''''
	,level_code = ''''
	,aisle_code = ''''
	,tenantName
	,dimensionModificationTime
	, Workstation_Code = WS.workstationPk
	, Workstation_Type = WS.workstationlocationtype
	, Workstation_Area = WS_PV.area
	, Workstation_Group = WS_PV.[group]
	, Workstation_Subgroup = WS_PV.subGroup
	, Put_Wall_Code = ''''
	FROM [STAGE].[DIM_Workstation_Location_'+ @lSourceSystem +'] WS
INNER JOIN (
	SELECT Key_id,locationPk,  
		[1] AS facility,  
		[2] AS area,  
		[3] AS [group],  
		[4] as [subGroup],
		[5] as [Location]
	FROM  
		(
			SELECT 
				key_id, 
				locationPk,
				subsection= [value], 
				subsection_id = ROW_NUMBER() OVER(partition by Key_id ORDER BY Key_id ASC)  
				FROM [STAGE].[DIM_Workstation_Location_'+ @lSourceSystem +']
			CROSS APPLY STRING_SPLIT (locationPk,''.'')
		)   AS LocBreakOut  
	PIVOT  
	(  
		MAX(subsection)   
		FOR subsection_id IN ([1], [2], [3], [4], [5])      
	) AS PivotTable  
	) WS_PV 
ON WS.Key_ID = WS_PV.Key_id
ORDER BY Row_number() over (partition by ws.LocationPk, tenantName order by dimensionModificationTime desc)
UNION ' 

SET @strLocationSQL = @strLocationSQL + '
/**************\
 P U T W A L L 
\**************/
SELECT TOP 1 WITH TIES
	src=''pwl''
	,pw.primaryKey
	,Location_Code = pw.primaryKey
	,id = pw.primaryKey
	,displayID = '''',side='''', x='''',y='''', coordinate=''''
	,usageType = ''Putwall''
	,sequenceNumber='''', velocityClassification='''', areHazourdousMaterialsAllowed ='''', heightClassificatio='''', weightLimit='''', bayWidth=''''
	,PW_PV.area
	,PW_PV.[group]
	,PW_PV.subGroup
	,locationType = putWallLocationType
	,putwall_cubby_code = CASE WHEN putWallLocationType =''OVERFLOW''
						       THEN '''' 
							   ELSE 
							      CASE WHEN LEN(pw.PutLocation) > 0 AND len(packLocation) > 0
								          THEN CASE WHEN CHARINDEX(''.'',putLocation) > 0 THEN REPLACE(RIGHT(putLocation,CHARINDEX(''.'',REVERSE(putLocation))),''.'','''') ELSE putLocation END + '':''+ CASE WHEN CHARINDEX(''.'',packLocation) > 0 THEN REPLACE(RIGHT(packLocation,CHARINDEX(''.'',REVERSE(packLocation))),''.'','''') ELSE packLocation END
									   WHEN LEN(pw.PutLocation) > 0
										  THEN CASE WHEN CHARINDEX(''.'',putLocation) > 0 THEN REPLACE(RIGHT(putLocation,CHARINDEX(''.'',REVERSE(putLocation))),''.'','''') ELSE putLocation END + '':'' + CASE WHEN CHARINDEX(''.'',putLocation) > 0 THEN REPLACE(RIGHT(putLocation,CHARINDEX(''.'',REVERSE(putLocation))),''.'','''') ELSE putLocation END
									   ELSE ''''
								  END 
								END
	,putlocation = pw.PutLocation
	,pw.packLocation
	,level_code = ''''
	,aisle_code = ''''
	,tenantName
	,dimensionModificationTime
	, Workstation_Code = ''''
	, Workstation_Type = ''''
	, Workstation_Area = ''''
	, Workstation_Group = ''''
	, Workstation_Subgroup = ''''
	, Put_Wall_Code = ISNULL(putwall,'''')
FROM PutWallLocations PW
INNER JOIN (
	SELECT primaryKey,  
		[1] AS facility,  
		[2] AS area,  
		[3] AS [group],  
		[4] as [subGroup],
		[5] as [Location]
	FROM  
		(
			SELECT 
				primaryKey,
				subsection= [value], 
				subsection_id = ROW_NUMBER() OVER(partition by primaryKey ORDER BY primaryKey ASC)  
				FROM PutWallLocations
			CROSS APPLY STRING_SPLIT (primaryKey,''.'')
		) AS LocBreakOut  
	PIVOT  
	(  
		MAX(subsection)   
		FOR subsection_id IN ([1], [2], [3], [4], [5])      
	) AS PivotTable  
	) PW_PV 
ON PW.primaryKey = PW_PV.primaryKey
ORDER BY Row_number() over (partition by PW.primaryKey, PW.tenantName order by PW.dimensionModificationTime desc)

)
/************************************\
  FINAL SELECT
\************************************/
SELECT --src,
    primaryKey,
	Location_Type_Code = MAX(ISNULL(UPPER(Location_Type_Code),'''')),
	Put_Wall_Code = MAX(ISNULL(Put_Wall_Code,'''')),
	Put_Wall_Cubby_Code = MAX(ISNULL(putwall_cubby_code,'''')), 
	Module_Code =  ISNULL([group] ,''''),
	Area_Code = ISNULL(area,''''),
	Level_Code = ISNULL(level_code ,''''),
	Aisle_Code =  ISNULL(CASE WHEN SUBSTRING(Location_Code,3,2) = ''AI'' THEN SUBSTRING(Location_Code,5,2) ELSE '''' END,''''),
	Workstation_Code = MAX(CASE WHEN src = ''wst'' THEN ISNULL(id,'''') ELSE '''' END) 
	, Workstation_Type = MAX(Workstation_Type)
	, Workstation_Area = MAX(Workstation_Area)
	, Workstation_Group = MAX(Workstation_Group)
	, Workstation_Subgroup = MAX(Workstation_SubGroup)
    , Location_Code = ISNULL(location_code,'''')
	, Location_Name = ISNULL(OA.Location_Name_Generator(location_code),'''')
	, Relative_X_Axis = MAX(ISNULL(x ,''''))
	, Relative_Y_Axis = MAX(ISNULL(y ,''''))
	, Relative_Z_Axis = ''''
	, Side_Position = MAX(ISNULL(side,''''))
	, Slot_Position = ''''
	, Location_Group = ''''
	, Bay = MAX(ISNULL(subgroup ,''''))
	, Weight_Limit = MAX(ISNULL(weightLimit ,''''))
	, Location_Size = MIN(ISNULL(Location_Size ,''''))
	, Hazardous_Material_Allowed = MAX(CASE areHazourdousMaterialsAllowed WHEN  ''TRUE'' THEN ''1'' ELSE ''0'' END)
	, Bay_Width = MAX(ISNULL(bayWidth ,''''))
	, Effective_Begin_Date = ''''
	, Effective_End_Date = ''''
	, Source_System = '''+@lSourceSystem + '''
	, Active_Rec_Ind = ''1'' 
 FROM Locations
 --WHERE OA.Location_Name_Generator(location_code) !=''0''
 GROUP BY primaryKey, [group], area, level_Code, aisle_code, location_code
 '

 SET @strDropSQL = '
 DROP TABLE IF EXISTS STAGE.DIM_Location_'+ @lSourceSystem +'
 DROP TABLE IF EXISTS STAGE.DIM_Workstation_Location_'+ @lSourceSystem +'
 DROP TABLE IF EXISTS STAGE.DIM_Putwall_Location_'+ @lSourceSystem +'
'

EXEC sp_executesql @strTableDeclareSQL
EXEC sp_executesql @strLocationSQL
EXEC sp_executesql @strDropSQL

