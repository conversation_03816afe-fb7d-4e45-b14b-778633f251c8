SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
-- DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 
--DECLARE @lSourceSystem VARCHAR(50) = 'EudoraDiQ'; 

DECLARE @lQuery_SQLStmt NVARCHAR(MAX),
        @lChecks_SQLStmt NVARCHAR(MAX),
        @lDrops_SQLStmt NVARCHAR(MAX);

SELECT @lChecks_SQLStmt =  
' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
' where t.name = ''FCT_FLEXSORT_FAULT_DETAIL_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
' BEGIN 
	
    CREATE TABLE [STAGE].[FCT_FLEXSORT_FAULT_DETAIL_' + @lSourceSystem + '] (
    	[deviceID] [varchar](255) NULL,
    	[sensorID] [varchar](255) NULL,
    	[messageID] [varchar](255) NULL,
    	[messageSubType] [varchar](255) NULL,
    	[messageSequence] [varchar](255) NULL,
    	[systemName] [varchar](255) NULL,
    	[subSystemName] [varchar](255) NULL,
    	[operationCapability] [varchar](255) NULL,
    	[conveyorName] [varchar](255) NULL,
    	[vFDName] [varchar](255) NULL,
    	[vFDAlarmCode] [varchar](255) NULL,
    	[vFDAlarmDescription] [varchar](255) NULL,
    	[timeStamp] [varchar](255) NULL
    ) ON [PRIMARY]
			
 END' 
 
SELECT @lQuery_SQLStmt = '

   SELECT DISTINCT 
         Hour_Quarter_ID = LEFT(REPLACE(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),REPLACE(timeStamp, ''T'','' ''),25),''-'',''''),3,50),'' '',''''),'':'',''''),''T'',''''),8) + LTRIM(STR(DATEPART(minute,REPLACE(timeStamp, ''T'','' ''))/15))
         , Record_Timestamp = REPLACE(timeStamp, ''T'','' '')
		 , FlexSort_Component_Code = DeviceID  + ''-'' + ISNULL(systemName,''0'') + ''-'' + ISNULL(subsystemName,''0'') + ''-'' + ISNULL(conveyorName,''0'') + ''-'' + ISNULL(vfdName,''0'')
         , FlexSort_System = ISNULL(deviceID,'''')
		 , FlexSort_System_Name = ISNULL(systemName,'''')
		 , FlexSort_Functional_Subsystem = ISNULL(subsystemName,'''')
		 , FlexSort_Conveyor_Subsystem = ISNULL(conveyorName,'''')
		 , FlexSort_Conveyor_VFD = ISNULL(vFDName ,'''')
		 , FlexSort_Function_Type = CASE WHEN vfdName is not null
		                                    THEN ''VFD''
		                                 WHEN conveyorName is not null and vfdName is null
		                                    THEN ''Conveyor''
										 WHEN subsystemName like ''%sort%'' 
		                                    THEN ''Sorter''
										 WHEN subsystemName like ''%gap%'' 
		                                    THEN ''Gapper''
										 WHEN subsystemName like ''%tran%'' 
		                                    THEN ''Transport''
										 ELSE ''System'' END 
		, Status_Code = CASE WHEN systemName is not null 
		                        THEN messageSubType
							 WHEN operationCapability is not null
							    THEN operationCapability
							 WHEN conveyorName is not null and vFDName is null
							    THEN messageSubType
							 WHEN vFDName is not null
							    THEN ISNULL(vFDAlarmCode,'''')
							 ELSE ''''
						END                                                                                                                                                                                    
		, Status_Locale_Code = ''EN''
		, Status_Locale_Name = ISNULL(vFDAlarmDescription,'''')
		, Fault_Type_Code = CASE WHEN systemName is not null 
		                            THEN ''System''
								 WHEN messageSubType = ''subsystemCapability''
								    THEN ''Functional_Subsystem'' 
								 WHEN conveyorName is not null and vFDName is null
								    THEN ''Conveyor'' 
								 WHEN vFDName is not null
									THEN messageSubType
							 ELSE ''''
						END
		, Message_Sequence = messageSequence
		, Event = ISNULL(messageID,'''')
		, Subsystem_Code = ''Sorter''
		, Subsystem_Category = ''FlexSort''
	    , Source_System = '''+ @lSourceSystem +'''
    FROM [STAGE].[FCT_FLEXSORT_FAULT_DETAIL_'+ @lSourceSystem +'] WITH (NOLOCK)
 '
SELECT @lDrops_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_FLEXSORT_FAULT_DETAIL_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  DROP TABLE [STAGE].[FCT_FLEXSORT_FAULT_DETAIL_'+@lSourceSystem +']'

EXEC sp_executesql @lChecks_SQLStmt
EXEC sp_executesql @lQuery_SQLStmt
EXEC sp_executesql @lDrops_SQLStmt



