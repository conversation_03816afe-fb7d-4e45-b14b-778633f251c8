SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--SET @lSourceSystem = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX);
DECLAR<PERSON> @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Scanner_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 scannerId= '''', 	sortDeviceId= '''', 	scannerName= '''', 	scannerFunctionalType= '''',
			manufacturer= '''', hardwareType= '''', engineeredRate= '''', tenantName= '''',dimensionModificationTime=''''
		INTO STAGE.DIM_Scanner_' + @lSourceSystem + '
	END;'
EXEC sp_executesql @lSQLStmt


SET @Transform_SQLStmt = '
--DIM_Scanner
WITH Latest_Dim_Scanner AS (
	SELECT TOP 1 WITH TIES
		Scanner_Code = UPPER(scannerId),
		Sort_Device_Code = sortDeviceId,
		Scanner_Name = scannerName,
		Scanner_Functional_type = scannerFunctionalType,
		Scanner_Hardware_Type = hardwareType,
		Scanner_Manufacturer = manufacturer,
		EngineeredRate = engineeredRate,
		DC_Tenant_Name = UPPER(tenantName)
	FROM [STAGE].[DIM_Scanner_' + @lSourceSystem + ']
	ORDER BY Row_number() over (partition by scannerId, sortDeviceId, tenantName order by dimensionModificationTime desc)
)
SELECT
	Scanner_Code = ISNULL(Scanner_Code, ''''),
	Sort_Device_Code = ISNULL(Sort_Device_Code, ''''),
	Scanner_Name = ISNULL(Scanner_Name, ''''),
	Scanner_Functional_type = ISNULL(Scanner_Functional_type, ''''),
	Scanner_Hardware_Type = ISNULL(Scanner_Hardware_Type, ''''),
	Scanner_Manufacturer = ISNULL(Scanner_Manufacturer, ''''),
	EngineeredRate = ISNULL(EngineeredRate, ''''),
	DC_Tenant_Name = ISNULL(DC_Tenant_Name, ''''),
	Source_System = ''' + @lSourceSystem +''',
	Subsystem_Code = ''SORTER'',
	Subsystem_Category = ''DEMATIC IQ''
FROM Latest_Dim_Scanner
'

SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Scanner_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[DIM_Scanner_'+@lSourceSystem +']'


--PRINT(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt