SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50)  = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''FCT_WORKSTATIONWORKFLOWSTATUS_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
  
   ' SELECT DISTINCT ' +
   '       Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),eventTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,eventTime)/15))  ' +
   '     , Record_Timestamp = eventTime '       +
   '     , Workstation_Code = ISNULL(workstationPk, '''') ' +
   '     , Work_Type_Code = ISNULL(workflow, '''') ' +
   '     , Workstation_Status_Code = ISNULL(workStationStatus, '''') ' +
   '     , Work_Type_Status_Code = ISNULL(workFlowStatus, '''') ' +
   '     , Operator_Code = ISNULL(modUser, '''') ' +
   '     , Subsystem_Code =  ''GTP'' ' +
   '     , Subsystem_Category = ''DEMATIC IQ'' ' +
   '     , Source_System = ''' + @lSourceSystem +  '''' +
   ' FROM [STAGE].FCT_WORKSTATIONWORKFLOWSTATUS_'+ @lSourceSystem +
   ' ORDER BY Record_Timestamp, Workstation_Code, Work_Type_Code ' +
   ' END '  
    + '-- IF OBJECT_ID(''[STAGE].FCT_WORKSTATIONWORKFLOWSTATUS_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_WORKSTATIONWORKFLOWSTATUS_'+@lSourceSystem; 
				   
EXEC sp_executesql @lSQLStmt
