SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
-- DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lQuery_SQLStmt NVARCHAR(MAX),
        @lChecks_SQLStmt NVARCHAR(MAX),
        @lDrops_SQLStmt NVARCHAR(MAX);


SELECT @lChecks_SQLStmt =  
' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
' where t.name = ''FCT_ROBOT_ACTIVITY_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
' BEGIN 
		CREATE TABLE [STAGE].FCT_ROBOT_ACTIVITY_' + @lSourceSystem + ' (
			[autostore_id] [varchar](255) default '''',
			[event_time] [varchar](255)  default '''',
			[process_epoch_ns] [varchar](255) default '''',
			[robot_id] [varchar](255)  default '''',
			[event_type] [varchar](255)  default '''',
			[event] [varchar](255)  default '''',
			initial_download  [varchar](255)  default '''')
 END' 
		

SELECT @lQuery_SQLStmt = '
SELECT DISTINCT
    Hour_Quarter_ID = LEFT(REPLACE(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),event_time,25),''-'',''''),3,50),'' '',''''),'':'',''''),''T'',''''),8) + LTRIM(STR(DATEPART(minute,event_time)/15)),  
	Record_Timestamp = DATEADD(ns, CAST(RIGHT(ISNULL(process_epoch_ns,0), 6) AS INT), CAST(event_time AS datetime2(7))),
	Robot_Device_Code = ISNULL(robot_id,''''),
	Status_Code = '''',
	Status_Type = ''AutoStore'',
	Status_Category_Code = ''AutoStore'',
	Module_Code = ISNULL(autostore_id,''''),
	Module_Type = ''AutoStore'',
	Event = ISNULL([event],''''),
	Subsystem_Code = ''AutoStore'',
	Initial_Download_Flg = CASE WHEN initial_download = ''true'' THEN 1 ELSE 0 END,
	Source_System = '''+ @lSourceSystem +'''
FROM [STAGE].[FCT_ROBOT_ACTIVITY_'+ @lSourceSystem +'] WITH(NOLOCK)'


SELECT @lDrops_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_ROBOT_ACTIVITY_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  DROP TABLE [STAGE].[FCT_ROBOT_ACTIVITY_'+@lSourceSystem +']'


EXEC sp_executesql @lChecks_SQLStmt
EXEC sp_executesql @lQuery_SQLStmt
EXEC sp_executesql @lDrops_SQLStmt
