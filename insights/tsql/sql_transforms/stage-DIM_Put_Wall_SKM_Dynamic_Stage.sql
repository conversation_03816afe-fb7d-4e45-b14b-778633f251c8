SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware';
DECLARE @Tables_SQLStmt NVARCHAR(MAX); 
DECLARE @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);


SET @Tables_SQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Put_Wall_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		CREATE TABLE [STAGE].[DIM_Put_Wall_'+  @lSourceSystem +'](
	         [putWallPk] [varchar](255) NULL,
	         [putWallDescription] [varchar](255) NULL,
	         [tenantName] [varchar](255) NULL,
	         [dimensionModificationTime] [varchar](255) NULL,
			 [Key_ID] [int] IDENTITY(1,1) NOT NULL
         ) ON [PRIMARY]
	END;'

SET @Tables_SQLStmt = @Tables_SQLStmt + '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_PutWall_Location_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		CREATE TABLE [STAGE].[DIM_Putwall_Location_'+  @lSourceSystem +'](
	          [primaryKey] [varchar](255) NULL,
	          [putWallLocationType] [varchar](255) NULL,
	          [location] [varchar](255) NULL,
	          [packLocation] [varchar](255) NULL,
	          [putWall] [varchar](255) NULL,
	          [tenantName] [varchar](255) NULL,
	          [dimensionModificationTime] [varchar](255) NULL,
			  [Key_ID] [int] IDENTITY(1,1) NOT NULL
            ) ON [PRIMARY]
	END;'

EXEC sp_executesql @Tables_SQLStmt

/**********************************************************\
  Build surrogate working tables so the functional code
  of this transform can be viewed with text decorations
\**********************************************************/

SET @Transform_SQLStmt = '
WITH PutWall_Location AS (
   SELECT DISTINCT PutWall, TenantName
   FROM [STAGE].[DIM_PutWall_Location_'+ @lSourceSystem +']
   WHERE PutWall IS NOT NULL
)
Select Put_Wall_Code        = ISNULL(SPW.putWallPk, PWL.PutWall)
      ,Put_Wall_Name        = CASE WHEN SPW.putWallPK IS NOT NULL 
	                                  AND CHARINDEX(''-'', SPW.putWallPk) > 0
	                                  THEN RIGHT(SPW.putWallPk, LEN(SPW.putWallPk) - CHARINDEX(''-'',SPW.putWallPk))
                                   WHEN PWL.PutWall IS NOT NULL 
	                                  AND CHARINDEX(''-'', PWL.PutWall) > 0
	                                  THEN RIGHT(PWL.PutWall, LEN(PWL.PutWall) - CHARINDEX(''-'',PWL.PutWall))
							       ELSE ISNULL(SPW.putWallPk, PWL.PutWall)
							  END 
	  ,Put_Wall_Description = SPW.putWallDescription
      ,Source_System        = '''+  @lSourceSystem +'''
      ,Active_Rec_Ind       = ''1''
FROM [STAGE].[DIM_Put_Wall_'+ @lSourceSystem +'] SPW
   JOIN (SELECT putWallPK, tenantName = ISNULL(tenantName,''''), MaxDimModTime = MAX(dimensionModificationTime)
         FROM [STAGE].[DIM_Put_Wall_'+ @lSourceSystem +']
		 GROUP BY putWallPk, tenantName) MaxPutWall
   ON SPW.putWallPk = MaxPutWall.putWallPk 
   AND ISNULL(SPW.tenantName,'''') = ISNULL(MaxPutWall.tenantName,'''')
   FULL JOIN PutWall_Location PWL ON SPW.PutWallPk = PWL.putWall
      AND ISNULL(SPW.TenantName,'''') = ISNULL(PWL.TenantName,'''')
GROUP BY SPW.putWallPk, SPW.putWallDescription, SPW.tenantName, PWL.putWall
ORDER BY Put_Wall_Code ASC 
'

SELECT @Drop_SQLStmt = 
' DROP TABLE IF EXISTS [STAGE].[DIM_Put_Wall_'+ @lSourceSystem +']
'
-- Put_Wall_Location stage table dropped in the Location Transform

EXEC sp_executesql @Transform_SQLStmt
EXEC(@Drop_SQLStmt)


