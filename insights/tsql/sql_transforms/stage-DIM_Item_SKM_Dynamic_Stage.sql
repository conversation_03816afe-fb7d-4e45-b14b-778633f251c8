SET NOCOUNT ON
DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'

DECLARE @lSQLStmt1 NVARCHAR(MAX), @lChecks_SQLStmt NVARCHAR(MAX), @lDrops_SQLStmt NVARCHAR(MAX)

SELECT @lChecks_SQLStmt =  
					' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                    ' where t.name = ''SkuDimension_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
					' BEGIN ' +
                    '    CREATE TABLE [STAGE].[SkuDimension_'+ @lSourceSystem +']( ' +
                    '	[baseQuantityUnit] [varchar](255) NULL,  ' +
                    '	[client] [varchar](255) NULL, ' +
                    '	[description] [varchar](255) NULL, ' +
                    '	[dimPrimaryKey] [varchar](255) NULL, ' +
                    '	[dispatchPackingGroup] [varchar](255) NULL, ' +
                    '	[hazardClassification] [varchar](255) NULL, ' +
                    '	[loadUnitType] [varchar](255) NULL, ' +
                    '	[mainProductRangeId] [varchar](255) NULL, ' +
                    '	[maximumUnitsPerPick] [varchar](255) NULL, ' +
                    '	[owner] [varchar](255) NULL, ' +
                    '	[palletizingMode] [varchar](255) NULL, ' +
                    '	[preferredLoadUnitType] [varchar](255) NULL, ' +
                    '	[primaryKey] [varchar](255) NULL, ' +
                    '	[productCode] [varchar](255) NULL, ' +
                    '	[shelfLife] [varchar](255) NULL, ' +
                    '	[status] [varchar](255) NULL, ' +
                    '	[supplierId] [varchar](255) NULL, ' +
                    '	[velocityClassification] [varchar](255) NULL, ' +
					'	[dimensionModificationTime] [varchar](255) NULL ' +
                    ') ON [PRIMARY] '+
					' END ' +
					
					' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                    ' where t.name = ''SKUProductCodeDimension_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
					' BEGIN  ' +
                    '    CREATE TABLE [STAGE].[SKUProductCodeDimension_' + @lSourceSystem + ']( ' +
                    '     	[dimPrimaryKey] [varchar](255) NULL, ' +
                    '     	[primaryKey] [varchar](255) NULL, ' +
                    '     	[productCode] [varchar](255) NULL, ' +
                    '     	[quantityUnit] [varchar](255) NULL, ' +
                    '     	[client] [varchar](255) NULL, ' +
					'	    [dimensionModificationTime] [varchar](255) NULL ' +
                    '     ) ON [PRIMARY] ' +
					'  END ' + 

					' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                    ' where t.name = ''SKUQuantityUnitDimension_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
					' BEGIN  ' +
					'    CREATE TABLE [STAGE].[SKUQuantityUnitDimension_' + @lSourceSystem + ']( '+
                    '    	[amcapQualification] [varchar](255) NULL, '+
                    '    	[defaultPickQuantityUnit] [varchar](255) NULL, '+
                    '    	[dimPrimaryKey] [varchar](255) NULL, '+
                    '    	[factorToBaseQU] [varchar](255) NULL, '+
                    '    	[height] [varchar](255) NULL, '+
                    '    	[length] [varchar](255) NULL, '+
                    '    	[preferredFulfillmentHandlingType] [varchar](255) NULL, '+
                    '    	[primaryKey] [varchar](255) NULL, '+
                    '    	[quantityUnitId] [varchar](255) NULL, '+
                    '    	[sku] [varchar](255) NULL, '+
                    '    	[totalVolume] [varchar](255) NULL, '+
                    '    	[totalWeight] [varchar](255) NULL, '+
                    '    	[width] [varchar](255) NULL, '+
                    '    	[client] [varchar](255) NULL, '+             	
					'	    [dimensionModificationTime] [varchar](255) NULL ' +
                    '     ) ON [PRIMARY] '+
					' END ' 

SET @lSQLStmt1 = '
WITH SKU AS (
  Select TOP 1 WITH TIES
	  SKUID = CASE WHEN CHARINDEX(''#'',SKU.primaryKey)> 0 THEN PARSENAME( REPLACE(SKU.primaryKey,''#'',''.''),1) ELSE SKU.primaryKey END
      ,SKU.[client]
	  ,SKU.[baseQuantityUnit]
      ,SKU.[description]
      ,SKU.[dimPrimaryKey]
      ,SKU.[dispatchPackingGroup]
      ,SKU.[hazardClassification]
      ,SKU.[loadUnitType]
      ,SKU.[mainProductRangeId]
      ,SKU.[maximumUnitsPerPick]
      ,SKU.[owner]
      ,SKU.[palletizingMode]
      ,SKU.[preferredLoadUnitType]
      ,SKU.[primaryKey]
      ,SKU.[productCode]
      ,SKU.[shelfLife]
      ,SKU.[status]
      ,SKU.[supplierId]
      ,SKU.[velocityClassification]
	FROM [STAGE].[SkuDimension_' + @lSourceSystem + '] SKU
	ORDER BY Row_number() over (partition by SKU.primaryKey order by dimensionModificationTime desc)
 )
, SKUProdCode as (
  --SKUProdCode
  SELECT TOP 1 WITH TIES
      ProdCode.dimPrimaryKey 
      , ProdCode.primaryKey
	  , ProdCode.productCode
	  , ProdCode.quantityUnit
	  , Client = CASE WHEN CHARINDEX(''#'',ProdCode.primaryKey)> 0 THEN PARSENAME( REPLACE(ProdCode.primaryKey,''#'',''.''),4) ELSE ProdCode.primaryKey END
	  , UOM = CASE WHEN CHARINDEX(''#'',ProdCode.primaryKey)> 0 THEN PARSENAME( REPLACE(ProdCode.primaryKey,''#'',''.''),2) ELSE ProdCode.primaryKey END
	  , SKUID = CASE WHEN CHARINDEX(''#'',ProdCode.primaryKey)> 0 THEN PARSENAME( REPLACE(ProdCode.primaryKey,''#'',''.''),3) ELSE ProdCode.primaryKey END
  FROM [STAGE].[SKUProductCodeDimension_' + @lSourceSystem + '] ProdCode
  ORDER BY Row_number() over (partition by Primarykey order by dimensionModificationTime desc)
)
, SKUUOM as (
--quantity
	Select TOP 1 WITH TIES 
	      Client = ISNULL(Client, CASE WHEN CHARINDEX(''#'',UOM.primaryKey)> 0 THEN PARSENAME( REPLACE(UOM.primaryKey,''#'',''.''),3) ELSE UOM.primaryKey END)
		  , UOM = ISNULL(quantityUnitId, CASE WHEN CHARINDEX(''#'',UOM.primaryKey)> 0 THEN PARSENAME( REPLACE(UOM.primaryKey,''#'',''.''),1) ELSE UOM.primaryKey END)
		  , SKUID = CASE WHEN CHARINDEX(''#'',UOM.primaryKey)> 0 THEN PARSENAME( REPLACE(UOM.primaryKey,''#'',''.''),2) ELSE UOM.primaryKey END
		  , amcapQualification
		  , defaultPickQuantityUnit
		  , dimPrimaryKey
		  , factorToBaseQU
		  , height
		  , length
		  , preferredFulfillmentHandlingType
		  , UOM.primaryKey
		  , sku
		  , totalVolume
		  , totalWeight
		  , width
	from [STAGE].[SKUQuantityUnitDimension_' + @lSourceSystem + '] UOM
	ORDER BY Row_number() over (partition by Primarykey order by dimensionModificationTime desc)
)
SELECT 
	   [Item_Code] = OA.Item_Code_Generator([Item_SKU],[Item_Product_Code],[UOM],[Client])
      ,[Item_Category_Code] = MAX([Item_Category_Code]) 
      ,[Item_Name] =  ISNULL([Item_Name],'''') 
      ,[Item_SKU] =  ISNULL([Item_SKU],'''') 
      ,[Item_Product_Code] =  ISNULL([Item_Product_Code],'''')
      ,[UOM] = ISNULL(UOM,'''')
      ,[Client] = ISNULL(Client,'''')
      ,[Total_Weight] = max(Total_Weight)
      ,[Total_Volume] = max(Total_Volume)
      ,[Length] = max(Length)
      ,[Width]  = max(Width)
      ,[Height]  =max(Height)
      ,[Palletizing_Mode]  = ISNULL(Palletizing_Mode,'''')
      ,[Max_Units_Per_Pick]  = ISNULL(Max_Units_Per_Pick,'''')
      ,[Item_Shelf_Life_In_Days]  = ISNULL(Item_Shelf_Life_In_Days,'''')
      ,[Preferred_Container_Type] = ISNULL(Preferred_Container_Type,'''')
      ,[Velocity_Classification] = ISNULL(velocity_Classification,'''')
      ,[Hazard_Classification]  = ISNULL(Hazard_Classification,'''')
      ,[Supplier_Code] = ISNULL(Supplier_Code,'''')
	  ,[Base_UOM] = ISNULL([Base_UOM],'''') 
	  ,[Default_Pick_UOM]
	  --,defaultPickQuantityUnit
      ,[Preferred_Fulfillment_Handling]  = ISNULL(Preferred_Fulfillment_Handling,'''')
      ,[AMCAP_Qualification_Ind] = ISNULL(AMCAP_Qualification_Ind,'''')
      ,[Toppling_Rate] = ISNULL(Toppling_Rate,'''')
      ,[Source_System] 
      ,[Active_Rec_Ind]
FROM 
	(
	SELECT [Item_Category_Code] = ISNULL(Item_Category_Code,'''')
		  ,[Item_Name] = ISNULL([description],'''')
		  ,[Item_SKU] = SKUID
		  ,[Item_Product_Code] = productCode
		  ,[UOM] = UOM
		  ,[Client] = client
		  ,[Total_Weight] = ISNULL(totalWeight,'''')
		  ,[Total_Volume] = ISNULL(totalVolume,'''')
		  ,[Length] = ISNULL(length,'''')
		  ,[Width] = ISNULL(width,'''')
		  ,[Height] = ISNULL(height,'''')
		  ,[Palletizing_Mode] = ISNULL(palletizingMode,'''')
		  ,[Max_Units_Per_Pick] = ISNULL(maximumUnitsPerPick,'''')
		  ,[Item_Shelf_Life_In_Days] = ISNULL(shelfLife,'''')
		  ,[Preferred_Container_Type] = ISNULL(preferredLoadUnitType,'''')
		  ,[Velocity_Classification] = ISNULL(velocityClassification,'''')
		  ,[Hazard_Classification] = ISNULL(hazardClassification,'''')
		  ,[Supplier_Code] = ISNULL(supplierId,'''')
		  ,[Base_UOM] = ISNULL(baseQuantityUnit,'''')
		  , defaultPickQuantityUnit
		  ,[Default_Pick_UOM] = CASE WHEN defaultPickQuantityUnit = ''false'' THEN 0
									 WHEN defaultPickQuantityUnit = ''true'' THEN 1
	                    		ELSE defaultPickQuantityUnit END
		  ,[Preferred_Fulfillment_Handling] = ISNULL(preferredFulfillmentHandlingType,'''')
		  ,[AMCAP_Qualification_Ind] = ISNULL(amcapQualification,'''')
		  ,[Toppling_Rate] = ''''
		  ,[Source_System] = ''' + @lSourceSystem + '''
		  ,[Active_Rec_Ind] = ISNULL(''1'','''')
	FROM (
		SELECT 
				SKUID = ISNULL(SKU.SKUID, ISNULL(SKUProdCode.SKUID, SKUUOM.SKUID))
			  , Client = ISNULL(SKU.Client, ISNULL(SKUProdCode.Client, SKUUOM.Client))
			  , UOM = ISNULL(SKUProdCode.UOM, SKUUOM.UOM)
			  , SKUProdCode.ProductCode 
			  , SKU.baseQuantityUnit
			  , SKU.description
			  , SKU.dimPrimaryKey
			  , Item_Category_Code = SKU.dispatchPackingGroup
			  , SKU.hazardClassification
			  , SKU.loadUnitType
			  , SKU.mainProductRangeId
			  , SKU.maximumUnitsPerPick
			  , SKU.owner
			  , SKU.palletizingMode
			  , SKU.preferredLoadUnitType
			  , SKU.shelfLife
			  , SKU.status
			  , SKU.supplierId
			  , SKU.velocityClassification
			  , SKUProdcode.quantityUnit
			  , SKUUOM.amcapQualification
			  , SKUUOM.defaultPickQuantityUnit
			  , SKUUOM.factorToBaseQU
			  , SKUUOM.height
			  , SKUUOM.length
			  , SKUUOM.preferredFulfillmentHandlingType
			  , SKUUOM.sku
			  , SKUUOM.totalVolume
			  , SKUUOM.totalWeight
			  , SKUUOM.width
		 FROM SKUProdCode 
				 FULL JOIN SKUUOM ON
					SKUProdCode.SKUID = SKUUom.SKUID
					AND SKUProdCode.Client = SKUUOM.Client
					AND SKUProdCode.UOM = SKUUOM.UOM 
				FULL OUTER JOIN SKU ON 
					SKU.SKUID =  SKUUom.SKUID
					AND SKU.Client = SKUUom.Client
					
	) ITEM
) Item_Dim
GROUP BY 
       Item_SKU
	  ,Item_Name
      ,[UOM]
      ,[Client]
      ,[Source_System]
      ,[Active_Rec_Ind]
	  ,[Item_Product_Code]
      ,[Palletizing_Mode]
      ,[Max_Units_Per_Pick]
      ,[Item_Shelf_Life_In_Days]
      ,[Preferred_Container_Type]
      ,[Velocity_Classification] 
      ,[Hazard_Classification]  
      ,[Supplier_Code] 
	  ,[Base_UOM] 
      ,[Preferred_Fulfillment_Handling]
      ,[AMCAP_Qualification_Ind]
      ,[Toppling_Rate] 
      ,[Source_System]
      ,[Active_Rec_Ind]	  	  
	  ,[Default_Pick_UOM]
	  ,defaultPickQuantityUnit
'

SELECT @lDrops_SQLStmt = ' IF OBJECT_ID(''[STAGE].SkuDimension_' + @lSourceSystem + ''', ''U'') IS NOT NULL  DROP TABLE [STAGE].SkuDimension_'+@lSourceSystem +
					     ' IF OBJECT_ID(''[STAGE].SKUProductCodeDimension_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].SKUProductCodeDimension_'+@lSourceSystem + 
					     ' IF OBJECT_ID(''[STAGE].SKUQuantityUnitDimension_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].SKUQuantityUnitDimension_'+@lSourceSystem					 
					 
EXEC sp_executesql @lChecks_SQLStmt
	     
EXEC sp_executesql @lSQLStmt1

EXEC sp_executesql @lDrops_SQLStmt