SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)'; 
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' + 
                   ' where t.name = ''EXTR_Fct_Equipment_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
				   ' BEGIN ' +
                   ' WITH CTE_Equipment AS ' + 
                   '    ( SELECT DISTINCT Record_Timestamp = DateAdd(ms,0,REPLACE(REPLACE(recordTimestamp,''T'','' ''), ''Z'','''')) ' +
                   ' , OPC_Tag_Code = metric ' +
   		           ' /* The formatting below converts whole numbers expressed as decimals to simple whole numbers, removing the trailing zeros */ ' +
                   ' , [Value] = ISNULL(CASE WHEN CHARINDEX(''.'',[Value]) > 0 AND RIGHT([Value],1)=''0'' THEN ' +
   				   '	REPLACE(LEFT([Value],CHARINDEX(''.'',[Value])),''.'','''') + ' +
				   '       CASE WHEN  REVERSE(LTRIM(STR(CAST(REPLACE(LEFT(reverse([Value]),CHARINDEX(''.'',reverse([Value]))),''.'','''') as int)))) > 0 ' +
   				   '            THEN  ''.''+REVERSE(LTRIM(STR(CAST(REPLACE(LEFT(reverse([Value]),CHARINDEX(''.'',reverse([Value]))),''.'','''') as int)))) ' +
   				   '			ELSE '''' END ' +
   				   '            ELSE [Value] ' +
   				   '       END, '''') ' +
                   ' , Monitor_Device_Code = ISNULL(dimensions_sensorID,'''') ' +
                   ' , Monitor_Device_Description = ISNULL(CASE WHEN dimensions_sensorID IS NOT NULL ' +
                   '   THEN dimensions_sensorName ELSE ''''  END,'''')  ' + 
                   ' , Monitor_Device_Type = CASE WHEN dimensions_sensorID IS NOT NULL  ' +
                   '  THEN ''IoT Sensor'' ELSE '''' END ' +
                   ' , Battery_Percent =  ISNULL(dimensions_BatteryLevel, '''') ' +
                   ' , Voltage = ISNULL(dimensions_Voltage, '''') ' + 
                   ' , Signal_Strength = ISNULL(dimensions_SignalStrength, '''') '  +
                   ' , Measurement_Code = ISNULL(dimensions_dataType, '''') ' +
                   ' , Unit_Of_Measure = ISNULL(dimensions_plotLabels, '''') ' +
                   ' , Source_System = '''+@lSourceSystem + '''' +
     ' FROM Stage.EXTR_FCT_EQUIPMENT_' + @lSourceSystem +' SFC) ' +
   ' SELECT Hour_Quarter_ID =  RIGHT(''00'' + CAST(DATEPART(year,Record_Timestamp) AS VARCHAR(4)),2)  /*year*/ ' +
   '                         +  RIGHT(''00'' + CAST(DATEPART(month,Record_Timestamp) AS VARCHAR(2)),2)  /* month*/ ' +
   '                         +  RIGHT(''00'' + CAST(DATEPART(day,Record_Timestamp) AS VARCHAR(2)),2)  /* day */ ' +
   '                         +  RIGHT (''00'' + CAST(DATEPART(hour, CASE WHEN DATEPART(ms,Record_Timestamp) = 999 ' +
   '                         THEN DATEADD(ms,-1,Record_Timestamp) ELSE Record_Timestamp END ) AS VARCHAR(2)),2) ' +
   '                       + RIGHT (''00'' + CAST(DATEPART(minute,Record_Timestamp) AS VARCHAR(2))/15,1) ' +
   ' , Record_Timestamp , OPC_Tag_Code ' +
   ' /* The formatting below converts whole numbers expressed as decimals to simple whole numbers, removing the trailing zeros */ ' +
            ', [Value] ' +
            ', Monitor_Device_Code ' +
            ', Monitor_Device_Description ' +
            ', Monitor_Device_Type ' +
            ', Battery_Percent ' + 
            ', Voltage ' +
            ', Signal_Strength ' +
            ', Measurement_Code ' +
            ', Unit_Of_Measure ' +
            ', Source_System ' +
   ' from CTE_Equipment ' +
   ' ORDER BY Hour_Quarter_ID, Record_Timestamp, OPC_Tag_Code ASC END ' +
   ' IF OBJECT_ID(''[STAGE].EXTR_FCT_EQUIPMENT_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].EXTR_FCT_EQUIPMENT_'+@lSourceSystem; 
   
EXEC sp_executesql @lSQLStmt