SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(100) = '$(Source_System)'
--DECLARE @lSourceSystem VARCHAR(100) = 'DematicSoftware'
DECLARE @lSQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id 
where t.name = ''FCT_Shuttle_Statistics_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
	-- Insert into copy table
	IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Device_Statistics_Fault_Detail_' + @lSourceSystem + ''' and s.name = ''Stage'')
		INSERT INTO STAGE.FCT_Device_Statistics_Fault_Detail_'+ @lSourceSystem + ' 
			SELECT 
				deviceid,eventTime,faultCode1stImportantFault,quantity1stImportantFault,faultCode2ndImportantFault, quantity2ndImportantFault
				,faultCode3rdImportantFault, quantity3rdImportantFault,faultCode4thImportantFault, quantity4thImportantFault,faultCode5thImportantFault, quantity5thImportantFault
				,faultCode6thImportantFault, quantity6thImportantFault,faultCode7thImportantFault, quantity7thImportantFault,faultCode8thImportantFault, quantity8thImportantFault
				,faultCode9thImportantFault, quantity9thImportantFault,faultCode10thImportantFault, quantity10thImportantFault 	
			FROM STAGE.FCT_Shuttle_Statistics_' + @lSourceSystem + ';
	ELSE
		SELECT 
			deviceid,eventTime,faultCode1stImportantFault,quantity1stImportantFault,faultCode2ndImportantFault, quantity2ndImportantFault
			,faultCode3rdImportantFault, quantity3rdImportantFault,faultCode4thImportantFault, quantity4thImportantFault,faultCode5thImportantFault, quantity5thImportantFault
			,faultCode6thImportantFault, quantity6thImportantFault,faultCode7thImportantFault, quantity7thImportantFault,faultCode8thImportantFault, quantity8thImportantFault
			,faultCode9thImportantFault, quantity9thImportantFault,faultCode10thImportantFault, quantity10thImportantFault 
		INTO STAGE.FCT_Device_Statistics_Fault_Detail_'+ @lSourceSystem + ' FROM STAGE.FCT_Shuttle_Statistics_' + @lSourceSystem + ';	

	SELECT 
		Record_Timestamp = eventTime
		, Hour_Quarter_ID=   substring(Replace(Replace(CONVERT(VARCHAR(13), eventTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), eventTime,120),15,2) as INT)/15))
		, Device_Code = deviceId 
		, Physical_Device_Code = MAX(ISNULL(hardwareId,''''))
		, Storage_Count = MAX(ISNULL(storageCycles, ''''))
		, Retrieval_Count = MAX(ISNULL(retrievalCycles, ''''))
		, Shuffle_Count = MAX(ISNULL(reShufflingCycles, ''''))
		, IAT_Count = MAX(ISNULL(iatCycles, ''''))
		, Bypass_Count = MAX(ISNULL(byPassCycles, ''''))
		, X_Travel_Distance_Meters = MAX(ISNULL(xtravelDistance, ''''))
		, Z_Travel_Distance_Meters = MAX(ISNULL(ztravelDistance, ''''))
		, W_Travel_Distance_Meters = MAX(ISNULL(wtravelDistance, ''''))
		, Finger_Moves_Pair_1 = MAX(ISNULL(fingerMovesPair1, ''''))
		, Finger_Moves_Pair_2 = MAX(ISNULL(fingerMovesPair2, ''''))
		, Finger_Moves_Pair_3 = MAX(ISNULL(fingerMovesPair3, ''''))
		, Finger_Moves_Pair_4 = MAX(ISNULL(fingerMovesPair4, ''''))
		, Down_Time_Minutes = MAX(ISNULL(downtime, ''''))
		, Active_Time_Minutes = MAX(ISNULL(timeActive, ''''))
		, Run_Time_Minutes = MAX(ISNULL(timeRunning, ''''))
		, Fault_Count = MAX(ISNULL(numberOfFaults, ''''))
		, Warning_Count = MAX(ISNULL(numberOfWarnings, ''''))
		, Source_System = ''' + @lSourceSystem +  '''
		, Subsystem_Code = ''MULTISHUTTLE''
		, Subsystem_Category = ''DEMATIC IQ''
	FROM [STAGE].FCT_Shuttle_Statistics_'+ @lSourceSystem + '
	GROUP BY eventTime, deviceId
END'


EXEC sp_executesql @lSQLStmt


SET @lSQLStmt = '
DROP TABLE IF EXISTS STAGE.FCT_Shuttle_Statistics_'+ @lSourceSystem 

EXEC sp_executesql @lSQLStmt
