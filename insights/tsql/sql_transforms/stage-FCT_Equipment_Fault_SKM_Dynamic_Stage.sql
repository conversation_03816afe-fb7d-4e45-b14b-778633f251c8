SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)'; 
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' + 
                   ' where t.name = ''VFD_DATA_POINT_FAULT_MSG_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' + 
				   ' SELECT DISTINCT Record_Timestamp = D.[DateTime] '      +       
				   '     , Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),D.[DateTime],25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,D.[DateTime])/15))  ' +
                   '     , Device_Code = D.DeviceID ' +
                   '     , Fault_Status_Code = D.FaultCode  ' +
                   '	 , Device_Status_Code = D.StatusCode ' +
                   '	 , Subsystem_Code = ''SORT'' ' +
                   '	 , Subsystem_Category = ''APE'' ' +
                   '	 , Fault_Number  ' +
				   '	 , Seq_Num = '''' ' +
                   '	 , Frequency  ' + 
                   '	 , Device_Current  ' +
                   '	 , Voltage ' +
                   '	 , Source_System = ''' + @lSourceSystem +  '''' +
				   ' FROM [STAGE].VFD_DATA_POINT_FAULT_MSG_'+ @lSourceSystem + 
				   ' CROSS APPLY (VALUES ([DateTime], DeviceID, F1FaultCode, F1Frequency, F1Current, F1Voltage, F1Status, ''F1''), ' +
                   ' ([DateTime], DeviceID, F2FaultCode, F2Frequency, F2Current, F2Voltage, F2Status, ''F2''), ' +
				   ' ([DateTime], DeviceID, F3FaultCode, F3Frequency,F3Current, F3Voltage, F3Status, ''F3'')) ' +
			       ' AS D([Datetime],DeviceID, FaultCode, Frequency, Device_Current, Voltage, StatusCode,Fault_Number) ' + 
				   ' END ' +
				   --' DROP TABLE IF EXISTS [STAGE].VFD_DATA_POINT_FAULT_MSG_' + @lSourceSystem
				   ' IF OBJECT_ID(''[STAGE].VFD_DATA_POINT_FAULT_MSG_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].VFD_DATA_POINT_FAULT_MSG_'+@lSourceSystem;

EXEC sp_executesql @lSQLStmt;

