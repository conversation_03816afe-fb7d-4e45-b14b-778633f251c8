SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)', 
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware',
        @lPrimary_SQLStmt NVARCHAR(MAX),
		@lSecondary_SQLStmt NVARCHAR(MAX);

SELECT @lPrimary_SQLStmt = 
' DECLARE @lStatement NVARCHAR(MAX) = '''',
          @lIncludeUnion BIT = 0;

IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' + 
           ' where t.name = ''ETHERNET_COMM_MSG_' + @lSourceSystem + ''' and s.name = ''Stage'') '  +
		   
' BEGIN ' + 
    
	' SELECT @lstatement = ''SELECT DISTINCT Record_Timestamp = [Datetime], '' + 
	  '' Hour_Quarter_ID = RIGHT(LEFT(REPLACE(REPLACE(REPLACE(CONVERT(VARCHAR(24),[Datetime],121),'''':'''',''''''''),''''-'''',''''''''),'''' '''',''''''''),10),8) + LTRIM(STR(datepart(minute,[Datetime]))/15), '' +
	  '' Device_Code = DeviceID '' +
      '' ,Subsystem_Code = ''''SORT'''' '' +
	  '' ,Subsystem_Category = ''''APE'''' '' +
	  '' ,Measurement_Code = '''''''' '' +
	  '' , Receive_Overrun_Count = ISNULL(RxOverruns,'''''''') ''+ 
	  '' , Receive_Packet_Count = ISNULL(RxPackets,'''''''') ''+ 
	  '' , Receive_Packet_Error_Count = ISNULL(RxErrors,'''''''') ''+ 
	  '' , Transmit_Packet_Count = ISNULL(TxPackets,'''''''') ''+ 
	  '' , Transmit_Packet_Error_Count = ISNULL(TxErrors,'''''''') ''+ 
	  '' , Missing_IO_Packet_Count = ISNULL(MissingIO,'''''''') ''+ 
	  '' , Not_Received_Error_Count = '''''''' '' + 
	  '' , Not_Found_Error_Count = '''''''' '' + 
	  '' , Source_System = ''''' + @lSourceSystem + ''''''' +
	  '' FROM STAGE.ETHERNET_COMM_MSG_'+ @lSourceSystem + '''' + 

	  ' SET @lIncludeUnion = ''1''' +
	      
' END  '+

'IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' + 
           ' where t.name = ''SORTER_COMM_DATA_POINT_MSG_' + @lSourceSystem + ''' and s.name = ''Stage'') '  +
		   
' BEGIN ' + 

     ' IF @lIncludeUnion = ''1'' '+
	 ' BEGIN SET @lstatement = @lstatement + '' UNION ALL '' END' + 

	' SELECT @lstatement = @lstatement + ''SELECT DISTINCT Record_Timestamp = [Datetime], '' + 
	  '' Hour_Quarter_ID = RIGHT(LEFT(REPLACE(REPLACE(REPLACE(CONVERT(VARCHAR(24),[Datetime],121),'''':'''',''''''''),''''-'''',''''''''),'''' '''',''''''''),10),8) + LTRIM(STR(datepart(minute,[Datetime]))/15) '' +
	  '' ,Device_Code = DeviceID '' +
      '' ,Subsystem_Code = ''''SORT'''' '' +
	  '' ,Subsystem_Category = ''''APE'''' '' +
	  '' ,Measurement_Code = ''''Dispatch'''' '' +
	  '' , Receive_Overrun_Count = '''''''' ''+ 
	  '' , Receive_Packet_Count = ISNULL(DispatchReceivedCount,'''''''') ''+ 
	  '' , Receive_Packet_Error_Count = '''''''' ''+ 
	  '' , Transmit_Packet_Count = ISNULL(DispatchTransCount,'''''''') ''+ 
	  '' , Transmit_Packet_Error_Count = '''''''' ''+ 
	  '' , Missing_IO_Packet_Count = '''''''' ''+ 
	  '' , Not_Received_Error_Count = ISNULL(DispatchNotReceiveCount,'''''''') ''+ 
	  '' , Not_Found_Error_Count = ISNULL(DispatchNotFoundCount,'''''''') ''+ 
	  '' , Source_System = ''''' + @lSourceSystem + ''''''' +
	  '' FROM STAGE.SORTER_COMM_DATA_POINT_MSG_'+ @lSourceSystem + ''' + 
	  '' UNION '' +
	  '' SELECT DISTINCT Record_Timestamp = [Datetime], '' + 
	  '' Hour_Quarter_ID = RIGHT(LEFT(REPLACE(REPLACE(REPLACE(CONVERT(VARCHAR(24),[Datetime],121),'''':'''',''''''''),''''-'''',''''''''),'''' '''',''''''''),10),8) + LTRIM(STR(datepart(minute,[Datetime]))/15) '' +
	  '' ,Device_Code = DeviceID '' +
      '' ,Subsystem_Code = ''''SORT'''' '' +
	  '' ,Subsystem_Category = ''''APE'''' '' +
	  '' ,Measurement_Code = ''''Divert'''' '' +
	  '' , Receive_Overrun_Count = '''''''' ''+ 
	  '' , Receive_Packet_Count = ISNULL(DivertReceiveCount,'''''''') ''+ 
	  '' , Receive_Packet_Error_Count = '''''''' ''+ 
	  '' , Transmit_Packet_Count = ISNULL(DivertTransCount,'''''''') ''+ 
	  '' , Transmit_Packet_Error_Count = '''''''' ''+ 
	  '' , Missing_IO_Packet_Count = '''''''' ''+ 
	  '' , Not_Received_Error_Count = ISNULL(DivertNotReceiveCount,'''''''') ''+ 
	  '' , Not_Found_Error_Count = ISNULL(DivertNotFoundCount,'''''''') ''+ 
	  '' , Source_System = ''''' + @lSourceSystem + ''''''' +
	  '' FROM STAGE.SORTER_COMM_DATA_POINT_MSG_'+ @lSourceSystem + ''' '+ 

' END ' +

' EXEC sp_executesql @lstatement ; ' 

SET @lSecondary_SQLStmt = ' IF OBJECT_ID(''[STAGE].ETHERNET_COMM_MSG_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].ETHERNET_COMM_MSG_'+@lSourceSystem;

EXEC sp_executesql @lPrimary_SQLStmt;
EXEC sp_executesql @lSecondary_SQLStmt;