SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--SET @lSourceSystem = 'DematicSoftware'; 
DECLARE @Tables_SQLStmt  NVARCHAR(MAX);
DECLAR<PERSON> @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);


SET @Tables_SQLStmt = ' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Operator_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN   SELECT TOP 0 userName= '''', fullName= '''', description= '''', locked= '''', 	emailAddress= '''', telephoneNumber= ''''
               , mobileTelephoneNumber= '''',  locale= '''', tenantName= '''', primaryKey= '''', dimensionModificationTime= ''''
		INTO STAGE.DIM_Operator_' + @lSourceSystem + '
END;'
--Select @Tables_SQLStmt
EXEC sp_executesql @Tables_SQLStmt


SET @Transform_SQLStmt = '

	WITH Operators AS (
		SELECT TOP 1 WITH TIES 
		  
			Operator_Code = ISNULL(primaryKey,ISNULL(userName,'''')),
			Operator_Name = ISNULL(userName,''''),
			Operator_Description = description,
			DC_Tenant_Name = ISNULL(UPPER(tenantName), ''''),
			Operator_Full_Name = fullName,
			Source_System = ''' + @lSourceSystem +''',
			Active_Rec_Ind = 1,
			FS_LEN = LEN(ISNULL(fullName,'''') + ISNULL(description,''''))
		FROM [STAGE].[DIM_Operator_' + @lSourceSystem + ']
		ORDER BY Row_number() over (partition by userName, tenantName order by dimensionModificationTime desc)
	)
	, Pre_Processed_Operators AS (
	SELECT DISTINCT
		Operator_Code=Operators.Operator_Code,
		Operator_Name,
		Operator_Description = CASE WHEN DB_LEN >= FS_LEN THEN Operator_Description_Exist ELSE Operator_Description END,
		DC_Tenant_Name,
		Operator_Full_Name,
		Source_System,
		Active_Rec_Ind,
		Start_Date = ISNULL(Start_Date_Exist, GETDATE()),
		End_Date = ISNULL(End_Date_Exist,GETDATE())
	FROM Operators
	LEFT OUTER JOIN (
		SELECT
			DB_LEN = LEN(ISNULL(Operator_Full_Name,'') + ISNULL(Operator_Description,'')),
			Operator_Description_Exist=Operator_Description, Operator_Code,
			Active_Rec_Ind_Exists = Active_Rec_Ind, Start_Date_Exist = Start_Date, End_Date_Exist = End_Date
		FROM OA.DIM_Operator WITH(NOLOCK) 
		WHERE Operator_Code IS NOT NULL AND Source_System = ''' + @lSourceSystem +'''
		) AS DB_Operators 
		ON Operators.Operator_Code = DB_Operators.Operator_Code
	)
	SELECT 
		Operator_Code,
		Operator_Name,
		Operator_Description = ISNULL(Operator_Description, ''''),
		DC_Tenant_Name,
		Operator_Full_Name,
		Source_System,
		Active_Rec_Ind,
		Start_Date,
		End_Date
	FROM Pre_Processed_Operators

'
SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Operator_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[DIM_Operator_'+@lSourceSystem +']'


--PRINT(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt