
SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;


DECLARE  @aETLBatchID INT = '$(ETL_Batch_ID)'
--DECLARE  @aETLBatchID INT = 15
       , @lMinHourQtr INT
   	   , @lMaxHourQtr INT
		
-- DECLARE @aBatchStartDateTime AS DATETIME = '2018-04-03 09:00:00.000' , @aBatchEndDateTime AS DATETIME = '2018-06-06 17:00:00.000', @aETLBatchID INT = 1

SELECT @lMinHourQtr = MIN(Hour_Quarter_ID), @lMaxHourQtr = MAX(Hour_Quarter_ID)
FROM OA.FCT_Inventory
WHERE ETL_Batch_ID = @aETLBatchID

SELECT FI.Hour_Quarter_ID
     , FI.Record_Timestamp
	 , F.Facility_ID
	 , I.Item_ID
	 , Load_Unit_Count = COUNT(DISTINCT(FI.Load_Unit_Code))
	 , Base_Load_Unit_Count = COUNT(DISTINCT(FI.Base_Load_Unit_Code))
	 , Total_SKU_Count = SUM(FI.Inventory_Qty)
	 , Unassigned_SKU_Count = SUM(FI.Inventory_Unassigned_Qty)
	 , ETL_Batch_ID = @aETLBatchID
FROM OA.FCT_Inventory FI
   JOIN OA.DIM_Item I ON I.Item_ID = FI.Item_ID
   JOIN OA.DIM_Facility F ON I.Source_System = F.Source_System
WHERE FI.Hour_Quarter_ID BETWEEN @lMinHourQtr AND @lMaxHourQtr
--WHERE FI.ETL_Batch_ID = @aETLBatchID
GROUP BY FI.Hour_Quarter_ID, FI.Record_Timestamp, I.Item_ID, I.Source_System, F.Facility_ID

