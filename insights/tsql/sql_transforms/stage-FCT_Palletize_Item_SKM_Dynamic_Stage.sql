SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 
DECLARE @lSQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Palletize_Item_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	SELECT DISTINCT
		Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), paletizingTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), paletizingTime,120),15,2) as INT)/15))
		,Record_Timestamp = paletizingTime
		,Pallet_Code = ISNULL(palletID,'''')
		,Load_Unit_Code = ISNULL(loadUnitId,'''')
		,Source_Location_Code = ISNULL(sourceLocationId,'''')
		,Device_Code = ISNULL(workstationId,'''')
		,Item_Code = OA.Item_Code_Generator(skuPk, null,null,null)
		,Wave_Code = ISNULL(waveId,'''')
		,Operator_Code = ''''
		,Retrieve_Date_Time = ISNULL(retrieveTime,'''')
		,Item_Stacked_Flg = CASE WHEN itemStacked = ''true'' THEN 1 ELSE 0 END
		,Manual_Flg = CASE WHEN palletizedManually = ''true'' THEN 1 ELSE 0 END
		,Stack_Sequence_Number = 0
		,Subsystem_Code = CASE WHEN workstationId IS NOT NULL THEN ''Conveyor'' ELSE '''' END
		,Subsystem_Category = CASE WHEN workstationId IS NOT NULL THEN ''Dematic IQ'' ELSE '''' END
		,Source_System = ''' + @lSourceSystem + '''
		,Item_Category_Code = ''''
	FROM [STAGE].[FCT_Palletize_Item_' + @lSourceSystem + ']
END
'


SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Palletize_Item_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
                           DROP TABLE [STAGE].[FCT_Palletize_Item_'+@lSourceSystem +']'

--PRINT(@lSQLStmt)
EXEC sp_executesql @lSQLStmt
EXEC sp_executesql @Drop_SQLStmt

