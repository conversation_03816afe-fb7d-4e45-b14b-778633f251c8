SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX), @Drop_SQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
-- Create transform table if it does not exists stage table
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Scada_Fault_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Fault_Dynamic_Stage_' + @lSourceSystem + ''' and s.name = ''Stage'')

		CREATE TABLE [STAGE].[FCT_Fault_Dynamic_Stage_' + @lSourceSystem + '] (
			Hour_Quarter_ID INTEGER NOT NULL, Container_Physical_Code VARCHAR(50) NULL, Device_Code VARCHAR(150) NULL,
			Fault_Acknowledgement_Date_Time Datetime2(7) NULL, Fault_End_Date_Time Datetime2(7)  NOT NULL, Fault_Duration_Seconds INTEGER NULL,
			Fault_Repair_Duration_Seconds INTEGER NULL, Fault_Start_Date_Time Datetime2(7)  NOT NULL, Fault_Tag_Reason_Code VARCHAR(150) NULL,
			Item_Category_Code VARCHAR(150) NULL, Item_Code VARCHAR(150) NULL, Location_Code VARCHAR(150) NULL, Module_Code VARCHAR(150) NULL,
			Operator_Code VARCHAR(150) NULL, Physical_Device_Code VARCHAR(150) NULL, PLC_Code VARCHAR(150) NULL, Reason_Name VARCHAR(150) NULL,
			Record_Timestamp Datetime2(7) NULL, Source_System VARCHAR(150) NULL, Fault_Code VARCHAR(50) NULL, Subsystem_Category VARCHAR(50) NULL,
			Subsystem_Code VARCHAR(50) NULL, Tenant_Name VARCHAR(50) NULL);

	WITH PreProcessed_Events as (
		SELECT
				Record_Timestamp = CONVERT(DATETIME, Time) + CONVERT(DATETIME,''20'' + RIGHT(Date,2) + ''/'' + SUBSTRING(Date,4,2) + ''/'' + LEFT(Date,2))	
				,PLC_Code = PLC
				,Fault_Code = CASE WHEN Number IS NULL THEN State ELSE Number END		
				,Fault_Message = REPLACE(LEFT(ISNULL(Messagetext, ''''), 255),''\"'','''')
				,Fault_Duration_Seconds = CASE WHEN "Duration[sec]" IS NULL or "Duration[sec]" = ''0'' 
										THEN 0 ELSE DATEDIFF(SECOND, DATEADD(ss,0 - RIGHT("Duration[sec]",LEN("Duration[sec]") - OA.CHARINDEX2("Duration[sec]",'':'',2)),DATEADD("mi",0 - SUBSTRING("Duration[sec]",OA.CHARINDEX2("Duration[sec]",'':'',1) + 1,OA.CHARINDEX2("Duration[sec]",'':'',2) - (OA.CHARINDEX2("Duration[sec]",'':'',1) + 1)),DATEADD(hh,0 - LEFT("Duration[sec]",OA.CHARINDEX2("Duration[sec]",'':'',1) - 1),CONVERT(DATETIME, 0)))),  CONVERT(DATETIME, 0)) END
				,Subsystem_Code = Subsystem
				,Device_Code = LEFT(Component,50)
		FROM 
			STAGE.FCT_Scada_Fault_' + @lSourceSystem + '
		WHERE
			(State = ''-'' or UPPER(State) = ''SERVER RESTART'') AND  Date IS NOT NULL AND Time IS NOT NULL
	), Cleaned_Events as (
		SELECT DISTINCT
			Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), PPE.Record_Timestamp,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), PPE.Record_Timestamp,120),15,2) as INT)/15))
			,Container_Physical_Code = NULL
			,PPE.Device_Code
			,Fault_Acknowledgement_Date_Time = NULL
			,Fault_End_Date_Time = PPE.Record_Timestamp
			,Fault_Duration_Seconds
			,Fault_Repair_Duration_Seconds = 0
			,Fault_Start_Date_Time = PPE.Record_Timestamp - DATEADD(second, Fault_Duration_Seconds, CONVERT(DATETIME, 0) )
			,Fault_Tag_Reason_Code = NULL
			,Item_Category_Code = NULL
			,Item_Code = NULL
			,Location_Code = NULL
			,Module_Code = NULL
			,Operator_Code = NULL
			,Physical_Device_Code = NULL
			,PLC_Code
			,Reason_Name = NULL
			,Record_Timestamp = PPE.Record_Timestamp
			,Source_System = ''' + @lSourceSystem + '''
			,Fault_Code
			,Subsystem_Category = ''SCADA''
			,Subsystem_Code
			,Tenant_Name = NULL
		 from PreProcessed_Events PPE
	)
	--Select * from Cleaned_Events
	INSERT INTO [STAGE].[FCT_Fault_Dynamic_Stage_' + @lSourceSystem + '] SELECT * from Cleaned_Events
END
'

SET @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Scada_Fault_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[FCT_Scada_Fault_'+@lSourceSystem +']'

--PRINT(@lSQLStmt)
EXEC sp_executesql @lSQLStmt;
EXEC sp_executesql @Drop_SQLStmt;