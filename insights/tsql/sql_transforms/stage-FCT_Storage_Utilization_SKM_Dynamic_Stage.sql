SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
-- DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''Fct_Storage_Utilization_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
                   ' SELECT DISTINCT Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),recordTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,recordTime)/15))  ' +
                   '     , Record_Timestamp = recordTime ' +
                   '     , Aisle_Code = ISNULL(Aisle, '''') ' +
				   '     , Aisle_Name = ISNULL(Aisle, '''') ' + 
				   '     , Module_Code = ISNULL(CASE WHEN CHARINDEX(''#'',grouppk) > 0  ' + 
                   '                            THEN RIGHT(grouppk, CHARINDEX(''#'',REVERSE(grouppk))-1) ' + 
                   '                            ELSE grouppk END,ISNULL(area,''''))  ' +
   				   '     , Module_Name = ISNULL(grouppk, ISNULL(area,'''')) ' +
				   '     , Work_Area_Code = ISNULL(CASE WHEN CHARINDEX(''#'',grouppk) > 0 ' +
				   '                            THEN REVERSE(PARSENAME(REPLACE(REVERSE(grouppk), ''#'', ''.''), 2)) ' +
				   '                            ELSE area END, '''') ' +
				   '     , Work_Area_Name = ISNULL(CASE WHEN CHARINDEX(''#'',grouppk)>0 ' +
				   '                               THEN REPLACE(grouppk,RIGHT(grouppk, CHARINDEX(''#'',REVERSE(grouppk))),'''') ' +
				   '                               ELSE area END,'''') ' +
                   '     , Total_Location_Count = ISNULL(TotalOpenings, '''') ' +
				   '     , Empty_Location_Count = ISNULL(EmptyOpenings, '''') ' +
				   '     , Occupied_Location_Count = ISNULL(occupiedLocations, '''') ' +
				   '     , Partial_Occupied_Location_Count = ISNULL(partiallyoccupiedLocations, '''') ' +
				   '     , Total_Location_Position_Count = ISNULL(TotalLocations, '''') ' +
				   '     , Empty_Location_Position_Count = ISNULL(EmptyLocations, '''') ' +
				   '     , Fault_Location_Position_Count = ISNULL(FaultedLocations, '''') ' +
				   '     , Unavailable_Location_Position_Count = ISNULL(UnavailableLocations, '''') ' +
               --  '     , LocationGroup_Utilization_Percentage = ISNULL(PercentEmptyOpenings, 100*CAST(EmptyOpenings AS real)/TotalOpenings) ' +
		       --  '     , LocationPositions_Utilization_Percentage = ISNULL(PercentEmptyStorageLocs, 100*CAST(EmptyLocations AS real)/TotalLocations) ' +
			       '     , Location_Utilization_Percentage = CAST((100*CAST((CAST(TotalOpenings AS INT) - CAST(EmptyOpenings AS INT)) AS real)/TotalOpenings) AS DECIMAL(10,2)) ' +
				   '     , Location_Position_Utilization_Percentage = CAST(100*(CAST((CAST(TotalLocations AS INT) - CAST(EmptyLocations AS INT)) AS real) / TotalLocations) AS DECIMAL(10,2))' +
				   '     , Source_System = ''' + @lSourceSystem +  '''' +
                   ' FROM [STAGE].Fct_Storage_Utilization_'+ @lSourceSystem +
                   '     ORDER BY Record_Timestamp, Aisle_Code, Work_Area_Code ' +
                   ' END '  +  
				   ' IF OBJECT_ID(''[STAGE].Fct_Storage_Utilization_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].Fct_Storage_Utilization_'+@lSourceSystem;  

EXEC sp_executesql @lSQLStmt