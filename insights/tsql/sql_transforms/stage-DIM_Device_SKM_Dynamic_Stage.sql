SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX);
DECLARE @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Sort_Device_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 sortDeviceId= '''', 	sortDeviceName= '''', deviceFunctionalType= '''', hardwareType= '''', manufacturer= '''', 
		engineeredRate= '''', area= '''', originator = '''', tenantName = '''', 	dimensionModificationTime= ''''
		INTO STAGE.DIM_Sort_Device_' + @lSourceSystem + '
	END;'
EXEC(@lSQLStmt)

SET @Transform_SQLStmt = '
WITH Latest_Sort_Device AS (
	SELECT TOP 1 WITH TIES
		Device_Code = sortDeviceId,
		Device_Name = ISNULL(sortDeviceName,''''),
		Device_Functional_Type = ISNULL(deviceFunctionalType,''''),
		Device_Hardware_Type = ISNULL(hardwareType,''''),
		Device_Manufacturer = ISNULL(manufacturer,''''),
		Device_Engineered_rate = ISNULL(engineeredRate,''''),
		Device_Area = ISNULL(area,''''),
		DC_Tenant_Name = ISNULL(UPPER(tenantName),''''),
		Subsystem_Code = ISNULL(UPPER(originator), ''SORTER'')
	FROM [STAGE].[DIM_Sort_Device_' + @lSourceSystem + ']
	ORDER BY Row_number() over (partition by sortDeviceId, originator, tenantName order by dimensionModificationTime desc)
)
SELECT DISTINCT
	Device_Code = ISNULL(Device_Code, ''''),
	Device_Name,
	Device_Weight_Factor = '''', 
	Device_Functional_Type,
	Device_Hardware_Type,
	Device_Manufacturer,
	Device_Engineered_rate,
	Device_Area,
	DC_Tenant_Name,
	Subsystem_Code,
	Subsystem_Category = ''DEMATIC IQ'',
	Source_System = ''' + @lSourceSystem +'''
FROM Latest_Sort_Device
'

SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Sort_Device_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[DIM_Sort_Device_'+@lSourceSystem +']'

--PRINT(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt