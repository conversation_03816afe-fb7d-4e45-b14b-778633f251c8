SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--SET @lSourceSystem = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX);
DECLARE @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Workflow_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 workflowPk= '''', 	technology= '''', 	vendor= '''', 	workType= '''',
			workTypeStyle= '''', tenantName= '''', dimensionModificationTime= ''''
		INTO STAGE.DIM_Workflow_' + @lSourceSystem + '
	END;'
EXEC sp_executesql @lSQLStmt

SET @Transform_SQLStmt = '
--DIM_Workflow
WITH Latest_WorkFlow AS (
	SELECT TOP 1 WITH TIES
		Technology_Code = ISNULL(technology, ''''),
		Technology_Vendor = vendor
	FROM [STAGE].[DIM_Workflow_' + @lSourceSystem + ']
	ORDER BY Row_number() over (partition by workflowPk order by dimensionModificationTime desc)
)
SELECT DISTINCT
	Technology_Code,
	Technology_Vendor,
	Technology_Name = '''',
	Source_System = ''' + @lSourceSystem +'''
FROM Latest_WorkFlow
'

SET @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Workflow_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[DIM_Workflow_'+@lSourceSystem +']'


--PRINT(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt
