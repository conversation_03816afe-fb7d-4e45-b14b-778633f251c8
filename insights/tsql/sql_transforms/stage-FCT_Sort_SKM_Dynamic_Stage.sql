SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--SET @lSourceSystem = 'DematicSoftware';
DECLARE @TempTable_SQLStmt VARCHAR(MAX),
		@Process_Pipe1_SQLStmt VARCHAR(MAX), @Process_Pipe2_SQLStmt VARCHAR(MAX), @Final_Transform_SQLStmt VARCHAR(MAX),
		@FinalSQLStmt NVARCHAR(MAX); 
DECLARE @Drop_SQLStmt NVARCHAR(MAX);


SET @TempTable_SQLStmt = '
CREATE TABLE #FCT_Raw_Sort_Events_' + @lSourceSystem + '(
	Record_Timestamp Datetime2(7) NOT NULL, Scanned_Code VARCHAR(150) NULL, Intended_Logical_Destination_Code VARCHAR(150) NULL,
	Actual_Logical_Destination_Code VARCHAR(150) NULL, Actual_Physical_Destination_Name VARCHAR(150) NULL,
	Actual_Physical_Destination_Code VARCHAR(150) NULL, Intended_Physical_Destination_Code VARCHAR(150) NULL,
	Intended_Physical_Destination_Name VARCHAR(150) NULL, Dispatch_Status_Code VARCHAR(150) NULL,
	Disposition_Status_Code VARCHAR(150) NULL, Wave_Code VARCHAR(150) NULL, Tracking_Code VARCHAR(150) NULL,
	Operator_Code VARCHAR(150) NULL, Reason_Code VARCHAR(150) NULL, Dispatch_Request_Timestamp Datetime2(7) NULL,
	Dispatch_Assignment_Timestamp Datetime2(7) NULL, Induction_Lane_Code VARCHAR(150) NULL, Terminal_Code VARCHAR(150) NULL,
	Recirculation_Count VARCHAR(150) NULL, QC_Ind VARCHAR(150) NULL, DC_Tenant_Name VARCHAR(150) NULL,
	Sort_Device_Code VARCHAR(150) NULL, Subsystem_Category VARCHAR(150) NULL, Scanner_Code VARCHAR(150) NULL,
	Disposition_Reason_Code VARCHAR(150) NULL, Container_Type_Code VARCHAR(150) NULL,
	Unit_Sorter_Carrier_Code VARCHAR(150) NULL, Intended_Destination_Type VARCHAR(150) NULL,
	dispositionBitStatus VARCHAR(150) NULL, Subsystem_Code VARCHAR(150) NULL
);

-- Sort Disposition Events
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Sort_Disposition_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	WITH FCT_Sort_Disposition_Events AS (
		SELECT
			Record_TimeStamp = dispositionTime
			,Scanned_Code =	matchCode
			,Intended_Logical_Destination_Code = plannedDestination 
			,Actual_Logical_Destination_Code = NULL
			,Actual_Physical_Destination_Name = actualPhysicalDestination
			,Actual_Physical_Destination_Code =  CASE 
												WHEN CHARINDEX(''.'',actualPhysicalDestination) > 0 THEN REVERSE(LEFT(REVERSE(actualPhysicalDestination), CHARINDEX(''.'',REVERSE(actualPhysicalDestination)) - 1))
													ELSE ISNULL(actualPhysicalDestination,'''')
												END
			,Intended_Physical_Destination_Code = CASE 
												WHEN CHARINDEX(''.'',plannedPhysicalDestination) > 0 THEN REVERSE(LEFT(REVERSE(plannedPhysicalDestination), CHARINDEX(''.'',REVERSE(plannedPhysicalDestination)) - 1))
													ELSE ISNULL(plannedPhysicalDestination,'''')
												END
			,Intended_Physical_Destination_Name = plannedPhysicalDestination
			,Dispatch_Status_Code = dispatchEventCode
			,Disposition_Status_Code = dispositionEventCode
			,Wave_Code = NULL
			,Tracking_Code = trackingId
			,Operator_Code = NULL
			,Reason_Code = recirculationReason
			,Dispatch_Request_Timestamp = requestTime
			,Dispatch_Assignment_Timestamp = responseTime
			,Induction_Lane_Code = inductStation
			,Terminal_Code = NULL
			,Recirculation_Count = recirculationcounter
			,QC_Ind = 0
			,DC_Tenant_Name = NULL
			,Sort_Device_Code = sorterId
			,Subsystem_Category = ''DEMATIC IQ''
			,Scanner_Code = requestLocation
			,Disposition_Reason_Code = dispositionDischargeCode
			,Container_Type_Code = type
			,Unit_Sorter_Carrier_Code = carrierId
			,Intended_Destination_Type = ''LOGICAL''
			,dispositionBitStatus = NULL
			,Subsystem_Code = ''MFC_Sorter''
		FROM [STAGE].[FCT_Sort_Disposition_' + @lSourceSystem + ']
		WHERE (dispositionEventCode IS NOT NULL OR dispositionEventCode != '''')
		AND dispositionTime IS NOT NULL
	)
	INSERT INTO #FCT_Raw_Sort_Events_' + @lSourceSystem + ' SELECT * from FCT_Sort_Disposition_Events;
END

IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Sort_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	WITH FCT_Sort_Events AS ( 
		SELECT
			Record_TimeStamp = dispositionTime
			,Scanned_Code =	scannedCode		
			,Intended_Logical_Destination_Code = intendedDestinationID
			,Actual_Logical_Destination_Code = actualLogicalDestinationId
			,Actual_Physical_Destination_Name = NULL
			,Actual_Physical_Destination_Code = actualPhysicalDestinationId
			,Intended_Physical_Destination_Code = intendedPhysicalDestinationId
			,Intended_Physical_Destination_Name = intendedPhysicalDestinationId
			,Dispatch_Status_Code = dispatchAssignmentStatus
			,Disposition_Status_Code = dispositionStatus
			,Wave_Code = waveId
			,Tracking_Code = trackingId
			,Operator_Code = userId
			,Reason_Code = recirculationReason
			,Dispatch_Request_Timestamp = dispatchRequestTime
			,Dispatch_Assignment_Timestamp = dispatchAssignmentTime
			,Induction_Lane_Code = inductionLaneId
			,Terminal_Code = terminalID
			,Recirculation_Count = recirculationCounter
			,QC_Ind = CASE WHEN UPPER(qc_Ind) = ''FALSE'' OR qc_Ind = ''0'' THEN 0 ELSE 1 END
			,DC_Tenant_Name = tenantName
			,Sort_Device_Code = sortDeviceId	
			,Subsystem_Category = ''DEMATIC IQ''
			,Scanner_Code = scannerId
			,Disposition_Reason_Code = NULL
			,Container_Type_Code = NULL
			,Unit_Sorter_Carrier_Code = NULL
			,Intended_Destination_Type = ISNULL(intendedDestinationType, ''LOGICAL'')
			,dispositionBitStatus = dispositionBitStatus
			,Subsystem_Code = originator	
		FROM [STAGE].[FCT_Sort_' + @lSourceSystem + '] 
		WHERE (dispositionStatus IS NOT NULL OR dispositionStatus != '''')
		AND dispositionTime IS NOT NULL
	)
	INSERT INTO #FCT_Raw_Sort_Events_' + @lSourceSystem + ' SELECT * from FCT_Sort_Events;
END
;
'

SET @Process_Pipe1_SQLStmt = '
	WITH Raw_Sort_Events AS (
		SELECT 
			Dispatch_Status_Code
			,Subsystem_Code = CASE WHEN UPPER(Subsystem_Code) = ''EMS'' OR Subsystem_Code IS NULL THEN ''SORTER'' ELSE Subsystem_Code END
			,Container_Type_Code = CASE WHEN Container_Type_Code = ''BARCODE'' THEN ''Load Unit Barcode'' WHEN Container_Type_Code = ''PRODUCT'' THEN ''Item Barcode'' ELSE ISNULL(Container_Type_Code,'''') END		
			,Intended_Logical_Destination_Code = CASE WHEN UPPER(Intended_Destination_Type) = ''LOGICAL'' THEN Intended_Logical_Destination_Code ELSE '''' END
			,qc_Ind
			,dispositionBitStatus
			,Disposition_Status_Code
			,Scanner_Code
			,Scanned_Code
			,Actual_Logical_Destination_Code
			,Actual_Physical_Destination_Code
			,Actual_Physical_Destination_Name
			,Recirculation_Count
			,Record_TimeStamp
			,DC_Tenant_Name
			,Sort_Device_Code
			,Wave_Code
			,Tracking_Code
			,Operator_Code
			,Reason_Code
			,Dispatch_Request_Timestamp
			,Dispatch_Assignment_Timestamp
			,Induction_Lane_Code
			,Terminal_Code
			,Disposition_Reason_Code
			,Unit_Sorter_Carrier_Code
			,Intended_Physical_Destination_Code
			,Intended_Physical_Destination_Name
			,Source_System = ''' + @lSourceSystem +'''
			,Subsystem_Category
		FROM #FCT_Raw_Sort_Events_' + @lSourceSystem + '
	)
	-- calculate disposition and dispatch error and success counts for BR00 events and do a lookup of diposition status name  
	, BR_Status_Lookup_Events AS (
		SELECT 
			Disposition_Status_Name = [status_lookup_cache].Status_Name
			,Dispatch_Status_Code = psle.Dispatch_Status_Code
			,Subsystem_Code = psle.Subsystem_Code
			,Container_Type_Code = psle.Container_Type_Code
			,Intended_Logical_Destination_Code = psle.Intended_Logical_Destination_Code
			,qc_Ind = psle.qc_Ind
			,Disposition_Status_Code = psle.Disposition_Status_Code
			,Scanner_Code
			,Scanned_Code
			,Actual_Logical_Destination_Code
			,Actual_Physical_Destination_Code
			,Actual_Physical_Destination_Name
			,Recirculation_Count
			,Record_TimeStamp
			,DC_Tenant_Name
			,Sort_Device_Code
			,Disposition_Reason_Code
			,Wave_Code
			,Tracking_Code
			,Operator_Code
			,Reason_Code
			,Dispatch_Request_Timestamp
			,Dispatch_Assignment_Timestamp
			,Terminal_Code
			,Induction_Lane_Code
			,Unit_Sorter_Carrier_Code
			,Intended_Physical_Destination_Code
			,Intended_Physical_Destination_Name
			,dispositionBitStatus
			,Source_System = psle.Source_System
			,Subsystem_Category = psle.Subsystem_Category
			,Dispatch_Total_Count = 1
			,Dispatch_Success_Count = CASE 
					WHEN UPPER(psle.Dispatch_Status_Code) IN (''SORTER INDUCT'', ''ROUTED'', ''STRAIGHT'', ''SHIPPING SORTER INDUCT'', ''CASE'', ''NONE'', ''SENT STRAIGHT'') THEN 1
					ELSE 0
				END
			,Disposition_Success_Count = CASE WHEN UPPER([status_lookup_cache].Status_Name) = ''DIVERTED'' OR UPPER([status_lookup_cache].Status_Name) = ''SUCCESSFUL DIVERT'' THEN 1 ELSE 0 END
			,Disposition_Error_Count = ISNULL(
										CASE 
											WHEN LEN(ISNULL(dispositionBitStatus,'''')) = 0 AND UPPER([status_lookup_cache].Status_Name) NOT IN (''SUCCESSFUL DIVERT'', ''DIVERTED'', ''DIVERTED TO LAN'') THEN 1
											WHEN LEN(ISNULL(dispositionBitStatus,'''')) = 0 THEN 0 
											ELSE LEN(ISNULL(dispositionBitStatus,0)) - LEN(REPLACE(dispositionBitStatus, '':'',''''))
										END, 0)
		FROM (SELECT * FROM Raw_Sort_Events WHERE UPPER(LEFT(Disposition_Status_Code,4)) = ''BR00'' ) AS psle
			LEFT OUTER JOIN (
				SELECT
					S.Status_ID,
					Status_Code = UPPER(ISNULL(s.Status_Code,''0'')),
					Status_Name = ISNULL(SL.Status_Locale_Name, S.Status_Code) ,  Subsystem_Category = UPPER(ISNULL(ss.Subsystem_Category, '''')),
					Subsystem_Code = UPPER(ISNULL(ss.Subsystem_Code, ''0'')),
					Tenant_Name = UPPER(ISNULL(s.DC_Tenant_Name, '''')),
					s.Source_System
				FROM OA.Dim_Status s WITH(NOLOCK)
				JOIN OA.DIM_Subsystem ss WITH(NOLOCK) ON s.Subsystem_ID = ss.Subsystem_ID
				LEFT OUTER JOIN OA.DIM_Status_Locale SL WITH(NOLOCK) ON s.Status_ID = SL.Status_ID
				 AND sl.Status_locale_code = ''en_US''  WHERE s.Source_System = ''' + @lSourceSystem + '''
		) AS [status_lookup_cache]
		ON [status_lookup_cache].[Status_Code] = psle.Disposition_Status_Code AND [status_lookup_cache].[Subsystem_Code] = psle.Subsystem_Code 
			AND [status_lookup_cache].[Source_System] = ''' + @lSourceSystem + ''' AND [status_lookup_cache].[Subsystem_Category] = ''DEMATIC IQ''
	)
	-- calculate disposition and dispatch error and success counts for BR00 events
	, BR_Error_Calculation_Events AS (
		SELECT 
			*	
			,Disposition_Total_Count = Disposition_Success_Count + Disposition_Error_Count
			,Dispatch_Error_Count = CASE WHEN Dispatch_Success_Count = 1 THEN 0 ELSE 1 END
		FROM BR_Status_Lookup_Events
	)
	'

SET @Process_Pipe2_SQLStmt = '
	-- calculate disposition and dispatch error and success counts for NONE BR00 events
	, Non_BR_calculation_Events AS (
		SELECT 
			*
			, Disposition_Total_Count = ISNULL(Disposition_Success_Count,0) + ISNULL(Disposition_Error_Count,0)
			, Dispatch_Error_Count = CASE WHEN Dispatch_Success_Count = 1 THEN 0 ELSE 1 END
			, Disposition_Status_Name = Disposition_Status_Code
		FROM (
			SELECT 
				*
				,Dispatch_Total_Count = 1
				,Dispatch_Success_Count = CASE 
						WHEN UPPER(Dispatch_Status_Code) IN (''NORMAL'', ''ROUTED'', ''STRAIGHT'', ''NONE'', ''SORTER INDUCT'', ''SHIPPING SORTER INDUCT'', ''CASE'', ''SENT STRAIGHT'', ''OK'', ''IR'') THEN 1
						ELSE 0
					END
				,Disposition_Success_Count = CASE 
						WHEN UPPER(Disposition_Status_Code) IN (''SUCCESS'', ''DIVERTED'', ''SUCCESSFUL DIVERT'', ''DIVERTED TO LANE'', ''OK'') OR LEN(Disposition_Status_Code) <= 0 THEN 1
						ELSE 0
					END
				,Disposition_Error_Count = ISNULL(
					CASE 
						WHEN LEN(ISNULL(dispositionBitStatus,'''')) = 0 AND UPPER(Disposition_Status_Code) NOT IN (''SUCCESS'', ''OK'', ''DIVERTED'', ''SUCCESSFUL DIVERT'', ''DIVERTED TO LANE'') THEN 1 
						WHEN LEN(ISNULL(dispositionBitStatus,'''')) = 0 THEN 0 
						ELSE LEN(ISNULL(dispositionBitStatus,0)) - LEN(REPLACE(ISNULL(dispositionBitStatus,0),'':'',''''))
					END
					, 0)
			FROM Raw_Sort_Events 
			WHERE UPPER(LEFT(Disposition_Status_Code,4)) != ''BR00''
		) tmp
	)
	-- merge back the BR00 and None BR00 events into same table
	, Post_Calculations_Events AS (
		SELECT 
			Scanned_Code, Intended_Physical_Destination_Name, Intended_Physical_Destination_Code, Unit_Sorter_Carrier_Code,
			Induction_Lane_Code, Terminal_Code, Wave_Code,Tracking_Code,Operator_Code,Reason_Code,Dispatch_Request_Timestamp,
			Dispatch_Assignment_Timestamp, Sort_Device_Code, DC_Tenant_Name,Record_TimeStamp, Recirculation_Count,
			Disposition_Reason_Code, Actual_Physical_Destination_Code, Actual_Physical_Destination_Name,
			Actual_Logical_Destination_Code, Scanner_Code,dispositionBitStatus, Dispatch_Status_Code,
			Subsystem_Code, Container_Type_Code, Intended_Logical_Destination_Code, qc_Ind, Disposition_Status_Code,
			Source_System, Subsystem_Category, Dispatch_Total_Count, Dispatch_Success_Count, Disposition_Success_Count,
			Disposition_Error_Count, Disposition_Total_Count, Dispatch_Error_Count, Disposition_Status_Name
		FROM BR_Error_Calculation_Events
		UNION
		SELECT 
			Scanned_Code, Intended_Physical_Destination_Name, Intended_Physical_Destination_Code, Unit_Sorter_Carrier_Code,
			Induction_Lane_Code, Terminal_Code, Wave_Code,Tracking_Code,Operator_Code,Reason_Code,Dispatch_Request_Timestamp,
			Dispatch_Assignment_Timestamp, Sort_Device_Code, DC_Tenant_Name,Record_TimeStamp, Recirculation_Count,
			Disposition_Reason_Code, Actual_Physical_Destination_Code, Actual_Physical_Destination_Name,
			Actual_Logical_Destination_Code, Scanner_Code,dispositionBitStatus, Dispatch_Status_Code,
			Subsystem_Code, Container_Type_Code, Intended_Logical_Destination_Code, qc_Ind, Disposition_Status_Code,
			Source_System, Subsystem_Category, Dispatch_Total_Count, Dispatch_Success_Count, Disposition_Success_Count,
			Disposition_Error_Count, Disposition_Total_Count, Dispatch_Error_Count, Disposition_Status_Name 
		FROM Non_BR_calculation_Events
	)
	-- do a lookup of physical and logical destination type 
	, Destination_Type_Lookup AS (
		SELECT 
			Intended_Physical_Destination_Name, Intended_Physical_Destination_Code, Unit_Sorter_Carrier_Code, Induction_Lane_Code, Terminal_Code
			,Disposition_Bit_Status = CASE
						WHEN (LEN(dispositionBitStatus) - LEN(REPLACE(dispositionBitStatus, '';'', '''')) = 1
							AND UPPER(Disposition_Status_Code) != ''SUCCESS'' AND UPPER(Disposition_Status_Name) NOT IN (''DIVERTED'', ''SUCCESSFUL DIVERT''))
						OR LEN(dispositionBitStatus) - LEN(REPLACE(dispositionBitStatus, '';'', '''')) > 1
							THEN OA.DispositionCalculatorFunction(dispositionBitStatus, '';'')
						ELSE
							ISNULL(dispositionBitStatus, ''0'')
			END
			,Scanned_Code,Wave_Code,Tracking_Code,Operator_Code,Reason_Code,Dispatch_Request_Timestamp,
			Dispatch_Assignment_Timestamp, Sort_Device_Code, DC_Tenant_Name, Record_TimeStamp, Recirculation_Count,
			Disposition_Reason_Code, Actual_Physical_Destination_Code, Actual_Physical_Destination_Name,
			Actual_Logical_Destination_Code, pce.Scanner_Code ,dispositionBitStatus, Dispatch_Status_Code,
			Subsystem_Code, Container_Type_Code, Intended_Logical_Destination_Code, qc_Ind, Disposition_Status_Code,
			pce.Source_System, Subsystem_Category, Dispatch_Total_Count, Dispatch_Success_Count, Disposition_Success_Count
			,Disposition_Error_Count, Disposition_Total_Count, Dispatch_Error_Count, Disposition_Status_Name
			,Actual_Physical_Destination_Type = PD.Physical_Destination_Type
			,Actual_Logical_Destination_Type = LD.Logical_Destination_Type
		FROM Post_Calculations_Events pce LEFT OUTER JOIN 
			(SELECT DISTINCT 
				Physical_Destination_Code = UPPER(ISNULL(Physical_Destination_Code, ''0'')),  PD.Source_System,  PD.Physical_Destination_Type, Scanner_Code = UPPER(ISNULL(Scanner_Code,''0''))
			FROM OA.DIM_Physical_Destination PD WITH(NOLOCK) 
			JOIN  OA.DIM_Scanner S  WITH(NOLOCK) ON PD.Scanner_ID = S.Scanner_ID WHERE PD.Source_System = ''' + @lSourceSystem + ''') AS PD 
			ON pce.Scanner_Code = PD.Scanner_Code AND pce.Actual_Physical_Destination_Code = PD.Physical_Destination_Code AND pce.Source_System = PD.Source_System
			LEFT OUTER JOIN 
			(SELECT DISTINCT 
				Logical_Destination_Code = UPPER(ISNULL(Logical_Destination_code, ''0'')), LD.Source_System,  Tenant_Name = UPPER(ISNULL(LD.DC_Tenant_Name,'''')),
				Logical_Destination_Type, Scanner_Code = UPPER(ISNULL(S.Scanner_Code,''0''))
			FROM OA.DIM_Logical_Destination LD  WITH(NOLOCK) 
			JOIN OA.DIM_Scanner S  WITH(NOLOCK) ON LD.Scanner_ID = S.Scanner_ID WHERE LD.Source_System = ''' + @lSourceSystem + ''') AS LD
			ON pce.Scanner_Code = PD.Scanner_Code AND pce.Actual_Logical_Destination_Code = LD.Logical_Destination_Code AND pce.Source_System = LD.Source_System
	)'

SET	@Final_Transform_SQLStmt = '
	SELECT DISTINCT
		Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), Record_TimeStamp,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), Record_TimeStamp,120),15,2) as INT)/15))
		,Record_TimeStamp
		,Scanned_Code = ISNULL(Scanned_Code, '''')
		,Intended_Logical_Destination_Code	 = ISNULL(Intended_Logical_Destination_Code, '''')
		,Actual_Logical_Destination_Code = ISNULL(Actual_Logical_Destination_Code, '''')
		,Intended_Physical_Destination_Code = ISNULL(Intended_Physical_Destination_Code, '''')
		,Actual_Physical_Destination_Code = ISNULL(Actual_Physical_Destination_Code, '''')
		,Dispatch_Status_Code = ISNULL(Dispatch_Status_Code, '''')
		,Disposition_Status_Code = ISNULL(Disposition_Status_Code, '''')
		,Wave_Code = ISNULL(Wave_Code, '''')
		,Tracking_Code = ISNULL(Tracking_Code, '''')
		,Operator_Code = ISNULL(Operator_Code, '''')
		,Reason_Code = ISNULL(Reason_Code, '''')
		,Dispatch_Request_Timestamp = ISNULL(CAST(Dispatch_Request_Timestamp AS varchar(30)), '''') 
		,Dispatch_Assignment_Timestamp = ISNULL(CAST(Dispatch_Assignment_Timestamp AS varchar(30)), '''')
		,Disposition_Bit_Status = ISNULL(Disposition_Bit_Status, '''')
		,Induction_Lane_Code = ISNULL(Induction_Lane_Code, '''')
		,Terminal_Code = ISNULL(Terminal_Code, '''')
		,Dispatch_Success_Count = ISNULL(Dispatch_Success_Count,'''')
		,Dispatch_Error_Count = ISNULL(Dispatch_Error_Count,'''')
		,Dispatch_Total_Count = ISNULL(Dispatch_Total_Count,'''')
		,Disposition_Success_Count = ISNULL(Disposition_Success_Count,'''')
		,Disposition_Error_Count = ISNULL(Disposition_Error_Count,'''')
		,Disposition_Total_Count = ISNULL(Disposition_Total_Count,'''')
		,Recirc_Destination_Ind = CASE 
				WHEN UPPER(COALESCE(Actual_Physical_Destination_Type,Actual_Logical_Destination_Type, '''')) = ''RECIRC'' THEN ''1''
				WHEN ISNULL(Recirculation_Count, 0) > 0 AND ISNULL(Disposition_Reason_Code, '''') = '''' THEN ''1''
				ELSE ''0''
			END
		,Reject_Destination_Ind = CASE
				WHEN UPPER(COALESCE(Actual_Physical_Destination_Type, Actual_Logical_Destination_Type, '''')) = ''REJECT'' THEN ''1''
				WHEN UPPER(Disposition_Reason_Code) IN(''RD'', ''PB'', ''PW'', ''TE'', ''NR'') THEN ''1''
				ELSE ''0''
			END
		,Recirculation_Count = ISNULL(Recirculation_Count,'''')
		,QC_Ind = ISNULL(QC_Ind,'''')
		,DC_Tenant_Name = ISNULL(DC_Tenant_Name, '''')
		,Sort_Device_Code = ISNULL(Sort_Device_Code, '''')
		,Subsystem_Code = ISNULL(Subsystem_Code, '''')
		,Subsystem_Category = ISNULL(Subsystem_Category, '''')
		,Source_System = ISNULL(Source_System,'''')
		,Scanner_Code = ISNULL(Scanner_Code, '''')
		,Actual_Logical_Destination_Type = ISNULL(Actual_Logical_Destination_Type, '''')
		,Actual_Physical_Destination_Type = ISNULL(Actual_Physical_Destination_Type, '''')
		,Status_Category_Code = ''''
		,Disposition_Reason_Code = ISNULL(Disposition_Reason_Code, '''')
		,Actual_Physical_Destination_Name = ISNULL(Actual_Physical_Destination_Name, '''')
		,Container_Type_Code = ISNULL(Container_Type_Code, '''')
		,Unit_Sorter_Carrier_Code = ISNULL(Unit_Sorter_Carrier_Code, '''')
		--,Intended_Physical_Destination_Name = ISNULL(Intended_Physical_Destination_Name, '''')
	FROM Destination_Type_Lookup
	Order by Record_TimeStamp
'

SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Sort_Disposition_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[FCT_Sort_Disposition_'+@lSourceSystem +'] 
	IF OBJECT_ID(''[STAGE].[FCT_Sort_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
	   DROP TABLE [STAGE].[FCT_Sort_'+@lSourceSystem +']'

SET @FinalSQLStmt = (@TempTable_SQLStmt+@Process_Pipe1_SQLStmt+@Process_Pipe2_SQLStmt+@Final_Transform_SQLStmt);

EXEC sp_executesql @FinalSQLStmt;
EXEC sp_executesql @Drop_SQLStmt;

