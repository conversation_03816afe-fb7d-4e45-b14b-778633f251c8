DECLARE @lSourceSystem VARCHAR(100) = '$(Source_System)'

-- move events that dont have Devi<PERSON><PERSON> to a tmp holdover table for future resolution
WITH Device_Code_Lookup AS (
	SELECT Subsystem_Short, DeviceID FROM (
			SELECT Subsystem_Short, DeviceID, rn = row_number() over (partition by [Subsystem_Short] order by [Subsystem_Short] ASC) FROM (
					SELECT 
					Subsystem_Short = CASE 
										WHEN LEFT(SourceLocationID, 4) = 'MSAI' THEN LEFT(SourceLocationID,6)
										WHEN LEFT(DestinationLocationID, 4) = 'MSAI' THEN LEFT(DestinationLocationID,6)
										ELSE NULL
									END
					,DeviceID = ISNULL(ShuttleID,LiftID)
				FROM [STAGE].[FCT_MOVEMENTS_MULTISHUTTLE_'+ @lSourceSystem +']
			) tmp
			WHERE Subsystem_Short IS NOT NULL
			GROUP BY Subsystem_Short, DeviceID
	) tmp2 WHERE rn = 1
)
INSERT INTO ##TMP_FCT_UNMAPPED_MOVEMENTS_MULTISHUTTLE_'+ @lSourceSystem +'
SELECT  transportRequestId, 	subsystemId, 	shuttleId, 	liftId, 	sourceLocationId, 	destinationLocationId, 	aisle, 	level, 	loadUnitId, 	MovementType, 	startTime, 	endTime, 	duration, 	eventCode, 	deltax, 	deltaAisle, 	skuId, 	productRangeId, 	transportReason, 	NOKCounter, 	FaultStartTime, 	FaultPhysicalShuttleID, 	FaultSourceLocationID, 	FaultCode, 	FaultClassification, 	FaultAckTime, 	FaultDuration, 	FaultRepairTime, 	FaultEndTime, 	Record_Timestamp_Offset, 	hardwareIdentifier, arriveTime, leaveTime, Key_ID  FROM (
	SELECT 
		*,
		DeviceID = ISNULL(ShuttleID,LiftID),
		Subsystem_Short = CASE 
										WHEN LEFT(SourceLocationID, 4) = 'MSAI' THEN LEFT(SourceLocationID,6)
										WHEN LEFT(DestinationLocationID, 4) = 'MSAI' THEN LEFT(DestinationLocationID,6)
										ELSE NULL
									END
		FROM (SELECT * FROM [STAGE].[FCT_MOVEMENTS_MULTISHUTTLE_'+ @lSourceSystem +']) t
) all_events LEFT JOIN (SELECT Subsystem_Short,DeviceID from Device_Code_Lookup) DCE ON all_events.Subsystem_Short = DCE.Subsystem_Short
WHERE DCE.DeviceID IS NULL AND all_events.DeviceID IS NULL

-- MULTISHUTTLE
WITH Device_Code_Lookup AS (
	SELECT SubsystemID, DeviceID FROM (
			SELECT SubsystemID, DeviceID, rn = row_number() over (partition by [subsystemId] order by [subsystemId] ASC) FROM (
					SELECT 
					SubsystemID = CASE 
										WHEN LEFT(SourceLocationID, 4) = 'MSAI' THEN LEFT(SourceLocationID,6)
										WHEN LEFT(DestinationLocationID, 4) = 'MSAI' THEN LEFT(DestinationLocationID,6)
										ELSE NULL
									END
					,DeviceID = ISNULL(ShuttleID,LiftID)
				FROM [STAGE].[FCT_MOVEMENTS_MULTISHUTTLE_'+ @lSourceSystem +']
			) tmp
			WHERE SubsystemID IS NOT NULL
			GROUP BY SubsystemID, DeviceID
	) tmp2 WHERE rn = 1
)
, TRANSFORMED_MS_EVENTS AS (
	SELECT
		Hour_Quarter_ID= substring(Replace(Replace(CONVERT(VARCHAR(13),endTime,120),'' '',''),'-',''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20),endTime,120),15,2) as INT)/15)),
		Record_Timestamp =  ISNULL(endTime,''),
		Move_Start_Date_Time =  ISNULL(startTime,''),
		Load_Unit_Code =  ISNULL(loadUnitId,''),
		Source_Location_Code = ISNULL(sourceLocationId, ''),
		Destination_Location_Code = ISNULL(destinationLocationId, ''),
		Device_Code = ISNULL(shuttleId,liftid),
		Physical_Device_Code =  ISNULL(hardwareIdentifier,''),
		Movement_Type_Code =  ISNULL(MovementType,''),
		Item_Code = OA.Item_Code_Generator(SkuID, null,null,null),
		Status_Code =  ISNULL(eventCode,''),
		Transport_Reason_Code =  ISNULL(transportReason,''),
		Transport_Request_ID =  ISNULL(transportRequestId,''),
		Group_Transport_Request_ID = ISNULL(transportRequestId,''),
		Move_Duration_Seconds = DATEDIFF(second,StartTime,EndTime),
		Item_Count= '',
		X_Distance_Traveled =  ISNULL(deltax,''),
		Aisles_Traveled_Count =  ISNULL(deltaAisle,''),
		Subsystem_Code =  'MULTISHUTTLE',
		Subsystem_Category= 'DEMATIC IQ',
		Source_System = @lSourceSystem,
	    Item_SKU =  ISNULL(CASE WHEN skuId LIKE '%EMPTY_SKU%' THEN '' ELSE skuId END, ''),
		Module_Code= '',
		Container_Type_Code= '',
		Load_Unit_Content_Code= '',
		Module_Name = '',
		Work_Area_Code = '',
		Relative_X_Axis = '',
		Relative_Y_Axis = '', 
		Relative_Z_Axis = '',
		Location_Type_Code = '',
		Put_Wall_Cubby_Code = '',
		Level_Code =  ISNULL(level,''),
		Aisle_Code =  ISNULL(aisle,''),
		Workstation_Code = '' ,
		SubsystemID = CASE 
			WHEN LEFT(SourceLocationID, 4) = 'MSAI' THEN LEFT(SourceLocationID,6)
			WHEN LEFT(DestinationLocationID, 4) = 'MSAI' THEN LEFT(DestinationLocationID,6)
			ELSE ''
		END,
		Move_Arrive_Date_Time =  ISNULL(arriveTime,''),
		Move_Leave_Date_Time =  ISNULL(leaveTime,'')
	FROM (SELECT * FROM [STAGE].[FCT_MOVEMENTS_MULTISHUTTLE_'+ @lSourceSystem +']) t
	WHERE startTime IS NOT NULL AND endTime IS NOT NULL
), To_Lookup_Events AS (
	SELECT * FROM TRANSFORMED_MS_EVENTS WHERE Device_Code IS NULL
)
SELECT 
	Hour_Quarter_ID,Record_Timestamp,Move_Start_Date_Time,Load_Unit_Code,Source_Location_Code,Destination_Location_Code 
	,Device_Code,Physical_Device_Code,Movement_Type_Code,Item_Code,Status_Code,Transport_Reason_Code
	,Transport_Request_ID,Group_Transport_Request_ID,Move_Duration_Seconds,Item_Count,X_Distance_Traveled,Aisles_Traveled_Count,Subsystem_Code
	,Subsystem_Category,Source_System,Item_SKU,Module_Code,Container_Type_Code,Load_Unit_Content_Code,Module_Name,Work_Area_Code,Relative_X_Axis
	,Relative_Y_Axis,Relative_Z_Axis,Location_Type_Code,Put_Wall_Cubby_Code,Level_Code,Aisle_Code,Workstation_Code, Move_Arrive_Date_Time
	, Move_Leave_Date_Time = MIN(Move_Leave_Date_Time)
FROM TRANSFORMED_MS_EVENTS WHERE Device_Code IS NOT NULL
GROUP BY Hour_Quarter_ID,Record_Timestamp,Move_Start_Date_Time,Load_Unit_Code,Source_Location_Code,Destination_Location_Code 
	,Device_Code,Physical_Device_Code,Movement_Type_Code,Item_Code,Status_Code,Transport_Reason_Code
	,Transport_Request_ID,Group_Transport_Request_ID,Move_Duration_Seconds,Item_Count,X_Distance_Traveled,Aisles_Traveled_Count,Subsystem_Code
	,Subsystem_Category,Source_System,Item_SKU,Module_Code,Container_Type_Code,Load_Unit_Content_Code,Module_Name,Work_Area_Code,Relative_X_Axis
	,Relative_Y_Axis,Relative_Z_Axis,Location_Type_Code,Put_Wall_Cubby_Code,Level_Code,Aisle_Code,Workstation_Code, Move_Arrive_Date_Time
UNION ALL
SELECT DISTINCT
	Hour_Quarter_ID,Record_Timestamp,Move_Start_Date_Time,Load_Unit_Code,Source_Location_Code,Destination_Location_Code 
	,Device_Code = COALESCE(DCE.DeviceID,''),Physical_Device_Code,Movement_Type_Code,Item_Code,Status_Code,Transport_Reason_Code
	,Transport_Request_ID,Group_Transport_Request_ID,Move_Duration_Seconds,Item_Count,X_Distance_Traveled,Aisles_Traveled_Count,Subsystem_Code
	,Subsystem_Category,Source_System,Item_SKU,Module_Code,Container_Type_Code,Load_Unit_Content_Code,Module_Name,Work_Area_Code,Relative_X_Axis
	,Relative_Y_Axis,Relative_Z_Axis,Location_Type_Code,Put_Wall_Cubby_Code,Level_Code,Aisle_Code,Workstation_Code, Move_Arrive_Date_Time
	, Move_Leave_Date_Time
FROM To_Lookup_Events TLE
	INNER JOIN (SELECT SubsystemID,DeviceID from Device_Code_Lookup) DCE ON TLE.SubsystemID = DCE.SubsystemID

UNION

SELECT DISTINCT
	Hour_Quarter_ID= substring(Replace(Replace(CONVERT(VARCHAR(13),endTime,120),' ',''),'-',''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20),endTime,120),15,2) as INT)/15)),
	Record_Timestamp =  ISNULL(endTime,''),
	Move_Start_Date_Time =  ISNULL(startTime,''),
	Load_Unit_Code =  ISNULL(loadUnitId,''),
	Source_Location_Code = ISNULL(sourceLocationId, ''), 
	Destination_Location_Code =  ISNULL(destinationLocationId, ''),
	Device_Code = COALESCE(shuttleId,liftid,''),
	Physical_Device_Code =  ISNULL(hardwareIdentifier,''),
	Movement_Type_Code =  ISNULL(MovementType,''),
	Item_Code = OA.Item_Code_Generator(SkuID, null,null,null),
	Status_Code =  ISNULL(eventCode,''),
	Transport_Reason_Code =  ISNULL(transportReason,''),
	Transport_Request_ID =  ISNULL(transportRequestId,''),
	Group_Transport_Request_ID = ISNULL(transportRequestId,''),
	Move_Duration_Seconds = DATEDIFF(second,StartTime,EndTime),
	Item_Count= '',
	X_Distance_Traveled =  ISNULL(deltax,''),
	Aisles_Traveled_Count =  ISNULL(deltaAisle,''),
	Subsystem_Code =  'CONVEYOR',
	Subsystem_Category= 'DEMATIC IQ',
	Source_System = ''+ @lSourceSystem +'',
	Item_SKU =  ISNULL(CASE WHEN skuId LIKE '%EMPTY_SKU%' THEN '' ELSE skuId END, ''),
	Module_Code= '',
	Container_Type_Code= '',
	Load_Unit_Content_Code= '', 
	Module_Name = '',
	Work_Area_Code = '',
	Relative_X_Axis = '',
	Relative_Y_Axis = '',
	Relative_Z_Axis = '',
	Location_Type_Code = '', 
	Put_Wall_Cubby_Code = '',
	Level_Code =  ISNULL(level,''),
	Aisle_Code =  ISNULL(aisle,''),
	Workstation_Code = '',
	Move_Arrive_Date_Time =  ISNULL(arriveTime,''),
	Move_Leave_Date_Time =  ISNULL(leaveTime,'') 
FROM [STAGE].[FCT_MOVEMENTS_CONNECTION_'+ @lSourceSystem +'] with(nolock)
WHERE endTime IS NOT NULL AND startTime IS NOT NULL

UNION

-- STACKER
SELECT DISTINCT
	Hour_Quarter_ID= substring(Replace(Replace(CONVERT(VARCHAR(13),endTime,120),' ',''),'-',''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20),endTime,120),15,2) as INT)/15)),
	Record_Timestamp =  ISNULL(endTime,''),
	Move_Start_Date_Time =  ISNULL(startTime,''),
	Load_Unit_Code =  ISNULL(loadUnitId,''),
	Source_Location_Code =  ISNULL(sourceLocationId, ''),
	Destination_Location_Code =  ISNULL(destinationLocationId, ''),
	Device_Code = COALESCE(shuttleId,liftid,''),
	Physical_Device_Code =  ISNULL(hardwareIdentifier,''),
	Movement_Type_Code =  ISNULL(MovementType,''),
	Item_Code = OA.Item_Code_Generator(SkuID, null,null,null),
	Status_Code =  ISNULL(eventCode,''),
	Transport_Reason_Code =  ISNULL(transportReason,''),
	Transport_Request_ID =  ISNULL(transportRequestId,''),
	Group_Transport_Request_ID = ISNULL(transportRequestId,''),
	Move_Duration_Seconds = DATEDIFF(second,StartTime,EndTime),
	Item_Count= '',
	X_Distance_Traveled =  ISNULL(deltax,''),
	Aisles_Traveled_Count =  ISNULL(deltaAisle,''),
	Subsystem_Code =  'STACKERCRANE',
	Subsystem_Category= 'DEMATIC IQ',
	Source_System = ''+ @lSourceSystem+'',
	Item_SKU =  ISNULL(CASE WHEN skuId LIKE '%EMPTY_SKU%' THEN '' ELSE skuId END, ''),
	Module_Code= '',-- AutoStore Only, 
	Container_Type_Code= '',-- AutoStore Only, 
	Load_Unit_Content_Code= '',-- AutoStore Only, 
	Module_Name = '',-- AutoStore Only, 
	Work_Area_Code = '',-- AutoStore Only, 
	Relative_X_Axis = '',-- AutoStore Only, 
	Relative_Y_Axis = '',-- AutoStore Only, 
	Relative_Z_Axis = '',-- AutoStore Only, 
	Location_Type_Code = '',-- AutoStore Only, 
	Put_Wall_Cubby_Code = '',-- AutoStore Only, 
	Level_Code =  ISNULL(level,''),
	Aisle_Code =  ISNULL(aisle,''),
	Workstation_Code = '' ,
	Move_Arrive_Date_Time =  ISNULL(arriveTime,''),
	Move_Leave_Date_Time =  ISNULL(leaveTime,'')
FROM [STAGE].[FCT_MOVEMENTS_VEHICLE_'+ @lSourceSystem +']
WHERE endTime IS NOT NULL AND startTime IS NOT NULL

UNION

-- STACKER
SELECT DISTINCT
	Hour_Quarter_ID= substring(Replace(Replace(CONVERT(VARCHAR(13),endTime,120),' ',''),'-',''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20),endTime,120),15,2) as INT)/15)),
	Record_Timestamp =  ISNULL(endTime,''),
	Move_Start_Date_Time =  ISNULL(startTime,''),
	Load_Unit_Code =  ISNULL(loadUnitId,''),
	Source_Location_Code =  ISNULL(sourceLocationId, ''),
	Destination_Location_Code =  ISNULL(destinationLocationId, ''),
	Device_Code = COALESCE(shuttleId,liftid,''),
	Physical_Device_Code =  ISNULL(hardwareIdentifier,''),
	Movement_Type_Code =  ISNULL(MovementType,''),
	Item_Code = OA.Item_Code_Generator(SkuID, null,null,null),
	Status_Code =  ISNULL(eventCode,''),
	Transport_Reason_Code =  ISNULL(transportReason,''),
	Transport_Request_ID =  ISNULL(transportRequestId,''),
	Group_Transport_Request_ID = ISNULL(transportRequestId,''),
	Move_Duration_Seconds = DATEDIFF(second,StartTime,EndTime),
	Item_Count= '',
	X_Distance_Traveled =  ISNULL(deltax,''),
	Aisles_Traveled_Count =  ISNULL(deltaAisle,''),
	Subsystem_Code =  'STACKERCRANE',
	Subsystem_Category= 'DEMATIC IQ',
	Source_System = ''+ @lSourceSystem+'',
	Item_SKU =  ISNULL(CASE WHEN skuId LIKE '%EMPTY_SKU%' THEN '' ELSE skuId END, ''),
	Module_Code= '',-- AutoStore Only, 
	Container_Type_Code= '',-- AutoStore Only, 
	Load_Unit_Content_Code= '',-- AutoStore Only, 
	Module_Name = '',-- AutoStore Only, 
	Work_Area_Code = '',-- AutoStore Only, 
	Relative_X_Axis = '',-- AutoStore Only, 
	Relative_Y_Axis = '',-- AutoStore Only, 
	Relative_Z_Axis = '',-- AutoStore Only, 
	Location_Type_Code = '',-- AutoStore Only, 
	Put_Wall_Cubby_Code = '',-- AutoStore Only, 
	Level_Code =  ISNULL(level,''),
	Aisle_Code =  ISNULL(aisle,''),
	Workstation_Code = '',
	Move_Arrive_Date_Time =  ISNULL(arriveTime,''),
	Move_Leave_Date_Time =  ISNULL(leaveTime,'') 
FROM [STAGE].[FCT_MOVEMENTS_STACKERCRANE_'+ @lSourceSystem +']
WHERE endTime IS NOT NULL AND startTime IS NOT NULL

UNION
-- Autostore
SELECT DISTINCT
	Hour_Quarter_ID= substring(Replace(Replace(CONVERT(VARCHAR(13),CAST(event_time as datetime2(7)),120),' ',''),'-',''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20),CAST(event_time as datetime2(7))),15,2) as INT)/15)),
	Record_Timestamp = ISNULL(DATEADD(ns, CAST(RIGHT(ISNULL(process_epoch_ns,0), 6) AS INT), CAST(event_time AS datetime2(7))), ''),
	Move_Start_Date_Time = ISNULL(DATEADD(ns, CAST(RIGHT(ISNULL(process_epoch_ns,0), 6) AS INT), CAST(event_time AS datetime2(7))), ''),
	Load_Unit_Code =  ISNULL(container_id,''),
	Source_Location_Code =  '', 
	Destination_Location_Code = 
		CASE WHEN x_position IS NULL 
			THEN LAG (ISNULL(autostore_id,'') +'-'+ ISNULL(grid_id,'NA') +'-'+ ISNULL(x_position,'*') +'-'+ ISNULL(y_position,'*') +'-'+ ISNULL(depth,'*')) OVER (PARTITION BY container_id ORDER BY event_time ASC) 
			ELSE ISNULL(autostore_id,'') +'-'+ ISNULL(grid_id,'NA') +'-'+ ISNULL(x_position,'*') +'-'+ ISNULL(y_position,'*') +'-'+ ISNULL(depth,'*')
		END,
	Device_Code = '',
	Physical_Device_Code = '',
	Movement_Type_Code = 'AutoStore Container',
	Item_Code= '',
	Status_Code = CASE container_mode WHEN 'G' THEN 'G-GRID' WHEN 'O' THEN 'O-OPEN' WHEN 'T' THEN 'T-PREPARED' WHEN 'F' THEN 'F-FORECAST' WHEN 'C' THEN 'C-CLOSED' WHEN 'P' THEN 'P-PORT' WHEN 'R' THEN 'R-TOGRID' WHEN 'X' THEN 'X-OUTSIDE' ELSE ISNULL(container_mode,'') END,
	Transport_Reason_Code = 'ContainerMovementFact',
	Transport_Request_ID = '',
	Group_Transport_Request_ID = '',
	Move_Duration_Seconds =  0,
	Item_Count='',
	X_Distance_Traveled ='' , 
	Aisles_Traveled_Count ='',
	Subsystem_Code = 'AUTOSTORE', 
	Subsystem_Category= 'AUTOSTORE',
	Source_System = ''+ @lSourceSystem+'',
	Item_SKU =  '',
	Module_Code= ISNULL(autostore_Id,''),
	Container_Type_Code= ISNULL(container_height,''),
	Load_Unit_Content_Code= ISNULL(container_content_code,''),
	Module_Name =  ISNULL(grid_id,''),
	Work_Area_Code = '',
	Relative_X_Axis = ISNULL(x_position,''),
	Relative_Y_Axis = ISNULL(y_position,''),
	Relative_Z_Axis = ISNULL(depth,''), 
	Location_Type_Code = '',
	Put_Wall_Cubby_Code = '',
	Level_Code =  '',
	Aisle_Code =  '',
	Workstation_Code = ''	,
	Move_Arrive_Date_Time =  '',
	Move_Leave_Date_Time =  ''
FROM [STAGE].[FCT_CONTAINER_MOVEMENT_'+ @lSourceSystem +']
