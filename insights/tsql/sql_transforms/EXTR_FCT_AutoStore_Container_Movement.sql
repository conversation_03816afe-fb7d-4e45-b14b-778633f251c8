SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lQuery_SQLStmt NVARCHAR(MAX),
        @lChecks_SQLStmt NVARCHAR(MAX),
        @lDrops_SQLStmt NVARCHAR(MAX);


SELECT @lChecks_SQLStmt =  
' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
' where t.name = ''FCT_CONTAINER_MOVEMENT_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
' BEGIN 
	CREATE TABLE [stage].FCT_CONTAINER_MOVEMENT_' + @lSourceSystem + ' (
		[autostore_id ] [varchar](255) NULL,
		[event_time] [varchar](255) NULL,
		[container_id] [varchar](255) NULL,
		[grid_id] [varchar](255) NULL,
		[x_position] [varchar](255) NULL,
		[y_position] [varchar](255) NULL,
		[depth] [varchar](255) NULL,
		[container_mode] [varchar](255) NULL,
		[container_content_code] [varchar](255) NULL,
		[container_height] [varchar](255) NULL,
		[event_type] [varchar](255) NULL,
		[process_epoch_ns] [varchar](255) default '''',
		[Key_ID] [int] IDENTITY(1,1) NOT NULL
)
 END' 
		

SELECT @lQuery_SQLStmt = '
SELECT DISTINCT
	TransportRequestID = '''',
	SourceLocationID = '''', 
	DestinationLocationID = 
		CASE WHEN x_position IS NULL 
			THEN LAG (ISNULL(autostore_id,'''') +''-''+ ISNULL(grid_id,''NA'') +''-''+ ISNULL(x_position,''*'') +''-''+ ISNULL(y_position,''*'') +''-''+ ISNULL(depth,''*'')) OVER (PARTITION BY container_id ORDER BY event_time ASC) 
			ELSE ISNULL(autostore_id,'''') +''-''+ ISNULL(grid_id,''NA'') +''-''+ ISNULL(x_position,''*'') +''-''+ ISNULL(y_position,''*'') +''-''+ ISNULL(depth,''*'')
		END,
	LoadUnitID = ISNULL(container_id,''''),
	StartTime = DATEADD(ns, ISNULL(CAST(RIGHT(ISNULL(process_epoch_ns,0), 6) AS INT),0), CAST(REPLACE(event_time, ''T'','' '') AS datetime2(7))),
	EndTime = DATEADD(ns, ISNULL(CAST(RIGHT(ISNULL(process_epoch_ns,0), 6) AS INT),0), CAST(REPLACE(event_time, ''T'','' '') AS datetime2(7))),
	Duration = '''',
	EventCode = CASE container_mode WHEN ''G'' THEN ''G-GRID'' WHEN ''O'' THEN ''O-OPEN'' WHEN ''T'' THEN ''T-PREPARED'' WHEN ''F'' THEN ''F-FORECAST'' WHEN ''C'' THEN ''C-CLOSED'' WHEN ''P'' THEN ''P-PORT'' WHEN ''R'' THEN ''R-TOGRID'' WHEN ''X'' THEN ''X-OUTSIDE'' ELSE ISNULL(container_mode,'''') END,
	TransportReason = ISNULL(event_type,''''),
	SubsystemID = '''',
	DeviceID = '''',
	Aisle = '''',
	Level = '''',
	MovementType = ''AutoStore Container'',
	DeltaX = '''',
	DeltaAisle = '''',
	SKUID = '''',
	ProductRangeID = '''',
	Subsystem_Code = ''AUTOSTORE'',
	Subsystem_Category = ''AUTOSTORE'',
	Record_Timestamp_Offset = '''',
	HardwareIdentifier = '''',
	Source_System = '''+ @lSourceSystem +''',
	Module_Code = ISNULL(autostore_Id,''''),
	Module_Name = ISNULL(grid_id,''''),
	Container_Type_ID = ISNULL(container_height,''''),
	Load_Unit_Content_Code = ISNULL(container_content_code,''''),
	Relative_X_Axis = ISNULL(x_position,''''),
	Relative_Y_Axis = ISNULL(y_position,''''),	
	Relative_Z_Axis = ISNULL(depth,''''),
	Location_Type_Code = '''',
	Put_Wall_Cubby_Code = '''',
	Level_Code = '''',
	Aisle_Code = '''',
	Workstation_Code = ''''
FROM [STAGE].[FCT_CONTAINER_MOVEMENT_'+ @lSourceSystem +'] WITH(NOLOCK) order by loadunitid'



SELECT @lDrops_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_CONTAINER_MOVEMENT_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  DROP TABLE [STAGE].[FCT_CONTAINER_MOVEMENT_'+@lSourceSystem +']'

--print @lQuery_SQLStmt
EXEC sp_executesql @lChecks_SQLStmt
EXEC sp_executesql @lQuery_SQLStmt
-- EXEC sp_executesql @lDrops_SQLStmt

