SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)'; 
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id' +   
                   ' where t.name = ''OVERHEAD_SORT_ERRORFACT_' +  @lSourceSystem + ''' and s.name = ''Stage'') ' + 
                   ' BEGIN ' +
   ' SELECT DISTINCT ' +
   '      Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),eventTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,eventTime)/15))  ' +
   '     , Record_Timestamp = eventTime '       +
   '     , Client = ISNULL(clientId, '''') ' +
   '     , Item_Code = OA.Item_Code_Generator(skuId,null,skuQuantityUnitId,clientId)' +
   '     , Item_SKU = ISNULL(skuId, '''') ' +
   '     , UOM = ISNULL(skuQuantityUnitId, '''') ' +
   '     , SKU_Qualifier = ISNULL(skuQualifier, '''') ' +
   '     , Fault_Status_Code = ISNULL(reasonCode, '''') ' +
   '     , Overhead_Intended_Destination_Name = ISNULL(plannedExit, '''') ' +
   '     , Overhead_Intended_Destination_Code = ISNULL(CASE WHEN CHARINDEX(''.'',plannedExit) > 0 ' +
   '                           THEN REVERSE(LEFT(REVERSE(plannedExit), CHARINDEX(''.'',REVERSE(plannedExit)) - 1)) ' +
   '                           ELSE plannedExit ' +
   '                           END,'''') ' +
   '     , Overhead_Intended_Destination_Area = ISNULL(CASE WHEN CHARINDEX(''.'',plannedExit) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(plannedExit),CHARINDEX(''.'',REVERSE(plannedExit)) + 1, LEN(plannedExit))),3) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Overhead_Intended_Destination_Group = ISNULL(CASE WHEN CHARINDEX(''.'',plannedExit) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(plannedExit),CHARINDEX(''.'',REVERSE(plannedExit)) + 1, LEN(plannedExit))),2) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Overhead_Intended_Destination_SubGroup = ISNULL(CASE WHEN CHARINDEX(''.'',plannedExit) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(plannedExit),CHARINDEX(''.'',REVERSE(plannedExit)) + 1, LEN(plannedExit))),1) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +  
   '     , Overhead_Destination_Name = ISNULL(actualExit, '''') ' +   
   '     , Overhead_Actual_Destination_Code = ISNULL(CASE WHEN CHARINDEX(''.'',actualExit) > 0 ' +
   '                           THEN REVERSE(LEFT(REVERSE(actualExit), CHARINDEX(''.'',REVERSE(actualExit)) - 1)) ' +
   '                           ELSE actualExit ' +
   '                           END,'''') ' +
   '     , Overhead_Destination_Area = ISNULL(CASE WHEN CHARINDEX(''.'',actualExit) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(actualExit),CHARINDEX(''.'',REVERSE(actualExit)) + 1, LEN(actualExit))),3) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Overhead_Destination_Group = ISNULL(CASE WHEN CHARINDEX(''.'',actualExit) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(actualExit),CHARINDEX(''.'',REVERSE(actualExit)) + 1, LEN(actualExit))),2) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Overhead_Destination_SubGroup = ISNULL(CASE WHEN CHARINDEX(''.'',actualExit) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(actualExit),CHARINDEX(''.'',REVERSE(actualExit)) + 1, LEN(actualExit))),1) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Overhead_Scanner_Location = ISNULL(readerLocation, '''') ' +  
   '     , Subsystem_Code = ''OVERHEAD_SORT'' ' +
   '     , Subsystem_Category = ''DEMATIC IQ'' ' +
   '     , Overhead_Order_Run = ISNULL(runId,'''')  ' +
   '     , Overhead_Order_Run_Position = ISNULL(runPosition,'''')  ' + 
   '     , Overhead_Order_Code = ISNULL(CASE WHEN orderId is not null AND clientId is not null AND orderId != ISNULL(clientId,'''') '+
   '                          THEN clientId + ''#'' + orderId ELSE orderId END ,'''') ' +
   '     , Overhead_Order_Line = ISNULL(orderLineId,'''') '+
   '     , Overhead_Carrier_Code = ISNULL(sortationLoadUnitid,'''') '+
   '     , Source_System = ''' + @lSourceSystem +  '''' +
   '     , Status_Category_Code = '''' '+
   ' FROM STAGE.OVERHEAD_SORT_ERRORFACT_' + @lSourceSystem +
   '	ORDER BY Record_Timestamp, Overhead_Intended_Destination_Code, Overhead_Actual_Destination_Code, Overhead_Scanner_Location, Overhead_Order_Code, Fault_Status_Code, Subsystem_Code, Subsystem_Category ' +  
   ' END ' +
   -- ' DROP TABLE IF EXISTS [STAGE].OVERHEAD_SORT_ERRORFACT_' + @lSourceSystem
   ' IF OBJECT_ID(''[STAGE].OVERHEAD_SORT_ERRORFACT_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].OVERHEAD_SORT_ERRORFACT_'+@lSourceSystem; 
   

EXEC sp_executesql @lSQLStmt