SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)', 
-- DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware',
        @lPrimary_SQLStmt NVARCHAR(MAX),
		@lSecondary_SQLStmt NVARCHAR(MAX);

SELECT @lPrimary_SQLStmt = 
' DECLARE @lStatement NVARCHAR(MAX) = '''',  @lIncludeUnion BIT = 0;
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' + 
           ' where t.name = ''VFD_DATA_POINT_PARAMETER_MSG_' + @lSourceSystem + ''' and s.name = ''Stage'') '  +
' BEGIN ' + 
	' SELECT @lstatement = '' SELECT DISTINCT Record_Timestamp '' + 
	  '' ,Hour_Quarter_ID = RIGHT(LEFT(REPLACE(REPLACE(REPLACE(CONVERT(VARCHAR(24),[Datetime],121),'''':'''',''''''''),''''-'''',''''''''),'''' '''',''''''''),10),8) + LTRIM(STR(datepart(minute,[Datetime]))/15) '' +
	  '' ,Device_Code '' +
	  '' ,Device_Name  ''+
      '' ,Subsystem_Code  '' +
	  '' ,Subsystem_Category  '' +
	  '' ,Measurement_Code  '' +
      '', Unit_Of_Measure ''+
	  '', Data_Point_Code ''+
	  '', Phase_Code ''+
	  '', Min_Value  '' +
	  '', Max_Value  '' +
	  '', Avg_Value  '' +
	  '', Source_System = ''''' + @lSourceSystem + ''''''' +
      '' FROM STAGE.VFD_DATA_POINT_PARAMETER_MSG_'+ @lSourceSystem + ' '' +
	  '' CROSS APPLY (VALUES ([DateTime], DeviceID, ''''VFD'''', ''''SORT'''', ''''APE'''', ''''Frequency'''', '''''''', '''''''', '''''''', OutputFreqMin, OutputFreqMax, OutputFreqAve), ''+
      ''               ([DateTime], DeviceID, ''''VFD'''', ''''SORT'''', ''''APE'''', ''''Current'''', '''''''', '''''''', '''''''', OutputCurrMin, OutputCurrMax, OutputCurrAve), ''+
      ''               ([DateTime], DeviceID, ''''VFD'''', ''''SORT'''', ''''APE'''', ''''Voltage'''', '''''''', '''''''', '''''''', OutputVoltMin,OutputVoltMax,OutputVoltAve), ''+
      ''               ([DateTime], DeviceID, ''''VFD'''', ''''SORT'''', ''''APE'''', ''''DC_Bus_Voltage'''', '''''''', '''''''', '''''''', DCBusMin,DCBusMax,DCBusAve), ''+
      ''               ([DateTime], DeviceID, ''''VFD'''', ''''SORT'''', ''''APE'''', ''''Drive_Temperature'''', '''''''', '''''''', '''''''', DriveTempMin,DriveTempMax,DriveTempAve), ''+
      ''	           ([DateTime], DeviceID, ''''VFD'''', ''''SORT'''', ''''APE'''', ''''Control_Module_Temperature'''', '''''''', '''''''', '''''''', ControlModTempMin ,ControlModTempMax,ControlModTempAve)) ''+
	  ''				 AS P ( Record_Timestamp, Device_Code, Device_Name, Subsystem_Code, Subsystem_Category, Measurement_Code, ''+
	  ''				        Unit_Of_Measure, Data_Point_Code, Phase_Code, Min_Value, Max_Value, Avg_Value) '''  +
      
	  ' SET @lIncludeUnion = ''1''' +

' End ' +

'IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' + 
           ' where t.name = ''PLC_STATUS_DATA_POINT_MSG_' + @lSourceSystem + ''' and s.name = ''Stage'') '  +
		   
' BEGIN ' + 
     ' IF @lIncludeUnion = ''1'' '+
	 ' BEGIN SET @lstatement = @lstatement + '' UNION ALL '' END' + 
    
	' SELECT @lstatement = @lstatement + ''SELECT DISTINCT Record_Timestamp = [Datetime] ''+ 
       '',Hour_Quarter_ID = RIGHT(LEFT(REPLACE(REPLACE(REPLACE(CONVERT(VARCHAR(24),[Datetime],121),'''':'''',''''''''),''''-'''',''''''''),'''' '''',''''''''),10),8) + LTRIM(STR(datepart(minute,[Datetime]))/15) '' +
	   '', Device_Code = DeviceID '' +
       '', Device_Name = ''''PLC'''' ''+
       '', Subsystem_Code = ''''SORT'''' ''+
	   '', Subsystem_Category = ''''APE''''  ''+
	   '', Measurement_Code = ''''Scan_Time'''' ''+
	   '', Unit_Of_Measure = ''''Seconds'''' ''+
	   '', Data_Point_Code = '''''''' ''+
	   '', Phase_Code = '''''''' ''+
	   '', Min_Value = ScanTimeMin ''+
	   '', Max_Value = ScanTimeMax ''+
	   '', Avg_Value = ScanTimeAve ''+
	   '', Source_System = ''''' + @lSourceSystem + ''''''' +
       '' FROM [STAGE].PLC_STATUS_DATA_POINT_MSG_'+ @lSourceSystem + ''''  +

	   ' SET @lIncludeUnion = ''1''' +

' END ' +

'IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' + 
           ' where t.name = ''SORTER_COMM_DATA_POINT_MSG_' + @lSourceSystem + ''' and s.name = ''Stage'') '  +
		   
' BEGIN ' + 
     ' IF @lIncludeUnion = ''1'' '+
	 ' BEGIN SET @lstatement = @lstatement + '' UNION ALL '' END' + 
    
	' SELECT @lstatement = @lstatement + ''SELECT DISTINCT Record_Timestamp ''+ 
       '', Hour_Quarter_ID = RIGHT(LEFT(REPLACE(REPLACE(REPLACE(CONVERT(VARCHAR(24),[Datetime],121),'''':'''',''''''''),''''-'''',''''''''),'''' '''',''''''''),10),8) + LTRIM(STR(datepart(minute,[Datetime]))/15) '' +
	   '', Device_Code  '' +
       '', Device_Name ''+
       '', Subsystem_Code  ''+
	   '', Subsystem_Category ''+
	   '', Measurement_Code ''+
	   '', Unit_Of_Measure  ''+
	   '', Data_Point_Code  ''+
	   '', Phase_Code  ''+
	   '', Min_Value ''+
	   '', Max_Value ''+
	   '', Avg_Value ''+
	   '', Source_System = ''''' + @lSourceSystem + ''''''' +
       '' FROM [STAGE].SORTER_COMM_DATA_POINT_MSG_'+ @lSourceSystem+ ' '' +
	   '' CROSS APPLY ( VALUES ([Datetime], DeviceID, '''''''', ''''SORT'''', ''''APE'''', ''''Dispatch'''',''''Milliseconds'''', '''''''','''''''',DispatchTimeMin, DispatchTimeMax, DispatchTimeAve),
                     ([Datetime], DeviceID, '''''''', ''''SORT'''', ''''APE'''', ''''Divert'''', ''''Milliseconds'''', '''''''','''''''', DivertTimeMin, DivertTimeMax, DivertTimeAve)) 
			AS SC (Record_Timestamp, Device_Code, Device_Name, Subsystem_Code, Subsystem_Category, Measurement_Code,
					         Unit_Of_Measure,Data_Point_Code, Phase_Code, Min_Value, Max_Value, Avg_Value) ''' +
' END ' +

' EXEC sp_executesql @lstatement; ' 

-- SELECT @lSecondary_SQLStmt = 'DROP TABLE IF EXISTS [STAGE].SORTER_COMM_DATA_POINT_MSG_' + @lSourceSystem
 SELECT @lSecondary_SQLStmt = ' IF OBJECT_ID(''[STAGE].SORTER_COMM_DATA_POINT_MSG_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].SORTER_COMM_DATA_POINT_MSG_'+@lSourceSystem +
 ' IF OBJECT_ID(''[STAGE].VFD_DATA_POINT_PARAMETER_MSG_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].VFD_DATA_POINT_PARAMETER_MSG_'+@lSourceSystem;  
 

EXEC sp_executesql @lPrimary_SQLStmt;
EXEC sp_executesql @lSecondary_SQLStmt; 
