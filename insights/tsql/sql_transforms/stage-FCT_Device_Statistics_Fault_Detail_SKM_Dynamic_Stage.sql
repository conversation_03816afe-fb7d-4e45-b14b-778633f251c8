SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)'  
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'
DECLARE @lSQLStmt NVARCHAR(MAX)

-- Verify the stage table exist
SET @lSQLStmt = '
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''Fct_Device_Statistics_Fault_Detail_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
	-- Use newly created table for output
	SELECT DISTINCT
		Record_Timestamp = ST.eventTime,
		Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13),ST.eventTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20),ST.eventTime,120),15,2) as INT)/15)),
 		Device_Code = ST.deviceid,
		Fault_Code = ISNULL(Fault_ID, ''''),
		Fault_Count = ISNULL(Fault_Count, ''''),
		Fault_Rank = ISNULL(Fault_Rank, ''''),
		Subsystem_Code = ''MULTISHUTTLE'',
		Subsystem_Category = ''DEMATIC IQ'',
		Source_System = '''+ @lSourceSystem +'''
	FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +' ST
	-- Pivot Faults
	LEFT  JOIN (
				SELECT deviceid,eventTime, faultCode1stImportantFault as Fault_ID,quantity1stImportantFault as Fault_Count, Fault_Rank = 1 FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +'  WHERE LEN(ISNULL(faultCode1stImportantFault,'''')) > 0 AND faultCode1stImportantFault !=0
				UNION
				SELECT deviceid,eventTime, faultCode2ndImportantFault, quantity2ndImportantFault, 2 FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +' WHERE LEN(ISNULL(faultCode2ndImportantFault,'''')) > 0 AND faultCode2ndImportantFault !=0
				UNION
				SELECT deviceid,eventTime, faultCode3rdImportantFault, quantity3rdImportantFault, 3 FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +' WHERE LEN(ISNULL(faultCode3rdImportantFault,'''')) > 0 AND faultCode3rdImportantFault !=0
				UNION
				SELECT deviceid,eventTime, faultCode4thImportantFault, quantity4thImportantFault, 4 FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +' WHERE LEN(ISNULL(faultCode4thImportantFault,'''')) > 0 AND faultCode4thImportantFault !=0
				UNION
				SELECT deviceid,eventTime, faultCode5thImportantFault, quantity5thImportantFault, 5 FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +' WHERE LEN(ISNULL(faultCode5thImportantFault,'''')) > 0 AND faultCode5thImportantFault !=0
				UNION
				SELECT deviceid,eventTime, faultCode6thImportantFault, quantity6thImportantFault, 6 FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +' WHERE LEN(ISNULL(faultCode6thImportantFault,'''')) > 0 AND faultCode6thImportantFault !=0
				UNION
				SELECT deviceid,eventTime, faultCode7thImportantFault, quantity7thImportantFault, 7 FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +' WHERE LEN(ISNULL(faultCode7thImportantFault,'''')) > 0 AND faultCode7thImportantFault !=0
				UNION
				SELECT deviceid,eventTime, faultCode8thImportantFault, quantity8thImportantFault, 8 FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +' WHERE LEN(ISNULL(faultCode8thImportantFault,'''')) > 0 AND faultCode8thImportantFault !=0
				UNION
				SELECT deviceid,eventTime, faultCode9thImportantFault, quantity9thImportantFault, 9 FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +' WHERE LEN(ISNULL(faultCode9thImportantFault,'''')) > 0 AND faultCode9thImportantFault !=0
				UNION
				SELECT deviceid,eventTime, faultCode10thImportantFault, quantity10thImportantFault, 10 FROM STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem +' WHERE LEN(ISNULL(faultCode10thImportantFault,'''')) > 0 AND faultCode10thImportantFault !=0
				) PIVOT_Faults
				ON ST.eventTime = PIVOT_Faults.eventTime AND ST.deviceid = PIVOT_Faults.deviceid
								
END
'

EXEC sp_executesql @lSQLStmt

SET @lSQLStmt = '
DROP TABLE IF EXISTS STAGE.Fct_Device_Statistics_Fault_Detail_'+ @lSourceSystem 

EXEC sp_executesql @lSQLStmt