SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 
DECLARE @lSQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Amcap_Singulation_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	SELECT DISTINCT
		Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), eventTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), eventTime,120),15,2) as INT)/15))
		,Record_Timestamp = eventTime
		,Source_Pallet_Code = ISNULL(sourcePalletId,'''')
		,Device_Code = ISNULL(sourceWorkstationId,'''')
		,Aisle_Code = ISNULL(aisle,'''')
		,Wave_Code = ''''
		,Item_Code = OA.Item_Code_Generator(skuId, null,null,null)
		,Operator_Code = ''''
		,Load_Unit_Code = ISNULL(loadUnitId,'''')
		,Transport_Request_ID = ISNULL(transportRequestId,'''')
		,Subsystem_Code = CASE WHEN sourceWorkstationId IS NOT NULL THEN ''Depalletizer'' ELSE '''' END
		,Subsystem_Category = CASE WHEN sourceWorkstationId IS NOT NULL THEN ''Dematic IQ'' ELSE '''' END
		,Source_System = ''' + @lSourceSystem + '''
		,Item_Category_Code = ''''
		,Buffer_Code = ''''
	  FROM [STAGE].[FCT_Amcap_Singulation_' + @lSourceSystem + ']
END
'

SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Amcap_Singulation_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
                           DROP TABLE [STAGE].[FCT_Amcap_Singulation_'+@lSourceSystem +']'

--PRINT(@lSQLStmt)
EXEC sp_executesql @lSQLStmt
EXEC sp_executesql @Drop_SQLStmt