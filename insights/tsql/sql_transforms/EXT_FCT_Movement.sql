SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(100) = '$(Source_System)'
--Set @lSourceSystem = 'DematicSoftware';


DECLARE @lSQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_MOVEMENTS_CONNECTION_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 transportRequestId= '''', 	subsystemId= '''', 	shuttleId= '''', 	liftId= '''', 	sourceLocationId= '''', 	destinationLocationId= '''', 	aisle= '''', 	level= '''', 	loadUnitId= '''', 	MovementType= '''', 	startTime= '''', 	endTime= '''', 	duration= '''', 	eventCode= '''', 	deltax= '''', 	deltaAisle= '''', 	skuId= '''', 	productRangeId= '''', 	transportReason= '''', 	NOKCounter= '''', 	FaultStartTime= '''', 	FaultPhysicalShuttleID= '''', 	FaultSourceLocationID= '''', 	FaultCode= '''', 	FaultClassification= '''', 	FaultAckTime= '''', 	FaultDuration= '''', 	FaultRepairTime= '''', 	FaultEndTime= '''', 	Record_Timestamp_Offset= '''', 	hardwareIdentifier= '''', 	Key_ID = ''''	
		INTO STAGE.FCT_MOVEMENTS_CONNECTION_' + @lSourceSystem + '
	END;'
 EXEC sp_executesql @lSQLStmt

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_MOVEMENTS_MULTISHUTTLE_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 transportRequestId= '''', 	subsystemId= '''', 	shuttleId= '''', 	liftId= '''', 	sourceLocationId= '''', 	destinationLocationId= '''', 	aisle= '''', 	level= '''', 	loadUnitId= '''', 	MovementType= '''', 	startTime= '''', 	endTime= '''', 	duration= '''', 	eventCode= '''', 	deltax= '''', 	deltaAisle= '''', 	skuId= '''', 	productRangeId= '''', 	transportReason= '''', 	NOKCounter= '''', 	FaultStartTime= '''', 	FaultPhysicalShuttleID= '''', 	FaultSourceLocationID= '''', 	FaultCode= '''', 	FaultClassification= '''', 	FaultAckTime= '''', 	FaultDuration= '''', 	FaultRepairTime= '''', 	FaultEndTime= '''', 	Record_Timestamp_Offset= '''', 	hardwareIdentifier= '''', 	Key_ID = ''''	
		INTO STAGE.FCT_MOVEMENTS_MULTISHUTTLE_' + @lSourceSystem + '
	END;'
 EXEC sp_executesql @lSQLStmt

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_MOVEMENTS_VEHICLE_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 transportRequestId= '''', 	subsystemId= '''', 	shuttleId= '''', 	liftId= '''', 	sourceLocationId= '''', 	destinationLocationId= '''', 	aisle= '''', 	level= '''', 	loadUnitId= '''', 	MovementType= '''', 	startTime= '''', 	endTime= '''', 	duration= '''', 	eventCode= '''', 	deltax= '''', 	deltaAisle= '''', 	skuId= '''', 	productRangeId= '''', 	transportReason= '''', 	NOKCounter= '''', 	FaultStartTime= '''', 	FaultPhysicalShuttleID= '''', 	FaultSourceLocationID= '''', 	FaultCode= '''', 	FaultClassification= '''', 	FaultAckTime= '''', 	FaultDuration= '''', 	FaultRepairTime= '''', 	FaultEndTime= '''', 	Record_Timestamp_Offset= '''', 	hardwareIdentifier= '''', 	Key_ID = ''''	
		INTO STAGE.FCT_MOVEMENTS_VEHICLE_' + @lSourceSystem + '
	END;'
 EXEC sp_executesql @lSQLStmt

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_CONTAINER_MOVEMENT_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0  autostore_id= '''', 	event_time= '''', 	container_id= '''', 	grid_id= '''', 	x_position= '''', 	y_position= '''', 	depth= '''', 	container_mode= '''', 	container_content_code= '''', 	container_height= '''', 	event_type= '''', 	process_epoch_ns= '''', 	Key_ID= '''' 	
		INTO STAGE.FCT_CONTAINER_MOVEMENT_' + @lSourceSystem + '
	END;'


EXEC sp_executesql @lSQLStmt

 
SET @lSQLStmt = '
SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

Select
	TransportRequestID= ISNULL(TransportRequestID,''''),
	SubsystemID = ISNULL(SubsystemID,''''),
	DeviceID = ISNULL(ISNULL(shuttleId,liftid),''''),
	SourceLocationID= ISNULL(SourceLocationID,''''),
	DestinationLocationID= ISNULL(DestinationLocationID,''''),
	Aisle= ISNULL(Aisle,''''),
	Level= ISNULL(Level,''''),
	LoadUnitID= ISNULL(LoadUnitID,''''),
	MovementType= ISNULL(MovementType,''''),
	StartTime= ISNULL(StartTime,''''),
	EndTime= ISNULL(EndTime,''''),
	Duration= ISNULL(Duration,''''),
	EventCode= ISNULL(EventCode,''''),
	Deltax= ISNULL(Deltax,''''),
	DeltaAisle= ISNULL(DeltaAisle,''''),
	SkuID= ISNULL(SkuID,''''),
	ProductRangeID= ISNULL(ProductRangeID,''''),
	TransportReason= ISNULL(TransportReason,''''),
	Subsystem_Code=  ISNULL(subsystemId,''''),--= ISNULL(productRangeId,''''),
	Subsystem_Category= ''Dematic IQ'',
	Record_Timestamp_Offset= ISNULL(Record_Timestamp_Offset,''''),
	hardwareIdentifier = ISNULL(hardwareIdentifier,'''')
FROM [STAGE].[FCT_MOVEMENTS_CONNECTION_'+ @lSourceSystem +'] with(nolock)
UNION
Select
	TransportRequestID= ISNULL(TransportRequestID,''''),
	SubsystemID = ISNULL(SubsystemID,''''),
	DeviceID = ISNULL(ISNULL(shuttleId,liftid),''''),
	SourceLocationID= ISNULL(SourceLocationID,''''),
	DestinationLocationID= ISNULL(DestinationLocationID,''''),
	Aisle= ISNULL(Aisle,''''),
	Level= ISNULL(Level,''''),
	LoadUnitID= ISNULL(LoadUnitID,''''),
	MovementType= ISNULL(MovementType,''''),
	StartTime= ISNULL(StartTime,''''),
	EndTime= ISNULL(EndTime,''''),
	Duration= ISNULL(Duration,''''),
	EventCode= ISNULL(EventCode,''''),
	Deltax= ISNULL(Deltax,''''),
	DeltaAisle= ISNULL(DeltaAisle,''''),
	SkuID= ISNULL(SkuID,''''),
	ProductRangeID= ISNULL(ProductRangeID,''''),
	TransportReason= ISNULL(TransportReason,''''),
	Subsystem_Code=  ISNULL(subsystemId,''''),--= ISNULL(productRangeId,''''),
	Subsystem_Category= ''Dematic IQ'',
	Record_Timestamp_Offset= ISNULL(Record_Timestamp_Offset,''''),
	hardwareIdentifier = ISNULL(hardwareIdentifier,'''')
FROM [STAGE].[FCT_MOVEMENTS_MULTISHUTTLE_'+ @lSourceSystem +']

UNION

Select
	TransportRequestID= ISNULL(TransportRequestID,''''),
	SubsystemID = ISNULL(SubsystemID,''''),
	DeviceID = ISNULL(ISNULL(shuttleId,liftid),''''),
	SourceLocationID= ISNULL(SourceLocationID,''''),
	DestinationLocationID= ISNULL(DestinationLocationID,''''),
	Aisle= ISNULL(Aisle,''''),
	Level= ISNULL(Level,''''),
	LoadUnitID= ISNULL(LoadUnitID,''''),
	MovementType= ISNULL(MovementType,''''),
	StartTime= ISNULL(StartTime,''''),
	EndTime= ISNULL(EndTime,''''),
	Duration= ISNULL(Duration,''''),
	EventCode= ISNULL(EventCode,''''),
	Deltax= ISNULL(Deltax,''''),
	DeltaAisle= ISNULL(DeltaAisle,''''),
	SkuID= ISNULL(SkuID,''''),
	ProductRangeID= ISNULL(ProductRangeID,''''),
	TransportReason= ISNULL(TransportReason,''''),
	Subsystem_Code=  ISNULL(subsystemId,''''),--= ISNULL(productRangeId,''''),
	Subsystem_Category= ''Dematic IQ'',
	Record_Timestamp_Offset= ISNULL(Record_Timestamp_Offset,''''),
	hardwareIdentifier = ISNULL(hardwareIdentifier,'''')
FROM [STAGE].[FCT_MOVEMENTS_VEHICLE_'+ @lSourceSystem +']

UNION

SELECT
	Transport_Request_ID = ''0'',
	SubsystemID = ''AUTOSTORE'', 
	DeviceID = '''',
	SourceLocationID = '''',
	DestinationLocationID = 
		CASE WHEN x_position IS NULL 
		THEN LAG (ISNULL(autostore_id,'''') +''-''+ ISNULL(grid_id,''NA'') +''-''+ ISNULL(x_position,''*'') +''-''+ ISNULL(y_position,''*'') +''-''+ ISNULL(depth,''*'')) OVER (PARTITION BY container_id ORDER BY event_time ASC) 
		ELSE ISNULL(autostore_id,'''') +''-''+ ISNULL(grid_id,''NA'') +''-''+ ISNULL(x_position,''*'') +''-''+ ISNULL(y_position,''*'') +''-''+ ISNULL(depth,''*'')
		END,
	Aisle = '''',
	Level = '''',
	LoadUnitID =  ISNULL(container_id,''''),
	MovementType = ''AutoStore Container'',
	StartTime =  ISNULL(DATEADD(ns, CAST(RIGHT(ISNULL(process_epoch_ns,0), 6) AS INT), CAST(event_time AS datetime2(7))), '''') ,
	EndTime =  ISNULL(DATEADD(ns, CAST(RIGHT(ISNULL(process_epoch_ns,0), 6) AS INT), CAST(event_time AS datetime2(7))), '''') ,
	Duration  = ''0'',
	EventCode = CASE container_mode WHEN ''G'' THEN ''G-GRID'' WHEN ''O'' THEN ''O-OPEN'' WHEN ''T'' THEN ''T-PREPARED'' WHEN ''F'' THEN ''F-FORECAST'' WHEN ''C'' THEN ''C-CLOSED'' WHEN ''P'' THEN ''P-PORT'' WHEN ''R'' THEN ''R-TOGRID'' WHEN ''X'' THEN ''X-OUTSIDE'' ELSE ISNULL(container_mode,'''') END,
	Deltax = '''',
	DeltaAisle= '''',
	SkuID = '''',
	ProductRangeID = '''',
	TransportReason= ''ContainerMovementFact'',
	Subsystem_Code = ''AUTOSTORE'',
	Subsystem_Category= ''AUTOSTORE'',
	Record_Timestamp_Offset= '''',
	hardwareIdentifier = ''''
FROM [STAGE].[FCT_CONTAINER_MOVEMENT_'+ @lSourceSystem +'];
 
'
EXEC sp_executesql @lSQLStmt