SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)'; 
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX),
        @lChecks_SQLStmt NVARCHAR(MAX),
		@lChecks_SQLStmt1 NVARCHAR(MAX),
		@lDrops_SQLStmt NVARCHAR(MAX) 

SELECT @lChecks_SQLStmt1 =  
' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
' where t.name = ''FCT_Pick_Order_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
' BEGIN 
	
CREATE TABLE [STAGE].[FCT_Pick_Order_' + @lSourceSystem + '](
	[pickOrderPk] [varchar](255) NULL,
	[pickOrderType] [varchar](255) NULL,
	[orderCategory] [varchar](255) NULL,
	[wavePk] [varchar](255) NULL,
	[orderPk] [varchar](255) NULL,
	[putWallPk] [varchar](255) NULL,
	[locationPk] [varchar](255) NULL,
	[zonePk] [varchar](255) NULL,
	[groupPk] [varchar](255) NULL,
	[tenantName] [varchar](255) NULL,
	[event] [varchar](255) NULL,
	[eventTime] [varchar](255) NULL,
	[userName] [varchar](255) NULL,
	[Record_Timestamp_Offset] [varchar](255) NULL,
	[userPk] [varchar](255) NULL,
	[fulfillmentHandlingType] [varchar](255) NULL,
	[totalLineCt] [varchar](255) NULL,
	[totalQtyCt] [varchar](255) NULL,
	[vasRequirement] [varchar](255) NULL,
	[urgency] [varchar](255) NULL,
	[packingRequirement] [varchar](255) NULL,
	[latestStagingTime] [varchar](255) NULL,
	[orderChannel] [varchar](255) NULL,
	[Key_ID] [int] IDENTITY(1,1) NOT NULL
)
 END' 

SELECT @lChecks_SQLStmt =  
' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
' where t.name = ''FCT_Pick_Order_Planning_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
' BEGIN 
	
CREATE TABLE [STAGE].[FCT_Pick_Order_Planning_' + @lSourceSystem + '](
	[eventUuid] [varchar](255) NULL,
	[pickOrderPk] [varchar](255) NULL,
	[pickOrderType] [varchar](255) NULL,
	[orderCategory] [varchar](255) NULL,
	[orderPk] [varchar](255) NULL,
	[locationPk] [varchar](255) NULL,
	[zonePk] [varchar](255) NULL,
	[groupPk] [varchar](255) NULL,
	[workstationId] [varchar](255) NULL,
	[event] [varchar](255) NULL,
	[eventTime] [varchar](255) NULL,
	[reason] [varchar](255) NULL,
	[jsonData] [varchar](255) NULL,
	[tenantName] [varchar](255) NULL
)
 END' 
 
 
SELECT @lSQLStmt = ' SELECT DISTINCT Record_Timestamp = eventtime '       +     
				   '     , Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),eventTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,eventTime)/15))  ' +
				   '     , Customer_Order_Code = ISNULL(CASE WHEN UPPER(orderPk) = ''N/A'' THEN '''' ELSE UPPER(orderPk) END,'''') ' +
				   '     , Customer_Order_Name = ISNULL(CASE WHEN CHARINDEX(''#'',orderPk) > 0 '  +
				   '                            THEN RIGHT(orderPk, CHARINDEX(''#'', REVERSE(orderPk))-1) ' +
				   '                            ELSE orderPk END,'''') ' + 
				   '     , Pick_Order_Code =  ISNULL(pickOrderPk,'''') ' +
				   '     , Pick_Order_Name =  ISNULL(CASE WHEN CHARINDEX(''#'',pickOrderPk) > 0 '  +
				   '                            THEN RIGHT(pickOrderPk, CHARINDEX(''#'', REVERSE(pickOrderPk))-1) ' +
				   '                            ELSE pickOrderPk END,'''')				   ' +
				   '     , Put_Wall_Flg =  CASE WHEN LEN(ISNULL(putWallPk,'''')) > 0 THEN 1 ELSE 0 END ' +
				   '     , Pick_Order_Type = ISNULL(CASE WHEN UPPER(pickOrderType) = ''N/A'' THEN '''' ' +
				   ' 							WHEN UPPER(pickOrderType) IN (''SHIPPING'',''CUSTOMER'',''STANDARD'',''STANDARDPW'',''CUSTOMPW'') THEN ''CUSTOMER'' ' +
				   '							ELSE UPPER(pickOrderType) END,'''') ' +
				   '     , Pick_Order_Category = MIN(ISNULL(orderCategory,'''')) ' +
				   '     , Operator_Code = ISNULL(ISNULL(userpk,userName),'''') ' +
				   '     , Operator_Name = ISNULL(userName,'''') ' +
				   '     , Location_Code = ISNULL(locationPk,'''') ' +
				   '     , Location_Name = ISNULL(OA.Location_Name_Generator(locationPk), '''') '+
				   '	 , Work_Area_Code = ISNULL(CASE WHEN CHARINDEX(''.'',locationPk) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(locationPk),CHARINDEX(''.'',REVERSE(locationPk)) + 1, LEN(locationPk))),3) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
				   '	 , Module_Code = ISNULL(CASE WHEN CHARINDEX(''.'',locationPk) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(locationPk),CHARINDEX(''.'',REVERSE(locationPk)) + 1, LEN(locationPk))),2) ' +
                   '                           WHEN CHARINDEX(''#'',grouppk) > 0 '  +
				   '                           THEN RIGHT(grouppk, CHARINDEX(''#'', REVERSE(grouppk))-1) ' + 
				   '                           ELSE grouppk ' +
                   '                           END,'''') ' +
                   '	 , Zone_Code = ISNULL(zonePk,'''') ' +
				   '     , Wave_Code = ISNULL(wavePk,'''') ' +
                   '     , Pick_Order_Event = ISNULL(event,'''') ' +
                   '	 , Source_System = ''' + @lSourceSystem +  '''' +
				   '     , Reason_Code =  '''' ' +
				   '     , Unique_Event_Identifier =  '''' ' +
				   '     , Event_Detail =  '''' ' + 
				   '     , Workstation_Code =  '''' ' +
				   '     , Pick_Order_Fulfillment_Handling_Type =  ISNULL(fulfillmentHandlingType,'''') ' + 
				   '     , Pick_Order_Line_Count_Expected = ISNULL(CASE WHEN totalLineCt = 0 then null else totalLineCt end,'''')  ' +
				   '     , Pick_Order_Total_Qty_Expected = ISNULL(MAX(totalQtyCt), '''') ' +
				   '     , Pick_Order_Value_Added_Service = ISNULL(vasRequirement,'''') ' +
				   '     , Pick_Order_Priority = ISNULL(urgency,'''') ' +
				   '     , Pick_Order_Packing_Requirement = ISNULL(packingRequirement,'''') ' +
				   '     , Pick_Order_Latest_Staging_Date_Time = ISNULL(latestStagingTime,'''') ' +
				   '     , Pick_Order_Order_Channel = ISNULL(orderChannel,'''') ' +
				   ' FROM [STAGE].FCT_PICK_ORDER_'+ @lSourceSystem + 
				   ' WHERE pickOrderPk IS NOT NULL' +
				   ' GROUP BY eventTime, orderPk, pickOrderpk, putWallPk, pickOrderType, userpk, userName, locationPk, grouppk, zonePK, wavepk, event' +
				   '       , fulfillmentHandlingType, vasRequirement, urgency, packingRequirement, latestStagingTime, orderChannel, totalLineCt, totalQtyCt '+
				   ' UNION ALL ' +
				   ' SELECT DISTINCT Record_Timestamp = eventtime '       +     
				   '     , Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),eventTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,eventTime)/15))  ' +
				   '     , Customer_Order_Code = ISNULL(UPPER(orderPk),'''') ' +
				   '     , Customer_Order_Name = ISNULL(CASE WHEN CHARINDEX(''#'',orderPk) > 0 '  +
				   '                            THEN RIGHT(orderPk, CHARINDEX(''#'', REVERSE(orderPk))-1) ' +
				   '                            ELSE orderPk END,'''') ' + 
				   '     , Pick_Order_Code =  ISNULL(pickOrderPk,'''') ' +
				   '     , Pick_Order_Name =  ISNULL(CASE WHEN CHARINDEX(''#'',pickOrderPk) > 0 '  +
				   '                            THEN RIGHT(pickOrderPk, CHARINDEX(''#'', REVERSE(pickOrderPk))-1) ' +
				   '                            ELSE pickOrderPk END,'''')				   ' +
				   '     , Put_Wall_Flg =  0 ' +
				   '     , Pick_Order_Type = ISNULL(CASE WHEN UPPER(pickOrderType) = ''N/A'' THEN '''' ' +
				   ' 							WHEN UPPER(pickOrderType) IN (''SHIPPING'',''CUSTOMER'',''STANDARD'',''STANDARDPW'',''CUSTOMPW'') THEN ''CUSTOMER'' ' +
				   '							ELSE UPPER(pickOrderType) END,'''') ' +
				   '     , Pick_Order_Category = MIN(ISNULL(orderCategory,'''')) ' +
				   '     , Operator_Code = '''' ' +
				   '     , Operator_Name = '''' ' +
				   '     , Location_Code = ISNULL(locationPk ,'''') ' +
				   '     , Location_Name = ISNULL(OA.Location_Name_Generator(locationPk), '''') ' +
				   '	 , Work_Area_Code = ISNULL(CASE WHEN CHARINDEX(''.'',locationPk) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(locationPk),CHARINDEX(''.'',REVERSE(locationPk)) + 1, LEN(locationPk))),3) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
				   '	 , Module_Code = ISNULL(CASE WHEN CHARINDEX(''.'',locationPk) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(locationPk),CHARINDEX(''.'',REVERSE(locationPk)) + 1, LEN(locationPk))),2) ' +
                   '                           WHEN CHARINDEX(''#'',grouppk) > 0 '  +
				   '                           THEN RIGHT(grouppk, CHARINDEX(''#'', REVERSE(grouppk))-1) ' + 
				   '                           ELSE grouppk ' +
                   '                           END,'''') ' +
                   '	 , Zone_Code = ISNULL(zonePk,'''') ' +
				   '     , Wave_Code = '''' ' +
                   '     , Pick_Order_Event = ISNULL(event,'''') ' +
                   '	 , Source_System = ''' + @lSourceSystem +  '''' +
				   '     , Reason_Code =  ISNULL(reason,'''') ' +
				   '     , Unique_Event_Identifier =  ISNULL(eventUuid,'''') ' +
				   '     , Event_Detail =  ISNULL(jsonData,'''') ' +
				   '     , Workstation_Code =  ISNULL(workstationid,'''') ' +
                   '     , Pick_Order_Fulfillment_Handling_Type =  '''' ' + 
                   '     , Pick_Order_Line_Count_Expected = '''' ' +
				   '     , Pick_Order_Total_Qty_Expected = '''' ' +
				   '     , Pick_Order_Value_Added_Service = '''' ' +
				   '     , Pick_Order_Priority = '''' ' +
				   '     , Pick_Order_Packing_Requirement = '''' ' +
				   '     , Pick_Order_Latest_Staging_Date_Time = ''''' +
				   '     , Pick_Order_Order_Channel = '''' ' +
				   ' FROM [STAGE].FCT_PICK_ORDER_PLANNING_'+ @lSourceSystem + 
				   ' WHERE pickOrderPk IS NOT NULL' +
				   ' GROUP BY eventTime, orderPk, pickOrderpk, pickOrderType, locationPk, grouppk, zonePK, event, eventUuid, reason, jsonData, workstationid' +
				   ' ORDER BY Pick_Order_Code, eventTime ASC  '
				   

SELECT @lDrops_SQLStmt = ' IF OBJECT_ID(''[STAGE].FCT_PICK_ORDER_PLANNING_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_PICK_ORDER_PLANNING_'+@lSourceSystem;  
-- Dropped as part of Pick_Order_Complete transform
--' IF OBJECT_ID(''[STAGE].FCT_PICK_ORDER_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_PICK_ORDER_'+@lSourceSystem +  
				   
EXEC sp_executesql @lChecks_SQLStmt
EXEC sp_executesql @lChecks_SQLStmt1
EXEC sp_executesql @lSQLStmt
EXEC sp_executesql @lDrops_SQLStmt

