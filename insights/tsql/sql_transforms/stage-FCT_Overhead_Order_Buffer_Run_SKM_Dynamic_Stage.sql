SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''ORDERBUFFER_RUN_FACT_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
                   ' SELECT DISTINCT Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),emptyTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,emptyTime)/15))  ' +
                   '     , Record_Timestamp = emptyTime ' +
                   '     , Overhead_Loop_Code = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN REVERSE(LEFT(REVERSE(loopLocation), CHARINDEX(''.'',REVERSE(loopLocation)) - 1)) ' +
                   '                           ELSE loopLocation ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_Name = ISNULL(loopLocation, '''') ' +
                   '     , Overhead_Loop_Type = ''ORDER_BUFFER'' ' +
                   '     , Overhead_Loop_Area = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),3) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_Group = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),2) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_SubGroup = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),1) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
				   '     , Overhead_Run = ISNULL(runId, '''') ' +
				   '     , Order_Buffer_Run_Create_Date_Time = ISNULL(eventTime, '''') ' +
				   '     , First_Carrier_Arrival_Date_Time = ISNULL(creationTime, '''') ' +
				   '     , Last_Carrier_Arrival_Date_Time = ISNULL(completionTime, '''') ' +
				   '     , Order_Buffer_Run_Release_Date_Time = ISNULL(releaseTime, '''') ' +
				   '     , Order_Buffer_Run_Qty = ISNULL(numberOfItems, '''') ' +
				   '     , Order_Buffer_Run_Duration_Seconds = ISNULL(sortationTime, '''') ' +   
				   '     , Order_Buffer_Run_Width_Millimeters = ISNULL(totalWidth, '''') ' +
                   '     , Source_System = ''' + @lSourceSystem +  '''' +
                   ' FROM [STAGE].ORDERBUFFER_RUN_FACT_'+ @lSourceSystem +
                   ' ORDER BY Record_Timestamp, Overhead_Loop_Code, Overhead_Run ' +
                   ' END '  +  
				   ' IF OBJECT_ID(''[STAGE].ORDERBUFFER_RUN_FACT_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].ORDERBUFFER_RUN_FACT_'+@lSourceSystem;  
				    
EXEC sp_executesql @lSQLStmt



