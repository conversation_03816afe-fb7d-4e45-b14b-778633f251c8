SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)'; 
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX), @lChecks_SQLStmt NVARCHAR(MAX), @lDrops_SQLStmt NVARCHAR(MAX)
 

SELECT @lChecks_SQLStmt =  
					' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                    ' where t.name = ''FCT_PICK_ACTIVITY_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
					' BEGIN ' +
                    '    CREATE TABLE [STAGE].[FCT_PICK_ACTIVITY_'+ @lSourceSystem +']( ' +
                    '    	[pickerId] [varchar](255) NULL, ' +
                    '    	[zonePk] [varchar](255) NULL,' +
                    '    	[inductionZonePk] [varchar](255) NULL,' +
                    '    	[groupId] [varchar](255) NULL,' +
                    '    	[workstationId] [varchar](255) NULL,' +
                    '    	[technology] [varchar](255) NULL,' +
                    '    	[loadUnitId] [varchar](255) NULL,' +
                    '    	[containerPk] [varchar](255) NULL,' +
                    '    	[cartPk] [varchar](255) NULL,' +
                    '    	[cartId] [varchar](255) NULL,' +
                    '    	[skuPk] [varchar](255) NULL,' +
                    '    	[mode] [varchar](255) NULL,' +
                    '    	[workflowPk] [varchar](255) NULL,' +
                    '    	[loadUnitType] [varchar](255) NULL,' +
                    '    	[inductType] [varchar](255) NULL,' +
                    '    	[primaryMergeZonePk] [varchar](255) NULL,' +
                    '    	[apportionedMergeZoneDurationInSeconds] [varchar](255) NULL,' +
                    '    	[trainId] [varchar](255) NULL,' +
                    '    	[mergeStartTime] [varchar](255) NULL,' +
                    '    	[mergeEndTime] [varchar](255) NULL,' +
                    '    	[tenantName] [varchar](255) NULL,' +
                    '    	[event] [varchar](255) NULL,' +
                    '    	[eventTime] [varchar](255) NULL,' +
                    '    	[Record_Timestamp_Offset] [varchar](255) NULL,' +
                    '    	[userPk] [varchar](255) NULL,' +
                    '    	[loadUnitUsageType] [varchar](255) NULL,' +
                    '    	[Key_ID] [int] IDENTITY(1,1) NOT NULL' +
                    '    ) ON [PRIMARY] END' +
					
					' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                    ' where t.name = ''FCT_Workstation_Mode_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
					' BEGIN  ' +
                    '     CREATE TABLE [STAGE].[FCT_WORKSTATION_MODE_' + @lSourceSystem + '] (  ' +
                    '     	[autostore_id] [varchar](255) NULL,  ' +
                    '     	[event_time] [varchar](255) NULL, ' +
					'       [process_epoch_ns] [varchar](255) NULL, ' +
                    '     	[workstation_id] [varchar](255) NULL, ' +
                    '     	[workstation_mode] [varchar](255) NULL, ' +
                    '     	[event_type] [varchar](255) NULL, ' +
                    '     	[Key_ID] [int] IDENTITY(1,1) NOT NULL ' +
                    '     ) ON [PRIMARY] END '  


SELECT @lSQLStmt = ' SELECT   Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),eventTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,eventTime)/15))  ' +
                   '     , Record_Timestamp = eventtime '       +       
                   '     , Operator_Code = ISNULL(ISNULL(userpk,pickerid),'''') ' +
                   '     , Workstation_Code = ISNULL(workstationid,'''') ' +
                   '	 , Zone_Code = ISNULL(zonepk,'''') ' +
                   '	 , Technology_Code = ISNULL(technology,'''') ' +
                   '	 , Work_Type_Code = ISNULL(CASE WHEN CHARINDEX(''#'',workflowpk) > 0 '  +
				   '                            THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(REPLACE(workflowpk, ''#'',''.'')),CHARINDEX(''.'',REVERSE(workflowpk)) + 1, LEN(workflowpk))),4) ' +
				   '                            ELSE workflowpk END,'''') ' +
				   '	 , Work_Type_Style = ISNULL(CASE WHEN CHARINDEX(''#'',workflowpk) > 0 '  +
				   '                            THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(REPLACE(workflowpk, ''#'',''.'')),CHARINDEX(''.'',REVERSE(workflowpk)) + 1, LEN(workflowpk))),3) ' +
				   '                            ELSE '''' END,'''') ' +
				   '	 , Technology_Vendor = ISNULL(CASE WHEN CHARINDEX(''#'',workflowpk) > 0 '  +
				   '                            THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(REPLACE(workflowpk, ''#'',''.'')),CHARINDEX(''.'',REVERSE(workflowpk)) + 1, LEN(workflowpk))),1) ' +
				   '                            ELSE '''' END,'''') ' +
                   '	 , Container_Type_Code = ISNULL(UPPER(LoadUnitType),'''') ' +
                   '	 , SKU_Code = ISNULL(skuPK,'''') ' +
				   '	 , Module_Code = ISNULL(CASE WHEN CHARINDEX(''#'',groupId) > 0 '  +
				   '                            THEN RIGHT(groupId, CHARINDEX(''#'', REVERSE(groupId))-1) ' +
				   '                            ELSE groupId END,'''') ' +
				   '     , Module_Name = ISNULL(groupId,'''') ' +
				   '	 , Work_Area_Code = ISNULL(CASE WHEN LEN(groupId)- LEN(REPLACE(groupId, ''#'', '''')) > 1 ' +
				   '                            THEN Substring(groupId, CHARINDEX(''#'',groupId) + 1 ,LEN(groupId) - ( CHARINDEX(''#'', REVERSE(groupId)) + CHARINDEX(''#'',groupId))) ' +
				   '                            ELSE '''' END,'''') ' +									   
                   '	 , Load_Unit_Code = ISNULL(loadUnitId,'''') ' +
				   '     , Event = CASE WHEN Event IN (''sourceLUArrived'',''pickContainerArrived'')  ' +
				   '                         THEN ''Arrival''  ' +
				   '                    WHEN Event IN (''sourceLUReleased'',''pickContainerDeparted'')  ' +
				   '                         THEN ''Release''  ' +
				   '                    WHEN Event IN (''sourceLUDeparted'')  ' +
				   '                         THEN ''Departure''  ' +				   
				   '                    ELSE Event END ' +
                   '	 , Cart_Code = ISNULL(CartID,'''') ' +
                   '	 , Container_Instance_Code = ISNULL(ContainerPK,'''') ' +
                   '	 , Cart_Instance_Code = ISNULL(cartpk,'''') ' +
                   '	 , Induct_Type = ISNULL(InductType,'''') ' +
                   '	 , Cluster_Train_Code = ISNULL(trainID,'''') ' +
                   '     , Induction_Zone_Code = MAX(ISNULL(InductionZonePk,'''')) ' +
                   '	 , Source_System = ''' + @lSourceSystem +  '''' +
				   '     , Load_Unit_Usage_Type_Code =   ' +
				   '                 CASE WHEN Event LIKE ''sourceLU%'' OR loadUnitUsageType = ''GTPWorkStationDonor'' ' +
				   '    			      THEN ''Donor''' +
				   '    				  WHEN Event LIKE ''pickContainer%'' OR loadUnitUsageType = ''GTPWorkStationOrder'' ' +
				   '    				  THEN ''Order''' +
				   '    				  ELSE ISNULL(loadUnitUsageType,'''')' +
				   '    		     END '+
				   ' FROM [STAGE].FCT_PICK_ACTIVITY_'+ @lSourceSystem + 
				   ' GROUP BY  EventTime, userpk, pickerid, workstationId, zonepk, technology, workflowPk, ' +
				   '  loadUnitId, skuPK, groupID, loadUnitId, event, cartId, containerPK, cartpk, ' +
				   '  inducttype, trainID, loadUnittype, loadUnitUsageType ' +
				   ' UNION ALL '+
				   'SELECT DISTINCT  Hour_Quarter_ID = LEFT(REPLACE(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),event_time,25),''-'',''''),3,50),'' '',''''),'':'',''''),''T'',''''),8) + LTRIM(STR(DATEPART(minute,event_time)/15)) ' +
                   '     , Record_Timestamp = DATEADD(ns, CAST(RIGHT(ISNULL(process_epoch_ns,0), 6) AS INT), CAST(event_time AS datetime2(7))) '       +
                   '     , Operator_Code = '''' ' +
                   '     , Workstation_Code = ISNULL(workstation_id,'''') ' +
                   '	 , Zone_Code = '''' ' +
                   '	 , Technology_Code = ''AutoStore'' ' +
                   '	 , Work_Type_Code = '''' ' +
				   '	 , Work_Type_Style = '''' ' +
				   '	 , Technology_Vendor = '''' ' +
                   '	 , Container_Type_Code = '''' ' +
                   '	 , SKU_Code = '''' ' +
				   '	 , Module_Code = ISNULL(autostore_id,'''') ' +
				   '     , Module_Name = '''' ' +
				   '	 , Work_Area_Code = '''' ' +									   
                   '	 , Load_Unit_Code = '''' ' +
				   '     , Event = ISNULL(workstation_mode,'''') ' +
                   '	 , Cart_Code = '''' ' +
                   '	 , Container_Instance_Code = '''' ' +
                   '	 , Cart_Instance_Code = '''' ' +
                   '	 , Induct_Type = '''' ' +
                   '	 , Cluster_Train_Code = '''' ' +
                   '     , Induction_Zone_Code = '''' ' +
                   '	 , Source_System = ''' + @lSourceSystem +  '''' +
				   '     , Load_Unit_Usage_Type_Code = '''' ' +
				   ' FROM [STAGE].FCT_WORKSTATION_MODE_'+ @lSourceSystem + ''
				   
				  
SELECT @lDrops_SQLStmt = ' IF OBJECT_ID(''[STAGE].FCT_WORKSTATION_MODE_' + @lSourceSystem + ''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_WORKSTATION_MODE_'+@lSourceSystem +
					     ' IF OBJECT_ID(''[STAGE].FCT_PICK_ACTIVITY_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_PICK_ACTIVITY_'+@lSourceSystem
						 
					 
EXEC sp_executesql @lChecks_SQLStmt	     
EXEC sp_executesql @lSQLStmt
EXEC sp_executesql @lDrops_SQLStmt
