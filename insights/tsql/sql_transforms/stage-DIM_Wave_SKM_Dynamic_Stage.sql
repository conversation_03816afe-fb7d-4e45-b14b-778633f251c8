SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 
DECLARE @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @Transform_SQLStmt = '
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Wave_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	SELECT TOP 1 WITH TIES
		Wave_Code = ISNULL(primaryKey, ''''),
		Wave_Name = id,
		Wave_Status = status,
		Wave_Priority = ISNULL(urgency,''''),
		Wave_Created_Date_Time = ISNULL(createDate,''''),
		Wave_Released_Date_Time = ISNULL(releaseTime,''''),
		Wave_Active_Date_Time = ISNULL(startTime,''''),
		Wave_Closed_Date_Time = ISNULL(endTime,''''),
		Active_Rec_Ind = 1,
		Source_System = ''' + @lSourceSystem +'''
	FROM [STAGE].[DIM_Wave_' + @lSourceSystem + ']
	ORDER BY Row_number() over (partition by primaryKey, tenantName order by dimensionModificationTime desc)
END
'
SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Wave_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[DIM_Wave_'+@lSourceSystem +']'


--PRINT(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt