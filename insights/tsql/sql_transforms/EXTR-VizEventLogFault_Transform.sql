SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX), @Drop_SQLStmt NVARCHAR(MAX);


SET @lSQLStmt = '
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Viz_EventLog_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
-- Create transform tabke if it does not exists stage table
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Fault_Dynamic_Stage_' + @lSourceSystem + ''' and s.name = ''Stage'')

		CREATE TABLE [STAGE].[FCT_Fault_Dynamic_Stage_' + @lSourceSystem + '] (
			Hour_Quarter_ID INTEGER NOT NULL, Container_Physical_Code VARCHAR(50) NULL, Device_Code VARCHAR(150) NULL,
			Fault_Acknowledgement_Date_Time Datetime2(7) NULL, Fault_End_Date_Time Datetime2(7)  NOT NULL, Fault_Duration_Seconds INTEGER NULL,
			Fault_Repair_Duration_Seconds INTEGER NULL, Fault_Start_Date_Time Datetime2(7)  NOT NULL, Fault_Tag_Reason_Code VARCHAR(150) NULL,
			Item_Category_Code VARCHAR(150) NULL, Item_Code VARCHAR(150) NULL, Location_Code VARCHAR(150) NULL, Module_Code VARCHAR(150) NULL,
			Operator_Code VARCHAR(150) NULL, Physical_Device_Code VARCHAR(150) NULL, PLC_Code VARCHAR(150) NULL, Reason_Name VARCHAR(150) NULL,
			Record_Timestamp Datetime2(7) NULL, Source_System VARCHAR(150) NULL, Fault_Code VARCHAR(50) NULL, Subsystem_Category VARCHAR(50) NULL,
			Subsystem_Code VARCHAR(50) NULL, Tenant_Name VARCHAR(50) NULL);

		WITH PreProcessed_Events AS (
			SELECT 
				Container_Physical_Code = TUID1
				,Container_Physical_Code2 = TUID2
				,Device_Code = Info
				,Fault_End_Date_Time = DATEADD("Ms",CAST(ISNULL(ms,0) AS INTEGER), CAST(EventTime AS datetime2(7)))
				,Fault_Start_Date_Time = CAST(ActiveTime AS datetime2(7))
				,Fault_Tag_Reason_Code = Tag
				,PLC_Code = OBJ_NAME
				,Reason_Name = Message
				,Fault_Code = FLT_CODE
				,Location_Code = CASE WHEN CHARINDEX(''\'',Area,1) > 0 THEN SUBSTRING(Area,CHARINDEX(''\'',Area,1) + 1,LEN(Area)) ELSE NULL END
				,Module_Code = CASE WHEN CHARINDEX(''\'',Area,1) > 0 THEN LEFT(Area,CHARINDEX(''\'',Area,1) - 1) ELSE Area END 
				,Subsystem_Code = CASE 
										WHEN Area IS NULL THEN NULL
										WHEN LEFT(UPPER(Area),3) = ''DMS'' THEN ''MULTISHUTTLE''
										WHEN CHARINDEX(''SORTER'', UPPER(Area)) > 0 THEN ''SORTER''
										WHEN CHARINDEX(''CROSSBELT'',UPPER(Area)) > 0 THEN ''SORTER''
										ELSE ''OTHER''
								  END
			  FROM [STAGE].[FCT_Viz_EventLog_' + @lSourceSystem + ']
			  WHERE ChangeMask = ''1''
		), Deduplicated_Events AS (
			--
			SELECT DISTINCT Container_Physical_Code, Device_Code,GRP.Fault_End_Date_Time,PPE.Fault_Start_Date_Time,GRP.Fault_Tag_Reason_Code,PLC_Code,Reason_Name,Fault_Code,Location_Code,Module_Code,Subsystem_Code 
			FROM PreProcessed_Events PPE 
				INNER JOIN (
					SELECT Fault_Start_Date_Time, MAX(Fault_End_Date_Time) AS Fault_End_Date_Time, Fault_Tag_Reason_Code
					FROM PreProcessed_Events
					GROUP BY Fault_Start_Date_Time,Fault_Tag_Reason_Code
				) AS GRP
				ON PPE.Fault_Start_Date_Time = GRP.Fault_Start_Date_Time AND PPE.Fault_Tag_Reason_Code = GRP.Fault_Tag_Reason_Code
			--GROUP BY Container_Physical_Code, Device_Code,Fault_End_Date_Time,Fault_Start_Date_Time,Fault_Tag_Reason_Code,PLC_Code,Reason_Name,Fault_Code,Location_Code,Module_Code,Subsystem_Code
		), Processed_Events AS (
			SELECT
				Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), Fault_End_Date_Time,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), Fault_End_Date_Time,120),15,2) as INT)/15))
				,Container_Physical_Code
				,Device_Code = LEFT(Device_Code,50)
				,Fault_Acknowledgement_Date_Time = NULL
				,Fault_End_Date_Time
				,Fault_Duration_Seconds = DATEDIFF(second,Fault_Start_Date_Time,Fault_End_Date_Time)
				,Fault_Repair_Duration_Seconds = NULL
				,Fault_Start_Date_Time
				,Fault_Tag_Reason_Code
				,Item_Category_Code = NULL
				,Item_Code = NULL
				,Location_Code = LEFT(Location_Code,50)
				,Module_Code = LEFT(Module_Code,50)
				,Operator_Code = NULL
				,Physical_Device_Code = NULL
				,PLC_Code
				,Reason_Name
				,Record_Timestamp = Fault_End_Date_Time
				,Source_System = ''' +@lSourceSystem +'''
				,Fault_Code
				,Subsystem_Category = ''VISUALIZATION''
				,Subsystem_Code = LEFT(Subsystem_Code,50)
				,Tenant_Name = NULL
			FROM  Deduplicated_Events
		)
	--	SELECT * from Processed_Events
	 INSERT INTO [STAGE].[FCT_Fault_Dynamic_Stage_' + @lSourceSystem + '] SELECT * from Processed_Events
END
'

SET @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Viz_EventLog_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[FCT_Viz_EventLog_'+@lSourceSystem +']'

--PRINT(@lSQLStmt)
EXEC sp_executesql @lSQLStmt
EXEC sp_executesql @Drop_SQLStmt

