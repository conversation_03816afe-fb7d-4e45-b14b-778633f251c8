SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

--===========================================================================================================
--  Extract will only run full hours
--===========================================================================================================

SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @aBatchStartDateTime AS DATETIME = '$(BatchFrom)' , @aBatchEndDateTime AS DATETIME = '$(BatchTo)', @aETLBatchID INT = '$(ETL_Batch_ID)'
-- DECLARE @aBatchStartDateTime AS DATETIME = '2018-04-03 09:00:00.000' , @aBatchEndDateTime AS DATETIME = '2018-06-06 17:00:00.000', @aETLBatchID INT = 1

SELECT @aBatchStartDateTime = DATEADD(hour,DATEPART(hour,@aBatchStartDateTime)-1,CAST(CONVERT(VARCHAR(10),@aBatchStartDateTime,101) AS DATETIME)),
       @aBatchEndDateTime = DATEADD(hour,DATEPART(hour,@aBatchEndDateTime),CAST(CONVERT(VARCHAR(10),@aBatchEndDateTime,101) AS DATETIME))
                      

--===========================================================================================================
--  Find the max ratio of inducts-to-orders across the history of data for each scanner and hour ID
--  Research suggests the time of day (i.e. 1:00 vs. 7:00) is a strong indicator for sortation activity
--===========================================================================================================

  ;WITH Max_Ratio AS(
  SELECT HR.Hour_of_Day
  ,MAX(Induct_Order_Ratio) AS Max_Ratio
  ,Scanner_ID
  FROM OA.AGG_FCT_OEE_Sort OEE
  JOIN OA.DIM_Hour HR ON OEE.Hour_ID = HR.Hour_ID
  GROUP BY HR.Hour_of_Day, Scanner_ID
  ),

--===========================================================================================================
--  Determine records for the full hour for each scanner between start times
--===========================================================================================================

  Current_Hour AS(
  SELECT OEE.Hour_Start_Date_Time AS Hour_Start_Date_Time
  ,OEE.Hour_ID
  ,HR.Hour_of_Day
  ,'Sortation' AS Type_Code
  ,OEE.Scanner_ID AS Device_ID
  ,Actual_Induct_Count AS Actual_Throughput
  ,Outstanding_Orders
  FROM OA.AGG_FCT_OEE_Sort OEE
  JOIN OA.DIM_Hour HR ON OEE.Hour_ID = HR.Hour_ID
  WHERE OEE.Hour_Start_Date_Time BETWEEN @aBatchStartDateTime AND @aBatchEndDateTime
  )

--===========================================================================================================
--  Determine the "Desired Throughput" by applying the max ratio of induct-to-orders to the outstanding
--		orders of the full hours consistent with the time of day
--===========================================================================================================

  SELECT CR.Hour_ID
  ,Hour_Start_Date_Time
  --,CR.Hour_of_Day
  ,Type_Code
  ,Device_ID
  ,Actual_Throughput
  ,CAST(ROUND(CAST(Outstanding_Orders AS INT) * CAST(MR.Max_Ratio AS DECIMAL(10,4)),0) AS INT) AS Target_Throughput
  ,ETL_Batch_ID = @aETLBatchID
  --,NULL AS ETL_Update_Timestamp
  FROM Current_Hour CR
  JOIN Max_Ratio MR ON CR.Device_ID = MR.Scanner_ID 
	AND CR.Hour_of_Day = MR.Hour_of_Day