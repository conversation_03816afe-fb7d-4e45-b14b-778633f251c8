SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX), @Drop_SQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Fault_Dynamic_Stage_' + @lSourceSystem + ''' and s.name = ''Stage'')
	CREATE TABLE [STAGE].[FCT_Fault_Dynamic_Stage_' + @lSourceSystem + '] (
		Hour_Quarter_ID INTEGER NOT NULL, Container_Physical_Code VARCHAR(50) NULL, Device_Code VARCHAR(150) NULL,
		Fault_Acknowledgement_Date_Time Datetime2(7) NULL, Fault_End_Date_Time Datetime2(7)  NOT NULL, Fault_Duration_Seconds INTEGER NULL,
		Fault_Repair_Duration_Seconds INTEGER NULL, Fault_Start_Date_Time Datetime2(7)  NOT NULL, Fault_Tag_Reason_Code VARCHAR(150) NULL,
		Item_Category_Code VARCHAR(150) NULL, Item_Code VARCHAR(150) NULL, Location_Code VARCHAR(150) NULL, Module_Code VARCHAR(150) NULL,
		Operator_Code VARCHAR(150) NULL, Physical_Device_Code VARCHAR(150) NULL, PLC_Code VARCHAR(150) NULL, Reason_Name VARCHAR(150) NULL,
		Record_Timestamp Datetime2(7) NULL, Source_System VARCHAR(150) NULL, Fault_Code VARCHAR(50) NULL, Subsystem_Category VARCHAR(50) NULL,
		Subsystem_Code VARCHAR(50) NULL, Tenant_Name VARCHAR(50) NULL);

BEGIN		
	DECLARE @temp_stage_' + @lSourceSystem + ' TABLE(endTime VARCHAR(100),loadUnitId VARCHAR(100),shuttleId VARCHAR(100)
		,liftId VARCHAR(100),duration VARCHAR(100),startTime VARCHAR(100),skuId VARCHAR(100)
		,destinationLocationId VARCHAR(100),hardwareIdentifier VARCHAR(100),eventCode VARCHAR(100), Subsystem_Code VARCHAR(100));

	IF object_id(''STAGE.FCT_Movements_Connection_' + @lSourceSystem +''') IS NOT NULL
		INSERT INTO @temp_stage_' + @lSourceSystem + ' SELECT endTime,loadUnitId,shuttleId,liftId,duration,startTime,skuId,destinationLocationId,hardwareIdentifier,eventCode,Subsystem_Code=''CONVEYOR'' FROM STAGE.FCT_Movements_Connection_' + @lSourceSystem + ';
	IF object_id(''STAGE.FCT_Movements_Multishuttle_' + @lSourceSystem +''') IS NOT NULL
		INSERT INTO @temp_stage_' + @lSourceSystem + ' SELECT endTime,loadUnitId,shuttleId,liftId,duration,startTime,skuId,destinationLocationId,hardwareIdentifier,eventCode,Subsystem_Code=''MULTISHUTTLE'' FROM STAGE.FCT_Movements_Multishuttle_' + @lSourceSystem + ';
	IF object_id(''STAGE.FCT_Movements_StackerCrane_' + @lSourceSystem +''') IS NOT NULL
		INSERT INTO @temp_stage_' + @lSourceSystem + ' SELECT endTime,loadUnitId,shuttleId,liftId,duration,startTime,skuId,destinationLocationId,hardwareIdentifier,eventCode,Subsystem_Code=''STACKERCRANE'' FROM STAGE.FCT_Movements_StackerCrane_' + @lSourceSystem + ';
	IF object_id(''STAGE.FCT_Movements_Vehicle_' + @lSourceSystem +''') IS NOT NULL
		INSERT INTO @temp_stage_' + @lSourceSystem + ' SELECT endTime,loadUnitId,shuttleId,liftId,duration,startTime,skuId,destinationLocationId,hardwareIdentifier,eventCode,Subsystem_Code=''STACKERCRANE'' FROM STAGE.FCT_Movements_Vehicle_' + @lSourceSystem + ';
		
	; WITH PreProcessed_Events AS (
		SELECT 
			Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), [endTime],120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), [endTime],120),15,2) as INT)/15))
			,Container_Physical_Code = [loadUnitId]
			,Device_Code = ISNULL([shuttleId],[liftId])
			,Fault_Acknowledgement_Date_Time = NULL
			,Fault_End_Date_Time = [endTime]
			,Fault_Duration_Seconds = ISNULL(DATEDIFF("S",[startTime],[endTime]), ROUND([duration] / 1000,1))
			,Fault_Repair_Duration_Seconds = NULL
			,Fault_Start_Date_Time = [startTime]
			,Fault_Tag_Reason_Code = NULL
			,Item_Category_Code = NULL
			,Item_Code = [skuId]
			,Location_Code = [destinationLocationId]
			,Module_Code = COALESCE(PARSENAME(REVERSE(SUBSTRING(REVERSE([destinationLocationId]),CHARINDEX(''.'',REVERSE([destinationLocationId])) + 1, LEN([destinationLocationId]))),2), '''')
			,Operator_Code = NULL
			,Physical_Device_Code = [hardwareIdentifier]
			,PLC_Code = NULL
			,Reason_Name  = NULL
			,Record_Timestamp = [endTime]
			,Source_System = ''' + @lSourceSystem +'''
			,Fault_Code = [eventCode]
			,Subsystem_Category = ''DEMATIC IQ''
			,Subsystem_Code
			,Tenant_Name = NULL
		FROM @temp_stage_' + @lSourceSystem + '
		WHERE UPPER([eventCode]) != ''OK''
	)
	INSERT INTO [STAGE].[FCT_Fault_Dynamic_Stage_' + @lSourceSystem + '] SELECT * from PreProcessed_Events
END
'

--PRINT(@lSQLStmt)
EXEC sp_executesql @lSQLStmt

