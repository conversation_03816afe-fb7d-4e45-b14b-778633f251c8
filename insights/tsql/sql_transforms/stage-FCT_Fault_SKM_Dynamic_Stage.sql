SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--SET @lSourceSystem  = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX);
DE<PERSON>AR<PERSON> @Drop_SQLStmt NVARCHAR(MAX);

DE<PERSON>AR<PERSON> @minDate VARCHAR(50) = DATEADD(day , -45 , GETDATE());
DECLARE @maxDate VARCHAR(50) = GETDATE();


DECLARE @columnList VARCHAR(500) = ' [Hour_Quarter_ID],[Container_Physical_Code],[Device_Code],[Fault_Acknowledgement_Date_Time],[Fault_End_Date_Time],[Fault_Duration_Seconds],[Fault_Repair_Duration_Seconds]
			,[Fault_Start_Date_Time],[Fault_Tag_Reason_Code],[Item_Category_Code],[Item_Code],[Location_Code],[Module_Code],[Operator_Code],[Physical_Device_Code],[PLC_Code]
			,[Reason_Name],[Record_Timestamp],[Source_System],[Fault_Code],[Subsystem_Category],[Subsystem_Code],[Tenant_Name] ';

/*
This query is used to try to find out the Item_Code that the fault was generated for.
1. We removed duplicates from the staging table (Deduplicated_Events)
2. We get all the events that do not have Item_Code (None_Item_Events)
3. We try to find the associated item based on FCT_Movement table based on Device_Code (Device_Joined_Events)
4. Events that still have no Item_Code (None_Item_Events2) we will try to do a look based on Container_Physical_Code
5. We join the result sets of 1,3,5 and return that result
*/

SET @lSQLStmt = '
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Fault_Dynamic_Stage_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	WITH Deduplicated_Events as (
			SELECT DISTINCT ' + @columnList + '
			FROM [STAGE].[FCT_Fault_Dynamic_Stage_' + @lSourceSystem + ']
	), None_Item_Events as (
		SELECT * FROM Deduplicated_Events WHERE Item_Code IS NULL AND Subsystem_Code = ''MULTISHUTTLE''
	)
	, Device_Joined_Events as (
			SELECT
				[Hour_Quarter_ID],[Container_Physical_Code],NIE.Device_Code,[Fault_Acknowledgement_Date_Time],[Fault_End_Date_Time],[Fault_Duration_Seconds],[Fault_Repair_Duration_Seconds]
				,[Fault_Start_Date_Time],[Fault_Tag_Reason_Code],[Item_Category_Code],IDT.Item_Code,[Location_Code],[Module_Code],[Operator_Code],[Physical_Device_Code],[PLC_Code]
				,[Reason_Name],[Record_Timestamp],[Source_System],[Fault_Code],[Subsystem_Category],[Subsystem_Code],[Tenant_Name]
				,row_number() over (partition by NIE.[Fault_End_Date_Time], NIE.Device_Code order by IDT.Move_Start_Date_Time DESC) as rp
			FROM None_Item_Events NIE
				LEFT JOIN (
					SELECT DATEADD(SS, 3, Record_Timestamp) AS Move_End_Date_Time, Move_Start_Date_Time, Item_Code, Device_Code
						FROM OA.FCT_Movement m WITH(NOLOCK) --INDEX(XIE2FCT_Movement_Record_Timestamp))
						INNER JOIN OA.DIM_Item it WITH(NOLOCK) ON m.Item_ID = it.Item_ID
						INNER JOIN OA.DIM_Device d WITH(NOLOCK) ON m.Device_ID = d.Device_ID
						INNER JOIN OA.DIM_Subsystem s WITH(NOLOCK) ON d.Subsystem_ID = s.Subsystem_ID
						WHERE s.Subsystem_Code =''MULTISHUTTLE'' AND  s.Source_System = ''' + @lSourceSystem + '''
							and m.Record_Timestamp >= ''' + @minDate + ''' and m.Record_Timestamp <= ''' + @maxDate + '''
						) AS IDT
								 ON IDT.Device_Code = NIE.Device_Code 
						AND NIE.Device_Code IS NOT NULL 
						AND NIE.Fault_Start_Date_Time >= IDT.Move_Start_Date_Time AND NIE.Fault_Start_Date_Time <= IDT.Move_End_Date_Time
	)
	, None_Item_Events2 as (
		SELECT ' + @columnList + '
		FROM Device_Joined_Events WHERE Item_Code IS NULL AND rp = 1
	)
	, Container_Joined_Events as (
		SELECT
				[Hour_Quarter_ID],NIE.Container_Physical_Code,Device_Code,[Fault_Acknowledgement_Date_Time],[Fault_End_Date_Time],[Fault_Duration_Seconds],[Fault_Repair_Duration_Seconds]
				,[Fault_Start_Date_Time],[Fault_Tag_Reason_Code],[Item_Category_Code],ICT.Item_Code,[Location_Code],[Module_Code],[Operator_Code],[Physical_Device_Code],[PLC_Code]
				,[Reason_Name],[Record_Timestamp],[Source_System],[Fault_Code],[Subsystem_Category],[Subsystem_Code],[Tenant_Name]
				,row_number() over (partition by NIE.[Fault_End_Date_Time], NIE.Container_Physical_Code order by ICT.Move_Start_Date_Time DESC) as rp
			FROM None_Item_Events2 NIE
				LEFT JOIN (
					SELECT  DATEADD(SS, 3, Record_Timestamp) AS Move_End_Date_Time, Move_Start_Date_Time, Item_Code, Load_Unit_Code as Container_Physical_Code
								FROM OA.FCT_Movement m WITH(NOLOCK) INNER JOIN OA.DIM_Item it WITH(NOLOCK) ON m.Item_ID = it.Item_ID
								INNER JOIN OA.DIM_Status st WITH(NOLOCK) ON m.Status_ID = st.Status_ID
								INNER JOIN OA.DIM_Subsystem s WITH(NOLOCK) ON st.Subsystem_ID = s.Subsystem_ID
								WHERE s.Subsystem_Code = ''MULTISHUTTLE'' AND st.Source_System = ''' + @lSourceSystem + '''
									AND Record_Timestamp >= ''' + @minDate + ''' AND Record_Timestamp < ''' + @maxDate + '''
				) AS ICT
								 ON ICT.Container_Physical_Code = NIE.Container_Physical_Code 
						AND NIE.Container_Physical_Code IS NOT NULL 
						AND NIE.Fault_Start_Date_Time >= ICT.Move_Start_Date_Time AND NIE.Fault_Start_Date_Time <= ICT.Move_End_Date_Time
	)
	, Combined_Events as (
		(SELECT ' + @columnList + ' FROM Deduplicated_Events WHERE Item_Code IS NOT NULL OR Subsystem_Code != ''MULTISHUTTLE'')
		UNION ALL
		(SELECT ' + @columnList + ' FROM Device_Joined_Events WHERE rp = 1 AND Item_Code IS NOT NULL)
		UNION ALL
		(SELECT ' + @columnList + ' FROM Device_Joined_Events WHERE rp = 1)
	)
	SELECT 
		  Hour_Quarter_ID
		, Container_Physical_Code = ISNULL(Container_Physical_Code,'''')
		, Device_Code = ISNULL(Device_Code,'''')
		, Fault_Acknowledgement_Date_Time = ISNULL(CAST(Fault_Acknowledgement_Date_Time AS varchar(30)), '''')
		, Fault_End_Date_Time = ISNULL(CAST(Fault_End_Date_Time AS varchar(30)), '''')
		, Fault_Duration_Seconds = ISNULL(Fault_Duration_Seconds,'''')
		, Fault_Repair_Duration_Seconds = ISNULL(Fault_Repair_Duration_Seconds,'''')
		, Fault_Start_Date_Time = ISNULL(CAST(Fault_Start_Date_Time AS varchar(30)), '''')
		, Fault_Tag_Reason_Code = ISNULL(Fault_Tag_Reason_Code,'''')
		, Item_Category_Code = ISNULL(Item_Category_Code,'''')
		, Module_Code = ISNULL(Module_Code,'''')
		, Operator_Code = ISNULL(Operator_Code,'''')
		, Physical_Device_Code = ISNULL(Physical_Device_Code,'''')
		, PLC_Code = ISNULL(PLC_Code,'''')
		, Reason_Name = ISNULL(Reason_Name,'''')
		, Record_Timestamp 
		, Source_System 
		, Status_Code = ISNULL(Fault_Code,'''')
		, Subsystem_Category
		, Subsystem_Code 
		, Tenant_Name = ISNULL(Tenant_Name,'''')
		, Item_Code = OA.Item_Code_Generator(Item_Code, null,null,null)
		, Location_Code = ISNULL(Location_Code,'''')
	    , Status_Category_Code = '''' 
	FROM Combined_Events
END
'

SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Fault_Dynamic_Stage_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
                           DROP TABLE [STAGE].[FCT_Fault_Dynamic_Stage_'+@lSourceSystem +']'

--PRINT(@lSQLStmt)
EXEC sp_executesql @lSQLStmt
EXEC sp_executesql @Drop_SQLStmt