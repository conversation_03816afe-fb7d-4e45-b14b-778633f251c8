SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''EMPTY_POUCH_BUFFERFACT_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
                   ' SELECT DISTINCT Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),timestamp,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,timestamp)/15))  ' +
                   '     , Record_Timestamp = timestamp ' +
                   '     , Overhead_Loop_Code = ISNULL(CASE WHEN CHARINDEX(''.'',emptyLocation) > 0 ' +
                   '                           THEN REVERSE(LEFT(REVERSE(emptyLocation), CHARINDEX(''.'',REVERSE(emptyLocation)) - 1)) ' +
                   '                           ELSE emptyLocation ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_Name = ISNULL(emptyLocation, '''') ' +
                   '     , Overhead_Loop_Type = ''EMPTY_BUFFER'' ' +
                   '     , Overhead_Loop_Area = ISNULL(CASE WHEN CHARINDEX(''.'',emptyLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(emptyLocation),CHARINDEX(''.'',REVERSE(emptyLocation)) + 1, LEN(emptyLocation))),3) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_Group = ISNULL(CASE WHEN CHARINDEX(''.'',emptyLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(emptyLocation),CHARINDEX(''.'',REVERSE(emptyLocation)) + 1, LEN(emptyLocation))),2) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_SubGroup = ISNULL(CASE WHEN CHARINDEX(''.'',emptyLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(emptyLocation),CHARINDEX(''.'',REVERSE(emptyLocation)) + 1, LEN(emptyLocation))),1) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
				   '     , Empty_Buffer_Utilization_Percentage = ISNULL(fillingRatio, '''') ' +
				   --'     , Overhead_Buffer_Carrier_Requested = ISNULL(numberOfRequestedEmpties, '''') ' +
				   '     , Empty_Buffer_Inbound_Carrier_Requested_Qty = ISNULL(inboundDemand, '''') ' +
				   '     , Empty_Upper_Buffer_Available_Outbound_Carrier_Qty = ISNULL(outboundUpperDemand, '''') ' +
				   '     , Empty_Lower_Buffer_Available_Outbound_Carrier_Qty = ISNULL(outboundLowerDemand, '''') ' +
				   '     , Empty_Buffer_Overflow_Planned_Carrier_Qty = ISNULL(outboundOverDemand, '''') ' +
				   '     , Subsystem_Category = ''DEMATIC IQ'' ' +
				   '     , Subsystem_Code = ''OVERHEAD_SORT'' ' +
                   '     , Source_System = ''' + @lSourceSystem +  '''' +
                   ' FROM [STAGE].EMPTY_POUCH_BUFFERFACT_'+ @lSourceSystem +
                   ' ORDER BY timestamp, Overhead_Loop_Code, Subsystem_Code, Subsystem_Category ' +
                   ' END ' +  
				   ' IF OBJECT_ID(''[STAGE].EMPTY_POUCH_BUFFERFACT_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].EMPTY_POUCH_BUFFERFACT_'+@lSourceSystem;  
				   
EXEC sp_executesql @lSQLStmt

