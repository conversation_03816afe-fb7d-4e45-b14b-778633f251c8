SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''FCT_Inventory_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
   /*'  DELETE FI ' +
   '  FROM OA.Fct_Inventory FI ' +
   '    JOIN OA.DIM_Item I ON I.Item_ID = FI.Item_ID ' +
   '  WHERE I.Source_System = '''+ @lSourceSystem + ''' ' +*/
   ' SELECT DISTINCT ' +
   '       Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),timeStamp,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,timeStamp)/15))  ' +
   '     , Record_Timestamp = timeStamp '       +
   '     , Item_Code = OA.Item_Code_Generator(skuPk, null,null,null) ' +
   '     , Item_SKU = ISNULL(CASE WHEN skuPk LIKE ''%Empty_SKU%'' THEN '''' ' +
   '                              WHEN CHARINDEX(''#'', ISNULL(skuPK,'''')) > 0 THEN RIGHT(skuPK, CHARINDEX(''#'',REVERSE(skuPK))-1) ' +
   '                              ELSE skuPK END,'''')'+
   '     , Location_Code = ISNULL(locationId, '''') ' +
   '     , Location_Name = ISNULL(OA.Location_Name_Generator(locationId), '''') ' +
   '     , Work_Area_Code = ISNULL(CASE WHEN CHARINDEX(''.'',locationId) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(locationId),CHARINDEX(''.'',REVERSE(locationId)) + 1, LEN(locationId))),3) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Work_Area_Name = ISNULL(CASE WHEN CHARINDEX(''.'',locationId) > 0 ' +
   '                           THEN LEFT(locationId, CHARINDEX (''.'' , locationId, (CHARINDEX(''.'',locationId)+1))-1) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Module_Code = ISNULL(CASE WHEN CHARINDEX(''.'',locationId) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(locationId),CHARINDEX(''.'',REVERSE(locationId)) + 1, LEN(locationId))),2) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Module_Name = ISNULL(CASE WHEN CHARINDEX(''.'',locationId) > 0 ' +
   '                           THEN LEFT(locationId, LEN(locationId) - CHARINDEX (''.'' , REVERSE(locationId), (CHARINDEX(''.'',REVERSE(locationId))+1))) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Bay = ISNULL(CASE WHEN CHARINDEX(''.'',locationId) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(locationId),CHARINDEX(''.'',REVERSE(locationId)) + 1, LEN(locationId))),1) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Load_Unit_Code = ISNULL(loadUnitId, '''') ' +
   '     , Inventory_Unit_Code = ISNULL(inventoryUnitPk, '''') ' +
   '     , Inventory_Batch_Code = ISNULL(batch, '''') ' +
   '     , Base_Load_Unit_Code = ISNULL(baseLoadUnitId, '''') ' +
   '     , Receiving_Processing_Code = ISNULL(receivingReferenceId, '''') ' +
   '     , Item_Category_Code = '''' ' +   
   '     , Inventory_Qty = ISNULL(quantity, '''') ' +
   '     , Inventory_Unassigned_Qty = ISNULL(quantityFree, '''') ' +
   '     , Source_System = ''' + @lSourceSystem +  '''' +
   ' FROM [STAGE].FCT_Inventory_'+ @lSourceSystem +
   ' ORDER BY Record_Timestamp, Load_Unit_Code,  Inventory_Unit_Code , Item_Code ' +
   ' END '  +
   ' IF OBJECT_ID(''[STAGE].FCT_Inventory_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_Inventory_'+@lSourceSystem; 
				   
EXEC sp_executesql @lSQLStmt
