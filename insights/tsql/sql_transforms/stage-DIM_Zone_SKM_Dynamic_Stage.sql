SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--SET @lSourceSystem = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX);
DECLARE @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Zone_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 zonePk= '''', 	description= '''', 	zoneType= '''', 	tenantName= '''', dimensionModificationTime=''''
		INTO STAGE.DIM_Zone_' + @lSourceSystem + '
	END;'
EXEC sp_executesql @lSQLStmt

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Target_Picking_Rate_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 zonePk= '''', 	quantityPickedTarget= '''', 	linePickedTarget= '''', 	workflowPk= '''', dimensionModificationTime=''''
		INTO STAGE.DIM_Target_Picking_Rate_' + @lSourceSystem + '
	END;'
EXEC sp_executesql @lSQLStmt

SET @Transform_SQLStmt = '
--DIM_Sort_Status
WITH Latest_Dim_Zones AS (
	SELECT TOP 1 WITH TIES
		Zone_Code = zonePk,
		Zone_Name = description,
		Zone_Type = zoneType,
		DC_Tenant_Name = tenantName
	FROM [STAGE].[DIM_Zone_' + @lSourceSystem + ']
	WHERE zonePk IS NOT NULL
	ORDER BY Row_number() over (partition by zonePk, tenantName order by dimensionModificationTime desc)
),
Latest_Dim_Target_Picking_Rate AS (
	SELECT TOP 1 WITH TIES
		Zone_Code = zonePk, 
		Qty_Picked_Target = quantityPickedTarget,
		Line_Picked_Target = linePickedTarget
	FROM [STAGE].[DIM_Target_Picking_Rate_' + @lSourceSystem + ']
	ORDER BY row_number() OVER (partition by zonePk order by dimensionModificationTime desc)
)
SELECT
	Zone_Code = Latest_Dim_Zones.Zone_Code,
	Zone_Name,
	Zone_Type,
	Qty_Picked_Target,
	Line_Picked_Target,
	Logical_Destination_Code = '''',
	Facility_Code = '''',
	Customer_Code = '''',
	Source_System = ''' + @lSourceSystem +'''
FROM Latest_Dim_Zones
LEFT JOIN Latest_Dim_Target_Picking_Rate ON Latest_Dim_Zones.Zone_Code = Latest_Dim_Target_Picking_Rate.Zone_Code
'

SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Target_Picking_Rate_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[DIM_Zone_'+@lSourceSystem +']'


--PRINT(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt