SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)', 
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware',
        @lPrimary_SQLStmt NVARCHAR(MAX),
		@lSecondary_SQLStmt NVARCHAR(MAX);

SELECT @lPrimary_SQLStmt = 
' DECLARE @lStatement NVARCHAR(MAX) = '''',
          @lIncludeUnion BIT = 0;

IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' + 
           ' where t.name = ''OEE_MEASURING_POINT_MSG_' + @lSourceSystem + ''' and s.name = ''Stage'') '  +
		   
' BEGIN ' + 
    
	' SELECT @lstatement = ''SELECT DISTINCT Record_Timestamp = [Datetime], '' + 
	  '' Hour_Quarter_ID = RIGHT(LEFT(REPLACE(REPLACE(REPLACE(CONVERT(VARCHAR(24),[Datetime],121),'''':'''',''''''''),''''-'''',''''''''),'''' '''',''''''''),10),8) + LTRIM(STR(datepart(minute,[Datetime]))/15), '' +
	  '' Device_Code = DeviceID '' +
      '' ,Subsystem_Code = ''''SORT'''' '' +
	  '' ,Subsystem_Category = ''''APE'''' '' +
	  '' ,Data_Point_Code = '''''''' '' +
	  '' ,Data_Point_Description = '''''''' '' +
	  '' ,Run_Time_Duration_Seconds =   ISNULL(CAST(CAST(StartedTime / 1000 AS DECIMAL(16,4)) AS VARCHAR),'''''''') ''+ 
	  '' ,Stop_Time_Duration_Seconds =  ISNULL(CAST(CAST(StoppedTime / 1000 AS DECIMAL(16,4)) AS VARCHAR),'''''''') ''+   
      '' ,Fault_Time_Duration_Seconds =  ISNULL(CAST(CAST(FaultedTime / 1000 AS DECIMAL(16,4)) AS VARCHAR),'''''''') ''+  
      '' ,Idle_Time_Duration_Seconds =  ISNULL(CAST(CAST(IdleTime / 1000 AS DECIMAL(16,4)) AS VARCHAR),'''''''') ''+  
      '' ,Blocked_Time_Duration_Seconds = ISNULL(CAST(CAST(BlockedTime / 1000 AS DECIMAL(16,4)) AS VARCHAR),'''''''') ''+   
      '' ,Starved_Time_Duration_Seconds = ISNULL(CAST(CAST(StarvedTime / 1000 AS DECIMAL(16,4))AS VARCHAR),'''''''') ''+
	  '' ,Total_Carton_Count = ISNULL(CartonCount,'''''''') ''+ 
	  '' , Avg_Carton_Length = ISNULL(AvgCartonLen,'''''''') ''+  
      '' , Total_Event_Count = ISNULL(EventCount,'''''''') ''+   
	  '' , Fault_Count = ISNULL(FaultCount,'''''''') ''+   
	  '' , VFD_Run_Event_Count  = '''''''' '' +  
      '' , VFD_Stop_Event_Count = '''''''' '' +   
	  '' , VFD_Idle_Event_Count = '''''''' '' +   
	  '' , VFD_Accelerate_Event_Count = '''''''' '' +  
      '' , VFD_Decelerate_Event_Count = '''''''' '' +  
	  '' , Source_System = ''''' + @lSourceSystem + ''''''' +
	  '' FROM STAGE.OEE_MEASURING_POINT_MSG_'+ @lSourceSystem + '''' + 

	  ' SET @lIncludeUnion = ''1''' +
	      
' End ' + 
'IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' + 
           ' where t.name = ''VFD_DATA_POINT_CONTROL_MSG_' + @lSourceSystem + ''' and s.name = ''Stage'') '  +
		   
' BEGIN ' + 

     ' IF @lIncludeUnion = ''1'' '+
	 ' BEGIN SET @lstatement = @lstatement + '' UNION ALL '' END' + 

	' SELECT @lstatement = @lStatement + ''SELECT DISTINCT Record_Timestamp = [Datetime], '' + 
	  '' Hour_Quarter_ID = RIGHT(LEFT(REPLACE(REPLACE(REPLACE(CONVERT(VARCHAR(24),[Datetime],121),'''':'''',''''''''),''''-'''',''''''''),'''' '''',''''''''),10),8) + LTRIM(STR(datepart(minute,[Datetime]))/15) '' +
	  '' ,Device_Code = DeviceID '' +
      '' ,Subsystem_Code = ''''SORT'''' '' +
	  '' ,Subsystem_Category = ''''APE'''' '' +
	  '' ,Data_Point_Code = ''''VFD1'''' '' +
	  '' ,Data_Point_Description = '''''''' '' +
	  '' ,Run_Time_Duration_Seconds =   ISNULL(CAST(CAST(StartedTime / 1000 AS DECIMAL(16,4)) AS VARCHAR),'''''''') ''+ 
	  '' ,Stop_Time_Duration_Seconds =  ISNULL(CAST(CAST(StoppedTime / 1000 AS DECIMAL(16,4)) AS VARCHAR),'''''''') ''+   
      '' ,Fault_Time_Duration_Seconds =  ISNULL(CAST(CAST(FaultedTime / 1000 AS DECIMAL(16,4)) AS VARCHAR),'''''''') ''+  
      '' ,Idle_Time_Duration_Seconds =  ISNULL(CAST(CAST(IdleTime / 1000 AS DECIMAL(16,4)) AS VARCHAR),'''''''') ''+  
      '' ,Blocked_Time_Duration_Seconds = '''''''' '' + 
      '' ,Starved_Time_Duration_Seconds = '''''''' '' +
	  '' ,Total_Carton_Count = '''''''' '' +
	  '' , Avg_Carton_Length = '''''''' '' +
      '' , Total_Event_Count = '''''''' '' +
	  '' , Fault_Count = ISNULL(FaultCount,'''''''') ''+  
	  '' , VFD_Run_Event_Count  = ISNULL(StartedCount,'''''''') ''+ 
      '' , VFD_Stop_Event_Count = ISNULL(StoppedCount,'''''''') ''+  
	  '' , VFD_Idle_Event_Count = ISNULL(IdleCount,'''''''') ''+  
	  '' , VFD_Accelerate_Event_Count = ISNULL(AccCount,'''''''') ''+   
      '' , VFD_Decelerate_Event_Count = ISNULL(DecCount,'''''''') ''+ 
	  '' , Source_System = ''''' + @lSourceSystem + ''''''' +
	  '' FROM STAGE.VFD_DATA_POINT_CONTROL_MSG_'+ @lSourceSystem + '''' + 
	 
' END ' +

'EXEC sp_executesql @lstatement ; ' 

--SELECT @lSecondary_SQLStmt = 'DROP TABLE IF EXISTS [STAGE].VFD_DATA_POINT_CONTROL_MSG_' + @lSourceSystem +
--' DROP TABLE IF EXISTS [STAGE].OEE_MEASURING_POINT_MSG_' + @lSourceSystem 
	  
SELECT @lSecondary_SQLStmt = ' IF OBJECT_ID(''[STAGE].VFD_DATA_POINT_CONTROL_MSG_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].VFD_DATA_POINT_CONTROL_MSG_'+ @lSourceSystem +
       ' IF OBJECT_ID(''[STAGE].OEE_MEASURING_POINT_MSG_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].OEE_MEASURING_POINT_MSG_'+ @lSourceSystem;  	  

EXEC sp_executesql @lPrimary_SQLStmt;
EXEC sp_executesql @lSecondary_SQLStmt; 