--===========================================================================================================
--  Extract needs to run for the entire hours worth of data
--  If the extract is run in fragments of an hour, there is no way to exclude double counted orders
--  Batch start/end times below will only run the most previous full hour relative to the current PC time
--===========================================================================================================

SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @aBatchStartDateTime AS DATETIME = '$(BatchFrom)' , @aBatchEndDateTime AS DATETIME = '$(BatchTo)', @aETLBatchID INT = '$(ETL_Batch_ID)'
-- DECLARE @aBatchStartDateTime AS DATETIME = '2018-04-03 09:00:00.000' , @aBatchEndDateTime AS DATETIME = '2018-06-06 17:00:00.000', @aETLBatchID INT = 1

SELECT @aBatchStartDateTime = DATEADD(hour,DATEPART(hour,@aBatchStartDateTime)-1,CAST(CONVERT(VARCHAR(10),@aBatchStartDateTime,101) AS DATETIME)),
       @aBatchEndDateTime = DATEADD(hour,DATEPART(hour,@aBatchEndDateTime),CAST(CONVERT(VARCHAR(10),@aBatchEndDateTime,101) AS DATETIME))
                      

--===========================================================================================================
--  CTEs for pulling sorter inducts and order counts from OA
--===========================================================================================================
;WITH SortData AS(
SELECT SC.Scanner_ID
      ,Record_Timestamp
	  ,DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Record_Timestamp) / 60 * 60 , 0) AS Sorter_BucketTime
	  ,HR.Hour_ID
  FROM OA.FCT_Sort FS
  JOIN OA.DIM_Physical_Destination PD ON FS.Intended_Physical_Destination_ID = PD.Physical_Destination_ID
  JOIN OA.DIM_Scanner SC ON PD.Scanner_ID = SC.Scanner_ID
  JOIN OA.DIM_Hour_Quarter HQ ON FS.Hour_Quarter_ID = HQ.Hour_Quarter_ID
  JOIN OA.DIM_Hour HR ON HQ.Hour_ID = HR.Hour_ID
  WHERE Record_Timestamp BETWEEN @aBatchStartDateTime AND @aBatchEndDateTime
  ),

  InductCount AS(
  SELECT Sorter_BucketTime
  ,COUNT(Record_Timestamp) AS Induct_Count
  ,Scanner_ID
  ,Hour_ID
  FROM SortData
  GROUP BY Sorter_BucketTime, Scanner_ID, Hour_ID
  ),

  OrderData AS(
  SELECT Order_Released_Date_Time
      ,Order_Complete_Date_Time
	  ,Pick_Order_Code
  FROM OA.FCT_Pick_Order_Complete POC
  JOIN OA.DIM_Pick_Order PO ON POC.Pick_Order_ID = PO.Pick_Order_ID
  )

--INSERT INTO OA.AGG_FCT_OEE_Sorter
SELECT Scanner_ID
,Hour_ID
,Sorter_BucketTime AS Hour_Start_Date_Time
--,DATEPART(HOUR,Sorter_BucketTime) AS Hour_Code
,Induct_Count AS Actual_Induct_Count
,Outstanding_Orders
,CASE WHEN Outstanding_Orders = 0 THEN 0
	ELSE CAST(CAST(Induct_Count AS DECIMAL(10,4))/CAST(Outstanding_Orders AS DECIMAL(10,4)) AS DECIMAL(10,4))
	END AS Induct_Order_Ratio
,ETL_Batch_ID = @aETLBatchID
--,NULL AS ETL_Update_Timestamp
FROM(
	SELECT Scanner_ID
	,Sorter_BucketTime
	,COUNT(Order_Released_Date_Time) AS Outstanding_Orders
	,Induct_Count
	,Hour_ID
	FROM(
			SELECT Scanner_ID
			,Sorter_BucketTime
			,Induct_Count
			,Pick_Order_Code
			,Order_Released_Date_Time
			,Order_Complete_Date_Time
			,Hour_ID
			FROM InductCount IC
			JOIN OrderData OD ON IC.Sorter_BucketTime > OD.Order_Released_Date_Time AND (IC.Sorter_BucketTime < OD.Order_Complete_Date_Time OR OD.Order_Complete_Date_Time IS NULL)
	)a
	GROUP BY Sorter_BucketTime, Scanner_ID, Induct_Count, Hour_ID
)b