SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--SET @lSourceSystem = 'DematicSoftware';
DECLARE @lSQLStmt NVARCHAR(MAX);
DECLAR<PERSON> @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);


SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Sort_Status_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 statusId= '''', 	originator= '''', 	statusName= '''', 	tenantName= '''', displayName=''''
			, eventTypeDisplayName='''', reasonCodeDisplayName='''', dimensionModificationTime=''''
		INTO STAGE.DIM_Sort_Status_' + @lSourceSystem + '
	END;'
EXEC sp_executesql @lSQLStmt

SET @lSQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_FaultText_Status_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 description= '''', 	faultCode= '''', 	mtsType= '''', 	tenantName= '''', dimensionModificationTime=''''
			, locale=''''
		INTO STAGE.DIM_FaultText_Status_' + @lSourceSystem + '
	END;'
EXEC sp_executesql @lSQLStmt


SET @Transform_SQLStmt = '
--DIM_Sort_Status
WITH Sort_Status AS (
	SELECT TOP 1 WITH TIES 
		Status_Code = ISNULL(statusId, ''''),
		Status_Locale_Name = ISNULL(displayName,''''),
		Subsystem_Code = ISNULL(UPPER(originator), ''''),
		Status_Category_Code = ISNULL(statusName, ''''),
		Status_Category_Name = statusName,
		Status_Type = '''',
		DC_Tenant_Name = ISNULL(UPPER(tenantName), ''''),
		Source_System = ''' + @lSourceSystem +''',
		Subsystem_Category = ''DEMATIC IQ'',
		Active_Rec_Ind = 1,
		Status_Locale_Code = ''en_US''
	FROM [STAGE].[DIM_Sort_Status_' + @lSourceSystem + ']
	ORDER BY Row_number() over (partition by statusId, tenantName, originator order by dimensionModificationTime desc)
)
SELECT DISTINCT
	Status_Code,
	Status_Locale_Name,
	Subsystem_Code,
	Status_Category_Code,
	Status_Category_Name,
	Status_Type,
	DC_Tenant_Name,
	Source_System,
	Subsystem_Category = Sort_Status.Subsystem_Category,
	Active_Rec_Ind,
	Status_Relevancy_Ind = CASE WHEN EXCLUDE_STATUSES.Value = Sort_Status.Status_Code THEN 0 ELSE 1 END,
	Status_Locale_Code
FROM Sort_Status
LEFT OUTER JOIN (
	SELECT Value, Subsystem_Category
	FROM OA.Configuration WITH (NOLOCK)
	WHERE Configuration_Name = ''Exclude_Status''
		AND Configuration_Code != ''DEFAULT''
		AND Source_System = ''' + @lSourceSystem +''') AS EXCLUDE_STATUSES 
	ON Sort_Status.Status_Code = EXCLUDE_STATUSES.Value AND Sort_Status.Subsystem_Category = EXCLUDE_STATUSES.Subsystem_Category	
UNION
--DIM_FaultText_Status
SELECT DISTINCT * FROM (
	SELECT TOP 1 WITH TIES 
		Status_Code = ISNULL(faultCode,''''),
		Status_Locale_Name = ISNULL(TRIM(description),''''),
		Subsystem_Code = ISNULL(mtsType,''''),
		Status_Category_Code = '''',
		Status_Category_Name = '''',
		Status_Type = '''',
		DC_Tenant_Name = ISNULL(tenantName,''''),
		Source_System = ''' + @lSourceSystem +''',
		Subsystem_Category = ''DEMATIC IQ'',
		Active_Rec_Ind = 1,
		Status_Relevancy_Ind = 1,
		Status_Locale_Code = CASE WHEN locale IS NULL OR UPPER(locale) = ''EN''  THEN ''en_US''
								WHEN UPPER(locale) = ''DE'' THEN ''de_DE''
								WHEN UPPER(locale) = ''FR'' THEN ''fr_FR''
								WHEN UPPER(locale) = ''NL'' THEN ''nl_NL''
								ELSE locale
							END
	FROM [STAGE].[DIM_FaultText_Status_' + @lSourceSystem + ']
	ORDER BY Row_number() over (partition by tenantName, faultCode, mtsType, locale order by dimensionModificationTime desc)
) t

'
SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Sort_Status_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[DIM_Sort_Status_'+@lSourceSystem +']
	IF OBJECT_ID(''[STAGE].[DIM_FaultText_Status_' + @lSourceSystem + ']'', ''U'') IS NOT NULL
		DROP TABLE [STAGE].[DIM_FaultText_Status_'+@lSourceSystem +']'
		

--EXEC(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt
