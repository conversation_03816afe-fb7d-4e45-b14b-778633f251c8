SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)'; 
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX), @lSQLDropsStmt NVARCHAR(MAX);


SET @lSQLStmt = '
IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Depal_Equipment_Status_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
	CREATE TABLE [STAGE].[FCT_Depal_Equipment_Status_' + @lSourceSystem + '](
		[eventTime] [varchar](255) NULL,
		[workstationId] [varchar](255) NULL,
		[status] [varchar](255) NULL,
		[palletIds] [varchar](255) NULL,
		[Record_Timestamp_Offset] [varchar](255) NULL,
		[Key_ID] [int] IDENTITY(1,1) NOT NULL
	) ON [PRIMARY]
END

IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Pal_Equipment_Status_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN

	CREATE TABLE [STAGE].[FCT_Pal_Equipment_Status_' + @lSourceSystem + '](
		[eventTime] [varchar](255) NULL,
		[workstationId] [varchar](255) NULL,
		[status] [varchar](255) NULL,
		[palletId] [varchar](255) NULL,
		[Record_Timestamp_Offset] [varchar](255) NULL,
		[Key_ID] [int] IDENTITY(1,1) NOT NULL
	) ON [PRIMARY]
END
'
EXEC sp_executesql @lSQLStmt

SET @lSQLStmt = '
SELECT DISTINCT
	Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), eventTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), eventTime,120),15,2) as INT)/15)),
	Record_Timestamp = ISNULL(eventTime,''''),
	Device_Code = ISNULL(UPPER(workstationId),''''),
	Status_Code = ISNULL(UPPER(status) ,''''),
	Subsystem_Code = CASE
						WHEN charindex(''PA'',workstationid,1) > 0 AND CHARINDEX(''DE'',workstationid,1) = 0  THEN ''CONVEYOR''
						WHEN CHARINDEX(''DE'',workstationid,1) > 0 THEN ''DEPALLATIZER''
						ELSE Subsystem_Type_Source
					END,
	Subsystem_Category = ''DEMATIC IQ'',
	Reason_Code = '''',
	Label_Print_Line_Code = '''',
	Pallet_Code = ISNULL(UPPER(palletId),''''),
	Source_System = '''+ @lSourceSystem +'''

FROM ( 
		SELECT *, Subsystem_Type_Source=''CONVEYOR''  FROM	[STAGE].[FCT_Pal_Equipment_Status_' + @lSourceSystem + '] WITH(NOLOCK)
		UNION
		SELECT *, Subsystem_Type_Source=''DEPALLATIZER'' FROM [STAGE].[FCT_Depal_Equipment_Status_' + @lSourceSystem + '] WITH(NOLOCK)
		)  TB_Equipment_Status;
		'
SET @lSQLDropsStmt = ' DROP TABLE IF EXISTS [STAGE].[FCT_Depal_Equipment_Status_'+@lSourceSystem +']'+  
   	   	   		     ' DROP TABLE IF EXISTS [STAGE].[FCT_Pal_Equipment_Status_'+@lSourceSystem +']';  


EXEC sp_executesql @lSQLStmt
EXEC sp_executesql @lSQLDropsStmt
