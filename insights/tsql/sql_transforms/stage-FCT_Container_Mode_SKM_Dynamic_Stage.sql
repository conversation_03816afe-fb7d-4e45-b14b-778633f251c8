SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lQuery_SQLStmt NVARCHAR(MAX),
        @lChecks_SQLStmt NVARCHAR(MAX),
        @lDrops_SQLStmt NVARCHAR(MAX);

SELECT @lChecks_SQLStmt =  
' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
' where t.name = ''FCT_Container_Mode_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
' BEGIN 
	
CREATE TABLE [STAGE].[FCT_Container_Mode_' + @lSourceSystem + '](
	[autostore_id] [varchar](255) NULL,
	[event_time] [varchar](255) NULL,
	[process_epoch_ns] [varchar](255) NULL,
	[container_id] [varchar](255) NULL,
	[container_mode] [varchar](255) NULL,
	[event_type] [varchar](255) NULL,
	[Key_ID] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
			
 END' 

SELECT @lQuery_SQLStmt = '
SELECT
	[Hour_Quarter_ID] = LEFT(REPLACE(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),REPLACE(event_time, ''T'','' ''),25),''-'',''''),3,50),'' '',''''),'':'',''''),''T'',''''),8) + LTRIM(STR(DATEPART(minute,REPLACE(event_time, ''T'','' ''))/15)),
	[Record_Timestamp] = DATEADD(ns, CAST(RIGHT(ISNULL(process_epoch_ns,0), 6) AS INT), cast(REPLACE(event_time, ''T'','' '') as Datetime2(7))),
	[Container_Code] = container_id,
	[Module_Code] = COALESCE(autostore_Id,''''),
	[Status_Code] = CASE container_mode WHEN ''G'' THEN ''G-GRID'' WHEN ''O'' THEN ''O-OPEN'' WHEN ''T'' THEN ''T-PREPARED'' WHEN ''F'' THEN ''F-FORECAST'' WHEN ''C'' THEN ''C-CLOSED'' WHEN ''P'' THEN ''P-PORT'' WHEN ''R'' THEN ''R-TOGRID'' WHEN ''X'' THEN ''X-OUTSIDE'' ELSE ISNULL(container_mode,'''') END,
	[Source_System] = '''+ @lSourceSystem +''',
	[Subsystem_Code] = ''AUTOSTORE'',
	[Subsystem_Category] = ''AUTOSTORE''
FROM [STAGE].[FCT_Container_Mode_' + @lSourceSystem + ']
';

SELECT @lDrops_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Container_Mode_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  DROP TABLE [STAGE].[FCT_Container_Mode_'+@lSourceSystem +']'

EXEC sp_executesql @lChecks_SQLStmt
EXEC sp_executesql @lQuery_SQLStmt
EXEC sp_executesql @lDrops_SQLStmt
