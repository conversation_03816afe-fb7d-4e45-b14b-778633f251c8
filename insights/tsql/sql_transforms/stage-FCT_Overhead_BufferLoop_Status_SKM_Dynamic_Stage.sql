SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''FCT_Overhead_BufferLoop_Status_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
                   ' SELECT  Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),eventTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,eventTime)/15))  ' +
                   '     , Record_Timestamp = eventTime '       +
                   '     , Overhead_Loop_Code = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN REVERSE(LEFT(REVERSE(loopLocation), CHARINDEX(''.'',REVERSE(loopLocation)) - 1)) ' +
                   '                           ELSE loopLocation ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_Name = ISNULL(loopLocation, '''') ' +
                   '     , Overhead_Loop_Type = ISNULL(loopType, '''') ' +
                   '     , Overhead_Loop_Area = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),3) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_Group = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),2) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_SubGroup = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),1) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
				   '     , Lock_Status_Code = ISNULL(lockStatus, '''') ' +
                   '     , Subsystem_Code = ''OVERHEAD_SORT'' ' +
                   '     , Subsystem_Category = ''DEMATIC IQ'' ' +
                   '     , Fault_Status_Code = ISNULL(faultStatus, '''') ' +
				   '     , Oldest_Overhead_Carrier_In_Loop_Duration_Seconds = ISNULL(longestResidentTime, '''') ' +
				   '     , Empty_Overhead_Carrier_Count = ISNULL(numberOfEmptySortationLoadUnits, '''') ' +
				   '     , Entered_Overhead_Carrier_Count = ISNULL(numberOfEntries, '''') ' + 
				   '     , Exited_Overhead_Carrier_Count = ISNULL(numberOfExits, '''') ' +
				   '     , Occupied_Overhead_Carrier_Count = ISNULL(numberOfFilledSortationLoadUnits, '''') ' +
				   '     , Overhead_BufferLoop_Utilization_Percentage = ISNULL(fillingPercentage, '''') ' +
				   '     , Overhead_BufferLoop_Fault_Count = ISNULL(numberOfFaults, '''') ' +
				   '     , Overhead_BufferLoop_Open_SKU_Requests = ISNULL(numberOfOpenSKURequests, '''') ' +
                   '     , Source_System = ''' + @lSourceSystem +  '''' +
				   '     , Status_Category_Code = '''' '+
                   ' FROM [STAGE].FCT_Overhead_BufferLoop_Status_'+ @lSourceSystem +
                   ' GROUP BY  eventTime, loopLocation, loopType, faultStatus, lockStatus, longestResidentTime  ' +
                   '    , numberOfEmptySortationLoadUnits, numberOfEntries, numberOfExits, numberOfFilledSortationLoadUnits  ' +
                   '    , fillingPercentage, numberOfFaults, numberOfOpenSKURequests ' +
                   ' END ' +
				   
				   --' DROP TABLE IF EXISTS [STAGE].FCT_Overhead_BufferLoop_Status_' + @lSourceSystem
				   ' IF OBJECT_ID(''[STAGE].FCT_Overhead_BufferLoop_Status_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_Overhead_BufferLoop_Status_'+@lSourceSystem;  

EXEC sp_executesql @lSQLStmt

