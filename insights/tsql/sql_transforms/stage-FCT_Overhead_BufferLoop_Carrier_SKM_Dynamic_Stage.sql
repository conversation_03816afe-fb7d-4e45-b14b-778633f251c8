SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''FCT_Overhead_BufferLoop_Carrier_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
   ' SELECT DISTINCT ' +
   '      Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),exitTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,exitTime)/15))  ' +
   '     , Record_Timestamp = exitTime '       +
   '     , Overhead_Order_Code = ISNULL(CASE WHEN orderId is not null AND client is not null AND orderId != ISNULL(client,'''') '+
   '                          THEN client + ''#'' + orderId ELSE orderId END ,'''') ' +
   '     , Overhead_Loop_Code = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
   '                           THEN REVERSE(LEFT(REVERSE(loopLocation), CHARINDEX(''.'',REVERSE(loopLocation)) - 1)) ' +
   '                           ELSE loopLocation ' +
   '                           END,'''') ' +
   '     , Overhead_Loop_Name = ISNULL(loopLocation, '''') ' +
   '     , Overhead_Loop_Type = ISNULL(loopType, '''') ' +
   '     , Overhead_Loop_Area = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),3) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Overhead_Loop_Group = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),2) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Overhead_Loop_SubGroup = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),1) ' +
   '                           ELSE '''' ' +
   '                           END,'''') ' +
   '     , Client = ISNULL(client, '''') ' +
   '     , Overhead_Line_Item_Code = ISNULL(orderLineId, '''') ' +
   '     , Item_Code = OA.Item_Code_Generator(skuId,null,skuQuantityUnitId,client)' +
   '     , Item_SKU = ISNULL(skuId, '''') ' +
   '     , UOM = ISNULL(skuQuantityUnitId, '''') ' +
   '     , SKU_Qualifier = ISNULL(skuQualifier, '''') ' +
   '     , Subsystem_Code = ''OVERHEAD_SORT'' ' +
   '     , Subsystem_Category = ''DEMATIC IQ'' ' +
   '     , Overhead_Carrier_Exit_Status_Code = ISNULL(exitReason, '''') ' +
   '     , Overhead_Carrier_Code = ISNULL(sluId, '''') ' +
   '     , Overhead_Carrier_Entry_Date_Time = ISNULL(entryTime, '''') ' +
   '     , Overhead_Carrier_Loop_Count = ISNULL(numberOfRevolutions, '''') ' + 
   '     , Overhead_Carrier_InLoop_Fail_Exit_Count = ISNULL(numberOfCapacityRevolutions, '''') ' +
   '     , Overhead_Carrier_InLoop_Duration_Seconds = ISNULL(timeInLoop, '''') ' +
   '     , Source_System = ''' + @lSourceSystem +  '''' +
   ' FROM [STAGE].FCT_Overhead_BufferLoop_Carrier_'+ @lSourceSystem +
   '     ORDER BY Record_Timestamp, Overhead_Order_Code, Overhead_Loop_Code, Overhead_Carrier_Exit_Status_Code, Subsystem_Code, Subsystem_Category ' +
   ' END ' +
   ' IF OBJECT_ID(''[STAGE].FCT_Overhead_BufferLoop_Carrier_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_Overhead_BufferLoop_Carrier_'+@lSourceSystem; 
				   
EXEC sp_executesql @lSQLStmt