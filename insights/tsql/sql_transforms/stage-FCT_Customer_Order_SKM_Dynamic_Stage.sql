SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
-- DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lQuery_SQLStmt NVARCHAR(MAX),
        @lChecks_SQLStmt NVARCHAR(MAX),
        @lDrops_SQLStmt NVARCHAR(MAX);

SELECT @lChecks_SQLStmt =  
					' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                    ' where t.name = ''FCT_ORDERCREATED_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
					' BEGIN CREATE TABLE [STAGE].FCT_ORDERCREATED_' + @lSourceSystem + ' ([orderId] varchar(250) default '''',[orderType] varchar(250)  default '''',[numberOfLines] varchar(250)  default '''',[tenantName] varchar(250)  default '''',[clientId] varchar(250)  default '''',[eventDate] varchar(250)  default '''',[Key_ID] integer not NULL ) END' +
					' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                    ' where t.name = ''FCT_ORDERCOMPLETED_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
					' BEGIN CREATE TABLE [STAGE].FCT_ORDERCOMPLETED_' + @lSourceSystem + ' ([orderId] varchar(250) default '''',[orderType] varchar(250)  default '''',[tenantName] varchar(250)  default '''',[clientId] varchar(250)  default '''',[eventDate] varchar(250)  default '''',[overdeliveredLines] varchar(250)  default '''',[notDeliveredLines] varchar(250)  default '''',[numberOfLoadUnits] varchar(250)  default '''',[underdeliveredLines] varchar(250)  default '''',[exactlyDeliveredLines] varchar(250)  default '''',[Key_ID] integer not NULL ) END'
		
SELECT @lQuery_SQLStmt = 
					' BEGIN ' +
						-- get the events that are both completed and created orders in stage '
						' WITH StageCreateCompleteOrder AS (  ' + 
						'  	SELECT DISTINCT Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),OCR.eventDate,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,OCR.eventDate)/15))   ' + 
				        '       , Customer_Order_Code = ISNULL(CASE WHEN OCR.orderId is not null AND OCR.clientId is not null AND OCR.orderId != ISNULL(OCR.clientId,'''') '+
                        '                          THEN OCR.clientId + ''#'' + OCR.orderId ELSE OCR.orderId END ,'''') ' +					
						'  		, Customer_Order_Name = ISNULL(OCR.orderId, '''')   ' + 
						'  		, Customer_Order_Type = ISNULL(OCR.orderType, '''')  ' + 
						'  		, Expected_Order_Line_Count = ISNULL(OCR.numberOfLines, '''')  ' + 
						'  		, Actual_Order_Line_Count = ISNULL(OCO.exactlyDeliveredLines, '''')  ' + 
						'  		, Overdelivered_Line_Count = ISNULL(OCO.overdeliveredLines, '''')  ' + 
						'  		, Undelivered_Line_Count = ISNULL(OCO.notDeliveredLines, '''')  ' + 
						'  		, Actual_Container_Count = ISNULL(OCO.numberOfLoadUnits, '''')  ' + 
						'  		, Underdelivered_Line_Count = ISNULL(OCO.underdeliveredLines, '''')  ' + 
						'  		, Record_Timestamp = ISNULL(OCR.eventDate, '''')  ' + 
						'  		, Order_Complete_Date_Time = ISNULL(OCO.eventDate, '''')  ' + 
						'       , Source_System = ''' + @lSourceSystem +  '''' + 
						'  	FROM [STAGE].FCT_ORDERCREATED_' + @lSourceSystem + ' OCR  ' + 
						'  	INNER JOIN [STAGE].FCT_ORDERCOMPLETED_' + @lSourceSystem + ' OCO ON (OCR.clientId = OCO.clientId AND OCR.orderId = OCO.orderId)  ' + 
						'  	) ' + 
						' , IncompleteCreatedOrders AS ( ' + 
						-- get the events that only have created orders ' 
						'     SELECT DISTINCT Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),OCR.eventDate,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,OCR.eventDate)/15)) ' + 
						'         , Customer_Order_Code = ISNULL(CASE WHEN OCR.orderId is not null AND OCR.clientId is not null AND OCR.orderId != ISNULL(OCR.clientId,'''') '+
                        '                          THEN OCR.clientId + ''#'' + OCR.orderId ELSE OCR.orderId END ,'''') ' +
						'         , Customer_Order_Name = ISNULL(OCR.orderId, '''') ' + 
						'         , Customer_Order_Type = ISNULL(OCR.orderType, '''') ' + 
						'         , Expected_Order_Line_Count = ISNULL(OCR.numberOfLines, '''') ' + 
						'         , Actual_Order_Line_Count = ISNULL(OCO.exactlyDeliveredLines, '''') ' + 
						'         , Overdelivered_Line_Count = ISNULL(OCO.overdeliveredLines, '''') ' + 
						'         , Undelivered_Line_Count = ISNULL(OCO.notDeliveredLines, '''') ' + 
						'         , Actual_Container_Count = ISNULL(OCO.numberOfLoadUnits, '''') ' + 
						'         , Underdelivered_Line_Count = ISNULL(OCO.underdeliveredLines, '''') ' + 
						'         , Record_Timestamp = ISNULL(OCR.eventDate, '''') ' + 
						'         , Order_Complete_Date_Time = ISNULL(OCO.eventDate, '''') ' + 
						'         , Source_System = ''' + @lSourceSystem +  '''' + 
						'     FROM [STAGE].FCT_ORDERCREATED_' + @lSourceSystem + ' OCR ' + 
						'     LEFT OUTER JOIN [STAGE].FCT_ORDERCOMPLETED_' + @lSourceSystem + ' OCO ON (OCR.clientId = OCO.clientId AND OCR.orderId = OCO.orderId) ' + 
						'     WHERE OCO.eventDate IS NULL ' + 
						' )   ' + 
						-- get the events that only have completed orders'
						' , IncompleteCompletedOrders AS ( ' + 
						'     SELECT DISTINCT Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),OCR.eventDate,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,OCR.eventDate)/15)) ' + 
                        '         , Customer_Order_Code = ISNULL(CASE WHEN OCO.orderId is not null AND OCO.clientId is not null AND OCO.orderId != ISNULL(OCO.clientId,'''') '+
                        '                          THEN OCO.clientId + ''#'' + OCO.orderId ELSE OCO.orderId END ,'''') ' +
						'         , Customer_Order_Name = ISNULL(OCO.orderId, '''') ' + 
						'         , Customer_Order_Type = ISNULL(OCO.orderType, '''') ' + 
						'         , Expected_Order_Line_Count = ISNULL(OCR.numberOfLines, '''') ' + 
						'         , Actual_Order_Line_Count = ISNULL(OCO.exactlyDeliveredLines, '''') ' + 
						'         , Overdelivered_Line_Count = ISNULL(OCO.overdeliveredLines, '''') ' + 
						'         , Undelivered_Line_Count = ISNULL(OCO.notDeliveredLines, '''') ' + 
						'         , Actual_Container_Count = ISNULL(OCO.numberOfLoadUnits, '''') ' + 
						'         , Underdelivered_Line_Count = ISNULL(OCO.underdeliveredLines, '''') ' + 
						'         , Record_Timestamp = ISNULL(OCR.eventDate, '''') ' + 
						'         , Order_Complete_Date_Time = ISNULL(OCO.eventDate, '''') ' + 
						'         , Source_System = ''' + @lSourceSystem +  '''' + 
						'     FROM [STAGE].FCT_ORDERCOMPLETED_' + @lSourceSystem + ' OCO ' + 
						'     LEFT OUTER JOIN [STAGE].FCT_ORDERCREATED_' + @lSourceSystem + ' OCR ON (OCR.clientId = OCO.clientId AND OCR.orderId = OCO.orderId) ' + 
						'     WHERE OCR.eventDate IS NULL ' + 
						' )  ' + 
						'  ' +
						' , NoneDedupResultSet AS ( ' +
						' SELECT * FROM StageCreateCompleteOrder '	 +
						'  ' + 
						' UNION  ' + 
						'  ' + 
						-- join stage complete orders with created in the fct table. This is for completing already created orders on the fct' 
						' SELECT DISTINCT FCT_CCO.Hour_Quarter_ID ' + 
						'     ,ICO.Customer_Order_Code  ' + 
						'     ,ICO.Customer_Order_Name  ' + 
						'     ,ICO.Customer_Order_Type  ' + 
						'     ,FCT_CCO.Expected_Order_Line_Count  ' + 
						'     ,ICO.Actual_Order_Line_Count  ' + 
						'     ,ICO.Overdelivered_Line_Count  ' + 
						'     ,ICO.Undelivered_Line_Count ' + 
						'     ,ICO.Actual_Container_Count  ' + 
						'     ,ICO.Underdelivered_Line_Count ' + 
						'     ,FCT_CCO.Record_Timestamp  ' + 
						'     ,ICO.Order_Complete_Date_Time  ' + 
						'    , ICO.Source_System  ' + 
						'  FROM IncompleteCompletedOrders ICO ' + 
						'  INNER JOIN (  ' + 
						'     SELECT FCO.Hour_Quarter_ID ' + 
						'         ,DCO.Customer_Order_Code  ' + 
						'         ,FCO.Expected_Order_Line_Count  ' + 
						'         ,FCO.Actual_Container_Count  ' + 
						'         ,FCO.Record_Timestamp  ' +  
						'     FROM [OA].[FCT_Customer_Order] FCO  ' + 
						'     INNER JOIN [OA].[DIM_Customer_Order] DCO ON FCO.Customer_Order_ID = DCO.Customer_Order_ID ' + 
						'     ) AS FCT_CCO ON ICO.Customer_Order_Code = FCT_CCO.Customer_Order_Code ' + 
						' 	 ' + 
						' UNION ' + 
						'  ' + 
						-- join stage created orders with completed in the fct. this is for only new created 
						-- orders that have not yet completed and for updates of the existing fct' 
						' SELECT DISTINCT ICO.Hour_Quarter_ID ' + 
						'     ,ICO.Customer_Order_Code  ' + 
						'     ,ICO.Customer_Order_Name  ' + 
						'     ,ICO.Customer_Order_Type  ' + 
						'     ,ICO.Expected_Order_Line_Count  ' + 
						'     ,Actual_Order_Line_Count = ISNULL(FCT_CCO.Actual_Order_Line_Count, '''')  ' + 
						'     ,Overdelivered_Line_Count = ISNULL(FCT_CCO.Overdelivered_Line_Count, '''')  ' + 
						'     ,Undelivered_Line_Count = ISNULL(FCT_CCO.Undelivered_Line_Count, '''') ' + 
						'     ,Actual_Container_Count = ISNULL(FCT_CCO.Actual_Container_Count, '''')  ' + 
						'     ,Underdelivered_Line_Count = ISNULL(FCT_CCO.Underdelivered_Line_Count, '''') ' + 
						'     ,ICO.Record_Timestamp  ' + 
						'     ,Order_Complete_Date_Time = ISNULL(CAST(FCT_CCO.Order_Complete_Date_Time AS VARCHAR(50)), '''')  ' + 
						'    , ICO.Source_System  ' + 
						'  FROM IncompleteCreatedOrders ICO ' + 
						'  LEFT OUTER JOIN (  ' + 
						'     SELECT FCO.Hour_Quarter_ID ' + 
						'         ,DCO.Customer_Order_Code  ' + 
						'         ,FCO.Actual_Order_Line_Count  ' + 
						'         ,FCO.Overdelivered_Line_Count  ' + 
						'         ,FCO.Undelivered_Line_Count ' + 
						'         ,FCO.Actual_Container_Count  ' + 
						'         ,FCO.Underdelivered_Line_Count ' + 
						'         ,FCO.Order_Complete_Date_Time  ' + 
						'     FROM [OA].[FCT_Customer_Order] FCO  ' + 
						'     INNER JOIN [OA].[DIM_Customer_Order] DCO ON FCO.Customer_Order_ID = DCO.Customer_Order_ID ' + 
						'     ) AS FCT_CCO ON ICO.Customer_Order_Code = FCT_CCO.Customer_Order_Code ' + 
						'  UNION ' +
						-- create completed orders that do not have create orders' 
						' SELECT DISTINCT Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),ICO.Order_Complete_Date_Time,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,ICO.Order_Complete_Date_Time)/15))' + 
						'     ,ICO.Customer_Order_Code  ' + 
						'     ,ICO.Customer_Order_Name  ' + 
						'     ,ICO.Customer_Order_Type  ' + 
						'     ,Expected_Order_Line_Count = ISNULL(ICO.Expected_Order_Line_Count, '''') ' + 
						'     ,ICO.Actual_Order_Line_Count  ' + 
						'     ,ICO.Overdelivered_Line_Count  ' + 
						'     ,ICO.Undelivered_Line_Count ' + 
						'     ,ICO.Actual_Container_Count  ' + 
						'     ,ICO.Underdelivered_Line_Count ' + 
						'     ,Record_Timestamp = ICO.Order_Complete_Date_Time ' +
						'     ,ICO.Order_Complete_Date_Time  ' + 
						'    , ICO.Source_System  ' + 
						'  FROM IncompleteCompletedOrders ICO ' + 
						'  LEFT OUTER JOIN (  ' + 
						'     SELECT FCO.Hour_Quarter_ID ' + 
						'         ,DCO.Customer_Order_Code  ' + 
						'         ,DCO.Customer_Order_Name  ' + 
						'         ,DCO.Customer_Order_Type ' + 
						'         ,FCO.Expected_Order_Line_Count  ' + 
						'         ,FCO.Actual_Order_Line_Count  ' + 
						'         ,FCO.Overdelivered_Line_Count  ' + 
						'         ,FCO.Undelivered_Line_Count ' + 
						'         ,FCO.Actual_Container_Count  ' + 
						'         ,FCO.Underdelivered_Line_Count ' + 
						'         ,FCO.Record_Timestamp  ' + 
						'         ,FCO.Order_Complete_Date_Time  ' + 
						'     FROM [OA].[FCT_Customer_Order] FCO  ' + 
						'     INNER JOIN [OA].[DIM_Customer_Order] DCO ON FCO.Customer_Order_ID = DCO.Customer_Order_ID ' + 
						'     ) AS FCT_CCO ON ICO.Customer_Order_Code = FCT_CCO.Customer_Order_Code WHERE FCT_CCO.Customer_Order_Code IS NULL ' +
						' )  '

SELECT @lQuery_SQLStmt = @lQuery_SQLStmt +
						' , FinalQuery AS ( ' +
						'		SELECT RANK() OVER (PARTITION BY Customer_Order_Code ORDER BY Record_Timestamp DESC, Order_Complete_Date_Time DESC) create_rank ' +
						'       , Hour_Quarter_ID ' +
						'		, Customer_Order_Code ' +
						'		, Customer_Order_Name ' +
						'		, Customer_Order_Type ' +
						'		, Expected_Order_Line_Count ' +
						'		, Actual_Order_Line_Count ' +
						'		, Overdelivered_Line_Count ' +
						'		, Undelivered_Line_Count ' +
						'		, Actual_Container_Count ' +
						'		, Underdelivered_Line_Count ' +
						'		, Record_Timestamp ' +
						'		, Order_Complete_Date_Time ' +
						'		, Source_System ' +
						'	FROM NoneDedupResultSet' +
						' ) SELECT * FROM FinalQuery WHERE create_rank = 1' +
						' END '

						
SELECT @lDrops_SQLStmt = ' IF OBJECT_ID(''[STAGE].FCT_ORDERCREATED_' + @lSourceSystem + ''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_ORDERCREATED_'+@lSourceSystem +
							' IF OBJECT_ID(''[STAGE].FCT_ORDERCOMPLETED_' + @lSourceSystem + ''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_ORDERCOMPLETED_'+@lSourceSystem

EXEC sp_executesql @lChecks_SQLStmt
EXEC sp_executesql @lQuery_SQLStmt
EXEC sp_executesql @lDrops_SQLStmt


