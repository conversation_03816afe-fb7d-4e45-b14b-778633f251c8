SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(100) = '$(Source_System)'
--DECLARE @lSourceSystem VARCHAR(100) = 'DematicSoftware'
DECLARE @lSQLStmt NVARCHAR(MAX);

SET @lSQLStmt = '
-- Insert into copy table
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_LIFT_STATISTICS_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
	IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Device_Statistics_Fault_Detail_' + @lSourceSystem + ''' and s.name = ''Stage'')
		INSERT INTO STAGE.FCT_Device_Statistics_Fault_Detail_'+ @lSourceSystem + '
			SELECT 
				deviceid,eventTime,faultCode1stImportantFault,quantity1stImportantFault,faultCode2ndImportantFault, quantity2ndImportantFault,faultCode3rdImportantFault, quantity3rdImportantFault,faultCode4thImportantFault, quantity4thImportantFault
				,faultCode5thImportantFault, quantity5thImportantFault,faultCode6thImportantFault, quantity6thImportantFault,faultCode7thImportantFault, quantity7thImportantFault,faultCode8thImportantFault, quantity8thImportantFault
				,faultCode9thImportantFault, quantity9thImportantFault,faultCode10thImportantFault, quantity10thImportantFault 
			FROM STAGE.FCT_LIFT_STATISTICS_' + @lSourceSystem + ';
	ELSE
		SELECT 
			deviceid,eventTime,faultCode1stImportantFault,quantity1stImportantFault,faultCode2ndImportantFault, quantity2ndImportantFault,faultCode3rdImportantFault, quantity3rdImportantFault,faultCode4thImportantFault, quantity4thImportantFault
			,faultCode5thImportantFault, quantity5thImportantFault,faultCode6thImportantFault, quantity6thImportantFault,faultCode7thImportantFault, quantity7thImportantFault,faultCode8thImportantFault, quantity8thImportantFault
			,faultCode9thImportantFault, quantity9thImportantFault,faultCode10thImportantFault, quantity10thImportantFault INTO STAGE.FCT_Device_Statistics_Fault_Detail_'+ @lSourceSystem + '
		FROM STAGE.FCT_LIFT_STATISTICS_' + @lSourceSystem + ';	

	SELECT 
		Record_Timestamp= eventTime  ,
		Hour_Quarter_ID=   substring(Replace(Replace(CONVERT(VARCHAR(13), eventTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), eventTime,120),15,2) as INT)/15)),
		Device_Code=  deviceid ,
		Up_Move_Count=  SUM(CAST( upMoves as INT)) ,
		Down_Move_Count=   SUM(CAST( downMoves as INT)),
		Y_Travel_Distance_Meters=  SUM(CAST( ytravelDistance as INT)),
		Infeed_Count=   SUM(CAST( inFeedCycles as INT)),
		Outfeed_Count=   SUM(CAST( outFeedCycles as INT)),
		Down_Time_Minutes=  SUM(CAST( downtime as INT)) ,
		Active_Time_Minutes=  SUM(CAST( timeActive as INT)) ,
		Run_Time_Minutes=   SUM(CAST( timeRunning as INT)),
		Fault_Count=   SUM(CAST( numberOfFaults as INT)),
		Warning_Count=   SUM(CAST( numberOfWarnings as INT)),
		Subsystem_Code = ''MULTISHUTTLE'',
		Subsystem_Category = ''DEMATIC IQ'',
		Source_System = '''+ @lSourceSystem +'''
	FROM STAGE.FCT_LIFT_STATISTICS_'+ @lSourceSystem +' 
	GROUP BY eventTime, deviceid
END
'

EXEC sp_executesql @lSQLStmt


SET @lSQLStmt = '
DROP TABLE IF EXISTS STAGE.FCT_LIFT_STATISTICS_'+ @lSourceSystem 

EXEC sp_executesql @lSQLStmt
