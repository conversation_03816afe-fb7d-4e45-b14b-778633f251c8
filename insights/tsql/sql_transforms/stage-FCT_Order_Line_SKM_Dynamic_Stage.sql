SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware';
DECLARE @Process_SQLStmt1 NVARCHAR(MAX); 
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @Process_SQLStmt1 = '
IF EXISTS (SELECT [name] FROM tempdb.sys.tables WHERE [name] like ''#FCT_Stage_Pick_Events_' + @lSourceSystem + '%'')
BEGIN
	DROP TABLE #FCT_Stage_Pick_Events_' + @lSourceSystem + ';
END
 
CREATE TABLE #FCT_Stage_Pick_Events_' + @lSourceSystem + '(
	eventTime Datetime2(7) NOT NULL, pickerId VARCHAR(150) NULL,orderPk VARCHAR(150) NULL, Pick_Order_Code VARCHAR(150) NULL,
	client VARCHAR(150) NULL, sourceLoadUnit VARCHAR(150) NULL, loadUnitId VARCHAR(150) NULL, locationPk VARCHAR(150) NULL,
	containerPk VARCHAR(150) NULL, skuPk VARCHAR(150) NULL, zonePk VARCHAR(150) NULL, inductionZonePk VARCHAR(150) NULL,
	terminalId VARCHAR(150) NULL, quantityPicked real NULL, quantityTarget real NULL, pickPk VARCHAR(150) NULL,
	hostLineId VARCHAR(150) NULL, pickBatchName VARCHAR(150) NULL, quantityUnit VARCHAR(150) NULL, sourceLoadUnitType VARCHAR(150) NULL,
	loadUnitType VARCHAR(150) NULL, cartPk VARCHAR(150) NULL, cartId VARCHAR(150) NULL, technology VARCHAR(150) NULL,
	processType VARCHAR(150) NULL, pickConfirmationCode VARCHAR(150) NULL, workflowPk VARCHAR(150) NULL, confirmationReason VARCHAR(150) NULL,
	trainId VARCHAR(150) NULL, workstationId VARCHAR(150) NULL, quantityRemaining real NULL, productCode VARCHAR(150) NULL,
	tenantName VARCHAR(150) NULL
);
'

SET @Process_SQLStmt1 = @Process_SQLStmt1 + '
-- Sort Disposition Events
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Pick_Activity_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	WITH FCT_OperatorActivity AS (
		SELECT DISTINCT
				eventTime
				,pickerId = UPPER(COALESCE(userPk,pickerId))
				,orderPk = NULL
				,Pick_Order_Code = NULL
				,client = NULL
				,sourceLoadUnit = NULL
				,loadUnitId
				,locationPk = NULL
				,containerPk
				,skuPk = CASE WHEN skuPk = ''-'' THEN NULL ELSE skuPk END 
				,zonePk
				,inductionZonePk
				,terminalId = NULL
				,quantityPicked = NULL
				,quantityTarget = NULL
				,pickPk = NULL
				,hostLineId = NULL
				,pickBatchName = NULL
				,quantityUnit = NULL
				,sourceLoadUnitType = NULL
				,loadUnitType
				,cartPk
				,cartId
				,technology
				,processType = NULL
				,pickConfirmationCode = UPPER(event)
				,workflowPk
				,confirmationReason = NULL
				,trainId
				,workstationId
				,quantityRemaining = NULL
				,productCode = NULL
				,tenantName 
		FROM [STAGE].[FCT_Pick_Activity_' + @lSourceSystem + ']
		WHERE (inductionZonePk IS NULL OR inductionZonePk = '''') AND (UPPER(event) = ''INDUCT'' AND LEN(ISNULL(cartId,'''')) = 0 OR UPPER(event) = ''CART_ACTIVATED'')
	)
	INSERT INTO #FCT_Stage_Pick_Events_' + @lSourceSystem + ' SELECT * from FCT_OperatorActivity;
END

IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Picking_OrderLine_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	WITH FCT_OrderLine AS ( 
		SELECT DISTINCT
			eventTime
			,pickerId = UPPER(COALESCE(userPk,pickerId))
			,orderPk
			,Pick_Order_Code = pickOrderPk
			,client
			,sourceLoadUnit
			,loadUnitId
			,locationPk
			,containerPk
			,skuPk = CASE WHEN skuPk = ''-'' THEN NULL ELSE skuPk END 
			,zonePk
			,inductionZonePk
			,terminalId
			,quantityPicked
			,quantityTarget
			,pickPk
			,hostLineId
			,pickBatchName
			,quantityUnit = quantityUnitPk
			,sourceLoadUnitType
			,loadUnitType
			,cartPk
			,cartId
			,technology
			,processType
			,pickConfirmationCode = UPPER(confirmationCode)
			,workflowPk
			,confirmationReason
			,trainId
			,workstationId
			,quantityRemaining
			,productCode
			,tenantName 	
		FROM [STAGE].[FCT_Picking_OrderLine_' + @lSourceSystem + '] 
	)
	INSERT INTO #FCT_Stage_Pick_Events_' + @lSourceSystem + ' SELECT * from FCT_OrderLine;
END
;'

SET @Process_SQLStmt1 =  @Process_SQLStmt1 + '
WITH Mapped_Events AS (
	SELECT 
		 Record_Timestamp = eventTime
		,Operator_Code = pickerId
		,Line_Item = pickPk
		,technology
		,productCode
		,quantityTarget
		,quantityPicked
		,pickBatchName
		,containerPk
		,confirmationReason
		,processType
		,sourceLoadUnit
		,sourceLoadUnitType
		,loadUnitType
		,loadUnitId
		,event = pickConfirmationCode
		,zonePk = UPPER(COALESCE(zonePk,workstationId))
		,trainId = COALESCE(trainId,'''')
		,Pick_Order_Code
		,Customer_Order_Code = ISNULL(orderPk,'''')
		,cartpk
		,cartId
		,sku_Code = CASE
						WHEN CHARINDEX(''#'',skuPk) > 0 THEN SUBSTRING(skuPk, CHARINDEX(''#'', skuPk)+1, LEN(skuPk))
						ELSE skuPk
					END
		,client_Code = CASE 
							WHEN client IS NOT NULL THEN client
							WHEN CHARINDEX(''#'', skuPk) > 0 THEN LEFT(skuPk, CHARINDEX(''#'', skuPk) - 1)
							ELSE NULL
						END
		,workType = CASE
						WHEN CHARINDEX(''#'',workflowPk) > 0 
						  THEN  LEFT (workflowpk, CHARINDEX(''#'', workflowpk)-1) 
						ELSE workflowPk
					END
		,Location_Code = locationPk
        ,Location_Name = ISNULL(OA.Location_Name_Generator(locationPk), '''') 
		,Workstation_Code = ISNULL(workstationId,'''')
		,Work_Area_Code = CASE 
							WHEN CHARINDEX(''.'',locationPk) > 0 THEN Substring(locationPk, OA.CHARINDEX2(locationPk,''.'',1)+1, (OA.CHARINDEX2(locationPk,''.'',2) - OA.CHARINDEX2(locationPk,''.'',1))-1)
							ELSE ''''
						 END
		,Module_Code = CASE 
							WHEN CHARINDEX(''.'',locationPk) > 0 THEN Substring(locationPk, OA.CHARINDEX2(locationPk,''.'',2)+1, (OA.CHARINDEX2(locationPk,''.'',3) - OA.CHARINDEX2(locationPk,''.'',2))-1)
							ELSE ''''
						 END
        , Bay_Code = CASE 
							WHEN CHARINDEX(''.'',locationPk) > 0 THEN Substring(locationPk, OA.CHARINDEX2(locationPk,''.'',3)+1, (OA.CHARINDEX2(locationPk,''.'',4) - OA.CHARINDEX2(locationPk,''.'',3))-1)
							ELSE ''''
				     END
		,UOM_Code = CASE
						WHEN LEN(quantityUnit)- LEN(REPLACE(quantityUnit, ''#'', '''')) > 1 THEN SUBSTRING(quantityUnit, OA.CHARINDEX2(quantityUnit,''#'', 2)+1, LEN(quantityUnit))
						ELSE quantityUnit
					END
		,workTypeStyle = CASE
							WHEN LEN(workflowPk)- LEN(REPLACE(workflowPk, ''#'', '''')) > 1 THEN SUBSTRING(workflowPk, OA.CHARINDEX2(workflowPk,''#'', 1)+1, OA.CHARINDEX2(workflowPk,''#'', 2)-OA.CHARINDEX2(workflowPk,''#'', 1)-1)
							ELSE NULL
						END
		,Technology_Vendor = CASE
								WHEN LEN(COALESCE(technology,'''')) > 0 AND OA.CHARINDEX2(workflowPk,''#'',3) > 0 
								THEN Substring(workflowpk, OA.CHARINDEX2(workflowpk,''#'',3)+1, (len(workflowpk) - OA.CHARINDEX2(workflowpk,''#'',3)))
								ELSE NULL
							END
		,quantityRemaining =  CASE
								WHEN quantityRemaining IS NULL THEN  ISNULL(quantityTarget,0) - ISNULL(quantityPicked, 0)
								ELSE quantityRemaining
							END
		,Mapped_Event = CASE
					WHEN pickConfirmationCode = ''OK'' THEN ''Completed''
					WHEN pickConfirmationCode = ''SPLIT'' THEN ''Ncar''
					WHEN pickConfirmationCode = ''SHORT'' AND CHARINDEX(''#'', Pick_Order_Code) > 0 THEN ''ShortCompleted''
					WHEN pickConfirmationCode = ''NOTPICKED'' AND CHARINDEX(''#'', Pick_Order_Code) > 0 THEN ''ShortCompleted''
					WHEN pickConfirmationCode = ''FULL'' THEN ''Ncar'' 
					WHEN pickConfirmationCode = ''DAMAGE'' THEN ''Ncar''
					ELSE pickConfirmationCode
				END
	FROM #FCT_Stage_Pick_Events_' + @lSourceSystem + '
	WHERE UPPER(TRIM(pickConfirmationCode)) != ''DELETED'' AND UPPER(processType) != ''PUTWALL_PACK'' AND LEN(ISNULL(pickerId,'''')) > 0
), Process_Events AS (
	SELECT 
		*
		,SkippedQty = CASE WHEN UPPER(Mapped_Event) = ''SKIPPED'' THEN quantityRemaining ELSE 0 END
		,ShortedQty = CASE WHEN UPPER(Mapped_Event) = ''SHORT'' OR UPPER(Mapped_Event) = ''SHORTCOMPLETED'' THEN quantityRemaining ELSE 0 END
		,OrderLineCompletedIdx = CASE WHEN UPPER(Mapped_Event) = ''SHORTCOMPLETED'' OR UPPER(Mapped_Event) = ''COMPLETED'' OR (UPPER(Mapped_Event) = ''NCAR'' AND quantityRemaining = 0) THEN 1 ELSE 0 END
		,NewContainerQty = CASE WHEN UPPER(Mapped_Event) = ''NCAR'' THEN quantityRemaining ELSE 0 END
		,Duration_Seconds = NULL
	FROM Mapped_Events
	-- filter only if is true LEN(REPLACENULL(Pick_Type_Code,"")) == 0 || (EventTime >= Effective_Begin_Date && EventTime < Effective_End_Date)
)
'

SET @Process_SQLStmt1 =  @Process_SQLStmt1 + '
SELECT 
	Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), Record_TimeStamp,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), Record_TimeStamp,120),15,2) as INT)/15))
	,Record_Timestamp
	,Location_Code = ISNULL(UPPER(Location_Code), '''')
	,Operator_Code = ISNULL(Operator_Code, '''')
	,Item_Code = OA.Item_Code_Generator(sku_Code,productCode,UOM_Code,client_Code)
	,Line_Item = ISNULL(UPPER(Line_Item), '''')
	,Cart_Instance_Code = ISNULL(ISNULL(cartpk,cartid),'''')
	,Cart_Instance_Name = ISNULL(ISNULL(cartid,cartpk),'''')
	,Technology_Code = ISNULL(UPPER(technology), '''')
	,Technology_Vendor = ISNULL(UPPER(Technology_Vendor), '''')
	,Pick_Order_Line_Complete_Ind = OrderLineCompletedIdx
	,Source_System = ''' + @lSourceSystem + '''
	,Zone_Code = ISNULL(UPPER(zonePk), '''')
	,Pick_To_Container_Physical_Code = ISNULL(UPPER(loadUnitId),'''')
	,Work_Request_Code = ''''
	,Pick_Order_Code = ISNULL(UPPER(Pick_Order_Code), '''')
	,Container_Train_Code = ISNULL(UPPER(trainId), '''')
	,Pick_To_Container_Type_Code = ISNULL(UPPER(loadUnitType), '''')
	,Work_Type_Code = ISNULL(UPPER(workType), '''')
	,Container_Type_Code = ISNULL(UPPER(sourceLoadUnitType), '''')
	,Process_Type_Code = ISNULL(UPPER(processType), '''')
	,Reason_Code = ISNULL(UPPER(confirmationReason), '''')
	,Container_Physical_Code = ISNULL(UPPER(sourceLoadUnit),'''')
	,Container_Instance_Code = ISNULL(UPPER(containerPk),'''')
	,Pick_Batch_Code = ISNULL(pickBatchName,'''')
	,Duration_Seconds =''''
	,Picked_Qty = SUM(quantityPicked)
	,Skipped_Qty = SUM(SkippedQty)
	,Shorted_Qty = SUM(ShortedQty)
	,New_Container_Qty = SUM(NewContainerQty)
	,Requested_Qty = SUM(CAST(quantityTarget as INTEGER))
	,Work_Type_Style = ISNULL(UPPER(workTypeStyle), '''')
	,Operator_Pick_Time = ''''
	,Work_Area_Code = ISNULL(Work_Area_Code,'''')
	,Module_Code = ISNULL(Module_Code,'''')
	,Bay =  ISNULL(Bay_Code,'''')
	,Location_Name = ISNULL(Location_Name,'''')
	,Workstation_Code = ISNULL(Workstation_Code,'''')
	,Customer_Order_Code = ISNULL(Customer_Order_Code,'''')
FROM Process_Events
GROUP BY Record_Timestamp, Location_Code, Operator_Code, sku_Code,productCode,UOM_Code,client_Code, Line_Item, cartpk,cartid, technology, Technology_Vendor, OrderLineCompletedIdx,
         zonePk, loadUnitId, Pick_Order_Code, trainId, loadUnitType, workType, sourceLoadUnitType, processType, confirmationReason, sourceLoadUnit, containerPk, pickBatchName, 
         workTypeStyle, Work_Area_Code, Module_Code, Bay_Code, Location_Name, Workstation_Code, Customer_Order_Code
	
'

SET @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Pick_Activity_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[FCT_Pick_Activity_'+@lSourceSystem +'] 
	IF OBJECT_ID(''[STAGE].[FCT_Picking_OrderLine_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
	   DROP TABLE [STAGE].[FCT_Picking_OrderLine_'+@lSourceSystem +']'


EXEC sp_executesql @Process_SQLStmt1
--EXEC sp_executesql @Drop_SQLStmt
