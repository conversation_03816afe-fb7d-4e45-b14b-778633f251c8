
SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

-- Set up local variables
DECLARE @lStartDate DATETIME2(7), @lEndDate DATETIME2(7);
DECLARE @lBatchID INT = 0 --'$(ETL_Batch_ID)'

-- Find the Start and End date based on Batch Run
SELECT @lStartDate = DATEADD(hour,-1,MIN(record_timestamp)) , @lEndDate = DATEADD(hour,1,MAX(Record_Timestamp)) 
FROM OA.FCT_Robot_Activity WITH (NOLOCK)
WHERE ETL_Batch_ID = @lBatchID 

-- Reformat Dates 
SET @lStartDate = DATEADD(hour,DATEPART(hour,@lStartDate),CONVERT(VARCHAR(10),@lStartDate,101))
/*** Set the end Date to the last 15 minute bucket ***/
SET @lEndDate = DATEADD(MINUTE, DATEDIFF(MINUTE, 0, @lEndDate) / 15 * 15, 0)

DECLARE @AllEvents TABLE (
		Hour_Quarter_ID INT,
		Record_Timestamp DateTime2(7),
		Robot_Device_ID INT,
		Status_ID INT,
		Module_ID INT,
		Event VARCHAR(100)
		)

-- Capture all events from the selected ETL_Batch_ID
INSERT INTO @AllEvents 
SELECT	Hour_Quarter_ID,
		Record_Timestamp,
		Robot_Device_ID,
		Status_ID,
		Module_ID,
		Event 
FROM OA.FCT_Robot_Activity RA 
WHERE Record_Timestamp >= @lStartDate 
AND  Record_Timestamp < @lEndDate
UNION -- Add the last event prior to this batch for each affected robot
Select Hour_Quarter_ID,
	   FA.Record_Timestamp,
	   FA.Robot_Device_ID,
	   Status_ID,
	   Module_ID,
	   Event  
FROM OA.FCT_Robot_Activity FA
join (Select Robot_Device_ID, Record_Timestamp = Max(Record_Timestamp)
      from oa.Fct_Robot_Activity
      Where Record_Timestamp < @lStartDate
      Group by Robot_Device_ID) MR
ON FA.Robot_Device_ID = MR.Robot_Device_ID and FA.Record_Timestamp = MR.Record_Timestamp
-- Limit Robot IDs to those from the current Batch ID
WHERE FA.Robot_Device_ID in (SELECT DISTINCT x.Robot_Device_ID FROM OA.FCT_Robot_Activity x WHERE Record_Timestamp >= @lStartDate AND  Record_Timestamp < @lEndDate)

-- Reset Start Date to new min time stamp
Select @lStartDate = MIN(Record_Timestamp) FROM @AllEvents
SET @lStartDate = DATEADD(hour,DATEPART(hour,@lStartDate),CONVERT(VARCHAR(10),@lStartDate,101))

-- Create each ten-minute time bucket in the span of this batch
DECLARE @lBucketDate DATETIME = DATEADD(Minute,-30,@lStartDate)
DECLARE @tbl_Bucket TABLE(bucket_date DATETIME, Bucket_end DATETIME)

WHILE @lBucketDate < @lEndDate BEGIN
	INSERT INTO @tbl_Bucket VALUES(@lBucketDate,DATEADD(minute,15,@lBucketDate))
	SET @lBucketDate = DATEADD(minute,15,@lBucketDate);
END


-- Snag lead/lag times and events
; WITH Cleaned_Events as (
	Select 
		Hour_Quarter_ID,
		Record_Timestamp,
		Robot_Device_ID,
		Status_ID,
		Module_ID,
		Events ,
		CASE Events
			WHEN '[available]' THEN 'available'
			WHEN '[available] [charging] [not_faulted]' THEN 'charging'
			WHEN '[available] [fault]' THEN 'faulted'
			WHEN '[available] [not_charging]' THEN 'available'
			WHEN '[available] [not_charging] [not_faulted]' THEN 'available'
			WHEN '[available] [not_faulted] [charging]' THEN 'charging'
			WHEN '[available] [not_faulted]' THEN 'available'
			WHEN '[charging]' THEN 'charging'
			WHEN '[charging] [available] [not_faulted]' THEN 'charging'
			WHEN '[charging] [not_faulted]' THEN 'charging'
			WHEN '[fault]' THEN 'faulted'
			WHEN '[not_charging]' THEN 'available' --?
			WHEN '[not_charging] [not_faulted] [unavailable]' THEN 'unavailable'
			WHEN '[not_charging] [unavailable]' THEN 'unavailable'
			WHEN '[not_charging] [unavailable] [not_faulted]' THEN 'unavailable'
			WHEN '[not_faulted]' THEN 'available'
			WHEN '[not_faulted] [unavailable]' THEN 'unavailable'
			WHEN '[unavailable]' THEN 'unavailable'
			ELSE 'unknown' 
		END as [Event],
		TB1.rows
	FROM (
		SELECT
			Hour_Quarter_ID,
			Record_Timestamp,
			Robot_Device_ID,
			Status_ID,
			Module_ID,
			Events = '['+ STRING_AGG(Event,'] [') +']', 
			rows = Count(*)
		FROM @AllEvents RA 
		--WHERE Robot_Device_ID = 26
		GROUP BY 
			Record_Timestamp, 
			Robot_Device_ID, 
			Hour_Quarter_ID, 
			Status_ID, 
			Module_ID
	) TB1
)

-- select * from Cleaned_Events
--order by Record_Timestamp
, Robot_Events as (
SELECT 
	Hour_Quarter_ID,
	Robot_Device_ID,
--	[Prev_Event] = LAG(Event) OVER (PARTITION BY Robot_Device_ID ORDER BY  Record_Timestamp ASC),
	[Curr_Event] = [Event],  
--	Prev_Time =  LAG(Record_Timestamp) OVER (PARTITION BY Robot_Device_ID ORDER BY  Record_Timestamp ASC),
	Record_Timestamp,
	Seconds = DATEDIFF(second, LEAD(Record_Timestamp) OVER (PARTITION BY Robot_Device_ID ORDER BY  Record_Timestamp ASC), Record_Timestamp),
	Date_Hour = CONVERT(VARCHAR(10),Record_Timestamp,101)  +' '+ LTRIM(str(DatePart(hour,Record_Timestamp))) +':00',
	Next_Event = LEAD(Event) OVER (PARTITION BY Robot_Device_ID ORDER BY  Record_Timestamp ASC),
	Next_Time =  LEAD(Record_Timestamp) OVER (PARTITION BY Robot_Device_ID ORDER BY  Record_Timestamp ASC),
	Status_ID, 
	Module_ID
FROM Cleaned_Events WITH(NOLOCK)
)
,
  tag_events as (
  SELECT *,
  CASE
	WHEN Curr_Event in ('available','not_charging','not_faulted')  THEN 1
	WHEN Curr_Event in ('charging', 'unavailable','faulted')  THEN 0
	ELSE NULL END Available
 FROM Robot_Events
  )
, 
activity_states_segmented as (
SELECT 
	CASE WHEN DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Record_Timestamp) / 15 * 15 , 0) = DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Next_Time) / 15 * 15 , 0) THEN
		--Event contain in time segment
		DATEDIFF_BIG(MILLISECOND, Record_Timestamp, Next_Time)/1000.00
	  ELSE
	  CASE 
			-- Event start at or before beginning of time segment
			WHEN bucket_date = DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Record_Timestamp) / 15 * 15 , 0) THEN
				DATEDIFF_BIG(MILLISECOND, Record_Timestamp, DATEADD(minute,15,bucket_date))/1000.00
			-- Event extends past the start and end of the time segment 
			WHEN bucket_date > DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Record_Timestamp) / 15 * 15 , 0) and bucket_date < DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Next_Time) / 15 * 15 , 0) THEN
				DATEDIFF_BIG(MILLISECOND, bucket_date, DATEADD(minute,15,bucket_date))/1000.00
	  		-- Event ends within the time segment
			WHEN bucket_date =  DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Next_Time) / 15 * 15 , 0) THEN
			 DATEDIFF_BIG(MILLISECOND, bucket_date, Next_Time)/1000.00
	        -- Event starts before the bucket_date and there is no ending event time; 
			WHEN bucket_date > DATEADD(MINUTE, DATEDIFF(MINUTE, 0, Record_Timestamp) / 15 * 15 , 0) AND Next_Time IS NULL THEN
			 DATEDIFF_BIG(MILLISECOND, bucket_date, Bucket_end)/1000.00
		END  	
	END AS state_times,

	Hour_Quarter_ID,
	Robot_Device_ID,
	Status_ID, 
	Module_ID,
	[Event] = Curr_Event,
	Record_Timestamp as Curr_Time,
	Next_Event,
	Next_Time, 
	Interval_Start_Time = bucket_date,
	bucket_date,
	Bucket_end,
	Start_Date = @lStartDate,
	End_Date = @lEndDate,
	[Robot_Available_Ind] = Available,
	Seconds
FROM  TAG_EVENTS TE 
INNER JOIN @tbl_Bucket ON 
	(bucket_date BETWEEN Record_Timestamp AND Next_Time) 
	or (Bucket_end between Record_Timestamp AND Next_Time) 
	or (Record_Timestamp <= bucket_date AND Next_Time >= Bucket_end)
	or (Record_Timestamp >= bucket_date AND Next_Time <= Bucket_end)
	or (Record_Timestamp >= bucket_date AND Next_Time is null)
	or (Record_Timestamp <= bucket_date AND Next_Time is null)
--WHERE [prev_event] IS NOT NULL
)

--Select * from activity_states_segmented order by Hour_Quarter_ID

SELECT
	Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13),Interval_Start_Time,120),' ',''),'-',''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20),Interval_Start_Time,120),15,2) as INT)/15)),
	Robot_Device_ID,
	Status_ID,
	Module_ID,
	[Event],
	Interval_Start_Time,
	[Robot_Available_Ind],
	Total_Time_In_Seconds = SUM(CAST(state_times as INT)),
	[ETL_Batch_ID] = @lBatchID
FROM activity_states_segmented
WHERE state_times IS NOT NULL
GROUP BY
	--Hour_Quarter_ID,
	Robot_Device_ID,
	Event,
	Robot_Available_Ind,
    Interval_Start_Time,
	Status_ID,
	Module_ID
ORDER BY Robot_Device_ID, Interval_Start_Time


