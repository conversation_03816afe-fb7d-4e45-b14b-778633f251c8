SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''FCT_Overhead_Induction_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
                   ' SELECT  Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),eventTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,eventTime)/15))  ' +
                   '     , Record_Timestamp = eventTime '       +
                   '     , Operator_Code = ISNULL(userId, '''') ' +
                   '     , Operator_Name = ISNULL(userId, '''') ' +
                   '     , Workstation_Code = ISNULL(CASE WHEN CHARINDEX(''.'',inductionStation) > 0 ' +
                   '                           THEN REVERSE(LEFT(REVERSE(inductionStation), CHARINDEX(''.'',REVERSE(inductionStation)) - 1)) ' +
                   '                           ELSE inductionStation ' +
                   '                           END,'''') ' +
                   '     , Workstation_Name = ISNULL(inductionStation, '''') ' +
                   '     , Workstation_Type = ''Induct Station'' ' +
                   '     , Workstation_Area = ISNULL(CASE WHEN CHARINDEX(''.'',inductionStation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(inductionStation),CHARINDEX(''.'',REVERSE(inductionStation)) + 1, LEN(inductionStation))),3) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Workstation_Group = ISNULL(CASE WHEN CHARINDEX(''.'',inductionStation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(inductionStation),CHARINDEX(''.'',REVERSE(inductionStation)) + 1, LEN(inductionStation))),2) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Workstation_SubGroup = ISNULL(CASE WHEN CHARINDEX(''.'',inductionStation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(inductionStation),CHARINDEX(''.'',REVERSE(inductionStation)) + 1, LEN(inductionStation))),1) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Overhead_Induction_Type = ISNULL(capability, '''') ' +
                   '     , Subsystem_Code = ''OVERHEAD_SORT'' ' +
                   '     , Subsystem_Category = ''DEMATIC IQ'' ' +
                   '     , Overhead_Station_Induct_Mode_Code = ISNULL(inductMode, '''') ' +
                   '     , Overhead_Station_Induct_Mode_Name = ISNULL(inductMode, '''') ' +
                   '     , Container_Code = ISNULL(sourceLoadUnitId, '''')' +
                   '     , Status_Code = ISNULL(error, '''') ' +
                   '     , Client = ISNULL(clientId, '''') ' +
				   '     , Item_Code = OA.Item_Code_Generator(skuId,null,skuQuantityUnitId,clientId)' +
                   '     , Item_SKU = ISNULL(skuId, '''') ' +
                   '     , UOM = ISNULL(skuQuantityUnitId, '''') ' +
                   '     , Container_Scan_Time = ISNULL(containerScanTime, '''') ' +
                   '     , SKU_Qualifier = ISNULL(skuQualifier, '''') ' +
                   '     , SKU_Confirm_Time = ISNULL(skuConfirmationTime, '''') ' +
                   '     , SKU_Scan_Time = ISNULL(skuScanTime, '''') ' +
                   '     , Overhead_Carrier_Code = ISNULL(sortationLoadUnitId,'''') ' +
                   '     , Overhead_Carrier_Scan_Time = ISNULL(sortationLoadUnitScanTime, '''') ' +
                   '     , Induct_Duration_Seconds = ISNULL(sortationLoadUnitWaitTime, '''') ' +
                   '     , Operator_Wait_Duration_Seconds = ISNULL(operatorWaitTime, '''') ' +
                   '     , Overhead_Induction_Exchange_Duration_Seconds = ISNULL(exchangeTime, '''') ' +
                   '     , Empty_Buffer_Filling_Ratio = ISNULL(emptyBufferFillingRatio, '''') ' +
                   '     , Previous_Overhead_Item_Release_Time = ISNULL(previousSortationLoadUnitLeftTime, '''') '+
                   '     , Source_System = ''' + @lSourceSystem +  '''' +
				   '     , Status_Category_Code = '''' '+
                   ' FROM [STAGE].FCT_Overhead_Induction_'+ @lSourceSystem +
                   ' GROUP BY  EventTime, userId, inductionStation, capability, inductMode, ' +
                   '    sortationLoadUnitId, error, skuId, clientId, skuQuantityUnitId, sourceLoadUnitId, containerScanTime, skuConfirmationTime, skuScanTime, sortationLoadUnitScanTime, skuQualifier, ' +
                   '    sortationLoadUnitReleaseTime, sortationLoadUnitWaitTime, operatorWaitTime, exchangeTime, emptyBufferFillingRatio, previousSortationLoadUnitLeftTime ' +
                   ' END ' +
				   
				   ' IF OBJECT_ID(''[STAGE].FCT_Overhead_Induction_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].FCT_Overhead_Induction_'+@lSourceSystem;  
				   
EXEC sp_executesql @lSQLStmt

