SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware';

DECLARE @lSQLStmt NVARCHAR(MAX),
        @lChecks_SQLStmt NVARCHAR(MAX),
        @lDrops_SQLStmt NVARCHAR(MAX);


SELECT @lChecks_SQLStmt =  
' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
' where t.name = ''FCT_Label_Print_Apply_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
' BEGIN 
        CREATE TABLE [STAGE].[FCT_Label_Print_Apply_' + @lSourceSystem + '] (
        	[lpaLineId] [varchar](255)  default '''',
        	[inductScannedCode] [varchar](255)  default '''',
        	[inductStatus] [varchar](255) default '''',
        	[inductScanEventTime] [varchar](255)  default '''',
        	[inductRequestCount] [varchar](255)  default '''',
        	[responseEventTime] [varchar](255)  default '''',
        	[responseCommand] [varchar](255)  default '''',
        	[printDetails] [varchar](255)  default '''',
        	[verificationScannedCode] [varchar](255)  default '''',
        	[verificationEventTime] [varchar](255)  default '''',
        	[verificationStatus] [varchar](255)  default '''',
        	[intendedDestinationId] [varchar](255)  default '''',
        	[trackingId] [varchar](255)  default '''',
        	[Key_ID] [int] IDENTITY(1,1) NOT NULL
        ) ON [PRIMARY]

 END' 


SELECT @lSQLStmt = 
  'SELECT DISTINCT Hour_Quarter_ID =  substring(Replace(Replace(CONVERT(VARCHAR(13), inductScanEventTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), inductScanEventTime,120),15,2) as INT)/15))
                    , Record_Timestamp = inductScanEventTime 
                    , Label_Print_Line_Code = ISNULL(lpaLineId,'''')
                    , Induct_Status_Code =  ISNULL(inductStatus,'''')  
                    , Response_Status_Code = ISNULL(responseCommand,'''') 
                    , Induct_Scanned_Code = ISNULL(inductScannedCode,'''')
                    , Response_Event_Time = ISNULL(responseEventTime,'''') 
                    , Intended_Destination_Code = ISNULL(intendedDestinationId,'''')
                    , Verification_Status_Code = ISNULL(verificationStatus,'''')
                    , Verification_Scanned_Code = ISNULL(verificationScannedCode,'''') 
                    , Verification_Event_Time = ISNULL(verificationEventTime ,'''')
                    , Print_Details = ISNULL(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(printDetails, ''"},{"'',''|''),''"'',''''),''printerId:'',''''),''labelType:'',''''),'','','':''),''[{'',''''),''}]'',''''),'''')  
					, Induct_Request_Count = ISNULL(inductRequestCount,'''')
					, Tracking_Code = ISNULL(trackingId,'''')
					, Subsystem_Code = ''LPA''
					, Subsystem_Category = ''Dematic IQ''
					, Source_System = ''' + @lSourceSystem +'''
    FROM Stage.Fct_Label_Print_Apply_' + @lSourceSystem +''
	
SELECT @lDrops_SQLStmt = ' IF OBJECT_ID(''[STAGE].Fct_Label_Print_Apply_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].Fct_Label_Print_Apply_'+@lSourceSystem;

EXEC sp_executesql @lChecks_SQLStmt
EXEC sp_executesql @lSQLStmt
EXEC sp_executesql @lDrops_SQLStmt
