SET NOCOUNT ON
SET QUOTED_IDENTIFIER ON

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--SET @lSourceSystem = 'DematicSoftware'

DECLARE @strSQL NVARCHAR(MAX)

 SET @strSQL = '
IF NOT (EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
                 WHERE TABLE_SCHEMA = ''STAGE'' 
                 AND  TABLE_NAME = ''DIM_Work_Area_'+ @lSourceSystem +'''))
BEGIN
	CREATE TABLE [STAGE].[DIM_Work_Area_'+ @lSourceSystem +'](
		[id] [varchar](255) NULL,
		[name] [varchar](255) NULL,
		[areaType] [varchar](255) NULL,
		[description] [varchar](255) NULL,
		[operationMode] [varchar](255) NULL,
		[Key_ID] [int] IDENTITY(1,1) NOT NULL
	) ON [PRIMARY]

END
'
EXEC sp_executesql @strSQL

/**********************************************************\
  Build surrogate working tables so the functional code
  of this transform can be viewed with text decorations
\**********************************************************/

SET @strSQL = '
SELECT 
	Work_Area_Code= ISNULL(id ,''''),
	Work_Area_Name= ISNULL([name] ,''''),
	Work_Area_Type= ISNULL(areaType ,''''),
	Work_Area_Description= ISNULL([description] ,''''),
	Work_Area_Operation_Mode= ISNULL(  operationMode ,''''),
	Source_System= ISNULL(''' +@lSourceSystem +''','''')
FROM   [STAGE].[DIM_Work_Area_'+ @lSourceSystem + '] 

'
EXEC sp_executesql @strSQL

SET @strSQL = '
	DROP TABLE IF EXISTS STAGE.DIM_Work_Area_'+ @lSourceSystem +'
'
EXEC sp_executesql @strSQL