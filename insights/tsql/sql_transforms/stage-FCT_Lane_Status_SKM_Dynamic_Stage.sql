SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 
DECLARE @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @Transform_SQLStmt = '
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Lane_Status_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	SELECT DISTINCT
		Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), eventTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), eventTime,120),15,2) as INT)/15))
		,Record_Timestamp = eventTime
		,Logical_Destination_Code = ISNULL(logicalDestinationID, '''')
		,Physical_Destination_Code = ISNULL(physicalDestinationID, '''')
		,Status_Code = ISNULL(event, '''')
		,Reason_Code = CASE WHEN UPPER(reasonCode) = ''NONE'' OR reasonCode IS NULL THEN '''' ELSE reasonCode END
		,Fill_Level_Value = ISNULL(value, '''')
		,Sort_Device_Code = ISNULL(sortDeviceID, '''')
		,Scanner_Code = ISNULL(scannerID, '''')
		,Subsystem_Code = ISNULL(originator, ''SORTER'')
		,Subsystem_Category = ''Dematic IQ''
		,Tenant_Name = ISNULL(tenantName, '''')
		,Source_System = ''' + @lSourceSystem + '''
	FROM [STAGE].[FCT_Lane_Status_' + @lSourceSystem + ']
END
'

SET @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Lane_Status_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[FCT_Lane_Status_'+@lSourceSystem +']'


--PRINT(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt
