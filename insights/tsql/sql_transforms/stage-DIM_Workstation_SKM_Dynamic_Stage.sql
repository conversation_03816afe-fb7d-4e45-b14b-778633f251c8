SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware';
DECLARE @Tables_SQLStmt NVARCHAR(MAX); 
DECLARE @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);


SET @Tables_SQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Workstation_Location_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 workstationPk= '''', 	locationPk= '''', 	workstationlocationtype= '''',
			tenantName= '''', 	dimensionModificationTime= '''', 	autostore_id= '''', 	process_epoch_ns= '''', Key_ID = ''''
		INTO STAGE.DIM_Workstation_Location_' + @lSourceSystem + '
	END;'
EXEC sp_executesql @Tables_SQLStmt

SET @Tables_SQLStmt = '
	IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''DIM_Workstation_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 workstationPk= '''', 	description= '''', 	workstationType= '''', 	terminalId= '''',
			operationMode= '''', 	tenantName= '''', 	dimensionModificationTime= '''', Key_ID = ''''
		INTO STAGE.DIM_Workstation_' + @lSourceSystem + '
	END;'
EXEC sp_executesql @Tables_SQLStmt

SET @Transform_SQLStmt = '
WITH Workstations AS (
	SELECT TOP 1 WITH TIES
		Workstation_Code = workstationPk,
		Workstation_Name = description,
		Workstation_Type = workstationType,
		Terminal_Code = terminalId,
		Operation_Mode = operationMode,
		DCTenantName = tenantName
	FROM [STAGE].[DIM_Workstation_' + @lSourceSystem + ']
	ORDER BY Row_number() over (partition by workstationPk, tenantName order by dimensionModificationTime desc)
)
SELECT DISTINCT
	Workstation_Code = COALESCE(Workstations.Workstation_Code, ''''),
	Workstation_Name,
	Workstation_Type,
	Terminal_Code,
	Operation_Mode,
	Active_Rec_Ind = 1,
	Source_System = ''' + @lSourceSystem +'''
FROM Workstations
'
-- Workstation_Location stage table dropped in Dim_Location transform
SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[DIM_Workstation_' + @lSourceSystem + ']'', ''U'') IS NOT NULL   DROP TABLE [STAGE].[DIM_Workstation_'+@lSourceSystem +']' 
                       -- + ' IF OBJECT_ID(''[STAGE].[DIM_Workstation_Location_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  DROP TABLE [STAGE].[DIM_Workstation_Location_'+@lSourceSystem +']'

--PRINT(@Transform_SQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt