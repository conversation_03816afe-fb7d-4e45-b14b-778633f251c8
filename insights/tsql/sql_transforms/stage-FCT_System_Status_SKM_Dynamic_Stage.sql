SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lQuery_SQLStmt NVARCHAR(MAX),
        @lChecks_SQLStmt NVARCHAR(MAX),
        @lDrops_SQLStmt NVARCHAR(MAX);

SELECT @lChecks_SQLStmt =  
' IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
' where t.name = ''EXTR_FCT_SYSTEM_STATUS_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
' BEGIN 
	
CREATE TABLE [STAGE].[EXTR_FCT_SYSTEM_STATUS_' + @lSourceSystem + '](
	[autostore_id] [varchar](255) NULL,
	[messageID] [varchar](255) NULL,
	[messageSubType] [varchar](255) NULL,
	[messageSequence] [varchar](255) NULL,
	[operationStatus] [varchar](255) NULL,
	[timeStamp] [varchar](255) NULL,
	[process_epoch_ns] [varchar](255) NULL,
	[sensorID] [varchar](255) NULL,
	[deviceID] [varchar](255) NULL,
	[event_type] [varchar](255) NULL,
	[event_source] [varchar](255) NULL,
	[Key_ID] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
			
 END' 

SELECT @lQuery_SQLStmt = '
SELECT
	[Hour_Quarter_ID] = LEFT(REPLACE(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),REPLACE(timeStamp, ''T'','' ''),25),''-'',''''),3,50),'' '',''''),'':'',''''),''T'',''''),8) + LTRIM(STR(DATEPART(minute,REPLACE(timeStamp, ''T'','' ''))/15)),
	[Record_Timestamp] = DATEADD(ns, CAST(RIGHT(process_epoch_ns, 6) AS INT), cast(REPLACE(timeStamp, ''T'','' '') as Datetime2(7))),
	[Message_Sequence] = ISNULL(messageSequence,''''), 
	[Operation_Status] = operationStatus,
	[Module_Code] = COALESCE(autostore_id, deviceID, ''''),
	[Module_Type] = ISNULL(event_source, ''''),
	[Subsystem_Code] = ISNULL(UPPER(event_source), ''''),
	[Subsystem_Category] = ISNULL(UPPER(event_source), ''''),
	[Source_System] = '''+ @lSourceSystem +'''	
FROM [STAGE].[EXTR_FCT_SYSTEM_STATUS_' + @lSourceSystem + ']
';

SELECT @lDrops_SQLStmt = ' IF OBJECT_ID(''[STAGE].[EXTR_FCT_SYSTEM_STATUS_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  DROP TABLE [STAGE].[EXTR_FCT_SYSTEM_STATUS_'+@lSourceSystem +']'

EXEC sp_executesql @lChecks_SQLStmt
EXEC sp_executesql @lQuery_SQLStmt
EXEC sp_executesql @lDrops_SQLStmt
