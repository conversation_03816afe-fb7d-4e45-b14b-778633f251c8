SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''MATRIX_SORTFACT_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
                   ' SELECT DISTINCT Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),matrixSortCompleteTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,matrixSortCompleteTime)/15))  ' +
                   '     , Record_Timestamp = matrixSortCompleteTime ' +
                   '     , Overhead_Loop_Code = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN REVERSE(LEFT(REVERSE(loopLocation), CHARINDEX(''.'',REVERSE(loopLocation)) - 1)) ' +
                   '                           ELSE loopLocation ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_Name = ISNULL(loopLocation, '''') ' +
                   '     , Overhead_Loop_Type = ''MATRIX_SORTER'' ' +
                   '     , Overhead_Loop_Area = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),3) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_Group = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),2) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
                   '     , Overhead_Loop_SubGroup = ISNULL(CASE WHEN CHARINDEX(''.'',loopLocation) > 0 ' +
                   '                           THEN PARSENAME(REVERSE(SUBSTRING(REVERSE(loopLocation),CHARINDEX(''.'',REVERSE(loopLocation)) + 1, LEN(loopLocation))),1) ' +
                   '                           ELSE '''' ' +
                   '                           END,'''') ' +
				   '     , Run_Qty = ISNULL(numberOfItems, '''') ' +
				   '     , Run_Lane_Count = ISNULL(numberOfLanes, '''') ' +
				   '     , Run_Unit_Assignment_Date_Time = ISNULL(creationTime, '''') ' +
				   '     , Run_PreSort_Completion_Date_Time = ISNULL(completionTime, '''') ' +
				   '     , Run_PreSort_Release_Date_Time = ISNULL(releaseTime, '''') ' +
				   '     , Run_PreSort_Empty_Date_Time = ISNULL(emptyTime, '''') ' +
				   '     , Run_Sort_Start_Date_Time = ISNULL(sortationActiveTime, '''') ' +
				   '     , Overhead_Run = ISNULL(runId, '''') ' +
				   '     , Overhead_Run_Width_Millimeters = ISNULL(totalWidth, '''') ' +
				   '     , Run_Sort_Duration_Seconds = ISNULL(sortationTime, '''') ' +   
				   '     , Subsystem_Category = ''DEMATIC IQ'' ' +
				   '     , Subsystem_Code = ''OVERHEAD_SORT'' ' +
                   '     , Source_System = ''' + @lSourceSystem +  '''' +
                   ' FROM [STAGE].MATRIX_SORTFACT_'+ @lSourceSystem +
                   ' ORDER BY matrixSortCompleteTime, Overhead_Loop_Code, Subsystem_Code, Subsystem_Category ' +
                   ' END ' +  
				   ' IF OBJECT_ID(''[STAGE].MATRIX_SORTFACT_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].MATRIX_SORTFACT_'+@lSourceSystem;  
				   
EXEC sp_executesql @lSQLStmt

