SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSRC VARCHAR(50) = '$(Source_System)'; 
--DECLARE @lSRC VARCHAR(50) = 'DematicSoftware'; 
DECLARE @lSourceSystem VARCHAR(50) = @lSRC 

DECLARE @lTime DATETIME2(7) = CAST(CURRENT_TIMESTAMP as DATETIME2(7)); 

DECLARE @lChecks_SQLStmt VARCHAR(MAX),
		@lSQLStmt0 VARCHAR(MAX),
		@lSQLStmt1 VARCHAR(MAX),
		@lSQLStmt2 VARCHAR(MAX),
		@lSQLStmt3 VARCHAR(MAX),
		@lSQLStmt4 VARCHAR(MAX),
		@lSQLDropsStmt NVARCHAR(MAX),
		@lFinalSQL NVARCHAR(MAX);
		
SET @lChecks_SQLStmt = '
IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Picking_OrderLine_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 pickerId='''', orderPk='''', pickOrderPk='''', client='''', sourceLoadUnit='''', loadUnitId='''', locationPk='''',
		containerPk='''', skuPk='''', zonePk='''', inductionZonePk='''', terminalId='''', quantityPicked='''',
		quantityTarget='''', pickPk='''', hostLineId='''', pickBatchName='''', quantityUnitPk='''', sourceLoadUnitType='''',
		loadUnitType='''', cartPk='''', cartId='''', technology='''', processType='''', confirmationCode='''', workflowPk='''',
		confirmationReason='''', trainId='''', workstationId='''', quantityRemaining='''', productCode='''', tenantName='''',
		eventTime='''', Record_Timestamp_Offset='''',  userPk=''''
	INTO STAGE.FCT_Picking_OrderLine_' + @lSourceSystem + '
END;'
EXEC(@lChecks_SQLStmt)

SET @lChecks_SQLStmt = '
IF NOT EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Pick_Order_' + @lSourceSystem + ''' and s.name = ''Stage'') BEGIN
		SELECT TOP 0 pickOrderPk='''', pickOrderType='''', orderCategory='''', wavePk='''', orderPk='''', putWallPk='''', locationPk='''', 
			zonePk='''', groupPk='''', tenantName='''', event='''', eventTime='''', userName='''', Record_Timestamp_Offset='''', userPk='''',
			fulfillmentHandlingType='''', totalLineCt='''', totalQtyCt='''', vasRequirement='''', urgency='''', packingRequirement='''', 
			latestStagingTime='''', orderChannel=''''
		INTO STAGE.FCT_Pick_Order_' + @lSourceSystem + '
END;'
EXEC(@lChecks_SQLStmt);

SET @lSQLStmt0 = '
DECLARE @lTime2 VARCHAR(50) = ''' + CAST(@lTime AS VARCHAR(50)) + '''
;WITH RAW_PICK_ORDER_EVENTS AS (
	SELECT DISTINCT
		userName = ISNULL(userPk,userName)
		,locationPk
		,zonePk
		,groupPk
		,eventTime
		,pickOrderPk
		,tenantName
		,wavePk
		,orderPk 
		,putWallPk
		,orderCategory
		,pickOrderType = CASE 
							WHEN UPPER(pickOrderType) in (''INTERNAL'', ''CUSTOMER'', ''STANDARD'') THEN ''Standard''
							WHEN pickOrderType IS NULL THEN ''Standard''
							ELSE pickOrderType
						END
		,actionType = CASE WHEN UPPER(event) = ''COMPLETED'' THEN ''complete'' else event END
	FROM STAGE.FCT_Pick_Order_' + @lSourceSystem + ' fpo
	WHERE pickOrderPk IS NOT NULL
)
-- mapped and deduplicated events
, MAPPED_PICK_ORDER_EVENTS AS (
	SELECT 
		fpo.pickOrderPk,
		tenantName,
		EndingEvent,
		pickOrderType,
		orderCategory = NULL,
		wavePk,
		orderPk,
		putWallPk,
		fpo.eventTime,
		(CASE WHEN AnaltyicsConformedState = ''Order_Created'' THEN fpo.eventTime END) AS Order_Created_Date_Time,
		(CASE WHEN AnaltyicsConformedState = ''Order_Released'' THEN fpo.eventTime END) AS Order_Released_Date_Time,
		(CASE WHEN AnaltyicsConformedState = ''Order_Activated'' THEN fpo.eventTime END) AS Order_Activated_Date_Time,
		(CASE WHEN AnaltyicsConformedState = ''Order_Assigned'' THEN fpo.eventTime END) AS Order_Assigned_Date_Time,
		(CASE WHEN AnaltyicsConformedState = ''Order_Assigned'' AND locationPk IS NOT NULL THEN locationPk END) AS Location_Order_Assigned,
		(CASE WHEN AnaltyicsConformedState = ''Order_Put_Lines_Complete'' THEN fpo.eventTime END) AS Order_Put_Lines_Complete_Date_Time,
		(CASE WHEN AnaltyicsConformedState = ''Order_Lines_Complete'' THEN fpo.eventTime END) AS Order_Lines_Complete_Date_Time,
		(CASE WHEN AnaltyicsConformedState = ''Order_Complete'' THEN fpo.eventTime END) AS Order_Complete_Date_Time,
		(CASE WHEN AnaltyicsConformedState = ''Order_Pack_Assigned'' THEN fpo.eventTime END) AS Order_Pack_Assigned_Date_Time,
		(CASE WHEN AnaltyicsConformedState = ''Order_Put_Wall_Pack_Complete'' THEN fpo.eventTime END) AS Order_Put_Wall_Pack_Complete_Date_Time,
		(CASE WHEN AnaltyicsConformedState = ''Order_Put_Wall_Pack_Complete'' AND locationPk IS NOT NULL THEN locationPk END) AS Location_Put_Wall_Pack,
		(CASE WHEN AnaltyicsConformedState = ''Order_Put_Wall_Pack_Complete'' AND zonePk IS NOT NULL THEN zonePk END) AS Zone_Put_Wall_Pack,
		(CASE WHEN AnaltyicsConformedState = ''Order_Put_Wall_Pack_Complete'' AND groupPk IS NOT NULL THEN groupPk END) AS Group_Put_Wall_Pack,
		(CASE WHEN AnaltyicsConformedState = ''Order_Put_Wall_Pack_Complete'' AND userName IS NOT NULL THEN userName END) AS UserName_Put_Wall_Pack,	
		(CASE WHEN AnaltyicsConformedState = ''Order_Pack_Complete'' THEN fpo.eventTime END) AS Order_Pack_Complete_Date_Time,
		(CASE WHEN AnaltyicsConformedState = ''Order_Pack_Complete'' AND locationPk IS NOT NULL THEN locationPk END) AS Location_Pack,
		(CASE WHEN AnaltyicsConformedState = ''Order_Pack_Complete'' AND zonePk IS NOT NULL THEN zonePk END) AS Zone_Pack,
		(CASE WHEN AnaltyicsConformedState = ''Order_Pack_Complete'' AND groupPk IS NOT NULL THEN groupPk END) AS Group_Pack,
		(CASE WHEN AnaltyicsConformedState = ''Order_Pack_Complete'' AND userName IS NOT NULL THEN userName END) AS UserName_Pack,
		(CASE WHEN AnaltyicsConformedState = ''Order_Cancelled'' THEN fpo.eventTime END) AS Order_Cancelled_Date_Time
	FROM RAW_PICK_ORDER_EVENTS fpo
		INNER JOIN (SELECT pickOrderPk, actionType, eventTime=max(eventTime) FROM RAW_PICK_ORDER_EVENTS GROUP BY pickOrderPk, actionType) as t ON fpo.pickOrderPk = t.pickOrderPk AND fpo.actionType = t.actionType
		INNER JOIN OA.DIM_Pick_Order_Event_Map poe ON fpo.pickOrderType = poe.OrderType  AND fpo.actionType = poe.SourceState
)
, AGG_PICK_ORDER_EVENTS AS (
SELECT * FROM (
	SELECT
		-- take the last value (first value if ordered by eventTime in descending order) and ignore nulls and also take the maximum datetime for the other columns
		row_number() over (partition by pickOrderPk order by eventTime desc) as rn
		,pickOrderPk
		,wavePk = first_value(wavePk) OVER(partition by pickOrderPk order by case when wavePk is not null then 1 else 2 end, eventTime desc)
		,putWallPk = first_value(putWallPk) OVER(partition by pickOrderPk order by case when putWallPk is not null then 1 else 2 end, eventTime desc)
		,Location_Order_Assigned = first_value(Location_Order_Assigned) OVER(partition by pickOrderPk order by case when Location_Order_Assigned is not null then 1 else 2 end, eventTime desc) --locationPk_OrderAssigned
		,Location_Put_Wall_Pack = first_value(Location_Put_Wall_Pack) OVER(partition by pickOrderPk order by case when Location_Put_Wall_Pack is not null then 1 else 2 end, eventTime desc)--locationPk_PutWallPack
		,Zone_Put_Wall_Pack = first_value(Zone_Put_Wall_Pack) OVER(partition by pickOrderPk order by case when Zone_Put_Wall_Pack is not null then 1 else 2 end, eventTime desc) --zonePk_PutWallPack
		,Group_Put_Wall_Pack = first_value(Group_Put_Wall_Pack) OVER(partition by pickOrderPk order by case when Group_Put_Wall_Pack is not null then 1 else 2 end, eventTime desc)  --groupPk_PutWallPack
		,Location_Pack = first_value(Location_Pack) OVER(partition by pickOrderPk order by case when Location_Pack is not null then 1 else 2 end, eventTime desc)--locationPk_Pack
		,Zone_Pack = first_value(Zone_Pack) OVER(partition by pickOrderPk order by case when Zone_Pack is not null then 1 else 2 end, eventTime desc)  --zonePk_Pack
		,UserName_Put_Wall_Pack = first_value(UserName_Put_Wall_Pack) OVER(partition by pickOrderPk order by case when UserName_Put_Wall_Pack is not null then 1 else 2 end, eventTime desc)--userName_PutWallPack
		,UserName_Pack = first_value(UserName_Pack) OVER(partition by pickOrderPk order by case when UserName_Pack is not null then 1 else 2 end, eventTime desc) 
		,tenantName = first_value(tenantName) OVER(partition by pickOrderPk order by case when tenantName is not null then 1 else 2 end, eventTime desc) 
		,pickOrderType = first_value(pickOrderType) OVER(partition by pickOrderPk order by case when pickOrderType is not null then 1 else 2 end, eventTime desc)
		,orderCategory 
		,EndingEvent = first_value(EndingEvent) OVER(partition by pickOrderPk order by case when EndingEvent is not null then 1 else 2 end, eventTime desc)
		,orderPk = first_value(orderPk) OVER(partition by pickOrderPk order by case when orderPk is not null then 1 else 2 end, eventTime desc) 
		,Group_Pack = first_value(Group_Pack) OVER(partition by pickOrderPk order by case when Group_Pack is not null then 1 else 2 end, eventTime desc) --groupPk_Pack
	FROM MAPPED_PICK_ORDER_EVENTS ) mpo
	INNER JOIN (
	SELECT 
		pickOrder = pickOrderPk
		,Order_Activated_Date_Time = max(Order_Activated_Date_Time)	
		,Order_Complete_Date_Time = max(Order_Complete_Date_Time)
		,Order_Assigned_Date_Time = max(Order_Assigned_Date_Time)
		,Order_Released_Date_Time = max(Order_Released_Date_Time)
		,Order_Lines_Complete_Date_Time = max(Order_Lines_Complete_Date_Time)
		,Order_Put_Wall_Pack_Complete_Date_Time = max(Order_Put_Wall_Pack_Complete_Date_Time)
		,Order_Put_Lines_Complete_Date_Time = max(Order_Put_Lines_Complete_Date_Time)
		,Order_Pack_Complete_Date_Time = max(Order_Pack_Complete_Date_Time)
		,Order_Pack_Assigned_Date_Time = max(Order_Pack_Assigned_Date_Time)
		,Order_Created_Date_Time = max(Order_Created_Date_Time)
		,Order_Cancelled_Date_Time = max(Order_Cancelled_Date_Time)
	 FROM MAPPED_PICK_ORDER_EVENTS GROUP BY pickOrderPk
	) t ON t.pickOrder = mpo.pickOrderPk
	where rn = 1
)
, RAW_PICK_ORDERLINE_EVENTS AS (
	SELECT DISTINCT
		 pickerId = COALESCE(userPk,pickerId,'''')
		, orderPk
		, pickOrderPk
		, locationPk
		, zonePk
		, processType
		, confirmationCode
		, eventTime
	FROM stage.FCT_Picking_OrderLine_' + @lSourceSystem + '
	WHERE confirmationCode IS NOT NULL AND UPPER(confirmationCode) != ''DELETED'' AND pickOrderPk IS NOT NULL
)
'

SET @lSQLStmt1 = '
, MAX_ORDERLINE_RP AS (SELECT * FROM (
	SELECT 
		rp = row_number() over (partition by rpo.pickOrderPk order by mo.eventTime ASC)
		, MaxOrderLine_pickerId = rpo.pickerId
		, MaxOrderLine_pickOrderPk = rpo.pickOrderPk
		, MaxOrderLine_locationPk = rpo.locationPk
		, MaxOrderLine_zonePk = rpo.zonePk
		, MaxOrderLine_eventTime = mo.eventTime
		, confirmationCode = rpo.confirmationCode
	 FROM RAW_PICK_ORDERLINE_EVENTS rpo INNER JOIN (SELECT pickOrderPk, eventTime=MAX(eventTime) FROM RAW_PICK_ORDERLINE_EVENTS GROUP BY pickOrderPk) as mo
		ON rpo.pickOrderPk = mo.pickOrderPk AND rpo.eventTime = mo.eventTime)  as t where rp=1
)
, MAX_PACK_LINE_RP AS (SELECT * FROM (
	SELECT
		rp = row_number() over (partition by rpo.pickOrderPk order by rpo.eventTime ASC)
		, pickerId_PackLine = rpo.pickerId
		, pickOrderPk_PackLine = rpo.pickOrderPk
		, locationPk_PackLine = rpo.locationPk
		, eventTime_PackLine = rpo.eventTime
		, zonePk_PackLine = rpo.zonePk
		, confirmationCode_PackLine = rpo.confirmationCode
	FROM RAW_PICK_ORDERLINE_EVENTS rpo INNER JOIN (SELECT pickOrderPk, eventTime=MAX(eventTime) FROM RAW_PICK_ORDERLINE_EVENTS WHERE UPPER(processType) IN (''PUTWALL_PACK'',''PACK'',''PACKING'') GROUP BY pickOrderPk) as mo
	ON rpo.pickOrderPk = mo.pickOrderPk AND rpo.eventTime = mo.eventTime) as t where rp=1)
, MAX_PACK_LINES AS (
	SELECT
		*
	FROM (SELECT pickOrderPk = ISNULL(pickOrderPk_PackLine,MaxOrderLine_pickOrderPk), pickOrderPk_PackLine,MaxOrderLine_pickOrderPk, MaxOrderLine_pickerId, MaxOrderLine_locationPk, MaxOrderLine_zonePk, MaxOrderLine_eventTime, pickerId_PackLine, locationPk_PackLine, eventTime_PackLine, zonePk_PackLine
 FROM MAX_PACK_LINE_RP mpl FULL OUTER JOIN MAX_ORDERLINE_RP mol ON mpl.pickOrderPk_PackLine = mol.MaxOrderLine_pickOrderPk) as tmp
)
, EVENTS_WITH_JOINED_MAX_PACK_LINES AS (
	SELECT
		Pick_Order_Code = ISNULL(ppo.pickOrderPk, mpl.pickOrderPk)
		, ppo.tenantName
		, ppo.pickOrderType
		, ppo.orderCategory
		, ppo.EndingEvent
		, ppo.wavePk
		, ppo.orderPk
		, ppo.Order_Assigned_Date_Time
		, ppo.Order_Complete_Date_Time
		, ppo.Order_Created_Date_Time
		, ppo.Order_Lines_Complete_Date_Time
		, ppo.Order_Pack_Assigned_Date_Time
		, ppo.Order_Pack_Complete_Date_Time
		, ppo.Order_Put_Lines_Complete_Date_Time
		, ppo.Order_Put_Wall_Pack_Complete_Date_Time
		, ppo.Order_Released_Date_Time
		, ppo.Location_Order_Assigned --locationPk_OrderAssigned
		, ppo.Location_Put_Wall_Pack --locationPk_PutWallPack
		, ppo.Zone_Put_Wall_Pack --zonePk_PutWallPack
		, ppo.Group_Put_Wall_Pack --groupPk_PutWallPack
		, ppo.UserName_Put_Wall_Pack --userName_PutWallPack
		, ppo.Location_Pack --locationPk_Pack
		, ppo.Zone_Pack --zonePk_Pack
		, ppo.Group_Pack --groupPk_Pack
		, ppo.userName_Pack
		, ppo.putWallPk
		, MaxOrderLine_pickerId
		, MaxOrderLine_pickOrderPk
		, MaxOrderLine_locationPk
		, MaxOrderLine_zonePk
		, MaxOrderLine_eventTime
		, pickerId_PackLine
		, pickOrderPk_PackLine
		, locationPk_PackLine
		, eventTime_PackLine
		, zonePk_PackLine
		, Order_Activated_Date_Time
		, Order_Cancelled_Date_Time
	FROM AGG_PICK_ORDER_EVENTS ppo FULL OUTER JOIN MAX_PACK_LINES mpl ON mpl.pickOrderPk = ppo.pickOrderPk
)
, ALL_PICK_ORDER_EVENTS AS (
	SELECT
		EVENTS_WITH_JOINED_MAX_PACK_LINES.*
		,PICK_ORDER_COMPLETE_DATA.*
	FROM EVENTS_WITH_JOINED_MAX_PACK_LINES
	LEFT OUTER JOIN
	(SELECT
		Order_Released_Date_Time_EXISTS = Order_Released_Date_Time,
		Order_Assigned_Date_Time_EXISTS = Order_Assigned_Date_Time,
		Order_Assigned_Location_Code_EXISTS = DimLocationAssigned.Location_Code,
		Order_Put_Start_Date_Time_EXISTS = Order_Put_Start_Date_Time,
		Order_Pack_Start_Date_Time_EXISTS = Order_Pack_Start_Date_Time,
		Order_Lines_Complete_Date_Time_EXISTS = Order_Lines_Complete_Date_Time,
		Order_Pack_Complete_Date_Time_EXISTS = Order_Pack_Complete_Date_Time,
		Location_Code_EXISTS = DIM_Location.Location_Code,
		Operator_Code_EXISTS = Dim_Operator.Operator_Code,
		Zone_Code_EXISTS = Dim_Zone.Zone_Code,
		Pick_Order_Code_EXISTS = Pick_Order_Code,
		Order_Created_Date_Time_EXISTS = Record_Timestamp,
		Order_Put_Lines_Complete_Date_Time_EXISTS = Order_Put_Lines_Complete_Date_Time,
		Order_Put_Wall_Pack_Complete_Date_Time_EXISTS = Order_Put_Wall_Pack_Complete_Date_Time,
		Order_Put_Wall_Pack_Operator_Code_EXISTS = PackOperator.Operator_Code,
		Order_Put_Wall_Pack_Location_Code_EXISTS = DimLocationPack.Location_Code,
		Order_Put_Wall_Pack_Zone_Code_EXISTS = DimZonePack.Zone_Code,
		Order_Complete_Date_Time_EXISTS = Order_Complete_Date_Time,
		Wave_Code_EXISTS = Wave_Code,
		Pick_Order_Type_EXISTS = Pick_Order_Type,
		Pick_Order_Category_EXISTS = Pick_Order_Category,
		Order_Cancelled_Date_Time_EXISTS = Order_Cancelled_Date_Time
	FROM oa.FCT_Pick_Order_Complete FCT_Pick_Order_Complete WITH(NOLOCK)
	JOIN Oa.DIM_Operator Dim_Operator WITH(NOLOCK) ON FCT_Pick_Order_Complete.Operator_ID = Dim_Operator.Operator_ID
	JOIN OA.DIM_Operator PackOperator WITH(NOLOCK) ON FCT_Pick_Order_Complete.Order_Put_Wall_Pack_Operator_ID = PackOperator.Operator_ID
	JOIN Oa.DIM_Location Dim_Location WITH(NOLOCK) ON FCT_Pick_Order_Complete.Location_ID = Dim_Location.Location_ID
	JOIN Oa.DIM_Pick_Order Dim_Pick_Order WITH(NOLOCK) ON FCT_Pick_Order_Complete.Pick_Order_ID = Dim_Pick_Order.Pick_Order_ID
	JOIN Oa.DIM_Location DimLocationAssigned WITH(NOLOCK) on FCT_Pick_Order_Complete.Order_Assigned_Location_ID = DimLocationAssigned.Location_ID
	JOIN Oa.DIM_Location DimLocationPack WITH(NOLOCK) on FCT_Pick_Order_Complete.Order_Put_Wall_Pack_Location_ID = DimLocationPack.Location_ID
	JOIN Oa.DIM_Zone ON  FCT_Pick_Order_Complete.Zone_ID = Dim_Zone.Zone_ID AND DIM_Zone.Source_System = ''' + @lSourceSystem + '''
	JOIN OA.DIM_Zone DimZonePack WITH(NOLOCK) ON FCT_Pick_Order_Complete.Order_Put_Wall_Pack_Zone_ID = DimZonePack.Zone_ID 
	JOIN OA.DIM_Wave DimWave WITH (NOLOCK) ON DimWave.Wave_ID = FCT_Pick_Order_Complete.Wave_ID
	WHERE FCT_Pick_Order_Complete.Record_Timestamp >= (SELECT DATEADD(dd,-10,CAST(MIN(eventTime) AS DATETIME2(7))) FROM Stage.FCT_Pick_Order_' + @lSourceSystem + ') 
	) AS PICK_ORDER_COMPLETE_DATA ON EVENTS_WITH_JOINED_MAX_PACK_LINES.Pick_Order_Code = PICK_ORDER_COMPLETE_DATA.Pick_Order_Code_EXISTS
)
'

SET @lSQLStmt2 = '
, ORDER_TYPE_CALC_EVENTS AS (
	-- Standard Order
	SELECT
		Pick_Order_Code,tenantName,pickOrderType,orderCategory,EndingEvent,wavePk,orderPk,Order_Assigned_Date_Time
		,Order_Created_Date_Time,Order_Lines_Complete_Date_Time,Order_Pack_Assigned_Date_Time
		,Order_Pack_Complete_Date_Time,Order_Put_Lines_Complete_Date_Time,Location_Code_EXISTS,Operator_Code_EXISTS
		,Order_Released_Date_Time,Location_Order_Assigned,Location_Put_Wall_Pack,Zone_Put_Wall_Pack,Group_Put_Wall_Pack
		,UserName_Put_Wall_Pack,Location_Pack,Zone_Pack,Group_Pack,userName_Pack,putWallPk,MaxOrderLine_pickerId
		,MaxOrderLine_pickOrderPk,MaxOrderLine_locationPk,MaxOrderLine_zonePk,MaxOrderLine_eventTime,pickerId_PackLine
		,pickOrderPk_PackLine,locationPk_PackLine,eventTime_PackLine,zonePk_PackLine,Order_Activated_Date_Time
		,Order_Cancelled_Date_Time,Order_Released_Date_Time_EXISTS,Order_Assigned_Date_Time_EXISTS,Order_Assigned_Location_Code_EXISTS
		,Order_Put_Start_Date_Time_EXISTS,Order_Pack_Start_Date_Time_EXISTS,Order_Lines_Complete_Date_Time_EXISTS,Order_Pack_Complete_Date_Time_EXISTS
		,Zone_Code_EXISTS,Pick_Order_Code_EXISTS,Order_Created_Date_Time_EXISTS,Order_Put_Lines_Complete_Date_Time_EXISTS,Order_Put_Wall_Pack_Complete_Date_Time_EXISTS
		,Order_Put_Wall_Pack_Operator_Code_EXISTS,Order_Put_Wall_Pack_Location_Code_EXISTS,Order_Put_Wall_Pack_Zone_Code_EXISTS,Order_Complete_Date_Time_EXISTS
		,Wave_Code_EXISTS,Pick_Order_Type_EXISTS,Pick_Order_Category_EXISTS,Order_Cancelled_Date_Time_EXISTS,

		Order_Assigned_Location_Code = Location_Order_Assigned,
		Order_Put_Wall_Pack_Operator_Code = NULL,
		Order_Put_Wall_Pack_Location_Code = NULL,
		Order_Put_Wall_Pack_Zone_Code = NULL,
		Operator_Code = CASE WHEN Order_Complete_Date_Time IS NOT NULL THEN MaxOrderLine_pickerId ELSE NULL END,
		Location_Code = CASE WHEN Order_Complete_Date_Time IS NOT NULL THEN MaxOrderLine_locationPk ELSE NULL END,
		Zone_Code = CASE WHEN Order_Complete_Date_Time IS NOT NULL THEN MaxOrderLine_zonePk ELSE NULL END,
		Order_Complete_Date_Time,
		Order_Put_Wall_Pack_Complete_Date_Time
	FROM ALL_PICK_ORDER_EVENTS
	WHERE UPPER(pickOrderType) = ''STANDARD'' OR UPPER(pickOrderType) = ''PUTAWAY''

	UNION ALL

	--Custom Put Wall
	SELECT
		Pick_Order_Code,tenantName,pickOrderType,orderCategory,EndingEvent,wavePk,orderPk,Order_Assigned_Date_Time
		,Order_Created_Date_Time,Order_Lines_Complete_Date_Time,Order_Pack_Assigned_Date_Time
		,Order_Pack_Complete_Date_Time,Order_Put_Lines_Complete_Date_Time, Location_Code_EXISTS,Operator_Code_EXISTS
		,Order_Released_Date_Time,Location_Order_Assigned,Location_Put_Wall_Pack,Zone_Put_Wall_Pack,Group_Put_Wall_Pack
		,UserName_Put_Wall_Pack,Location_Pack,Zone_Pack,Group_Pack,userName_Pack,putWallPk,MaxOrderLine_pickerId
		,MaxOrderLine_pickOrderPk,MaxOrderLine_locationPk,MaxOrderLine_zonePk,MaxOrderLine_eventTime,pickerId_PackLine
		,pickOrderPk_PackLine,locationPk_PackLine,eventTime_PackLine,zonePk_PackLine,Order_Activated_Date_Time
		,Order_Cancelled_Date_Time,Order_Released_Date_Time_EXISTS,Order_Assigned_Date_Time_EXISTS,Order_Assigned_Location_Code_EXISTS
		,Order_Put_Start_Date_Time_EXISTS,Order_Pack_Start_Date_Time_EXISTS,Order_Lines_Complete_Date_Time_EXISTS,Order_Pack_Complete_Date_Time_EXISTS
		,Zone_Code_EXISTS,Pick_Order_Code_EXISTS,Order_Created_Date_Time_EXISTS,Order_Put_Lines_Complete_Date_Time_EXISTS,Order_Put_Wall_Pack_Complete_Date_Time_EXISTS
		,Order_Put_Wall_Pack_Operator_Code_EXISTS,Order_Put_Wall_Pack_Location_Code_EXISTS,Order_Put_Wall_Pack_Zone_Code_EXISTS,Order_Complete_Date_Time_EXISTS
		,Wave_Code_EXISTS,Pick_Order_Type_EXISTS,Pick_Order_Category_EXISTS,Order_Cancelled_Date_Time_EXISTS,
		Order_Assigned_Location_Code = Location_Order_Assigned,
		Order_Put_Wall_Pack_Operator_Code = pickerId_PackLine,
		Order_Put_Wall_Pack_Location_Code = locationPk_PackLine,
		Order_Put_Wall_Pack_Zone_Code = zonePk_PackLine,
		Operator_Code = pickerId_PackLine,
		Location_Code = locationPk_PackLine,
		Zone_Code = zonePk_PackLine,
		Order_Complete_Date_Time = eventTime_PackLine,
		Order_Put_Wall_Pack_Complete_Date_Time = eventTime_PackLine
	FROM ALL_PICK_ORDER_EVENTS
	WHERE UPPER(pickOrderType) = ''CUSTOMPW''
	
	UNION ALL
	-- Custom Put Wall
	SELECT
		Pick_Order_Code,tenantName,pickOrderType,orderCategory,EndingEvent,wavePk,orderPk,Order_Assigned_Date_Time
		,Order_Created_Date_Time,Order_Lines_Complete_Date_Time,Order_Pack_Assigned_Date_Time
		,Order_Pack_Complete_Date_Time,Order_Put_Lines_Complete_Date_Time,Location_Code_EXISTS,Operator_Code_EXISTS
		,Order_Released_Date_Time,Location_Order_Assigned,Location_Put_Wall_Pack,Zone_Put_Wall_Pack,Group_Put_Wall_Pack
		,UserName_Put_Wall_Pack,Location_Pack,Zone_Pack,Group_Pack,userName_Pack,putWallPk,MaxOrderLine_pickerId
		,MaxOrderLine_pickOrderPk,MaxOrderLine_locationPk,MaxOrderLine_zonePk,MaxOrderLine_eventTime,pickerId_PackLine
		,pickOrderPk_PackLine,locationPk_PackLine,eventTime_PackLine,zonePk_PackLine,Order_Activated_Date_Time
		,Order_Cancelled_Date_Time,Order_Released_Date_Time_EXISTS,Order_Assigned_Date_Time_EXISTS,Order_Assigned_Location_Code_EXISTS
		,Order_Put_Start_Date_Time_EXISTS,Order_Pack_Start_Date_Time_EXISTS,Order_Lines_Complete_Date_Time_EXISTS,Order_Pack_Complete_Date_Time_EXISTS
		,Zone_Code_EXISTS,Pick_Order_Code_EXISTS,Order_Created_Date_Time_EXISTS,Order_Put_Lines_Complete_Date_Time_EXISTS,Order_Put_Wall_Pack_Complete_Date_Time_EXISTS
		,Order_Put_Wall_Pack_Operator_Code_EXISTS,Order_Put_Wall_Pack_Location_Code_EXISTS,Order_Put_Wall_Pack_Zone_Code_EXISTS,Order_Complete_Date_Time_EXISTS
		,Wave_Code_EXISTS,Pick_Order_Type_EXISTS,Pick_Order_Category_EXISTS,Order_Cancelled_Date_Time_EXISTS,

		Order_Assigned_Location_Code = Location_Order_Assigned,
		Order_Put_Wall_Pack_Operator_Code = pickerId_PackLine,
		Order_Put_Wall_Pack_Location_Code = locationPk_PackLine,
		Order_Put_Wall_Pack_Zone_Code = zonePk_PackLine,
		Operator_Code = pickerId_PackLine,
		Location_Code = locationPk_PackLine,
		Zone_Code = zonePk_PackLine,
		Order_Complete_Date_Time,
		Order_Put_Wall_Pack_Complete_Date_Time
	FROM ALL_PICK_ORDER_EVENTS
	WHERE UPPER(pickOrderType) = ''STANDARDPW''
	
	UNION ALL
'

SET @lSQLStmt3 = '
	-- Replenishment
	SELECT
		Pick_Order_Code,tenantName,pickOrderType,orderCategory,EndingEvent,wavePk,orderPk,Order_Assigned_Date_Time
		,Order_Created_Date_Time,Order_Lines_Complete_Date_Time,Order_Pack_Assigned_Date_Time
		,Order_Pack_Complete_Date_Time,Order_Put_Lines_Complete_Date_Time,Location_Code_EXISTS,Operator_Code_EXISTS
		,Order_Released_Date_Time,Location_Order_Assigned,Location_Put_Wall_Pack,Zone_Put_Wall_Pack,Group_Put_Wall_Pack
		,UserName_Put_Wall_Pack,Location_Pack,Zone_Pack,Group_Pack,userName_Pack,putWallPk,MaxOrderLine_pickerId
		,MaxOrderLine_pickOrderPk,MaxOrderLine_locationPk,MaxOrderLine_zonePk,MaxOrderLine_eventTime,pickerId_PackLine
		,pickOrderPk_PackLine,locationPk_PackLine,eventTime_PackLine,zonePk_PackLine,Order_Activated_Date_Time
		,Order_Cancelled_Date_Time,Order_Released_Date_Time_EXISTS,Order_Assigned_Date_Time_EXISTS,Order_Assigned_Location_Code_EXISTS
		,Order_Put_Start_Date_Time_EXISTS,Order_Pack_Start_Date_Time_EXISTS,Order_Lines_Complete_Date_Time_EXISTS,Order_Pack_Complete_Date_Time_EXISTS
		,Zone_Code_EXISTS,Pick_Order_Code_EXISTS,Order_Created_Date_Time_EXISTS,Order_Put_Lines_Complete_Date_Time_EXISTS,Order_Put_Wall_Pack_Complete_Date_Time_EXISTS
		,Order_Put_Wall_Pack_Operator_Code_EXISTS,Order_Put_Wall_Pack_Location_Code_EXISTS,Order_Put_Wall_Pack_Zone_Code_EXISTS,Order_Complete_Date_Time_EXISTS
		,Wave_Code_EXISTS,Pick_Order_Type_EXISTS,Pick_Order_Category_EXISTS,Order_Cancelled_Date_Time_EXISTS,

		Order_Assigned_Location_Code = Location_Order_Assigned,
		Order_Put_Wall_Pack_Operator_Code = NULL,
		Order_Put_Wall_Pack_Location_Code = NULL,
		Order_Put_Wall_Pack_Zone_Code = NULL,
		Operator_Code = CASE WHEN Order_Complete_Date_Time IS NOT NULL THEN MaxOrderLine_pickerId ELSE NULL END,
		Location_Code = CASE WHEN Order_Complete_Date_Time IS NOT NULL THEN MaxOrderLine_locationPk ELSE NULL END,
		Zone_Code = CASE WHEN Order_Complete_Date_Time IS NOT NULL THEN MaxOrderLine_zonePk ELSE NULL END,
		Order_Complete_Date_Time,
		Order_Put_Wall_Pack_Complete_Date_Time

	FROM ALL_PICK_ORDER_EVENTS
	WHERE UPPER(pickOrderType) = ''REPLENISHMENT''
	
	UNION ALL
	-- Shipping
	SELECT
		Pick_Order_Code,tenantName,pickOrderType,orderCategory,EndingEvent,wavePk,orderPk,Order_Assigned_Date_Time
		,Order_Created_Date_Time,Order_Lines_Complete_Date_Time,Order_Pack_Assigned_Date_Time
		,Order_Pack_Complete_Date_Time,Order_Put_Lines_Complete_Date_Time,Location_Code_EXISTS,Operator_Code_EXISTS
		,Order_Released_Date_Time,Location_Order_Assigned,Location_Put_Wall_Pack,Zone_Put_Wall_Pack,Group_Put_Wall_Pack
		,UserName_Put_Wall_Pack,Location_Pack,Zone_Pack,Group_Pack,userName_Pack,putWallPk,MaxOrderLine_pickerId
		,MaxOrderLine_pickOrderPk,MaxOrderLine_locationPk,MaxOrderLine_zonePk,MaxOrderLine_eventTime,pickerId_PackLine
		,pickOrderPk_PackLine,locationPk_PackLine,eventTime_PackLine,zonePk_PackLine,Order_Activated_Date_Time
		,Order_Cancelled_Date_Time,Order_Released_Date_Time_EXISTS,Order_Assigned_Date_Time_EXISTS,Order_Assigned_Location_Code_EXISTS
		,Order_Put_Start_Date_Time_EXISTS,Order_Pack_Start_Date_Time_EXISTS,Order_Lines_Complete_Date_Time_EXISTS,Order_Pack_Complete_Date_Time_EXISTS
		,Zone_Code_EXISTS,Pick_Order_Code_EXISTS,Order_Created_Date_Time_EXISTS,Order_Put_Lines_Complete_Date_Time_EXISTS,Order_Put_Wall_Pack_Complete_Date_Time_EXISTS
		,Order_Put_Wall_Pack_Operator_Code_EXISTS,Order_Put_Wall_Pack_Location_Code_EXISTS,Order_Put_Wall_Pack_Zone_Code_EXISTS,Order_Complete_Date_Time_EXISTS
		,Wave_Code_EXISTS,Pick_Order_Type_EXISTS,Pick_Order_Category_EXISTS,Order_Cancelled_Date_Time_EXISTS,
		Order_Assigned_Location_Code = Location_Order_Assigned,
		Order_Put_Wall_Pack_Operator_Code = UserName_Put_Wall_Pack,
		Order_Put_Wall_Pack_Location_Code = Location_Put_Wall_Pack,
		Order_Put_Wall_Pack_Zone_Code = Zone_Put_Wall_Pack,
		Operator_Code = userName_Pack,
		Location_Code = Location_Pack,
		Zone_Code = Zone_Pack,
		Order_Complete_Date_Time,
		Order_Put_Wall_Pack_Complete_Date_Time
	FROM ALL_PICK_ORDER_EVENTS
	WHERE UPPER(pickOrderType) = ''SHIPPING''
)
'
SET @lSQLStmt4 = '
, MERGE_DB_AND_STAGE_EVENTS AS ( 
	SELECT DISTINCT
		Pick_Order_Complete_Ind = CASE WHEN Order_Complete_Date_Time IS NOT NULL THEN 1 ELSE 0 END,
		Order_Cancelled_Ind = CASE WHEN Order_Cancelled_Date_Time IS NOT NULL THEN 1 ELSE 0 END,
		Location_Code =  ISNULL(Location_Code, Location_Code_EXISTS),
		Order_Assigned_Location_Code = ISNULL(Order_Assigned_Location_Code_EXISTS, Order_Assigned_Location_Code),						
		Order_Put_Wall_Pack_Location_Code = ISNULL(Order_Put_Wall_Pack_Location_Code, Order_Put_Wall_Pack_Location_Code_EXISTS),
		Pick_Order_Code = ORDER_TYPE_CALC_EVENTS.Pick_Order_Code,
		Order_Released_Date_Time = ISNULL(Order_Released_Date_Time, Order_Released_Date_Time_EXISTS),
		Order_Assigned_Date_Time = ISNULL(Order_Assigned_Date_Time, Order_Assigned_Date_Time_EXISTS),
		Order_Created_Date_Time = CASE WHEN Order_Created_Date_Time_EXISTS IS NOT NULL THEN Order_Created_Date_Time_EXISTS
									  WHEN Order_Created_Date_Time IS NOT NULL THEN Order_Created_Date_Time
									  WHEN Order_Released_Date_Time IS NOT NULL THEN Order_Released_Date_Time
									  WHEN Order_Activated_Date_Time IS NOT NULL THEN Order_Activated_Date_Time
									  ELSE Order_Lines_Complete_Date_Time
								  END,
		Order_Pack_Assigned_Date_Time = ISNULL(Order_Pack_Start_Date_Time_EXISTS, Order_Pack_Assigned_Date_Time),
		Order_Put_Lines_Complete_Date_Time = ISNULL(Order_Put_Lines_Complete_Date_Time_EXISTS, Order_Put_Lines_Complete_Date_Time),
		Order_Put_Wall_Pack_Complete_Date_Time = ISNULL(Order_Put_Wall_Pack_Complete_Date_Time_EXISTS,Order_Put_Wall_Pack_Complete_Date_Time),
		Order_Lines_Complete_Date_Time = ISNULL(Order_Lines_Complete_Date_Time_EXISTS, Order_Lines_Complete_Date_Time),
		Order_Pack_Complete_Date_Time = ISNULL(Order_Pack_Complete_Date_Time_EXISTS,Order_Pack_Complete_Date_Time),
		Order_Complete_Date_Time = ISNULL(Order_Complete_Date_Time_EXISTS, Order_Complete_Date_Time),
		Wave_Code = ISNULL(Wave_Code_EXISTS, wavePk),
		Operator_Code = ISNULL(Operator_Code,Operator_Code_EXISTS),
		Zone_Code = ISNULL(Zone_Code,Zone_Code_EXISTS),
		Order_Put_Wall_Pack_Operator_Code = ISNULL(Order_Put_Wall_Pack_Operator_Code,Order_Put_Wall_Pack_Operator_Code_EXISTS),
		Order_Put_Wall_Pack_Zone_Code = ISNULL(Order_Put_Wall_Pack_Zone_Code,Order_Put_Wall_Pack_Zone_Code_EXISTS),
		Order_Cancelled_Date_Time = ISNULL(Order_Cancelled_Date_Time_EXISTS, Order_Cancelled_Date_Time),
		Order_Put_Start_Date_Time = order_quantities.Order_Put_Start_Date_Time,
		Order_Line_Count = order_quantities.Order_Line_Count,
		Order_Total_Qty = order_quantities.Order_Total_Qty,
		Customer_Order_Code = ORDER_TYPE_CALC_EVENTS.orderpk
	FROM ORDER_TYPE_CALC_EVENTS
	LEFT OUTER JOIN (
		SELECT
		  DimPickOrder.Pick_Order_Code,
		  Order_Total_Qty = SUM(Picked_Qty),
		  Order_Line_Count = SUM(CAST(Pick_Order_Line_Complete_Ind AS REAL)),
		  Order_Put_Start_Date_Time = MIN(Record_Timestamp)
		FROM
		  OA.FCT_Order_Line FctOrderLine
		WITH
		  (NOLOCK)
		  JOIN OA.DIM_Pick_Order DimPickOrder
		WITH
		  (NOLOCK) ON FctOrderLine.Pick_Order_ID = DimPickOrder.Pick_Order_ID
		WHERE
		  FctOrderLine.Record_Timestamp >= (SELECT MIN(eventTime) FROM Stage.FCT_Pick_Order_' + @lSourceSystem + ')
		  AND DimPickOrder.Source_System = ''' + @lSourceSystem + '''
		GROUP BY
		  DimPickOrder.Pick_Order_Code,
		  DimPickOrder.Source_System
	) as order_quantities ON ORDER_TYPE_CALC_EVENTS.Pick_Order_Code = order_quantities.Pick_Order_Code
)
SELECT 
	Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),Order_Created_Date_Time,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,Order_Created_Date_Time)/15))
	, Record_Timestamp = Order_Created_Date_Time
	, Pick_Order_Code = ISNULL(UPPER(Pick_Order_Code), '''')
	, Customer_Order_Code = ISNULL(UPPER(Customer_Order_Code),'''')
	, Operator_Code = ISNULL(UPPER(Operator_Code), '''')
	, Location_Code = CASE WHEN Location_Code IS NULL OR Location_Code = ''0'' THEN '''' ELSE UPPER(Location_Code) END
	, Wave_Code = ISNULL(UPPER(Wave_Code), '''')
	, Zone_Code = ISNULL(UPPER(Zone_Code), '''')
	, Order_Released_Date_Time = CASE WHEN Order_Released_Date_Time IS NULL AND Order_Lines_Complete_Date_Time IS NOT NULL THEN Order_Created_Date_Time ELSE Order_Released_Date_Time END
	, Order_Assigned_Date_Time = ISNULL(Order_Assigned_Date_Time,'''')
	, Order_Assigned_Location_Code = ISNULL(UPPER(Order_Assigned_Location_Code), '''')
	, Order_Put_Start_Date_Time = ISNULL(CAST(Order_Put_Start_Date_Time AS VARCHAR(50)),'''')
	, Order_Pack_Start_Date_Time = ISNULL(CAST(Order_Pack_Assigned_Date_Time AS VARCHAR(50)),'''')
	, Overflow_Location_Count = ''''
	, Order_Put_Lines_Complete_Date_Time = ISNULL(CAST(Order_Put_Lines_Complete_Date_Time AS VARCHAR(50)),'''')
	, Order_Put_Wall_Pack_Complete_Date_Time = ISNULL(CAST(Order_Put_Wall_Pack_Complete_Date_Time AS VARCHAR(50)),'''')
	, Order_Put_Wall_Pack_Operator_Code = ISNULL(UPPER(Order_Put_Wall_Pack_Operator_Code), '''')
	, Order_Put_Wall_Pack_Location_Code = ISNULL(UPPER(Order_Put_Wall_Pack_Location_Code), '''')
	, Order_Put_Wall_Pack_Zone_Code = ISNULL(UPPER(Order_Put_Wall_Pack_Zone_Code), '''')
	, Order_Lines_Complete_Date_Time = ISNULL(CAST(Order_Lines_Complete_Date_Time AS VARCHAR(50)),'''')
	, Order_Pack_Complete_Date_Time = ISNULL(CAST(Order_Pack_Complete_Date_Time AS VARCHAR(50)),'''')
	, Pick_Order_Complete_Ind
	, Order_Complete_Date_Time = ISNULL(CAST(Order_Complete_Date_Time AS VARCHAR(50)),'''')
	, Source_System = ''' + @lSourceSystem + '''
	, Order_Cancelled_Date_Time = ISNULL(CAST(Order_Cancelled_Date_Time AS VARCHAR(50)),'''')
	, Order_Cancelled_Ind
	, Order_Line_Count = ISNULL(CAST(Order_Line_Count AS VARCHAR(50)), '''')
	, Order_Total_Qty = ISNULL(CAST(Order_Total_Qty AS VARCHAR(50)),'''')
FROM MERGE_DB_AND_STAGE_EVENTS
'

SET @lSQLDropsStmt = ' DROP TABLE IF EXISTS [STAGE].[FCT_Picking_OrderLine_'+@lSourceSystem +']'+  
   	   	   		     ' DROP TABLE IF EXISTS [STAGE].[FCT_Pick_Order_'+@lSourceSystem +']';  

SET @lFinalSQL = @lSQLStmt0+@lSQLStmt1+@lSQLStmt2+@lSQLStmt3+@lSQLStmt4;

--Select @lFinalSQL

EXEC sp_executesql @lFinalSQL;
EXEC sp_executesql @lSQLDropsStmt;


/*
--EXEC(@lSQLStmt0+@lSQLStmt1+@lSQLStmt2+@lSQLStmt3+@lSQLStmt4)
print(@lSQLStmt0)
print(@lSQLStmt1)
print(@lSQLStmt2)
print(@lSQLStmt3)
print(@lSQLStmt4)
*/
