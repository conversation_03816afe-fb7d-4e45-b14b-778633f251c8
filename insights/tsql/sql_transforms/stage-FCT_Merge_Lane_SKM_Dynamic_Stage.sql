SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 
DECLARE @Transform_SQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX);

SET @Transform_SQLStmt = '
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Merge_Lane_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	SELECT DISTINCT
		Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), eventTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), eventTime,120),15,2) as INT)/15))
		,Record_Timestamp = eventTime	
		,Merge_Physical_Destination_Code = ISNULL(mergePhysicalDestinationID, '''')
		,Merge_PLC_Code = ISNULL(plc, '''')
		,Reason_Code = ISNULL(CASE WHEN UPPER(reasonCode) = ''NONE'' THEN NULL ELSE reasonCode END, '''') 
		,Container_Count = ISNULL(containerCount, '''')
		,Event_Code = ISNULL(event, '''')
		,Scanner_Code = ISNULL(scannerID, '''')
		,Sort_Device_Code = ISNULL(sortDeviceID, '''')
		,Subsystem_Code = ISNULL(originator, ''SORTER'')
		,Subsystem_Category = ''Dematic IQ''
		,Source_System = ''' + @lSourceSystem + '''
	FROM [STAGE].[FCT_Merge_Lane_' + @lSourceSystem + ']
END
'
SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Merge_Lane_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
		DROP TABLE [STAGE].[FCT_Merge_Lane_'+@lSourceSystem +']'


--PRINT(@lSQLStmt)
EXEC sp_executesql @Transform_SQLStmt
EXEC sp_executesql @Drop_SQLStmt
