SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 
DECLARE @lSQLStmt NVARCHAR(MAX);
DECLARE @Drop_SQLStmt NVARCHAR(MAX); 

SET @lSQLStmt = '
DECLARE @currentDate Datetime2(7) = GETDATE();
DECLARE @maxHourID INTEGER = substring(Replace(Replace(CONVERT(VARCHAR(13), @currentDate,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), @currentDate,120),15,2) as INT)/15));
-- minimum quarter hour minHourID = @maxHourID - 10 days in quarter hours
DECLARE @minHourID INTEGER = @maxHourID - 960;

CREATE TABLE #FCT_Pal_Depal_Events_' + @lSourceSystem + '(
	Hour_Quarter_ID INTEGER NOT NULL, Record_Timestamp Datetime2(7) NOT NULL, Pallet_Code VARCHAR(150) NULL, Device_Code VARCHAR(150) NULL,
	Wave_Code VARCHAR(150) NULL, Container_Type_Code VARCHAR(50) NULL, Operator_Code VARCHAR(150) NULL,  Order_Code VARCHAR(150) NULL, 
	Pallet_Start_Date_Time Datetime2(7) NULL,Pallet_Duration_In_Seconds INTEGER NULL, Total_Case_Count INTEGER NULL,
	Manual_Case_Count INTEGER NULL, Event_Type VARCHAR(150) NULL, Subsystem_Code VARCHAR(150) NULL, Subsystem_Category VARCHAR(50) NULL,
	Source_System VARCHAR(150) NULL, Layer_Count INTEGER NULL
);

-- Depal Events
IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Depal_Summary_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	WITH Depal_Processed_Events as (
		SELECT
			  Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), endTime,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), endTime,120),15,2) as INT)/15)) 
			  ,Record_Timestamp = endTime
			  ,Pallet_Code = palletId
			  ,Device_Code = workstationId
			  ,Wave_Code = NULL
			  ,Container_Type_Code = NULL
			  ,Operator_Code = NULL
			  ,Order_Code = NULL
			  ,Pallet_Start_Date_Time = startTime
			  ,Pallet_Duration_In_Seconds = DATEDIFF("second",startTime,endTime)
			  ,Total_Case_Count = items
			  ,Manual_Case_Count = manualitems
			  ,Event_Type = ''Depal''  
			  ,Subsystem_Code = CASE WHEN workstationId IS NOT NULL THEN ''Depalletizer'' ELSE NULL END
			  ,Subsystem_Category = ''Dematic IQ''
			  ,Source_System = ''' + @lSourceSystem + '''
			  ,Layer_Count = layers
		  FROM [STAGE].[FCT_Depal_Summary_' + @lSourceSystem + ']
	)
	INSERT INTO #FCT_Pal_Depal_Events_' + @lSourceSystem + ' SELECT * from Depal_Processed_Events;
END

IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id  where t.name = ''FCT_Pallet_Summary_' + @lSourceSystem + ''' and s.name = ''Stage'')
BEGIN
	WITH Pal_Processed_Events AS (
		SELECT
			  Hour_Quarter_ID = substring(Replace(Replace(CONVERT(VARCHAR(13), endDate,120),'' '',''''),''-'',''''),3,13)+ LTRIM(STR(CAST(SUBSTRING(CONVERT(VARCHAR(20), endDate,120),15,2) as INT)/15)) 
			  ,Record_Timestamp = endDate
			  ,Pallet_Code = palletId
			  ,Device_Code = workstationId
			  ,Wave_Code = waveId
			  ,Container_Type_Code = loadUnitType
			  ,Operator_Code = NULL
			  ,Order_Code = orderPk
			  ,Pallet_Start_Date_Time = startDate
			  ,Pallet_Duration_In_Seconds = DATEDIFF("second",startDate,endDate)
			  ,Total_Case_Count = caseCount
			  ,Manual_Case_Count = NULL	
			  ,Event_Type = ''Pal''  
			  ,Subsystem_Code = CASE WHEN workstationId IS NOT NULL THEN ''Conveyor'' ELSE NULL END
			  ,Subsystem_Category = ''Dematic IQ''
			  ,Source_System = ''' + @lSourceSystem + '''
			  ,Layer_Count = 0
		  FROM [STAGE].[FCT_Pallet_Summary_' + @lSourceSystem + ']
	)
	INSERT INTO #FCT_Pal_Depal_Events_' + @lSourceSystem + ' SELECT * from Pal_Processed_Events;
END

SELECT DISTINCT
	PDE.Hour_Quarter_ID
	,PDE.Record_Timestamp
	,Pallet_Code=ISNULL(PDE.Pallet_Code,'''')
	,Device_Code=ISNULL(PDE.Device_Code,'''')
	,Wave_Code=ISNULL(PDE.Wave_Code,'''')
	,Container_Type_Code=ISNULL(PDE.Container_Type_Code,'''')
	,Operator_Code=ISNULL(PDE.Operator_Code,'''')
	,Order_Code=ISNULL(PDE.Order_Code,'''')
	,Pallet_Start_Date_Time=ISNULL(CAST(PDE.Pallet_Start_Date_Time AS varchar(30)),'''')
	,Pallet_Duration_In_Seconds=ISNULL(PDE.Pallet_Duration_In_Seconds,'''')
	,PDE.Total_Case_Count 
	,Manual_Case_Count = COALESCE(PDE.Manual_Case_Count, Manual_Pick_Lookup.Manual_Items, 0)
	,Event_Type=ISNULL(PDE.Event_Type,'''')	
	,Subsystem_Code=ISNULL(PDE.Subsystem_Code,'''')
	,Subsystem_Category = ''Dematic IQ''
	,PDE.Source_System
	,Layer_Count=ISNULL(PDE.Layer_Count,'''')	
FROM #FCT_Pal_Depal_Events_' + @lSourceSystem + ' PDE
LEFT OUTER JOIN (
	SELECT Pallet_Code, dw.Wave_Code, dw.Source_System AS Source_System, count(Manual_Flg) AS Manual_Items FROM OA.FCT_Palletize_Item fpe
	INNER JOIN [OA].[DIM_Wave] dw 
		ON dw.Wave_ID = fpe.Wave_ID
	WHERE Hour_Quarter_ID >= @minHourID AND Hour_Quarter_ID < @maxHourID AND dw.Source_System = ''' + @lSourceSystem + ''' GROUP BY Pallet_Code, Wave_Code, Source_System
	) AS Manual_Pick_Lookup 
	ON Manual_Pick_Lookup.Pallet_Code = PDE.Pallet_Code AND Manual_Pick_Lookup.Wave_Code = PDE.Wave_Code AND Manual_Pick_Lookup.Source_System = ''' + @lSourceSystem + '''
'

SELECT @Drop_SQLStmt = ' IF OBJECT_ID(''[STAGE].[FCT_Depal_Summary_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
                           DROP TABLE [STAGE].[FCT_Depal_Summary_'+@lSourceSystem +'] 
                        IF OBJECT_ID(''[STAGE].[FCT_Pallet_Summary_' + @lSourceSystem + ']'', ''U'') IS NOT NULL  
                           DROP TABLE [STAGE].[FCT_Pallet_Summary_'+@lSourceSystem +'] ' 						   
					   

--PRINT(@lSQLStmt)
EXEC sp_executesql @lSQLStmt
EXEC sp_executesql @Drop_SQLStmt
