SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @lSourceSystem VARCHAR(50) = '$(Source_System)';
--DECLARE @lSourceSystem VARCHAR(50) = 'DematicSoftware'; 

DECLARE @lSQLStmt NVARCHAR(MAX)

SELECT @lSQLStmt = ' IF EXISTS (Select * FROM sys.tables t join sys.schemas s on t.schema_id = s.schema_id ' +
                   ' where t.name = ''OVERHEAD_SCANNER_READS_FACT_' + @lSourceSystem + ''' and s.name = ''Stage'') ' +
                   ' BEGIN ' +
                   ' SELECT Hour_Quarter_ID = LEFT(REPLACE(REPLACE(SUBSTRING(REPLACE(CONVERT(VARCHAR(50),endTime,25),''-'',''''),3,50),'' '',''''),'':'',''''),8) + LTRIM(STR(DATEPART(minute,endTime)/15))  ' +
                   '     , Record_Timestamp = endTime '       +
				   '     , Scanner_Code = ISNULL(deviceId, '''') ' + 
				   '     , Scanner_Name = ISNULL(deviceId, '''') ' +
				   '     , Scanner_Location_Code = ISNULL(locationId, '''') ' +
				   '     , Location_Name = ISNULL(locationId, '''') ' +
				   '     , Location_Type_Code = ISNULL(scannerType, '''') ' +
				   '     , Location_Type_Name = ISNULL(scannerType, '''') ' +
				   '     , Min_Response_Time_In_Millisecond = ISNULL(MIN(minResponseTime), '''') ' +
				   '     , Max_Response_Time_In_Millisecond = ISNULL(MAX(maxResponseTime), '''') ' +
				   '     , Mean_Response_Time_In_Millisecond = ISNULL(AVG(CAST(meanResponseTime AS REAL)), '''') ' +
				   '     , Scan_Fail_Count = ISNULL(MAX(noReads), '''') ' +
				   '     , Scan_Success_Count = ISNULL(MAX(validReads), '''') ' +
				   '     , Telegram_Type = ISNULL(telegramType, '''') ' +
				   '     , Start_TimeFrame_Date_Time = ISNULL(MIN(startTime), '''') ' +
				   '     , Subsystem_Category = ''DEMATIC IQ'' ' +
				   '     , Subsystem_Code = ''OVERHEAD_SORT'' ' +
                   '     , Source_System = ''' + @lSourceSystem +  '''' +
                   ' FROM [STAGE].OVERHEAD_SCANNER_READS_FACT_'+ @lSourceSystem +
				   ' GROUP BY endTime, deviceId, locationId, scannerType, telegramType /*, startTime*/ ' +
				   ' ORDER BY Hour_Quarter_ID,Record_Timestamp, scanner_code, Scanner_Location_Code  ' +
				   ' END '  +  
				   ' IF OBJECT_ID(''[STAGE].OVERHEAD_SCANNER_READS_FACT_' + @lSourceSystem +''', ''U'') IS NOT NULL  DROP TABLE [STAGE].OVERHEAD_SCANNER_READS_FACT_'+@lSourceSystem;  
				   
EXEC sp_executesql @lSQLStmt


