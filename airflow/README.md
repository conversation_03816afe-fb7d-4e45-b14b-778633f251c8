
## Airflow Project Structure Overview

The project is structured to support multiple tenants, each with standard ETL processes and the capability to run tenant-specific data transformations. The folder structure organizes DAGs and SQL transformations by tenant, with utility functions shared across all DAGs.


**/control-tower-etl/airflow**:
```
│
├── dags                # Contains all Airflow DAGs, organized by tenant
│   ├── tenant_name     # Tenant-specific directory
│   │   ├── tenant_dag.py           # Standard DAG for the tenant
│   │   └── tenant_drop_rows_dag.py # DAG for dropping rows in BigQuery tables
│   └── utils           # Shared utility functions for DAGs
│       └── airflow_utils.py
│
└── transforms          # SQL transformations for BigQuery
    ├── core_sql        # Core transformations applicable to all tenants
    └── tenants         # Tenant-specific transformations
```

## DAG Task Ordering and Task Groups

In Airflow, a Directed Acyclic Graph (DAG) consists of tasks and their dependencies. The ordering of tasks is determined by these dependencies, specifying how tasks are sequenced in relation to each other during execution.

### Task Ordering

Tasks within a DAG are executed based on their dependencies. A task will only run after all its upstream dependencies have successfully completed. This dependency mechanism allows for complex workflows where the output of one task is the input of another, ensuring tasks are executed in the correct order.

Tasks are defined using the `@task` decorator or by instantiating objects from operator classes. Ordering is typically managed using the bitshift operators:

- `>>` (bitshift right) denotes that a task or task group must be executed after another.

- `<<` (bitshift left) denotes that a task or task group must be executed before another.

- Tasks or task groups inside left and right brackets `[]` are executed in parallel.


For example, `task_a >> task_b` means `task_b` will run after `task_a` has successfully completed. `[task_a, task_b] >> task_c` means that `task_a` and `task_b` will run simultaneously and `task_c` will only run after both tasks complete.


### Task vs. Task Group

**Task**: A single, atomic unit of work within a DAG. It could be anything from executing a Python function, running an SQL query, transferring data between systems, or any other discrete operation you define using Airflow's operators.

**Task Group**: A logical grouping of tasks within a DAG. Task groups are used to organize tasks that share a common purpose or dependency into a collapsible, hierarchical structure within the Airflow UI. This helps in managing and visualizing complex DAGs with many tasks. Task groups are defined using the `TaskGroup` context manager.

### Example

``` python

with TaskGroup("group1") as group1:    
    task1 = create_task("task1")    
    task2 = create_task("task2")  
with TaskGroup("group2") as group2:    
    task3 = create_task("task3")  
group1 >> group2  
```
In this example, `task3` in `group2` will only start after both `task1` and `task2` in `group1` have completed, demonstrating how task groups can be used to manage task dependencies effectively.

# How to Run Airflow

## Running Airflow Locally

### Prerequisites

- Ensure Python and `pip` are installed.

### Installation

1. **Install Apache Airflow with Celery** for distributed task execution:

    `pip install "apache-airflow[celery]==2.5.3" --constraint "https://raw.githubusercontent.com/apache/airflow/constraints-2.5.3/constraints-3.7.txt"`

2. **Add Airflow's bin directory to your PATH**:

    - Edit your `~/.zshrc` file to include:

        `export PATH=$PATH:~/.local/bin`

    - Apply the changes:

        `source ~/.zshrc`

### Database Setup

- Initialize the Airflow database (SQLite by default):

    `airflow db upgrade`

### Starting Airflow

- Start Airflow in standalone mode for development:


    `airflow standalone`

    - Note: Standalone mode is for development only.


### Accessing the Web Interface
  
- Navigate to `http://localhost:8080/` and log in using the credentials from the terminal output. See next section for setting airflow environment variables.

### Setting Airflow Variables Locally

For local development, Airflow variables such as `SOURCE_BUCKET` can be defined and imported from a JSON file. This approach simplifies managing environment-specific configurations and is particularly useful when transitioning between different stages of development, testing, and production.

#### Creating the Variables JSON File

1. **Create a JSON File**: Create a file named `airflow_variables.json` in a convenient location, such as the root of your Airflow directory.

2. **Define Variables**: Add your variables as key-value pairs in the JSON file. For the `SOURCE_BUCKET`, your file should look like this:

    ```

    { "SOURCE_BUCKET": "ict-d-etl-bq-queries" }

    ```
    This file can contain other variables as needed by your DAGs, each as an additional key-value pair.
    
#### Importing Variables into Airflow

After defining your variables in the JSON file, use the Airflow CLI to import them into your local Airflow environment:

`airflow variables import /path/to/your/airflow_variables.json`


Replace `/path/to/your/` with the actual path where you saved the `airflow_variables.json` file.

#### Verifying Variable Import

To ensure your variables were imported successfully, you can list all variables defined in your Airflow environment:

`airflow variables list`

## Testing DAGs in the Prototype Project

### Testing Environment

- If you are unable to run Airflow locally, test new or significantly changed DAGs in the `ict-b-prototype-etl` project before you create a merge request.

### Uploading Your DAG

1. **Access the Prototype Project's Airflow Environment**:

    - Upload your DAG file directly to the prototype project's [DAG folder](https://console.cloud.google.com/storage/browser/us-central1-cloud-composer-c710e7ad-bucket/dags;tab=objects?project=ict-b-prototype-etlt&supportedpurview=project)
    - Remember that the DAG should be uploaded to {DAG_folder}/{tenant_id}

### Triggering a DAG

1. **Navigate to the [Airflow Web Interface](https://console.cloud.google.com/composer/environments/detail/us-central1/cloud-composer/monitoring?referrer=search&project=ict-b-prototype-etlt&supportedpurview=project)** in the `ict-b-prototype-etlt` project.

2. **Click Open Airflow UI**

3. **Find and Trigger Your DAG**:
    - Locate the DAG corresponding to the tenant and process you wish to run.
    - Use the "Trigger DAG" option to start an instance of the DAG.
    - Monitor the execution logs and outputs to ensure successful completion.

### DAG Parameters and Configuration

Each DAG utilizes a set of configuration variables to tailor the ETL process to specific tenants and environments. These variables are defined at the beginning of each DAG file and include:


> [!These three are what need to be set for a new tenant]

> - `tenant_id`: Identifier for the tenant, used to specify paths and database schemas.

> - `facility_id`: Identifier for the specific facility or instance within the tenant's operations.

> - `source_system`: The system from which data originates, helping to specify source-specific logic or transformations.


- `sql_queries_dir`: Directory path where SQL query files are stored.

- `src_bucket`: The GCS bucket name where source SQL files are located.

- `core_src_folder`: The directory within `src_bucket` containing core SQL transformations.

- `tenant_src_folder`: The directory within `src_bucket` specific to the tenant's SQL transformations.


### Standard DAG Tasks/Task Groups

DAGs typically include the following standard tasks or task groups:

- **Initialization (`INIT`)**: Prepares the environment by creating necessary directories and clearing out old files.

- **Load Dimension Data (`LOAD_DIM_QUERY_DATA`)**: Executes SQL transformations to load or update dimension tables in BigQuery.

- **Load Fact Data (`LOAD_FCT_DATA`)**: Similar to dimension data loading but focused on fact tables, which contain transactional or event data.

- **Load Aggregated Data (`LOAD_AGG_DATA`)**: Processes and loads aggregated data into BigQuery, often using aggregations which depend on loaded fact data.

- **Load Daily Calcuations**: Runs daily historical calculations and saves them to BigQuery.

### Utility Functions

### Adding a New Utility Function to the Airflow Utility Library and Implementing it in a DAG

This guide walks you through the steps for adding a new utility function to the Airflow utility library (`airflow_utils.py`) and how to implement it using the task decorator in an Airflow DAG.

#### Step 1: Create the Utility Function
  
1. **Open the `airflow_utils.py` file** located in your `utils` package.

2. **Define your new utility function**. For example:
   ```python

   def new_utility_function(param1, param2):

       # Your function logic here
       return result

3. **Add logging** within your function using the existing `task_logger` for consistency.

    ```python
    task_logger.info("Useful information about the process")

    ```
4. **Handle exceptions** appropriately to ensure robustness of function.

``` python
    try:
    # Your function logic
    except Exception as e:
        task_logger.error(f"Error occurred: {e}")
        raise
```

#### Step 2: Import the new utility function

1. **Add the new function's name to the __all__ list in airflow_utils.py**:

``` python
    __all__ = [..., 'new_utility_function']
```
2. **Import the new utility function** into your DAG from the utils package:

``` python
    from utils import new_utility_function
```

4. **Decorate the function** with `@task` to make it an Airflow task. You can do this in two ways:

- Directly as a decorated function:
``` python
    @task(task_id='task_new_utility_function')
    def task_new_function_wrapper(param1, param2):
        return new_utility_function(param1, param2)
```

- Or, define a wrapper function if additional Airflow-specific logic is needed:

``` python
    def create_new_task(param1, param2):
    @task(task_id='task_new_utility_function')
    def task_new_function():
        return new_utility_function(param1, param2)
    return task_new_function
```