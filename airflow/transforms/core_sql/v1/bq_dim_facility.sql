-- DECLARE etl_batch_uuid STRING DEFAULT STRING(CURRENT_TIMESTAMP);
-- DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2022-09-27 00:00:00');
-- DECLARE end_query_date TIMESTAMP DEFAULT TIMESTAMP('2024-09-27 02:00:00');

CREATE TEMP TABLE tmp_dim_facility
(
  record_timestamp TIMESTAMP NOT NULL,
  facility_code STRING NOT NULL,
  facility_name STRING,
  primary_key STRING NOT NULL,
  description STRING,

  facility STRING NOT NULL,
  tenant STRING NOT NULL,
  source_system STRING NOT NULL
);

INSERT INTO tmp_dim_facility
WITH
  extracted_data AS (
    SELECT
      JSON_VALUE(data, '$.dimensionModificationTime') as dimension_mod_time,
      JSON_VALUE(data, '$.id') as facility_code,
      JSON_VALUE(data, '$.name') as facility_name,
      JSON_VALUE(data, '$.primaryKey') as primary_key,
      JSON_VALUE(data, '$.description') as description,
      facility AS facility,
      @ltarget_tenant AS tenant,
      source_system AS source_system
    FROM `${tenant_id}_landing.FacilityDimension`
    WHERE ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  normalized_data AS (
    SELECT
      `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(dimension_mod_time, NULL) AS record_timestamp,
      facility_code AS facility_code,
      facility_name AS facility_name,
      primary_key AS primary_key,
      COALESCE(description, '') AS description,
      facility AS facility,
      tenant AS tenant,
      source_system AS source_system
    FROM extracted_data
  )
SELECT * FROM normalized_data;

MERGE INTO `${tenant_id}_oa_curated.dim_facility` df
USING (
  SELECT
    record_timestamp,
    facility_code,
    facility_name,
    primary_key,
    description,
    tenant,
    source_system,
    facility,
  FROM tmp_dim_facility  
) tdf
ON df.primary_key = tdf.primary_key
WHEN NOT MATCHED THEN
  INSERT (
    facility_uuid,
    etl_batch_id,
    record_timestamp,
    facility_code,
    facility_name,
    primary_key,
    description,
    facility,
    tenant,
    source_system
  ) VALUES (
    `${tenant_id}_oa_curated.Dimension_PK_Generator`([
      tdf.tenant,
      tdf.facility,
      tdf.source_system,
      tdf.facility_code
    ]),
    @etl_batch_uuid,
    tdf.record_timestamp,
    tdf.facility_code,
    tdf.facility_name,
    tdf.primary_key,
    tdf.description,
    tdf.facility,
    tdf.tenant,
    tdf.source_system
  );
