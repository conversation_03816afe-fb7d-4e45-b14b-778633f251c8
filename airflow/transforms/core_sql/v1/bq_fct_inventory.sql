/*
declare ltarget_tenant string default 'ict_development';
declare etl_batch_uuid string default FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', CURRENT_TIMESTAMP());

declare start_query_date timestamp default timestamp('2024-01-08 00:00:00');
declare end_query_date timestamp default timestamp('2024-01-08 14:00:00');
*/

CREATE TEMP TABLE tmp_staging_fct_inventory
(
    record_timestamp TIMESTAMP,
    item_code STRING,
    item_sku STRING,
    location_code STRING,
    location_name STRING,
    work_area_code STRING,
    work_area_name STRING,
    module_code STRING,
    module_name STRING,
    bay STRING,
    load_unit_code STRING,
    inventory_unit_code STRING,
    inventory_batch_code STRING,
    base_load_unit_code STRING,
    receiving_processing_code STRING,
    item_category_code STRING,
    inventory_qty INT64,
    inventory_unassigned_qty INT64,
    tenant STRING,
    facility STRING,
    source_system STRING,
    item_uuid STRING DEFAULT '',
    location_uuid STRING DEFAULT NULL,
    work_area_uuid STRING DEFAULT NULL,
    module_uuid STRING DEFAULT NULL,
    min_cont_in_dms STRING DEFAULT NULL,
    max_cont_in_dms STRING DEFAULT NULL,
    condition_code STRING DEFAULT NULL,
    item_name STRING,
    last_activity_date TIMESTAMP DEFAULT NULL,
    last_cc_inspection_date TIMESTAMP DEFAULT NULL
);


INSERT INTO tmp_staging_fct_inventory
WITH extracted_fct_inventory_data AS (
    SELECT
        CASE
            WHEN JSON_VALUE(data, '$.eventTime') IS NOT NULL
                THEN `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(JSON_VALUE(data, '$.eventTime'), NULL)
            WHEN JSON_VALUE(data, '$.eventDate') IS NOT NULL
                THEN `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(JSON_VALUE(data, '$.eventDate'), NULL)
            ELSE SAFE_CAST('' AS TIMESTAMP) -- Fallback if neither field is present
        END AS record_timestamp,
        JSON_VALUE(data, '$.inventoryUnitPk') AS inventoryunitpk,
        JSON_VALUE(data, '$.skuPk') AS skupk,
        SAFE_CAST(JSON_VALUE(data, '$.quantity') AS NUMERIC) AS quantity,
        SAFE_CAST(JSON_VALUE(data, '$.quantityFree') AS NUMERIC)
            AS quantityfree,
        JSON_VALUE(data, '$.batch') AS batch,
        JSON_VALUE(data, '$.loadUnitId') AS loadunitid,
        JSON_VALUE(data, '$.baseLoadUnitId') AS baseloadunitid,
        JSON_VALUE(data, '$.locationId') AS locationid,
        JSON_VALUE(data, '$.receivingReferenceId') AS receivingreferenceid,
        facility,
        source_system,
        `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(JSON_VALUE(data, '$.LASTACTIVITYDATE'), NULL) as last_activity_date,
        `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(JSON_VALUE(data, '$.LASTCCINSPECTIONDATE'), NULL) as last_cc_inspection_date
    FROM `${tenant_id}_landing.InventoryFact`
    WHERE
        tenant = @ltarget_tenant
        AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(
            @end_query_date
        )
)

SELECT DISTINCT
    record_timestamp,
    ${tenant_id}_oa_curated.Item_Code_Generator(skupk, NULL, NULL, NULL) AS item_code,
    COALESCE(CASE
        WHEN skupk LIKE '%Empty_SKU%' THEN ''
        WHEN
            STRPOS(COALESCE(skupk, ''), '#') > 0
            THEN RIGHT(skupk, STRPOS(REVERSE(skupk), '#') - 1)
        ELSE skupk
    END, '') AS item_sku,
    UPPER(COALESCE(locationid, '')) AS location_code,
    `${tenant_id}_oa_curated.Location_Name_Generator`(locationid) AS location_name,
    CASE
        WHEN
            STRPOS(locationid, '.') > 0
            THEN UPPER(SUBSTR(locationid, 0, INSTR(locationid, '.',1, 2)-1))
        ELSE ''
    END AS work_area_code,
    CASE
        WHEN
            STRPOS(locationid, '.') > 0
            THEN UPPER(SPLIT(locationid, '.')[OFFSET(1)])
        ELSE NULL
    END AS work_area_name,
    CASE
        WHEN
            STRPOS(locationid, '.') > 0
            THEN UPPER(SUBSTR(locationid, 0, INSTR(locationid, '.',1, 3)-1))
        ELSE ''
    END AS module_code,
    CASE
        WHEN
            STRPOS(locationid, '.') > 0
            THEN UPPER(SPLIT(locationid, '.')[OFFSET(2)])
        ELSE NULL
    END AS module_name,
    CASE
        WHEN
            STRPOS(locationid, '.') > 0
            THEN SPLIT(locationid, '.')[OFFSET(3)]
        ELSE NULL
    END AS bay,
    UPPER(COALESCE(loadunitid, '')) AS load_unit_code,
    UPPER(COALESCE(inventoryunitpk, '')) AS inventory_unit_code,
    UPPER(COALESCE(batch, '')) AS inventory_batch_code,
    UPPER(baseloadunitid) AS base_load_unit_code,
    UPPER(receivingreferenceid) AS receiving_processing_code,
    '' AS item_category_code,
    SAFE_CAST(quantity AS INT64) AS inventory_qty,
    SAFE_CAST(quantityfree AS INT64) AS inventory_unassigned_qty,
    @ltarget_tenant AS tenant,
    facility,
    source_system,
    CAST(NULL AS STRING) AS item_uuid,
    CAST(NULL AS STRING) AS location_uuid,
    CAST(NULL AS STRING) AS work_area_uuid,
    CAST(NULL AS STRING) AS module_uuid,
    CAST(NULL AS STRING) AS min_cont_in_dms,
    CAST(NULL AS STRING) AS max_cont_in_dms,
    CAST(NULL AS STRING) AS condition_code,
    CAST(NULL AS STRING) AS item_name,
    last_activity_date,
    last_cc_inspection_date
FROM
    extracted_fct_inventory_data
ORDER BY
    record_timestamp,
    load_unit_code,
    inventory_unit_code,
    item_code
;

/*
    New WMSInventoryFact used by Superior Uniform
*/
INSERT INTO tmp_staging_fct_inventory
(record_timestamp, item_code, item_category_code, item_sku, location_code, work_area_code, module_code, load_unit_code, inventory_unit_code, inventory_batch_code, inventory_qty,
tenant, facility, source_system, min_cont_in_dms, max_cont_in_dms, condition_code, last_activity_date, last_cc_inspection_date)
SELECT
-- the QUERYDATETIME field is given to us in the Central Standard Time timezone
`${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(COALESCE(JSON_VALUE(data, '$.QUERYDATETIME'), JSON_VALUE(data, '$.LASTACTIVITYDATE')), 'America/Chicago') as record_timestamp,
-- Hard coding NULLs here because that's what Superior sends us in other facts for client code, and WMSInventoryFact is only used by SU currently
`${tenant_id}_oa_curated`.Item_Code_Generator(JSON_VALUE(data, '$.SKUSTYLE') || JSON_VALUE(data, '$.SKUSIZE'), NULL, NULL, NULL) AS item_code,
'' as item_category_code,
JSON_VALUE(data, '$.SKUSTYLE') || JSON_VALUE(data, '$.SKUSIZE') as item_sku,
UPPER(COALESCE(JSON_VALUE(data, '$.LOCATIONID'), '')) as location_code,
UPPER(COALESCE(JSON_VALUE(data, '$.ZONE'), '')) as work_area_code,
'' as module_code,
UPPER(COALESCE(JSON_VALUE(data, '$.CONTAINERID'), '')) as load_unit_code,
UPPER(COALESCE(JSON_VALUE(data, '$.CONTAINERID'), '')) as inventory_unit_code,
'' AS inventory_batch_code,
SAFE_CAST(JSON_VALUE(data, '$.QUANTITY') AS INT64) as inventory_qty,
tenant,
facility,
source_system,
JSON_VALUE(data, '$.MINCONTINDMS') as min_cont_in_dms,
JSON_VALUE(data, '$.MAXCONTINDMS') as max_cont_in_dms,
JSON_VALUE(data, '$.CONDITIONCODE') as condition_code,
`${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(JSON_VALUE(data, '$.LASTACTIVITYDATE'),  'America/Chicago') as last_activity_date,
`${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(JSON_VALUE(data, '$.LASTCCINSPECTIONDATE'),  'America/Chicago') as last_cc_inspection_date
FROM `${tenant_id}_landing.WMSInventoryFact`
WHERE
    tenant = @ltarget_tenant
    AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date);


--DIM_Item
MERGE `${tenant_id}_oa_curated.dim_item` di
USING
    (SELECT DISTINCT
        item_code,
        item_category_code,
        max(item_sku) as item_sku,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_inventory 
    group by item_code, item_category_code, tenant, facility, source_system) v
    ON
        di.item_code = v.item_code
        AND di.item_category_code = v.item_category_code
        AND di.tenant = v.tenant
        AND di.facility = v.facility
        AND di.source_system = v.source_system
WHEN MATCHED AND di.item_sku != v.item_sku THEN
    UPDATE SET item_sku = v.item_sku
WHEN NOT MATCHED THEN
    INSERT (item_uuid, item_code, item_category_code, item_sku, tenant, facility, source_system, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.item_code]
        ), v.item_code, v.item_category_code, v.item_sku, v.tenant, v.facility, v.source_system, @etl_batch_uuid);
UPDATE tmp_staging_fct_inventory tt 
SET item_uuid = d.item_uuid
FROM
    (
        select distinct
            item_uuid,
            item_code,
            item_sku,
            item_category_code,
            tenant,
            facility,
            source_system
        from
         `${tenant_id}_oa_curated.dim_item`) d
WHERE
    tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system 
    AND tt.item_code = d.item_code 
    AND tt.item_category_code = d.item_category_code;

--DIM_Location
MERGE `${tenant_id}_oa_curated.dim_location` dl
USING
    (SELECT DISTINCT
        location_code,
        max(bay) as bay,
        max(location_name) as location_name,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_inventory GROUP BY location_code, tenant, facility, source_system) v
    ON
        dl.location_code = v.location_code
        AND dl.tenant = v.tenant
        AND dl.facility = v.facility
        AND dl.source_system = v.source_system
WHEN MATCHED AND (dl.location_name <> v.location_name OR dl.bay <> v.bay) THEN
    UPDATE
        SET
            dl.location_name = COALESCE(v.location_name, dl.location_name),
            dl.bay = COALESCE(v.bay, dl.bay),
            etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
    INSERT (location_uuid, location_code, location_name, bay, tenant, facility, source_system, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.location_code]
        ), v.location_code,v.location_name, v.bay, v.tenant, v.facility, v.source_system, @etl_batch_uuid);

UPDATE tmp_staging_fct_inventory tt 
SET location_uuid = d.location_uuid
FROM
    (
        select distinct
            location_uuid,
            location_code,
            tenant,
            facility,
            source_system
        from  
        `${tenant_id}_oa_curated.dim_location`) d
WHERE
    tt.location_code = d.location_code 
    AND tt.tenant = d.tenant 
    AND d.facility = tt.facility 
    AND d.source_system = tt.source_system;

--DIM_MODULE
MERGE `${tenant_id}_oa_curated.dim_module` dm
USING (
    SELECT DISTINCT
        module_code,
        max(module_name) as module_name,
        facility,
        source_system
    FROM tmp_staging_fct_inventory
    GROUP BY module_code, facility, source_system
) v
    ON
        dm.module_code = v.module_code
        AND dm.tenant = @ltarget_tenant
        AND dm.facility = v.facility
        AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (module_uuid, module_code, module_name, tenant, facility, source_system, active_rec_ind, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.module_code]
        ), v.module_code, v.module_name, @ltarget_tenant, v.facility, v.source_system, 1, @etl_batch_uuid);

UPDATE tmp_staging_fct_inventory tt 
SET module_uuid = d.module_uuid
FROM
    (
        select distinct
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        from
         `${tenant_id}_oa_curated.dim_module`) d
WHERE
      d.module_code = tt.module_code 
      AND d.tenant = @ltarget_tenant 
      AND d.facility = tt.facility 
      AND d.source_system = tt.source_system;

--DIM_WORK_AREA
MERGE `${tenant_id}_oa_curated.dim_work_area` dwa
USING (
    SELECT DISTINCT
        work_area_code,
        max(work_area_name) as work_area_name,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_inventory
    GROUP BY work_area_code, tenant, facility, source_system
) v
    ON
        dwa.work_area_code = v.work_area_code
        AND dwa.tenant = @ltarget_tenant
        AND dwa.facility = v.facility
        AND dwa.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (work_area_uuid, work_area_code, work_area_name, tenant, facility, source_system, active_rec_ind, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.work_area_code]
        ), 
        v.work_area_code, 
        v.work_area_name, 
        @ltarget_tenant, 
        v.facility, 
        v.source_system, 
        1, 
        @etl_batch_uuid);

UPDATE tmp_staging_fct_inventory tt 
SET work_area_uuid = d.work_area_uuid
FROM
    (
        select distinct
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        from 
    `${tenant_id}_oa_curated.dim_work_area`) d
WHERE
      d.work_area_code = tt.work_area_code 
      AND d.tenant = @ltarget_tenant 
      AND d.facility = tt.facility 
      AND d.source_system = tt.source_system;


-- FINAL FCT Insert
MERGE INTO `${tenant_id}_oa_curated.fct_inventory` fi
USING (SELECT DISTINCT * FROM tmp_staging_fct_inventory) tt
ON  TIMESTAMP_TRUNC(tt.record_timestamp, DAY) = TIMESTAMP_TRUNC(fi.record_timestamp, DAY)
    AND tt.item_category_code = fi.item_category_code
    AND tt.item_code = fi.item_code
    AND tt.load_unit_code = fi.load_unit_code
    AND tt.inventory_batch_code = fi.inventory_batch_code
    AND tt.inventory_unit_code = fi.inventory_unit_code
    AND tt.base_load_unit_code = fi.base_load_unit_code
    AND tt.receiving_processing_code = fi.receiving_processing_code
    AND tt.condition_code = fi.condition_code
    AND tt.tenant = fi.tenant
    AND tt.facility = fi.facility
    AND tt.source_system = fi.source_system
WHEN NOT MATCHED THEN INSERT
(
    record_timestamp,
    item_uuid,
    location_uuid,
    item_category_code,
    item_code,
    item_name,
    item_sku,
    module_uuid,
    work_area_uuid,
    load_unit_code,
    inventory_batch_code,
    inventory_unit_code,
    base_load_unit_code,
    receiving_processing_code,
    inventory_qty,
    inventory_unassigned_qty,
    min_cont_in_dms,
    max_cont_in_dms,
    condition_code,
    last_activity_date,
    last_cc_inspection_date,
    tenant,
    facility,
    source_system,
    etl_batch_id
) VALUES (
    tt.record_timestamp,
    tt.item_uuid,
    tt.location_uuid,
    tt.item_category_code,
    tt.item_code,
    tt.item_name,
    tt.item_sku,
    tt.module_uuid,
    tt.work_area_uuid,
    tt.load_unit_code,
    tt.inventory_batch_code,
    tt.inventory_unit_code,
    tt.base_load_unit_code,
    tt.receiving_processing_code,
    tt.inventory_qty,
    tt.inventory_unassigned_qty,
    tt.min_cont_in_dms,
    tt.max_cont_in_dms,
    tt.condition_code,
    tt.last_activity_date,
    tt.last_cc_inspection_date,
    tt.tenant,
    tt.facility,
    tt.source_system,
    @etl_batch_uuid
);
