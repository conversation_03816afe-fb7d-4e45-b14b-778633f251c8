/*
declare ltarget_tenant string default '${tenant_id}';
declare etl_batch_uuid string default '2024-01-02';

declare start_query_date timestamp default timestamp('2022-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2022-09-27 02:00:00');
 */
-- Creating the temporary staging table
CREATE TEMP TABLE
    temp_task_confirmed (
        record_timestamp TIMESTAMP,
        source_location_code STRING DEFAULT NULL,
        source_work_area_code STRING DEFAULT NULL,
        source_module_code STRING DEFAULT NULL,
        destination_location_code STRING DEFAULT NULL,
        destination_work_area_code STRING DEFAULT NULL,
        destination_module_code STRING DEFAULT NULL,
        item_code STRING DEFAULT NULL,
        container_type_code STRING DEFAULT NULL,
        movement_type_code STRING DEFAULT NULL,
        transport_request_code STRING DEFAULT NULL,
        handling_unit_code STRING DEFAULT NULL,
        transport_request_type STRING DEFAULT NULL,
        tenant STRING DEFAULT NULL,
        item_category_code STRING DEFAULT NULL,
        facility STRING DEFAULT NULL,
        source_system STRING DEFAULT NULL,
        etl_batch_id STRING DEFAULT NULL,
        source_location_uuid STRING DEFAULT NULL,
        source_work_area_uuid STRING DEFAULT NULL,
        source_module_uuid STRING DEFAULT NULL,
        destination_location_uuid STRING DEFAULT NULL,
        destination_work_area_uuid STRING DEFAULT NULL,
        destination_module_uuid STRING DEFAULT NULL,
        item_uuid STRING DEFAULT NULL,
        container_type_uuid STRING DEFAULT NULL,
        movement_type_uuid STRING DEFAULT NULL
    );

-- Inserting data into the temporary staging table
INSERT INTO
    temp_task_confirmed
WITH
    raw_data AS (
        SELECT
            JSON_VALUE(data, '$.eventTime') AS record_timestamp,
            JSON_VALUE(data, '$.sourceLocationId') AS source_location_id,
            JSON_VALUE(data, '$.sourceArea') AS source_work_area_id,
            JSON_VALUE(data, '$.modUser.value') AS module_id,
            JSON_VALUE(data, '$.destinationLocationId') AS destination_location_id,
            JSON_VALUE(data, '$.destinationArea') AS destination_work_area_id,
            JSON_VALUE(data, '$.skuId') AS item_id,
            '' AS container_type_id, -- placeholder while we do not receive this
            JSON_VALUE(data, '$.movementTypeName') AS movement_type_id,
            JSON_VALUE(data, '$.transportRequestEntityKey') AS transport_request_id,
            JSON_VALUE(data, '$.simplifiedLoadUnitTypeName') AS handling_unit_id,
            JSON_VALUE(data, '$.transportRequestType') AS transport_request_type,
            @ltarget_tenant AS tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.TransportTaskConfirmationFact`
        WHERE
            ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    normalized_data AS (
        SELECT
            COALESCE(TIMESTAMP(record_timestamp), TIMESTAMP(NULL)) AS record_timestamp,
            COALESCE(CAST(source_location_id AS STRING), '') AS source_location_code,
            COALESCE(CAST(source_work_area_id AS STRING), '') AS source_work_area_code,
            COALESCE(CAST(module_id AS STRING), '') AS source_module_code,
            COALESCE(CAST(destination_location_id AS STRING), '') AS destination_location_code,
            COALESCE(CAST(destination_work_area_id AS STRING), '') AS destination_work_area_code,
            '' AS destination_module_code,
            COALESCE(CAST(item_id AS STRING), '') AS item_code,
            COALESCE(CAST(container_type_id AS STRING), '') AS container_type_code,
            COALESCE(CAST(movement_type_id AS STRING), '') AS movement_type_code,
            COALESCE(CAST(transport_request_id AS STRING), '') AS transport_request_code,
            COALESCE(CAST(handling_unit_id AS STRING), '') AS handling_unit_code,
            COALESCE(transport_request_type, '') AS transport_request_type,
            '' AS item_category_code,
            facility,
            source_system,
            tenant,
            @etl_batch_uuid AS etl_batch_id,
            '' AS source_location_uuid,
            '' AS source_work_area_uuid,
            '' AS source_module_uuid,
            '' AS destination_location_uuid,
            '' AS destination_work_area_uuid,
            '' AS destination_module_uuid,
            '' AS item_uuid,
            '' AS container_type_uuid,
            '' AS movement_type_uuid
        FROM
            raw_data
    )
SELECT
    *
FROM
    normalized_data;

-- Merging into Dimension Tables and updating UUIDs in temporary table
-- DIM_Location
MERGE
    `${tenant_id}_oa_curated.dim_location` dsl USING (
        SELECT DISTINCT
            source_location_code,
            tenant,
            facility,
            source_system
        FROM
            temp_task_confirmed
    ) src ON dsl.location_code = src.source_location_code
    AND dsl.tenant = src.tenant
    AND dsl.facility = src.facility
    AND dsl.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        location_uuid,
        location_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.source_location_code]
        ),
        src.source_location_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

UPDATE temp_task_confirmed tt
SET
    tt.source_location_uuid = dsl.location_uuid
FROM
    (
        select distinct
            location_uuid,
            location_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_location`
    ) dsl
WHERE
    tt.source_location_code = dsl.location_code
    AND tt.tenant = dsl.tenant
    AND tt.facility = dsl.facility
    AND tt.source_system = dsl.source_system;

-- DIM_Work_Area
MERGE
    `${tenant_id}_oa_curated.dim_work_area` dswa USING (
        SELECT DISTINCT
            source_work_area_code,
            tenant,
            facility,
            source_system
        FROM
            temp_task_confirmed
    ) src ON dswa.work_area_code = src.source_work_area_code
    AND dswa.tenant = src.tenant
    AND dswa.source_system = src.source_system
    AND dswa.facility = src.facility
WHEN NOT MATCHED THEN
INSERT
    (
        work_area_uuid,
        work_area_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.source_work_area_code]
        ),
        src.source_work_area_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

UPDATE temp_task_confirmed tt
SET
    tt.source_work_area_uuid = dswa.work_area_uuid
FROM
    (
        select distinct
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_work_area`
    ) dswa
WHERE
    tt.source_work_area_code = dswa.work_area_code
    AND tt.tenant = dswa.tenant
    AND tt.facility = dswa.facility
    AND tt.source_system = dswa.facility;

-- DIM_Module
MERGE
    `${tenant_id}_oa_curated.dim_module` dsm USING (
        SELECT DISTINCT
            source_module_code,
            tenant,
            facility,
            source_system
        FROM
            temp_task_confirmed
    ) src ON dsm.module_code = src.source_module_code
    AND dsm.tenant = src.tenant
    AND dsm.facility = src.facility
    AND dsm.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        module_uuid,
        module_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.source_module_code]
        ),
        src.source_module_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

UPDATE temp_task_confirmed tt
SET
    tt.source_module_uuid = dsm.module_uuid
FROM
    (
        select distinct
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_module`
    ) dsm
WHERE
    tt.source_module_code = dsm.module_code
    AND tt.tenant = dsm.tenant
    AND tt.facility = dsm.facility
    AND tt.source_system = dsm.source_system;

-- DIM_Location
MERGE
    `${tenant_id}_oa_curated.dim_location` ddl USING (
        SELECT DISTINCT
            destination_location_code,
            tenant,
            facility,
            source_system
        FROM
            temp_task_confirmed
    ) src ON ddl.location_code = src.destination_location_code
    AND ddl.tenant = src.tenant
    AND ddl.facility = src.facility
    AND ddl.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        location_uuid,
        location_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.destination_location_code]
        ),
        src.destination_location_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

UPDATE temp_task_confirmed tt
SET
    tt.destination_location_uuid = ddl.location_uuid
FROM
    (
        select distinct
            location_uuid,
            location_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_location`
    ) ddl
WHERE
    tt.destination_location_code = ddl.location_code
    AND tt.tenant = ddl.tenant
    AND tt.facility = ddl.facility
    AND tt.source_system = ddl.source_system;

-- DIM_Work_Area
MERGE
    `${tenant_id}_oa_curated.dim_work_area` ddwa USING (
        SELECT DISTINCT
            destination_work_area_code,
            tenant,
            facility,
            source_system
        FROM
            temp_task_confirmed
    ) src ON ddwa.work_area_code = src.destination_work_area_code
    AND ddwa.tenant = src.tenant
    AND ddwa.facility = src.facility
    AND ddwa.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        work_area_uuid,
        work_area_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.destination_work_area_code]
        ),
        src.destination_work_area_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

UPDATE temp_task_confirmed tt
SET
    tt.destination_work_area_uuid = ddwa.work_area_uuid
FROM
    (
        select distinct
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_work_area`
    ) ddwa
WHERE
    tt.destination_work_area_code = ddwa.work_area_code
    AND tt.tenant = ddwa.tenant
    AND tt.facility = ddwa.facility
    AND tt.source_system = ddwa.source_system;

-- DIM_Module
MERGE
    `${tenant_id}_oa_curated.dim_module` ddm USING (
        SELECT DISTINCT
            destination_module_code,
            tenant,
            facility,
            source_system
        FROM
            temp_task_confirmed
    ) src ON ddm.module_code = src.destination_module_code
    AND ddm.tenant = src.tenant
    AND ddm.facility = src.facility
    AND ddm.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        module_uuid,
        module_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.destination_module_code]
        ),
        src.destination_module_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

UPDATE temp_task_confirmed tt
SET
    tt.destination_module_uuid = ddm.module_uuid
FROM
    (
        select distinct
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_module`
    ) ddm
WHERE
    tt.destination_module_code = ddm.module_code
    AND tt.tenant = ddm.tenant
    AND tt.facility = ddm.facility
    AND tt.source_system = ddm.source_system;

-- DIM_Container_Type
MERGE
    `${tenant_id}_oa_curated.dim_container_type` dct USING (
        SELECT DISTINCT
            container_type_code,
            tenant,
            facility,
            source_system
        FROM
            temp_task_confirmed
    ) src ON dct.container_type_code = src.container_type_code
    AND dct.tenant = src.tenant
    AND dct.facility = src.facility
    AND dct.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        container_type_uuid,
        container_type_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.container_type_code]
        ),
        src.container_type_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

UPDATE temp_task_confirmed tt
SET
    tt.container_type_uuid = dct.container_type_uuid
FROM
    (
        select distinct
            container_type_uuid,
            container_type_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_container_type`
    ) dct
WHERE
    tt.container_type_code = dct.container_type_code
    AND tt.tenant = dct.tenant
    AND tt.facility = dct.facility
    AND tt.source_system = dct.source_system;

--DIM_Item
MERGE
    `${tenant_id}_oa_curated.dim_item` di USING (
        SELECT DISTINCT
            item_code,
            item_category_code,
            tenant,
            facility,
            source_system
        FROM
            temp_task_confirmed
        group by
            item_code,
            item_category_code,
            tenant,
            facility,
            source_system
    ) v ON di.item_code = v.item_code
    AND di.item_category_code = v.item_category_code
    AND di.tenant = v.tenant
    AND di.facility = v.facility
    AND di.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        item_uuid,
        item_code,
        item_category_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.item_code]
        ),
        v.item_code,
        v.item_category_code,
        v.tenant,
        v.facility,
        v.source_system,
        @etl_batch_uuid
    );

UPDATE temp_task_confirmed tt
SET
    item_uuid = d.item_uuid
FROM
    (
        select distinct
            item_uuid,
            item_code,
            item_category_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_item`
    ) d
WHERE
    tt.tenant = d.tenant
    AND tt.item_code = d.item_code
    AND tt.item_category_code = d.item_category_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- FINAL FCT Insert
INSERT INTO
    `${tenant_id}_oa_curated.fct_transport` (
        record_timestamp,
        source_location_uuid,
        source_work_area_uuid,
        source_module_uuid,
        destination_location_uuid,
        destination_work_area_uuid,
        destination_module_uuid,
        item_uuid,
        container_type_uuid,
        movement_type_uuid,
        transport_request_code,
        handling_unit_code,
        transport_request_type,
        facility,
        source_system,
        etl_batch_id
    )
SELECT
    record_timestamp,
    source_location_uuid,
    source_work_area_uuid,
    source_module_uuid,
    destination_location_uuid,
    destination_work_area_uuid,
    destination_module_uuid,
    item_uuid,
    container_type_uuid,
    movement_type_uuid,
    transport_request_code,
    handling_unit_code,
    transport_request_type,
    facility,
    source_system,
    @etl_batch_uuid
FROM
    temp_task_confirmed;