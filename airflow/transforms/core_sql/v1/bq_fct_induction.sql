/*
 **Set variables for running outside of DAG in BQ directly**
declare tenant_id string default 'your_tenant_id';
declare etl_batch_uuid string default '2020-12-12';

declare start_query_date timestamp default timestamp('2022-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2024-09-27 02:00:00');
 */
-- Creating the temporary staging table
CREATE TEMP TABLE
    tmp_staging_fct_induction (
        record_timestamp TIMESTAMP,
        location_code STRING,
        work_area_code STRING,
        module_code STRING,
        container_type_code STRING,
        load_unit_code STRING,
        height FLOAT64,
        weight FLOAT64,
        contour_details STRING,
        source_system STRING,
        tenant STRING,
        facility STRING,
        location_uuid STRING DEFAULT NULL,
        work_area_uuid STRING DEFAULT NULL,
        module_uuid STRING DEFAULT NULL,
        container_type_uuid STRING DEFAULT NULL
    );

-- Inserting data into the temporary staging table
INSERT INTO
    tmp_staging_fct_induction
WITH
    raw_data AS (
        SELECT
            COALESCE(JSON_VALUE(data, '$.eventTime'), JSON_VALUE(data, '$.eventDate')) AS record_timestamp,
            JSON_VALUE(data, '$.locationId') AS location_id,
            JSON_VALUE(data, '$.workAreaId') AS work_area_id,
            JSON_VALUE(data, '$.moduleId') AS module_id,
            JSON_VALUE(data, '$.containerTypeId') AS container_type_id,
            JSON_VALUE(data, '$.loadUnitId.value') AS load_unit_code,
            JSON_VALUE(data, '$.height') AS height,
            JSON_VALUE(data, '$.weight') AS weight,
            JSON_VALUE(data, '$.contourInfo.value') AS contour_details,
            @ltarget_tenant AS tenant,
            facility,
            source_system,
            ingestion_date
        FROM
            `${tenant_id}_landing.IPointMovementFact`
        WHERE
            ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    normalized_data AS (
        SELECT
            `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(record_timestamp, NULL) AS record_timestamp,
            COALESCE(CAST(location_id AS STRING), '') AS location_code,
            COALESCE(CAST(work_area_id AS STRING), '') AS work_area_code,
            COALESCE(CAST(module_id AS STRING), '') AS module_code,
            COALESCE(CAST(container_type_id AS STRING), '') AS container_type_code,
            COALESCE(CAST(load_unit_code AS STRING), '') AS load_unit_code,
            COALESCE(CAST(height AS FLOAT64), 0.0) AS height,
            COALESCE(CAST(weight AS FLOAT64), 0.0) AS weight,
            COALESCE(CAST(contour_details AS STRING), '') AS contour_details,
            source_system,
            tenant,
            facility
        FROM
            raw_data
    )
SELECT DISTINCT
    record_timestamp,
    location_code,
    work_area_code,
    module_code,
    container_type_code,
    load_unit_code,
    height,
    weight,
    contour_details,
    source_system,
    tenant,
    facility,
    CAST(NULL AS STRING) AS location_uuid,
    CAST(NULL AS STRING) AS work_area_uuid,
    CAST(NULL AS STRING) AS module_uuid,
    CAST(NULL AS STRING) AS container_type_uuid
FROM
    normalized_data;

-- Merging into Dimension Tables
-- DIM_Module
MERGE
    `${tenant_id}_oa_curated.dim_module` dm USING (
        SELECT DISTINCT
            module_code,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_fct_induction
    ) src ON dm.module_code = src.module_code
    AND dm.facility = src.facility
    AND dm.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        module_uuid,
        module_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.module_code]
        ),
        src.module_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

-- Update tmp_staging_fct_induction with module_uuid
UPDATE tmp_staging_fct_induction tt
SET
    tt.module_uuid = dm.module_uuid
FROM
    (
        select distinct
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_module`
    ) dm
WHERE
    tt.module_code = dm.module_code
    AND tt.tenant = dm.tenant
    AND tt.facility = dm.facility
    AND tt.source_system = dm.source_system;

-- DIM_Location
MERGE
    `${tenant_id}_oa_curated.dim_location` dl USING (
        SELECT DISTINCT
            location_code,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_fct_induction
    ) src ON dl.location_code = src.location_code
    AND dl.tenant = src.tenant
    AND dl.facility = src.facility
    AND dl.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        location_uuid,
        location_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.location_code]
        ),
        src.location_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

-- Update tmp_staging_fct_induction with location_uuid
UPDATE tmp_staging_fct_induction tt
SET
    tt.location_uuid = dl.location_uuid
FROM
    (
        select distinct
            location_uuid,
            location_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_location`
    ) dl
WHERE
    tt.location_code = dl.location_code
    AND tt.tenant = dl.tenant
    AND tt.facility = dl.facility
    AND tt.source_system = dl.source_system;

-- DIM_Work_Area
MERGE
    `${tenant_id}_oa_curated.dim_work_area` dwa USING (
        SELECT DISTINCT
            work_area_code,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_fct_induction
    ) src ON dwa.work_area_code = src.work_area_code
    AND dwa.facility = src.facility
    AND dwa.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        work_area_uuid,
        work_area_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.work_area_code]
        ),
        src.work_area_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

-- Update tmp_staging_fct_induction with work_area_uuid
UPDATE tmp_staging_fct_induction tt
SET
    tt.work_area_uuid = dwa.work_area_uuid
FROM
    (
        select distinct
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_work_area`
    ) dwa
WHERE
    tt.work_area_code = dwa.work_area_code
    AND tt.tenant = dwa.tenant
    AND tt.facility = dwa.facility
    AND tt.source_system = dwa.source_system;

-- DIM_Container_Type
MERGE
    `${tenant_id}_oa_curated.dim_container_type` dct USING (
        SELECT DISTINCT
            container_type_code,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_fct_induction
    ) src ON dct.container_type_code = src.container_type_code
    AND dct.tenant = src.tenant
    AND dct.facility = src.facility
    AND dct.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        container_type_uuid,
        container_type_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [src.tenant,
            src.facility,
            src.source_system,
            src.container_type_code]
        ),
        src.container_type_code,
        src.tenant,
        src.facility,
        src.source_system,
        @etl_batch_uuid
    );

-- Update tmp_staging_fct_induction with container_type_uuid
UPDATE tmp_staging_fct_induction tt
SET
    tt.container_type_uuid = dct.container_type_uuid
FROM
    (
        select distinct
            container_type_uuid,
            container_type_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_container_type`
    ) dct
WHERE
    tt.container_type_code = dct.container_type_code
    AND tt.tenant = dct.tenant
    AND tt.facility = dct.facility
    AND tt.source_system = dct.source_system;

-- FINAL FCT Insert
INSERT INTO
    `${tenant_id}_oa_curated.fct_induction` (
        record_timestamp,
        location_uuid,
        work_area_uuid,
        module_uuid,
        container_type_uuid,
        load_unit_code,
        height,
        weight,
        contour_details,
        source_system,
        tenant,
        facility,
        etl_batch_id
    )
SELECT DISTINCT
    src.record_timestamp,
    src.location_uuid,
    src.work_area_uuid,
    src.module_uuid,
    src.container_type_uuid,
    src.load_unit_code,
    src.height,
    src.weight,
    src.contour_details,
    src.source_system,
    src.tenant,
    src.facility,
    @etl_batch_uuid
FROM
    tmp_staging_fct_induction src;