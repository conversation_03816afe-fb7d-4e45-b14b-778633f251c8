-- DECLAR<PERSON> ltarget_tenant STRING DEFAULT 'test tenant';
-- DECLARE etl_batch_uuid STRING DEFAULT '2020-12-12';
-- DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2023-09-27 00:00:00');
-- DECLARE end_query_date TIM<PERSON><PERSON><PERSON> DEFAULT TIMESTAMP('2024-09-27 02:00:00');
 
-- Creating the temporary staging table
CREATE TEMP TABLE
    tmp_staging_facility_order (
        record_timestamp TIMESTAMP NOT NULL,
        facility_order_code STRING NOT NULL,
        subsystem_code STRING NOT NULL,
        subsystem_category STRING DEFAULT 'SCADA',
        facility_order_event STRING NOT NULL,
        facility_order_owner STRING,
        reason_code STRING NOT NULL,
        wave_code STRING NOT NULL,
        event_detail STRING,
        tenant STRING,
        source_system STRING,
        facility STRING,
        etl_batch_id STRING,
        subsystem_uuid STRING DEFAULT '',
        facility_order_uuid STRING DEFAULT '',
        reason_uuid STRING DEFAULT '',
        wave_uuid STRING DEFAULT ''
    );

-- Inserting data into the temporary staging table
INSERT INTO
    tmp_staging_facility_order
WITH
    raw_data_facility_order AS (
        SELECT
            JSON_VALUE(data, '$.eventDateTime') AS record_timestamp,
            JSON_VALUE(data, '$.facilityOrderId') AS facility_order_id,
            JSON_VALUE(data, '$.subsystemId') AS subsystem_id,
            'SCADA' AS subsystem_category,
            JSON_VALUE(data, '$.event') AS facility_order_event,
            JSON_VALUE(data, '$.ownerId') AS facility_order_owner,
            JSON_VALUE(data, '$.reason') AS reason_id,
            JSON_VALUE(data, '$.waveId') AS wave_id,
            JSON_VALUE(data, '$.jsonData') AS event_detail,
            source_system,
            facility,
            CAST(NULL AS STRING) AS etl_batch_id
        FROM
            `${tenant_id}_landing.FacilityOrderFact`
        WHERE
            ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    normalized_data_facility_order AS (
        SELECT
            COALESCE(TIMESTAMP(record_timestamp), TIMESTAMP(NULL)) AS record_timestamp,
            COALESCE(facility_order_id, '') AS facility_order_code,
            COALESCE(subsystem_id, '') AS subsystem_code,
            'SCADA' AS subsystem_category,
            COALESCE(facility_order_event, '') AS facility_order_event,
            COALESCE(facility_order_owner, '') AS facility_order_owner,
            COALESCE(reason_id, '') AS reason_code,
            COALESCE(wave_id, '') AS wave_code,
            COALESCE(event_detail, '') AS event_detail,
            @ltarget_tenant AS tenant,
            'SCADA' AS source_system,
            facility,
            @etl_batch_uuid AS etl_batch_id,
            '' AS subsystem_uuid,
            '' AS facility_order_uuid,
            '' AS reason_uuid,
            '' AS wave_uuid
        FROM
            raw_data_facility_order
    )
SELECT
    *
FROM
    normalized_data_facility_order;

-- Merging into Dimension Tables
--DIM_Subsystem 
MERGE
    `${tenant_id}_oa_curated.dim_subsystem` ds USING (
        SELECT DISTINCT
            subsystem_code,
            subsystem_category,
            facility,
            source_system
        FROM
            tmp_staging_facility_order
        GROUP BY
            subsystem_code,
            subsystem_category,
            facility,
            source_system
    ) v ON ds.subsystem_code = v.subsystem_code
    AND ds.subsystem_category = v.subsystem_category
    AND ds.tenant = @ltarget_tenant
    AND ds.facility = v.facility
    AND ds.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        subsystem_uuid,
        subsystem_code,
        subsystem_category,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.subsystem_code,
            v.subsystem_category]
        ),
        v.subsystem_code,
        v.subsystem_category,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        @etl_batch_uuid
    );

UPDATE tmp_staging_facility_order tt
SET
    subsystem_uuid = d.subsystem_uuid
FROM
    (
        select distinct
            subsystem_uuid,
            subsystem_code,
            subsystem_category,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_subsystem`
    ) d
WHERE
    tt.subsystem_code = d.subsystem_code
    AND tt.subsystem_category = d.subsystem_category
    and d.tenant = @ltarget_tenant
    and d.facility = tt.facility
    and d.source_system = tt.source_system;

-- dim_facility_order
MERGE INTO 
    `${tenant_id}_oa_curated.dim_facility_order` dm USING (
        SELECT DISTINCT
            facility_order_code,
            facility_order_owner,
            facility_order_event AS facility_order_name,
            subsystem_code,
            source_system,
            facility,
            tenant
        FROM
            tmp_staging_facility_order
    ) src ON dm.facility_order_code = src.facility_order_code
    AND dm.tenant = src.tenant
    AND dm.facility = src.facility
    AND dm.source_system = src.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        facility_order_uuid,
        facility_order_code,
        facility_order_name,
        facility_order_owner,
        source_system,
        tenant,
        facility
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            src.facility,
            src.source_system,
            src.facility_order_code]
        ),
        src.facility_order_code,
        src.facility_order_name,
        src.facility_order_owner,
        src.source_system,
        src.tenant,
        src.facility
    );

UPDATE tmp_staging_facility_order tt
SET
    facility_order_uuid = dfo.facility_order_uuid
FROM
    (
        SELECT DISTINCT
            facility_order_uuid,
            facility_order_code,
            tenant,
            facility,
            source_system
        FROM `${tenant_id}_oa_curated.dim_facility_order`
    ) dfo
WHERE
    tt.facility_order_code = dfo.facility_order_code
    AND tt.tenant = dfo.tenant
    AND tt.facility = dfo.facility
    AND tt.source_system = dfo.source_system;

--DIM_Reason
MERGE
    `${tenant_id}_oa_curated.dim_reason` dr USING (
        SELECT DISTINCT
            reason_code,
            facility,
            source_system
        FROM
            tmp_staging_facility_order
        group by
            reason_code,
            facility,
            source_system
    ) v ON dr.reason_code = v.reason_code
    AND dr.tenant = @ltarget_tenant
    AND dr.facility = v.facility
    AND dr.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        reason_uuid,
        reason_code,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.reason_code]
        ),
        v.reason_code,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_facility_order tt
SET
    reason_uuid = d.reason_uuid
FROM
    (
        select distinct
            reason_uuid,
            reason_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_reason`
    ) d
WHERE
    tt.tenant = d.tenant
    AND tt.reason_code = d.reason_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- dim_wave merge
MERGE
    `${tenant_id}_oa_curated.dim_wave` DW USING (
        SELECT DISTINCT
            wave_code,
            tenant,
            facility,
            source_system,
            MAX(record_timestamp) AS record_timestamp,
        FROM
            tmp_staging_facility_order
        GROUP BY
            wave_code,
            tenant,
            facility,
            source_system
    ) AS v ON DW.wave_code = v.wave_code
    AND DW.tenant = v.tenant
    AND DW.facility = v.facility
    AND DW.source_system = v.source_system
WHEN MATCHED THEN
UPDATE SET
    wave_created_date_time = COALESCE(v.record_timestamp, DW.wave_created_date_time),
    wave_status = COALESCE('', DW.wave_status), -- Placeholder, consider providing a value or remove
    wave_priority = COALESCE('', DW.wave_priority), -- Placeholder, consider providing a value or remove
    wave_item_count = COALESCE(SAFE_CAST(NULL AS INT64), DW.wave_item_count),
    etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
    (
        wave_uuid,
        wave_code,
        wave_name,
        wave_status,
        wave_priority,
        wave_active_date_time,
        wave_closed_date_time,
        wave_item_count,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.wave_code]
        ),
        v.wave_code,
        v.wave_code, -- Assuming wave_code can be used as wave_name if not available
        '', -- Placeholder for wave_status, provide a default value if applicable
        '', -- Placeholder for wave_priority, provide a default value if applicable
        v.record_timestamp,
        SAFE_CAST(NULL AS TIMESTAMP), -- Assuming placeholder for wave_closed_date_time
        SAFE_CAST(NULL AS INT64), -- Assuming placeholder for wave_item_count
        v.tenant,
        v.facility,
        v.source_system,
        1, -- Assuming new records should be active
        @etl_batch_uuid
    );

-- Update tmp_staging_facility_order with wave_uuid
UPDATE tmp_staging_facility_order TSPO
SET
    TSPO.wave_uuid = DW.wave_uuid
FROM
    (
        select distinct
            wave_uuid,
            wave_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_wave`
    ) DW
WHERE
    TSPO.wave_code = DW.wave_code
    AND TSPO.tenant = DW.tenant
    AND TSPO.facility = DW.facility
    AND TSPO.source_system = DW.source_system;

-- Insert temp table into it's fact table
INSERT INTO `${tenant_id}_oa_curated.fct_facility_order`
(
    record_timestamp,
    facility_order_uuid,
    subsystem_uuid,
    facility_order_event,
    reason_uuid,
    wave_uuid,
    event_detail,
    etl_batch_id,
    tenant,
    facility,
    source_system
)
SELECT
    record_timestamp,
    facility_order_uuid,
    subsystem_uuid,
    facility_order_event,
    reason_uuid,
    wave_uuid,
    event_detail,
    etl_batch_id,
    @ltarget_tenant AS tenant,
    facility,
    source_system,
FROM tmp_staging_facility_order;