/*
DECLARE ltarget_tenant string default '${tenant_id}';
DECLARE etl_batch_uuid STRING DEFAULT FORMAT_DATE('%Y-%m-%d', CURRENT_DATE());
DECLARE start_query_date timestamp default timestamp('2023-11-02');
DECLARE end_query_date timestamp default timestamp('2023-11-03');
*/
-- Set min timestamp here from pick order fact for future filtering
DECLARE eventTimeVar default (
  SELECT
    MIN(TIMESTAMP(JSON_VALUE(data, '$.eventTime')))
  FROM
    `${tenant_id}_landing.PickOrderFact`
);

-- Determine the date threshold once and use it in the main query to avoid a costly subquery
DECLARE date_threshold TIMESTAMP default (
  SELECT
    TIMESTAMP_ADD(MIN(eventTimeVar), INTERVAL -10 DAY)
  FROM
    `${tenant_id}_landing.PickOrderFact`
);

-- Create a temp table to insert into and merge into other tables (dims and facts)
CREATE TEMP TABLE tmp_staging_pick_order_complete (
    record_timestamp TIMESTAMP,
    pick_order_code STRING,
    customer_order_code STRING,
    operator_code STRING,
    location_code STRING,
    wave_code STRING,
    zone_code STRING,
    order_released_date_time TIMESTAMP,
    order_assigned_date_time TIMESTAMP,
    order_assigned_location_code STRING,
    order_put_start_date_time TIMESTAMP,
    order_pack_start_date_time TIMESTAMP,
    overflow_location_count STRING,
    order_put_lines_complete_date_time TIMESTAMP,
    order_put_wall_pack_complete_date_time TIMESTAMP,
    order_put_wall_pack_operator_code STRING,
    order_put_wall_pack_location_code STRING,
    order_put_wall_pack_zone_code STRING,
    order_lines_complete_date_time TIMESTAMP,
    order_pack_complete_date_time TIMESTAMP,
    pick_order_complete_ind INT64,
    order_complete_date_time TIMESTAMP,
    order_cancelled_date_time TIMESTAMP,
    order_cancelled_ind INT64,
    order_line_count STRING,
    order_total_qty STRING,
    tenant STRING,
    facility STRING,
    source_system STRING,
    order_type STRING,
    pick_order_category STRING,
    pick_order_uuid STRING,
    customer_order_uuid STRING,
    operator_uuid STRING,
    location_uuid STRING,
    wave_uuid STRING,
    zone_uuid STRING
);

-- Insert and list the columns in correct order
INSERT INTO
  tmp_staging_pick_order_complete (
    record_timestamp,
    pick_order_code,
    customer_order_code,
    operator_code,
    location_code,
    wave_code,
    zone_code,
    order_released_date_time,
    order_assigned_date_time,
    order_assigned_location_code,
    order_put_start_date_time,
    order_pack_start_date_time,
    overflow_location_count,
    order_put_lines_complete_date_time,
    order_put_wall_pack_complete_date_time,
    order_put_wall_pack_operator_code,
    order_put_wall_pack_location_code,
    order_put_wall_pack_zone_code,
    order_lines_complete_date_time,
    order_pack_complete_date_time,
    pick_order_complete_ind,
    order_complete_date_time,
    tenant,
    facility,
    source_system,
    order_cancelled_date_time,
    order_cancelled_ind,
    order_line_count,
    order_total_qty,
    order_type,
    pick_order_category,
    pick_order_uuid,
    customer_order_uuid,
    operator_uuid,
    location_uuid,
    wave_uuid,
    zone_uuid
  )
  -- CTE's translated from SQL
  -- raw_pick_order_events CTE
WITH
  raw_pick_order_events AS (
    SELECT DISTINCT
      COALESCE(
        JSON_VALUE(data, '$.userPk'),
        JSON_VALUE(data, '$.userName'),
        ''
      ) AS user_name,
      JSON_VALUE(data, '$.locationPk') AS location_pk,
      JSON_VALUE(data, '$.zonePk') AS zone_pk,
      JSON_VALUE(data, '$.groupPk') AS group_pk,
      SAFE_CAST(JSON_VALUE(data, '$.eventTime') AS TIMESTAMP) AS event_time,
      JSON_VALUE(data, '$.pickOrderPk') AS pick_order_pk,
      JSON_VALUE(data, '$.tenantName') AS tenant_name,
      JSON_VALUE(data, '$.wavePk') AS wave_pk,
      JSON_VALUE(data, '$.orderPk') AS order_pk,
      JSON_VALUE(data, '$.putWallPk') AS put_wall_pk,
      JSON_VALUE(data, '$.orderCategory') AS order_category,
      CASE
        WHEN UPPER(JSON_VALUE(data, '$.pickOrderType')) IN ('INTERNAL', 'CUSTOMER', 'STANDARD') THEN 'Standard'
        WHEN JSON_VALUE(data, '$.pickOrderType') IS NULL THEN 'Standard'
        ELSE JSON_VALUE(data, '$.pickOrderType')
      END AS pick_order_type,
      CASE
        WHEN UPPER(JSON_VALUE(data, '$.event')) = 'COMPLETED' THEN 'complete'
        ELSE JSON_VALUE(data, '$.event')
      END AS action_type,
      facility,
      source_system
    FROM
      `${tenant_id}_landing.PickOrderFact`
    WHERE
      tenant = @ltarget_tenant
      AND JSON_VALUE(data, '$.pickOrderPk') IS NOT NULL
      AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  -- mapped_pick_order_events CTE
  mapped_pick_order_events AS (
    SELECT
      fpo.pick_order_pk,
      tenant_name,
      ending_event,
      pick_order_type,
      NULL AS order_category,
      wave_pk,
      order_pk,
      put_wall_pk,
      fpo.event_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Created' THEN fpo.event_time
      END AS order_created_date_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Released' THEN fpo.event_time
      END AS order_released_date_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Activated' THEN fpo.event_time
      END AS order_activated_date_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Assigned' THEN fpo.event_time
      END AS order_assigned_date_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Assigned'
        AND location_pk IS NOT NULL THEN location_pk
      END AS location_order_assigned,
      CASE
        WHEN analytics_conformed_state = 'Order_Put_Lines_Complete' THEN fpo.event_time
      END AS order_put_lines_complete_date_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Lines_Complete' THEN fpo.event_time
      END AS order_lines_complete_date_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Complete' THEN fpo.event_time
      END AS order_complete_date_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Pack_Assigned' THEN fpo.event_time
      END AS order_pack_assigned_date_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Put_Wall_Pack_Complete' THEN fpo.event_time
      END AS order_put_wall_pack_complete_date_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Put_Wall_Pack_Complete'
        AND location_pk IS NOT NULL THEN location_pk
      END AS location_put_wall_pack,
      CASE
        WHEN analytics_conformed_state = 'Order_Put_Wall_Pack_Complete'
        AND zone_pk IS NOT NULL THEN zone_pk
      END AS zone_put_wall_pack,
      CASE
        WHEN analytics_conformed_state = 'Order_Put_Wall_Pack_Complete'
        AND group_pk IS NOT NULL THEN group_pk
      END AS group_put_wall_pack,
      CASE
        WHEN analytics_conformed_state = 'Order_Put_Wall_Pack_Complete'
        AND user_name IS NOT NULL THEN user_name
      END AS user_name_put_wall_pack,
      CASE
        WHEN analytics_conformed_state = 'Order_Pack_Complete' THEN fpo.event_time
      END AS order_pack_complete_date_time,
      CASE
        WHEN analytics_conformed_state = 'Order_Pack_Complete'
        AND location_pk IS NOT NULL THEN location_pk
      END AS location_pack,
      CASE
        WHEN analytics_conformed_state = 'Order_Pack_Complete'
        AND zone_pk IS NOT NULL THEN zone_pk
      END AS zone_pack,
      CASE
        WHEN analytics_conformed_state = 'Order_Pack_Complete'
        AND group_pk IS NOT NULL THEN group_pk
      END AS group_pack,
      CASE
        WHEN analytics_conformed_state = 'Order_Pack_Complete'
        AND user_name IS NOT NULL THEN user_name
      END AS user_name_pack,
      CASE
        WHEN analytics_conformed_state = 'Order_Cancelled' THEN fpo.event_time
      END AS order_cancelled_date_time,
      facility,
      source_system
    FROM
      raw_pick_order_events AS fpo
      INNER JOIN (
        SELECT
          pick_order_pk,
          action_type,
          MAX(event_time) AS event_time
        FROM
          raw_pick_order_events
        GROUP BY
          pick_order_pk,
          action_type
      ) AS t ON fpo.pick_order_pk = t.pick_order_pk
      AND fpo.action_type = t.action_type
      INNER JOIN `${tenant_id}_oa_curated.dim_pick_order_event_map` AS poe ON fpo.pick_order_type = poe.order_type
      AND fpo.action_type = poe.source_state
  ),
  agg_pick_order_events AS (
    SELECT
      *
    FROM
      (
        SELECT
          ROW_NUMBER() OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              event_time DESC
          ) as rn,
          pick_order_pk,
          FIRST_VALUE(wave_pk) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(wave_pk IS NOT NULL, 1, 2),
              event_time DESC
          ) AS wave_pk,
          FIRST_VALUE(put_wall_pk) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(put_wall_pk IS NOT NULL, 1, 2),
              event_time DESC
          ) AS put_wall_pk,
          FIRST_VALUE(location_order_assigned) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(location_order_assigned IS NOT NULL, 1, 2),
              event_time DESC
          ) AS location_order_assigned,
          FIRST_VALUE(location_put_wall_pack) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(location_put_wall_pack IS NOT NULL, 1, 2),
              event_time DESC
          ) AS location_put_wall_pack,
          FIRST_VALUE(zone_put_wall_pack) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(zone_put_wall_pack IS NOT NULL, 1, 2),
              event_time DESC
          ) AS zone_put_wall_pack,
          FIRST_VALUE(group_put_wall_pack) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(group_put_wall_pack IS NOT NULL, 1, 2),
              event_time DESC
          ) AS group_put_wall_pack,
          FIRST_VALUE(location_pack) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(location_pack IS NOT NULL, 1, 2),
              event_time DESC
          ) AS location_pack,
          FIRST_VALUE(zone_pack) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(zone_pack IS NOT NULL, 1, 2),
              event_time DESC
          ) AS zone_pack,
          FIRST_VALUE(user_name_put_wall_pack) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(user_name_put_wall_pack IS NOT NULL, 1, 2),
              event_time DESC
          ) AS user_name_put_wall_pack,
          FIRST_VALUE(user_name_pack) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(user_name_pack IS NOT NULL, 1, 2),
              event_time DESC
          ) AS user_name_pack,
          FIRST_VALUE(tenant_name) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(tenant_name IS NOT NULL, 1, 2),
              event_time DESC
          ) AS tenant_name,
          FIRST_VALUE(pick_order_type) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(pick_order_type IS NOT NULL, 1, 2),
              event_time DESC
          ) AS order_type,
          order_category,
          FIRST_VALUE(ending_event) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(ending_event IS NOT NULL, 1, 2),
              event_time DESC
          ) AS ending_event,
          FIRST_VALUE(order_pk) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(order_pk IS NOT NULL, 1, 2),
              event_time DESC
          ) AS order_pk,
          FIRST_VALUE(group_pack) OVER (
            PARTITION BY
              pick_order_pk
            ORDER BY
              IF(group_pack IS NOT NULL, 1, 2),
              event_time DESC
          ) AS group_pack,
          t.order_activated_date_time AS order_activated_date_time,
          t.order_complete_date_time AS order_complete_date_time,
          t.order_assigned_date_time AS order_assigned_date_time,
          t.order_released_date_time AS order_released_date_time,
          t.order_lines_complete_date_time AS order_lines_complete_date_time,
          t.order_put_wall_pack_complete_date_time AS order_put_wall_pack_complete_date_time,
          t.order_put_lines_complete_date_time AS order_put_lines_complete_date_time,
          t.order_pack_complete_date_time AS order_pack_complete_date_time,
          t.order_pack_assigned_date_time AS order_pack_assigned_date_time,
          t.order_created_date_time AS order_created_date_time,
          t.order_cancelled_date_time AS order_cancelled_date_time,
          facility,
          source_system
        FROM
          mapped_pick_order_events
          INNER JOIN (
            SELECT
              pick_order_pk AS pick_order,
              MAX(order_activated_date_time) AS order_activated_date_time,
              MAX(order_complete_date_time) AS order_complete_date_time,
              MAX(order_assigned_date_time) AS order_assigned_date_time,
              MAX(order_released_date_time) AS order_released_date_time,
              MAX(order_lines_complete_date_time) AS order_lines_complete_date_time,
              MAX(order_put_wall_pack_complete_date_time) AS order_put_wall_pack_complete_date_time,
              MAX(order_put_lines_complete_date_time) AS order_put_lines_complete_date_time,
              MAX(order_pack_complete_date_time) AS order_pack_complete_date_time,
              MAX(order_pack_assigned_date_time) AS order_pack_assigned_date_time,
              MAX(order_created_date_time) AS order_created_date_time,
              MAX(order_cancelled_date_time) AS order_cancelled_date_time
            FROM
              mapped_pick_order_events
            GROUP BY
              pick_order_pk
          ) t ON t.pick_order = pick_order_pk
      ) x
    WHERE
      x.rn = 1
  ),
  -- RAW_PICK_ORDERLINE_EVENTS CTE
  raw_pick_orderline_events AS (
    SELECT DISTINCT
      COALESCE(
        JSON_VALUE(data, '$.userPk'),
        JSON_VALUE(data, '$.pickerId'),
        ""
      ) AS picker_id,
      JSON_VALUE(data, '$.orderPk') AS order_pk,
      JSON_VALUE(data, '$.pickOrderPk') AS pick_order_pk,
      JSON_VALUE(data, '$.locationPk') AS location_pk,
      JSON_VALUE(data, '$.zonePk') AS zone_pk,
      JSON_VALUE(data, '$.processType') AS process_type,
      JSON_VALUE(data, '$.confirmationCode') AS confirmation_code,
      SAFE_CAST(JSON_VALUE(data, '$.eventTime') AS TIMESTAMP) AS event_time
    FROM
      `${tenant_id}_landing.PickOrderFact`
    WHERE
      tenant = @ltarget_tenant
      AND JSON_VALUE(data, '$.confirmationCode') IS NOT NULL
      AND UPPER(JSON_VALUE(data, '$.confirmationCode')) != 'DELETED'
      AND JSON_VALUE(data, '$.pickOrderPk') IS NOT NULL
      AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  max_orderline_rp AS (
    SELECT
      sub.picker_id AS max_orderline_picker_id,
      sub.pick_order_pk AS max_orderline_order_pk,
      sub.location_pk AS max_orderline_location_pk,
      sub.zone_pk AS max_orderline_zone_pk,
      sub.event_time AS max_orderline_event_time,
      sub.confirmation_code
    FROM
      (
        SELECT
          rpo.picker_id,
          rpo.pick_order_pk,
          rpo.location_pk,
          rpo.zone_pk,
          rpo.event_time,
          rpo.confirmation_code,
          ROW_NUMBER() OVER (
            PARTITION BY
              rpo.pick_order_pk
            ORDER BY
              rpo.event_time ASC
          ) AS rn
        FROM
          raw_pick_orderline_events rpo
          INNER JOIN (
            SELECT
              pick_order_pk,
              MAX(event_time) AS event_time
            FROM
              raw_pick_orderline_events
            GROUP BY
              pick_order_pk
          ) AS mo ON rpo.pick_order_pk = mo.pick_order_pk
          AND rpo.event_time = mo.event_time
      ) AS sub
    WHERE
      sub.rn = 1
  ),
  -- MAX_PACK_LINE_RP CTE
  max_pack_line_rp AS (
    SELECT
      sub.picker_id AS picker_id_pack_line,
      sub.pick_order_pk AS pick_order_pk_pack_line,
      sub.location_pk AS location_pk_pack_line,
      sub.event_time AS event_time_pack_line,
      sub.zone_pk AS zone_pk_pack_line,
      sub.confirmation_code AS confirmation_code_pack_line
    FROM
      (
        SELECT
          rpo.picker_id,
          rpo.pick_order_pk,
          rpo.location_pk,
          rpo.event_time,
          rpo.zone_pk,
          rpo.confirmation_code,
          ROW_NUMBER() OVER (
            PARTITION BY
              rpo.pick_order_pk
            ORDER BY
              rpo.event_time ASC
          ) AS rn
        FROM
          raw_pick_orderline_events rpo
          INNER JOIN (
            SELECT
              pick_order_pk,
              MAX(event_time) AS event_time
            FROM
              raw_pick_orderline_events
            WHERE
              UPPER(process_type) IN ('PUTWALL_PACK', 'PACK', 'PACKING')
            GROUP BY
              pick_order_pk
          ) AS mo ON rpo.pick_order_pk = mo.pick_order_pk
          AND rpo.event_time = mo.event_time
      ) AS sub
    WHERE
      sub.rn = 1
  ),
  -- MAX_PACK_LINES CTE
  max_pack_lines AS (
    SELECT
      COALESCE(pick_order_pk_pack_line, max_orderline_order_pk) AS pick_order_pk,
      pick_order_pk_pack_line,
      max_orderline_order_pk,
      max_orderline_picker_id,
      max_orderline_location_pk,
      max_orderline_zone_pk,
      max_orderline_event_time,
      picker_id_pack_line,
      location_pk_pack_line,
      event_time_pack_line,
      zone_pk_pack_line,
    FROM
      (
        SELECT
          mpl.*,
          mol.*
        FROM
          max_pack_line_rp AS mpl
          FULL OUTER JOIN max_orderline_rp AS mol ON mpl.pick_order_pk_pack_line = mol.max_orderline_order_pk
      ) AS tmp
  ),
  -- EVENTS_WITH_JOINED_MAX_PACK_LINES CTE
  events_with_joined_max_pack_lines AS (
    SELECT
      COALESCE(ppo.pick_order_pk, mpl.pick_order_pk) AS pick_order_code,
      ppo.tenant_name,
      ppo.order_type,
      ppo.order_category,
      ppo.ending_event,
      ppo.wave_pk,
      ppo.order_pk,
      ppo.order_assigned_date_time,
      ppo.order_complete_date_time,
      ppo.order_created_date_time,
      ppo.order_lines_complete_date_time,
      ppo.order_pack_assigned_date_time,
      ppo.order_pack_complete_date_time,
      ppo.order_put_lines_complete_date_time,
      ppo.order_put_wall_pack_complete_date_time,
      ppo.order_released_date_time,
      ppo.location_order_assigned,
      ppo.location_put_wall_pack,
      ppo.zone_put_wall_pack,
      ppo.group_put_wall_pack,
      ppo.user_name_put_wall_pack,
      ppo.location_pack,
      ppo.zone_pack,
      ppo.group_pack,
      ppo.user_name_pack,
      ppo.put_wall_pk,
      mpl.max_orderline_picker_id,
      mpl.max_orderline_order_pk,
      mpl.max_orderline_location_pk,
      mpl.max_orderline_zone_pk,
      mpl.max_orderline_event_time,
      mpl.picker_id_pack_line,
      mpl.pick_order_pk_pack_line,
      mpl.location_pk_pack_line,
      mpl.event_time_pack_line,
      mpl.zone_pk_pack_line,
      ppo.order_activated_date_time,
      ppo.order_cancelled_date_time,
      ppo.facility AS facility,
      ppo.source_system AS source_system
    FROM
      agg_pick_order_events ppo
      FULL OUTER JOIN max_pack_lines mpl ON mpl.pick_order_pk = ppo.order_pk
  ),
  -- Creating the temporary table
  all_pick_order_events AS (
    SELECT
      ewjmpl.*,
      fpoc.*
    FROM
      events_with_joined_max_pack_lines ewjmpl
      LEFT OUTER JOIN (
        SELECT
          order_released_date_time AS order_released_date_time_exists,
          order_assigned_date_time AS order_assigned_date_time_exists,
          dim_location_assigned.location_uuid AS order_assigned_location_uuid_exists,
          order_put_start_date_time AS order_put_start_date_time_exists,
          order_pack_start_date_time AS order_pack_start_date_time_exists,
          order_lines_complete_date_time AS order_lines_complete_date_time_exists,
          order_pack_complete_date_time AS order_pack_complete_date_time_exists,
          fpoc.location_uuid AS location_uuid_exists,
          fpoc.operator_uuid AS operator_uuid_exists,
          fpoc.zone_uuid AS zone_uuid_exists,
          fpoc.pick_order_uuid AS pick_order_uuid_exists,
          record_timestamp AS order_created_date_time_exists,
          order_put_lines_complete_date_time AS order_put_lines_complete_date_time_exists,
          order_put_wall_pack_complete_date_time AS order_put_wall_pack_complete_date_time_exists,
          order_put_wall_pack_operator_uuid AS order_put_wall_pack_operator_uuid_exists,
          order_put_wall_pack_location_uuid AS order_put_wall_pack_location_uuid_exists,
          order_put_wall_pack_zone_uuid AS order_put_wall_pack_zone_uuid_exists,
          order_complete_date_time AS order_complete_date_time_exists,
          fpoc.wave_uuid AS wave_uuid_exists,
          pick_order_category AS pick_order_category_exists,
          order_cancelled_date_time AS order_cancelled_date_time_exists
        FROM
          `${tenant_id}_oa_curated.fct_pick_order_complete` fpoc
          JOIN `${tenant_id}_oa_curated.dim_operator` USING (operator_uuid)
          JOIN `${tenant_id}_oa_curated.dim_location` USING (location_uuid)
          JOIN `${tenant_id}_oa_curated.dim_pick_order` USING (pick_order_uuid)
          JOIN `${tenant_id}_oa_curated.dim_location` AS dim_location_assigned ON fpoc.order_assigned_location_uuid = dim_location_assigned.location_uuid
          JOIN `${tenant_id}_oa_curated.dim_location` AS dim_location_pack ON fpoc.order_put_wall_pack_location_uuid = dim_location_pack.location_uuid
          JOIN `${tenant_id}_oa_curated.dim_zone` USING (zone_uuid)
          JOIN `${tenant_id}_oa_curated.dim_zone` AS dim_zone_pack ON fpoc.order_put_wall_pack_zone_uuid = dim_zone_pack.zone_uuid
          JOIN `${tenant_id}_oa_curated.dim_wave` USING (wave_uuid)
        WHERE
          fpoc.record_timestamp >= date_threshold
      ) fpoc ON ewjmpl.pick_order_code = pick_order_uuid_exists
  ),
  -- ORDER_TYPE_CALC_EVENTS CTE
  order_type_calc_events AS (
    SELECT
      *,
      CASE
        WHEN UPPER(order_type) IN ('STANDARD', 'PUTAWAY') THEN location_order_assigned
        WHEN UPPER(order_type) = 'CUSTOMPW' THEN picker_id_pack_line
        ELSE location_order_assigned
      END AS order_assigned_location_code,
      CASE
        WHEN UPPER(order_type) IN ('CUSTOMPW', 'STANDARDPW') THEN picker_id_pack_line
        ELSE NULL
      END AS order_put_wall_pack_operator_code,
      CASE
        WHEN UPPER(order_type) IN ('CUSTOMPW', 'STANDARDPW') THEN location_pk_pack_line
        ELSE NULL
      END AS order_put_wall_pack_location_code,
      CASE
        WHEN UPPER(order_type) IN ('CUSTOMPW', 'STANDARDPW') THEN zone_pk_pack_line
        ELSE NULL
      END AS order_put_wall_pack_zone_code,
      CASE
        WHEN UPPER(order_type) = 'SHIPPING' THEN user_name_put_wall_pack
        WHEN order_complete_date_time IS NOT NULL THEN max_orderline_picker_id
        ELSE NULL
      END AS operator_code,
      CASE
        WHEN UPPER(order_type) = 'SHIPPING' THEN location_put_wall_pack
        WHEN order_complete_date_time IS NOT NULL THEN max_orderline_location_pk
        ELSE NULL
      END AS location_code,
      CASE
        WHEN UPPER(order_type) = 'SHIPPING' THEN zone_put_wall_pack
        WHEN order_complete_date_time IS NOT NULL THEN max_orderline_zone_pk
        ELSE NULL
      END AS zone_code,
      CASE
        WHEN UPPER(order_type) IN ('CUSTOMPW', 'STANDARDPW') THEN event_time_pack_line
        WHEN UPPER(order_type) = 'SHIPPING' THEN order_complete_date_time
        ELSE NULL
      END AS complete_date_time,
      CASE
        WHEN UPPER(order_type) = 'CUSTOMPW' THEN SAFE_CAST(event_time_pack_line AS TIMESTAMP)
        ELSE NULL
      END AS put_wall_pack_complete_date_time_final,
      order_pack_complete_date_time AS pack_complete_date_time,
      order_type AS order_type_final
    FROM
      all_pick_order_events
    WHERE
      UPPER(order_type) IN (
        'STANDARD',
        'PUTAWAY',
        'CUSTOMPW',
        'STANDARDPW',
        'REPLENISHMENT',
        'SHIPPING'
      )
  ),
  -- Now, filter the fct_order_line on that timestamp in a way that reduces its size before joining
  filtered_order_line AS (
    SELECT
      *
    FROM
      `${tenant_id}_oa_curated.fct_order_line`
    WHERE
      record_timestamp >= eventTimeVar
  ),
  pre_filtered_order_quantities AS (
    SELECT
      dop.pick_order_code,
      SUM(fol.picked_qty) AS order_total_qty,
      COUNTIF(fol.pick_order_line_complete_ind IS NOT NULL) AS order_line_count,
      MIN(fol.record_timestamp) AS order_put_start_date_time
    FROM
      filtered_order_line fol
      INNER JOIN `${tenant_id}_oa_curated.dim_pick_order` dop ON fol.pick_order_uuid = dop.pick_order_uuid
    GROUP BY
      dop.pick_order_code
  ),
  -- Merge with distinct selected columns
  merge_db_and_stage_events AS (
    SELECT DISTINCT
      CASE
        WHEN oce.order_complete_date_time_exists IS NOT NULL THEN 1
        ELSE 0
      END AS pick_order_complete_ind,
      CASE
        WHEN oce.order_cancelled_date_time IS NOT NULL THEN 1
        ELSE 0
      END AS order_cancelled_ind,
      COALESCE(oce.location_code, oce.location_uuid_exists) AS location_code,
      COALESCE(
        oce.order_assigned_location_uuid_exists,
        oce.order_assigned_location_code
      ) AS order_assigned_location_code,
      COALESCE(
        oce.order_put_wall_pack_location_code,
        oce.order_put_wall_pack_location_uuid_exists
      ) AS order_put_wall_pack_location_code,
      oce.pick_order_code,
      COALESCE(
        oce.order_released_date_time,
        oce.order_released_date_time_exists
      ) AS order_released_date_time,
      COALESCE(
        oce.order_assigned_date_time,
        oce.order_assigned_date_time_exists
      ) AS order_assigned_date_time,
      CASE
        WHEN oce.order_created_date_time_exists IS NOT NULL THEN oce.order_created_date_time_exists
        WHEN oce.order_created_date_time IS NOT NULL THEN oce.order_created_date_time
        WHEN oce.order_released_date_time IS NOT NULL THEN oce.order_released_date_time
        WHEN oce.order_activated_date_time IS NOT NULL THEN oce.order_activated_date_time
        ELSE oce.order_lines_complete_date_time
      END AS order_created_date_time,
      COALESCE(
        oce.order_pack_start_date_time_exists,
        oce.order_pack_assigned_date_time
      ) AS order_pack_assigned_date_time,
      COALESCE(
        oce.order_put_lines_complete_date_time_exists,
        oce.order_put_lines_complete_date_time
      ) AS order_put_lines_complete_date_time,
      COALESCE(
        oce.order_put_wall_pack_complete_date_time_exists,
        oce.order_put_wall_pack_complete_date_time
      ) AS order_put_wall_pack_complete_date_time,
      COALESCE(
        oce.order_lines_complete_date_time_exists,
        oce.order_lines_complete_date_time
      ) AS order_lines_complete_date_time,
      COALESCE(
        oce.order_pack_complete_date_time_exists,
        oce.pack_complete_date_time
      ) AS order_pack_complete_date_time,
      COALESCE(
        oce.order_complete_date_time_exists,
        oce.complete_date_time
      ) AS order_complete_date_time,
      COALESCE(oce.wave_uuid_exists, oce.wave_pk) AS wave_code,
      COALESCE(oce.operator_code, oce.operator_uuid_exists) AS operator_code,
      COALESCE(oce.zone_code, oce.zone_uuid_exists) AS zone_code,
      COALESCE(
        oce.order_put_wall_pack_operator_code,
        oce.order_put_wall_pack_operator_uuid_exists
      ) AS order_put_wall_pack_operator_code,
      COALESCE(
        oce.order_put_wall_pack_zone_code,
        oce.order_put_wall_pack_zone_uuid_exists
      ) AS order_put_wall_pack_zone_code,
      COALESCE(
        oce.order_cancelled_date_time_exists,
        oce.order_cancelled_date_time
      ) AS order_cancelled_date_time,
      oq.order_put_start_date_time AS order_put_start_date_time,
      oq.order_line_count AS order_line_count,
      oq.order_total_qty AS order_total_qty,
      oce.order_pk AS customer_order_code,
      oce.order_type_final AS order_type_final,
      oce.order_category AS order_category_final,
      oce.put_wall_pk AS put_wall_pk,
      oce.facility AS facility,
      oce.source_system AS source_system
    FROM
      order_type_calc_events oce
      LEFT JOIN pre_filtered_order_quantities oq ON oce.pick_order_code = oq.pick_order_code
  )
  -- Final Main Query
SELECT
  order_created_date_time AS record_timestamp,
  COALESCE(UPPER(pick_order_code), '') AS pick_order_code,
  COALESCE(UPPER(customer_order_code), '') AS customer_order_code,
  COALESCE(UPPER(operator_code), '') AS operator_code,
  CASE
    WHEN location_code IS NULL
    OR location_code = '0' THEN ''
    ELSE UPPER(location_code)
  END AS location_code,
  COALESCE(UPPER(wave_code), '') AS wave_code,
  COALESCE(UPPER(zone_code), '') AS zone_code,
  CASE
    WHEN order_released_date_time IS NULL
    AND order_lines_complete_date_time IS NOT NULL THEN order_created_date_time
    ELSE order_released_date_time
  END AS order_released_date_time,
  COALESCE(
    SAFE_CAST(order_assigned_date_time AS TIMESTAMP),
    NULL
  ) AS order_assigned_date_time,
  COALESCE(UPPER(order_assigned_location_code), '') AS order_assigned_location_code,
  COALESCE(
    SAFE_CAST(order_put_start_date_time AS TIMESTAMP),
    NULL
  ) AS order_put_start_date_time,
  COALESCE(
    SAFE_CAST(order_pack_assigned_date_time AS TIMESTAMP),
    NULL
  ) AS order_pack_start_date_time,
  '' AS overflow_location_count,
  COALESCE(
    SAFE_CAST(order_put_lines_complete_date_time AS TIMESTAMP),
    NULL
  ) AS order_put_lines_complete_date_time,
  COALESCE(
    SAFE_CAST(
      order_put_wall_pack_complete_date_time AS TIMESTAMP
    ),
    NULL
  ) AS order_put_wall_pack_complete_date_time,
  COALESCE(UPPER(order_put_wall_pack_operator_code), '') AS order_put_wall_pack_operator_code,
  COALESCE(UPPER(order_put_wall_pack_location_code), '') AS order_put_wall_pack_location_code,
  COALESCE(UPPER(order_put_wall_pack_zone_code), '') AS order_put_wall_pack_zone_code,
  COALESCE(
    SAFE_CAST(order_lines_complete_date_time AS TIMESTAMP),
    NULL
  ) AS order_lines_complete_date_time,
  COALESCE(
    SAFE_CAST(order_pack_complete_date_time AS TIMESTAMP),
    NULL
  ) AS order_pack_complete_date_time,
  pick_order_complete_ind,
  COALESCE(
    SAFE_CAST(order_complete_date_time AS TIMESTAMP),
    NULL
  ) AS order_complete_date_time,
  @ltarget_tenant AS tenant,
  merge_db_and_stage_events.facility AS facility,
  merge_db_and_stage_events.source_system AS source_system,
  COALESCE(
    SAFE_CAST(order_cancelled_date_time AS TIMESTAMP),
    NULL
  ) AS order_cancelled_date_time,
  order_cancelled_ind,
  COALESCE(SAFE_CAST(order_line_count AS STRING), '') AS order_line_count,
  COALESCE(SAFE_CAST(order_total_qty AS STRING), '') AS order_total_qty,
  COALESCE(order_type_final, '') AS order_type,
  COALESCE(SAFE_CAST(order_category_final AS STRING), '') AS pick_order_category,
  '' AS pick_order_uuid,
  '' AS customer_order_uuid,
  '' AS operator_uuid,
  '' AS location_uuid,
  '' AS wave_uuid,
  '' AS zone_uuid
FROM
  merge_db_and_stage_events;

-- dim merges
-- dim_customer_order merge
MERGE
  `${tenant_id}_oa_curated.dim_customer_order` DCO USING (
    SELECT DISTINCT
      customer_order_code,
      tenant,
      facility,
      source_system,
      MAX(order_type) AS order_type
    FROM
      tmp_staging_pick_order_complete
    GROUP BY
      customer_order_code,
      tenant,
      facility,
      source_system
  ) AS v ON DCO.customer_order_code = v.customer_order_code
  AND DCO.tenant = v.tenant
WHEN MATCHED THEN
UPDATE SET
  customer_order_type = COALESCE(DCO.customer_order_type, v.order_type),
  etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
  (
    customer_order_uuid,
    tenant,
    facility,
    source_system,
    customer_order_code,
    customer_order_name,
    customer_order_type,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [v.tenant,
      v.facility,
      v.source_system,
      v.customer_order_code]
    ),
    v.tenant,
    v.facility,
    v.source_system,
    v.customer_order_code,
    v.customer_order_code, -- Assuming customer_order_code can be used as customer_order_name, adjust if needed
    v.order_type,
    1, -- Assuming new records should be active
    @etl_batch_uuid
  );

-- Update the tmp_staging_pick_order_complete with the customer_order_uuid
UPDATE tmp_staging_pick_order_complete TSP
SET
  TSP.customer_order_uuid = DCO.customer_order_uuid
FROM
  (
    select distinct
      customer_order_uuid,
      customer_order_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_customer_order`
  ) DCO
WHERE
  TSP.customer_order_code = DCO.customer_order_code
  AND TSP.tenant = DCO.tenant
  AND TSP.facility = DCO.facility
  AND TSP.source_system = DCO.source_system;

-- dim_pick_order merge
MERGE
  `${tenant_id}_oa_curated.dim_pick_order` DPO USING (
    SELECT DISTINCT
      pick_order_code,
      tenant,
      facility,
      source_system,
      MAX(customer_order_uuid) AS customer_order_uuid,
      MAX(order_type) AS order_type,
      MAX(pick_order_category) AS pick_order_category,
      MAX(order_line_count) AS order_line_count,
      MAX(order_total_qty) AS order_total_qty
    FROM
      tmp_staging_pick_order_complete
    GROUP BY
      pick_order_code,
      tenant,
      facility,
      source_system
  ) AS v ON DPO.pick_order_code = v.pick_order_code
  AND DPO.tenant = v.tenant
WHEN MATCHED THEN
UPDATE SET
  customer_order_uuid = COALESCE(v.customer_order_uuid, DPO.customer_order_uuid),
  pick_order_type = COALESCE(v.order_type, DPO.pick_order_type),
  pick_order_category = COALESCE(v.pick_order_category, DPO.pick_order_category),
  etl_batch_id = @etl_batch_uuid -- replace with your actual batch identifier variable or value
WHEN NOT MATCHED THEN
INSERT
  (
    pick_order_uuid,
    tenant,
    facility,
    source_system,
    customer_order_uuid,
    pick_order_code,
    pick_order_name,
    put_wall_flg,
    pick_order_type,
    pick_order_category,
    pick_order_fulfillment_handling_type,
    pick_order_value_added_service,
    pick_order_priority,
    pick_order_packing_requirement,
    pick_order_latest_staging_date_time,
    pick_order_order_channel,
    pick_order_line_count_expected,
    pick_order_total_qty_expected,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [v.tenant,
      v.facility,
      v.source_system,
      v.pick_order_code]
    ),
    v.tenant,
    v.facility,
    v.source_system,
    v.customer_order_uuid,
    v.pick_order_code,
    NULL,
    1,
    v.order_type,
    v.pick_order_category,
    NULL, -- Assuming default value is NULL
    NULL, -- Assuming default value is NULL
    SAFE_CAST(NULL AS INT64), -- Assuming default value is NULL
    NULL, -- Assuming default value is NULL
    SAFE_CAST(NULL AS TIMESTAMP), -- Assuming default value is NULL
    NULL, -- Assuming default value is NULL
    SAFE_CAST(v.order_line_count AS INT64),
    SAFE_CAST(v.order_total_qty AS INT64),
    1, -- Assuming new records should be active
    @etl_batch_uuid
  );

-- Update tmp_staging_pick_order_complete with pick_order_uuid
UPDATE tmp_staging_pick_order_complete TSPO
SET
  TSPO.pick_order_uuid = DPO.pick_order_uuid
FROM
  (
    select distinct
      pick_order_uuid,
      pick_order_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_pick_order`
  ) DPO
WHERE
  TSPO.pick_order_code = DPO.pick_order_code
  AND TSPO.tenant = DPO.tenant
  AND TSPO.facility = DPO.facility
  AND TSPO.source_system = DPO.source_system;

-- dim_location merge
MERGE
  `${tenant_id}_oa_curated.dim_location` dl USING (
    SELECT
      location_code,
      '' as location_name,
      facility,
      source_system
    FROM
      (
        SELECT DISTINCT
          location_code,
          '' AS location_name,
          facility,
          source_system
        FROM
          tmp_staging_pick_order_complete
        UNION DISTINCT
        -- Logical destination in dim_zone
        SELECT
          '' AS location_code,
          NULL AS location_name,
          '' AS facility,
          '' AS source_system
      ) t
    GROUP BY
      location_code,
      facility,
      source_system
  ) v ON dl.location_code = v.location_code
  AND dl.tenant = @ltarget_tenant
WHEN MATCHED
  AND (dl.location_name <> v.location_name) THEN
UPDATE SET
  dl.location_name = COALESCE(v.location_name, dl.location_name),
  etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
  (
    location_uuid,
    location_code,
    location_name,
    tenant,
    facility,
    source_system,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [v.tenant,
      v.facility,
      v.source_system,
      v.location_code]
    ),
    v.location_code,
    v.location_name,
    @ltarget_tenant,
    v.facility,
    v.source_system,
    1,
    @etl_batch_uuid
  );

-- Update tmp_staging_pick_order_complete with location_uuid
UPDATE tmp_staging_pick_order_complete tt
SET
  tt.location_uuid = d.location_uuid
FROM
  (
    select distinct
      location_uuid,
      location_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_location`
  ) d
WHERE
  tt.location_code = d.location_code
  AND d.tenant = @ltarget_tenant
  AND d.tenant = tt.facility
  AND d.tenant = tt.facility
  AND TRIM(tt.location_code) <> '';

-- dim_operator merge
MERGE
  `${tenant_id}_oa_curated.dim_operator` DO USING (
    SELECT DISTINCT
      operator_code,
      MAX(tenant) AS tenant,
      facility,
      source_system
    FROM
      tmp_staging_pick_order_complete
    GROUP BY
      operator_code,
      facility,
      source_system
  ) AS v ON DO.operator_code = v.operator_code
  AND DO.tenant = v.tenant
  AND DO.facility = v.facility
  AND DO.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    operator_uuid,
    operator_code,
    operator_name,
    operator_full_name,
    operator_description,
    start_date,
    end_date,
    tenant,
    facility,
    source_system,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [v.tenant,
      v.facility,
      v.source_system,
      v.operator_code]
    ),
    v.operator_code,
    v.operator_code, -- Assuming using operator_code as the operator_name when not available
    v.operator_code, -- Assuming using operator_code as the operator_full_name when not available
    '', -- operator_description
    SAFE_CAST(NULL AS TIMESTAMP), -- start_date
    SAFE_CAST(NULL AS TIMESTAMP), -- end_date
    v.tenant,
    v.facility,
    v.source_system,
    1, -- Assuming new records should be active
    @etl_batch_uuid -- replace with your actual batch identifier variable or value
  );

-- Update tmp_staging_pick_order_complete with operator_uuid
UPDATE tmp_staging_pick_order_complete TSPO
SET
  TSPO.operator_uuid = DO.operator_uuid
FROM
  (
    select distinct
      operator_uuid,
      operator_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_operator`
  ) DO
WHERE
  TSPO.operator_code = DO.operator_code
  AND TSPO.tenant = DO.tenant
  AND TSPO.facility = DO.facility
  AND TSPO.source_system = DO.source_system;

-- dim_zone merge
MERGE
  `${tenant_id}_oa_curated.dim_zone` DZ USING (
    SELECT DISTINCT
      zone_code,
      tenant,
      facility,
      source_system,
      MAX(order_total_qty) AS order_total_qty,
      MAX(order_line_count) AS order_line_count
    FROM
      tmp_staging_pick_order_complete
    GROUP BY
      zone_code,
      facility,
      source_system
  ) AS v ON DZ.zone_code = v.zone_code
  AND DZ.tenant = v.tenant
WHEN MATCHED THEN
UPDATE SET
  qty_picked_target = COALESCE(
    SAFE_CAST(v.order_total_qty AS INT64),
    DZ.qty_picked_target
  ),
  line_picked_target = COALESCE(
    SAFE_CAST(v.order_line_count AS INT64),
    DZ.line_picked_target
  ),
  etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
  (
    zone_uuid,
    tenant,
    facility,
    source_system,
    zone_code,
    logical_destination_uuid,
    zone_name,
    zone_type,
    qty_picked_target,
    line_picked_target,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [v.tenant,
      v.facility,
      v.source_system,
      v.zone_code]
    ),
    v.tenant,
    v.facility,
    v.source_system,
    v.zone_code,
    GENERATE_UUID(), -- Assuming a new UUID needs to be generated for logical_destination_uuid
    v.zone_code, -- Assuming zone_code can be used as zone_name, adjust if needed
    '', -- Assuming placeholder for zone_type, adjust as needed
    SAFE_CAST(v.order_total_qty AS INT64),
    SAFE_CAST(v.order_line_count AS INT64),
    1, -- Assuming new records should be active
    @etl_batch_uuid -- replace with your actual batch identifier variable or value
  );

-- Update tmp_staging_pick_order_complete with zone_uuid
UPDATE tmp_staging_pick_order_complete TSPO
SET
  TSPO.zone_uuid = DZ.zone_uuid
FROM
  (
    select distinct
      zone_uuid,
      zone_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_zone`
  ) DZ
WHERE
  TSPO.zone_code = DZ.zone_code
  AND TSPO.tenant = DZ.tenant
  AND TSPO.facility = DZ.facility
  AND TSPO.source_system = DZ.source_system;

-- dim_wave merge
MERGE
  `${tenant_id}_oa_curated.dim_wave` DW USING (
    SELECT DISTINCT
      wave_code,
      MAX(tenant) AS tenant,
      facility,
      source_system,
      MAX(record_timestamp) AS record_timestamp,
      MAX(order_released_date_time) AS order_released_date_time,
      MAX(order_assigned_date_time) AS order_assigned_date_time
    FROM
      tmp_staging_pick_order_complete
    GROUP BY
      wave_code,
      facility,
      source_system
  ) AS v ON DW.wave_code = v.wave_code
  AND DW.tenant = v.tenant
WHEN MATCHED THEN
UPDATE SET
  wave_released_date_time = COALESCE(
    v.order_released_date_time,
    DW.wave_released_date_time
  ),
  wave_created_date_time = COALESCE(v.record_timestamp, DW.wave_created_date_time),
  wave_active_date_time = COALESCE(
    v.order_assigned_date_time,
    DW.wave_active_date_time
  ),
  wave_status = COALESCE('', DW.wave_status), -- Placeholder, consider providing a value or remove
  wave_priority = COALESCE('', DW.wave_priority), -- Placeholder, consider providing a value or remove
  wave_item_count = COALESCE(SAFE_CAST(NULL AS INT64), DW.wave_item_count),
  etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
  (
    wave_uuid,
    wave_code,
    wave_name,
    wave_status,
    wave_priority,
    wave_created_date_time,
    wave_released_date_time,
    wave_active_date_time,
    wave_closed_date_time,
    wave_item_count,
    tenant,
    facility,
    source_system,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [v.tenant,
      v.facility,
      v.source_system,
      v.wave_code]
    ),
    v.wave_code,
    v.wave_code, -- Assuming wave_code can be used as wave_name if not available
    '', -- Placeholder for wave_status, provide a default value if applicable
    '', -- Placeholder for wave_priority, provide a default value if applicable
    v.record_timestamp,
    v.order_released_date_time,
    v.order_assigned_date_time,
    SAFE_CAST(NULL AS TIMESTAMP), -- Assuming placeholder for wave_closed_date_time
    SAFE_CAST(NULL AS INT64), -- Assuming placeholder for wave_item_count
    v.tenant,
    v.facility,
    v.source_system,
    1, -- Assuming new records should be active
    @etl_batch_uuid
  );

-- Update tmp_staging_pick_order_complete with wave_uuid
UPDATE tmp_staging_pick_order_complete TSPO
SET
  TSPO.wave_uuid = DW.wave_uuid
FROM
  (
    select distinct
      wave_uuid,
      wave_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_wave`
  ) DW
WHERE
  TSPO.wave_code = DW.wave_code
  AND TSPO.tenant = DW.tenant
  AND TSPO.facility = DW.facility
  AND TSPO.source_system = DW.source_system;

-- FINAL FCT Insert
INSERT INTO
  `${tenant_id}_oa_curated.fct_pick_order_complete` (
    record_timestamp,
    pick_order_uuid,
    operator_uuid,
    location_uuid,
    wave_uuid,
    zone_uuid,
    order_released_date_time,
    order_assigned_date_time,
    order_line_count,
    order_total_qty,
    order_put_lines_complete_date_time,
    order_put_wall_pack_complete_date_time,
    order_put_wall_pack_operator_uuid,
    order_put_wall_pack_location_uuid,
    order_put_wall_pack_zone_uuid,
    order_lines_complete_date_time,
    order_pack_complete_date_time,
    order_complete_date_time,
    pick_order_complete_ind,
    order_cancelled_ind,
    order_cancelled_date_time,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
SELECT DISTINCT
  record_timestamp,
  pick_order_uuid,
  operator_uuid,
  location_uuid,
  wave_uuid,
  zone_uuid,
  order_released_date_time,
  order_assigned_date_time,
  SAFE_CAST(order_line_count AS INT64),
  SAFE_CAST(order_total_qty AS INT64),
  order_put_lines_complete_date_time,
  order_put_wall_pack_complete_date_time,
  GENERATE_UUID() AS order_put_wall_pack_operator_uuid,
  GENERATE_UUID() AS order_put_wall_pack_location_uuid,
  GENERATE_UUID() AS order_put_wall_pack_zone_uuid,
  order_lines_complete_date_time,
  order_pack_complete_date_time,
  order_complete_date_time,
  pick_order_complete_ind,
  order_cancelled_ind,
  order_cancelled_date_time,
  tenant,
  facility,
  source_system,
  @etl_batch_uuid
FROM
  tmp_staging_pick_order_complete;