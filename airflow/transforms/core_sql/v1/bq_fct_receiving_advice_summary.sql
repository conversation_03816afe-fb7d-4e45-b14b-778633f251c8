/*
DECLARE ltarget_tenant STRING DEFAULT 'dematic';
DECLARE etl_batch_uuid STRING DEFAULT STRING(CURRENT_TIMESTAMP);
DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2022-09-27 00:00:00');
DECLARE end_query_date TIMES<PERSON>MP DEFAULT TIMESTAMP('2024-09-27 02:00:00');
*/

CREATE TEMP TABLE tmp_staging_fct_advice (
    record_timestamp TIMESTAMP,
    receiving_advice_uuid STRING DEFAULT NULL,
    receiving_advice_code STRING,
    receiving_advice_owner STRING,
    receiving_advice_type STRING,
    supplier STRING,
    receiving_advice_complete_date_time DATETIME,
    receiving_advice_completed_pallet_count INT64,
    receiving_advice_expected_line_count INT64,
    receiving_advice_over_delivered_line_count INT64,
    receiving_advice_completed_line_count INT64,
    receiving_advice_under_delivered_line_count INT64,
    receiving_advice_over_delivered_pallet_count INT64,
    receiving_advice_exactly_delivered_line_count INT64,
    receiving_advice_under_delivered_pallet_count INT64,
    receiving_advice_exactly_delivered_pallet_count INT64,
    active_rec_ind INT64 DEFAULT NULL,
    tenant STRING,
    facility STRING,
    source_system STRING,
    finish_time TIMESTAMP DEFAULT NULL,
    create_time TIMESTAMP DEFAULT NULL
);


/* Inserting data into the temporary staging table */
INSERT INTO tmp_staging_fct_advice
WITH
     created_data AS  (
          SELECT COALESCE(JSON_VALUE(data, '$.eventTime'), JSON_VALUE(data, '$.eventDate')) AS record_timestamp
               , COALESCE(JSON_VALUE(data, '$.adviceId'),JSON_VALUE(data, '$.adviceBid')) AS receiving_advice_code
               , COALESCE(JSON_VALUE(data, '$.clientId'), JSON_VALUE(data, '$.ownerBid')) AS receiving_advice_owner
               , JSON_VALUE(data, '$.adviceType') AS receiving_advice_type
               , JSON_VALUE(data, '$.supplierId') AS supplier
               , JSON_VALUE(data, '$.numberOfAdviceLines') AS receiving_advice_expected_line_count
               , 0 AS active_rec_ind
               , @ltarget_tenant AS tenant
               , facility
               , source_system
               , ingestion_date
            FROM `${tenant_id}_landing.AdviceCreatedFact`
           WHERE ingestion_date BETWEEN TIMESTAMP(@start_query_date)
             AND TIMESTAMP(@end_query_date) AND tenant = @ltarget_tenant)

   , finished_data AS  (
        SELECT COALESCE(JSON_VALUE(data, '$.eventTime'), JSON_VALUE(data, '$.eventDate'))AS record_timestamp
            , SAFE_CAST(SUBSTR(COALESCE(JSON_VALUE(data, '$.eventTime'), JSON_VALUE(data, '$.eventDate')), 1, 19) AS DATETIME) AS receiving_advice_complete_date_time
            , COALESCE(JSON_VALUE(data, '$.adviceId'),JSON_VALUE(data, '$.adviceBid')) AS receiving_advice_code
            , COALESCE(JSON_VALUE(data, '$.clientId'), JSON_VALUE(data, '$.ownerBid')) AS receiving_advice_owner
            , JSON_VALUE(data, '$.adviceType') AS receiving_advice_type
            , JSON_VALUE(data, '$.supplierId') AS supplier
            , JSON_VALUE(data, '$.exactlyDeliveredLines') AS receiving_advice_exactly_delivered_line_count
            , SAFE_CAST(JSON_VALUE(data, '$.exactlyDeliveredPallets') AS INT64) AS receiving_advice_exactly_delivered_pallet_count
            , JSON_VALUE(data, '$.numberOfAdviceLines') AS receiving_advice_completed_line_count
            , JSON_VALUE(data, '$.numberOfPallets') AS receiving_advice_completed_pallet_count
            , JSON_VALUE(data, '$.overdeliveredLines') AS receiving_advice_over_delivered_line_count
            , JSON_VALUE(data, '$.overdeliveredPallets') AS receiving_advice_over_delivered_pallet_count
            , JSON_VALUE(data, '$.underdeliveredLines') AS receiving_advice_under_delivered_line_count
            , JSON_VALUE(data, '$.underdeliveredPallets') AS receiving_advice_under_delivered_pallet_count
            , 0 AS active_rec_ind
            , @ltarget_tenant AS tenant
            , facility
            , source_system
            , ingestion_date
        FROM `${tenant_id}_landing.AdviceFinishedFact`
        WHERE ingestion_date BETWEEN TIMESTAMP(@start_query_date)
            AND TIMESTAMP(@end_query_date) AND tenant = @ltarget_tenant)
/* CTE to merge the results from both transformed tables */
/* Outcome */
     SELECT 
        SAFE_CAST(
            COALESCE(
                `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(finished_data.record_timestamp, NULL),
                `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(created_data.record_timestamp, NULL)
            ) AS TIMESTAMP
        ) AS record_timestamp,
        "" AS receiving_advice_uuid,
        COALESCE(created_data.receiving_advice_code, finished_data.receiving_advice_code) AS receiving_advice_code,
        COALESCE(created_data.receiving_advice_owner, finished_data.receiving_advice_owner) AS receiving_advice_owner,
        COALESCE(created_data.receiving_advice_type, finished_data.receiving_advice_type) AS receiving_advice_type,
        COALESCE(created_data.supplier, finished_data.supplier, '') AS supplier,
        receiving_advice_complete_date_time,
        SAFE_CAST(receiving_advice_completed_pallet_count AS INT64),
        SAFE_CAST(receiving_advice_expected_line_count AS INT64),
        SAFE_CAST(receiving_advice_over_delivered_line_count AS INT64),
        SAFE_CAST(receiving_advice_completed_line_count AS INT64),
        SAFE_CAST(receiving_advice_under_delivered_line_count AS INT64),
        SAFE_CAST(receiving_advice_over_delivered_pallet_count AS INT64),
        SAFE_CAST(receiving_advice_exactly_delivered_line_count AS INT64),
        SAFE_CAST(receiving_advice_under_delivered_pallet_count AS INT64),
        SAFE_CAST(receiving_advice_exactly_delivered_pallet_count AS INT64),
        COALESCE(finished_data.active_rec_ind,created_data.active_rec_ind) AS active_rec_ind,
        COALESCE(created_data.tenant, finished_data.tenant) AS tenant,
        COALESCE(created_data.facility, finished_data.facility) AS facility,
        COALESCE(created_data.source_system, finished_data.source_system) AS source_system,
        `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(finished_data.record_timestamp, NULL) AS finish_time,
        `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(created_data.record_timestamp, NULL) AS create_time
    FROM 
        created_data
    FULL JOIN 
        finished_data ON created_data.receiving_advice_code = finished_data.receiving_advice_code;


/* DIM_Receiving_Advice */   
MERGE `${tenant_id}_oa_curated.dim_receiving_advice` dra USING (
    SELECT DISTINCT 
        receiving_advice_uuid,
        receiving_advice_code,
        receiving_advice_owner,
        receiving_advice_type,
        supplier,
        source_system,
        active_rec_ind,
        @etl_batch_uuid AS etl_batch_id,
        tenant,
        facility,
        MAX(create_time) AS create_time,
        MAX(finish_time) AS finish_time 
    FROM tmp_staging_fct_advice
    GROUP BY receiving_advice_uuid,
             receiving_advice_code,
             receiving_advice_owner,
             receiving_advice_type,
             supplier,
             active_rec_ind,
             tenant, facility, source_system
) v
        ON  dra.receiving_advice_code = v.receiving_advice_code
AND dra.receiving_advice_owner = v.receiving_advice_owner
AND dra.receiving_advice_type = v.receiving_advice_type
AND dra.supplier = v.supplier
AND dra.tenant = v.tenant
AND dra.facility = v.facility
AND dra.source_system = v.source_system
WHEN MATCHED THEN 
    UPDATE SET
        dra.create_time = COALESCE(dra.create_time, v.create_time),
        dra.finish_time = COALESCE(v.finish_time, dra.finish_time),
        dra.active_rec_ind = v.active_rec_ind
WHEN NOT MATCHED THEN 
    INSERT (
        receiving_advice_uuid, 
        receiving_advice_code, 
        receiving_advice_owner, 
        receiving_advice_type,
        active_rec_ind,
        create_time,
        finish_time,
        supplier, 
        tenant, 
        facility, 
        source_system, 
        etl_batch_id
    )
    VALUES (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.receiving_advice_code]
        ), 
        v.receiving_advice_code, 
        v.receiving_advice_owner, 
        v.receiving_advice_type, 
        v.active_rec_ind,
        v.create_time,
        v.finish_time,
        v.supplier, 
        v.tenant, 
        v.facility, 
        v.source_system, 
        v.etl_batch_id
    );


UPDATE
     tmp_staging_fct_advice tt
        SET receiving_advice_uuid = d.receiving_advice_uuid
       FROM `${tenant_id}_oa_curated.dim_receiving_advice` d
      WHERE tt.receiving_advice_code = d.receiving_advice_code
      AND tt.tenant = d.tenant
      AND tt.facility = d.facility
      AND tt.source_system = d.source_system;

-- FINAL FCT Insert
INSERT INTO `${tenant_id}_oa_curated.fct_receiving_advice_summary` (
    record_timestamp,
    receiving_advice_uuid,
    receiving_advice_code,
    receiving_advice_complete_date_time,
    receiving_advice_completed_pallet_count,
    receiving_advice_expected_line_count,
    receiving_advice_over_delivered_line_count,
    receiving_advice_completed_line_count,
    receiving_advice_under_delivered_line_count,
    receiving_advice_over_delivered_pallet_count,
    receiving_advice_exactly_delivered_line_count,
    receiving_advice_under_delivered_pallet_count,
    receiving_advice_exactly_delivered_pallet_count,
    source_system,
    etl_batch_id,
    tenant,
    facility
)
SELECT DISTINCT
    record_timestamp,
    receiving_advice_uuid,
    receiving_advice_code,
    receiving_advice_complete_date_time,
    receiving_advice_completed_pallet_count,
    receiving_advice_expected_line_count,
    receiving_advice_over_delivered_line_count,
    receiving_advice_completed_line_count,
    receiving_advice_under_delivered_line_count,
    receiving_advice_over_delivered_pallet_count,
    receiving_advice_exactly_delivered_line_count,
    receiving_advice_under_delivered_pallet_count,
    receiving_advice_exactly_delivered_pallet_count,
    source_system,
    @etl_batch_uuid AS etl_batch_id,
    tenant,
    facility
FROM tmp_staging_fct_advice
GROUP BY
    receiving_advice_uuid,
    receiving_advice_code,
    receiving_advice_complete_date_time,
    receiving_advice_completed_pallet_count,
    receiving_advice_expected_line_count,
    receiving_advice_over_delivered_line_count,
    receiving_advice_completed_line_count,
    receiving_advice_under_delivered_line_count,
    receiving_advice_over_delivered_pallet_count,
    receiving_advice_exactly_delivered_line_count,
    receiving_advice_under_delivered_pallet_count,
    receiving_advice_exactly_delivered_pallet_count,
    source_system,
    tenant,
    facility,
    record_timestamp;
