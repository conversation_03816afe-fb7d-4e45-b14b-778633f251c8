/*
 **Set variables for running outside of DAG in BQ directly**
declare ltarget_tenant string default 'ict_development';
declare etl_batch_uuid string default '2020-12-12';

declare start_query_date timestamp default timestamp('2023-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2023-09-27 02:00:00');
 */
CREATE TEMP TABLE
    tmp_staging_dim_status (
        status_code STRING NOT NULL,
        status_category_code STRING NOT NULL,
        subsystem_code STRING NOT NULL,
        status_locale_code STRING NOT NULL,
        status_locale_name STRING,
        status_type STRING,
        dc_tenant_name STRING,
        subsystem_category STRING,
        status_relevancy_ind INT64,
        tenant STRING,
        facility STRING,
        source_system STRING,
        active_rec_ind INT64,
        subsystem_uuid STRING DEFAULT ''
    );

INSERT INTO
    tmp_staging_dim_status (
        status_code,
        status_locale_name,
        subsystem_code,
        status_category_code,
        status_type,
        dc_tenant_name,
        subsystem_category,
        active_rec_ind,
        status_locale_code,
        tenant,
        facility,
        source_system,
        status_relevancy_ind
    )
    -- Sort_Status
WITH
    extracted_sort_status AS (
        SELECT
            JSON_VALUE(data, '$.statusId') AS status_code,
            JSON_VALUE(data, '$.statusName') AS status_category_code,
            JSON_VALUE(data, '$.displayName') AS status_locale_name,
            JSON_VALUE(data, '$.eventTypeDisplayName') AS event_type_display_name,
            JSON_VALUE(data, '$.originator') AS subsystem_code,
            JSON_VALUE(data, '$.reasonCodeDisplayName') AS reason_code_display_name,
            JSON_VALUE(data, '$.tenantName') AS dc_tenant_name,
            SAFE_CAST(
                JSON_VALUE(data, '$.dimensionModificationTime') AS TIMESTAMP
            ) AS dimensionmodificationtime,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.StatusDimension`
        WHERE
            ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    staged_sort_status AS (
        SELECT
            COALESCE(UPPER(status_code), '') AS status_code,
            status_locale_name,
            COALESCE(UPPER(subsystem_code), '') AS subsystem_code,
            COALESCE(UPPER(status_category_code), '') AS status_category_code,
            CAST(NULL AS STRING) AS status_type,
            UPPER(dc_tenant_name) AS dc_tenant_name,
            'DEMATIC IQ' AS subsystem_category,
            1 AS active_rec_ind,
            'en_US' AS status_locale_code,
            ROW_NUMBER() OVER (
                PARTITION BY
                    status_code,
                    dc_tenant_name
                ORDER BY
                    dimensionmodificationtime DESC
            ) AS rnk,
            @ltarget_tenant AS tenant,
            extracted_sort_status.facility AS facility,
            extracted_sort_status.source_system AS source_system,
            1 AS status_relevancy_ind
        FROM
            extracted_sort_status
    ),
    ordered_sort_status AS (
        SELECT
            staged_sort_status.* EXCEPT (rnk)
        FROM
            staged_sort_status
        WHERE
            staged_sort_status.rnk = 1
    ),
    extracted_fault_text AS (
        SELECT
            JSON_VALUE(data, '$.faultCode') AS status_code,
            JSON_VALUE(data, '$.description') AS status_category_code,
            JSON_VALUE(data, '$.mtsType') AS subsystem_code,
            JSON_VALUE(data, '$.locale') AS locale,
            JSON_VALUE(data, '$.tenantName') AS dc_tenant_name,
            CAST(
                JSON_VALUE(data, '$.dimensionModificationTime') AS TIMESTAMP
            ) AS dimensionmodificationtime,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.FaultTextDimension`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    -- DIM_FaultText_Status
    staged_fault_text AS (
        SELECT
            COALESCE(UPPER(status_code), '') AS status_code,
            TRIM(status_category_code) AS status_locale_name,
            COALESCE(UPPER(subsystem_code), '') AS subsystem_code,
            '' AS status_category_code,
            CAST(NULL AS STRING) AS status_type,
            dc_tenant_name AS dc_tenant_name,
            'DEMATIC IQ' AS subsystem_category,
            1 AS active_rec_ind,
            CASE
                WHEN locale IS NULL
                OR UPPER(locale) = 'EN' THEN 'en_US'
                WHEN UPPER(locale) = 'DE' THEN 'de_DE'
                WHEN UPPER(locale) = 'FR' THEN 'fr_FR'
                WHEN UPPER(locale) = 'NL' THEN 'nl_NL'
                ELSE locale
            END AS status_locale_code,
            ROW_NUMBER() OVER (
                PARTITION BY
                    dc_tenant_name,
                    status_code,
                    subsystem_code,
                    locale
                ORDER BY
                    dimensionmodificationtime DESC
            ) AS rnk,
            @ltarget_tenant AS tenant,
            extracted_fault_text.facility AS facility,
            extracted_fault_text.source_system AS source_system,
            1 AS status_relevancy_ind
        FROM
            extracted_fault_text
    ),
    ordered_fault_text AS (
        SELECT
            staged_fault_text.* EXCEPT (rnk)
        FROM
            staged_fault_text
        WHERE
            staged_fault_text.rnk = 1
    )
SELECT
    *
FROM
    ordered_sort_status
UNION DISTINCT
SELECT
    *
FROM
    ordered_fault_text;

-- DIM_Subsystem 
MERGE
    `${tenant_id}_oa_curated.dim_subsystem` ds USING (
        SELECT DISTINCT
            subsystem_code,
            subsystem_category,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_dim_status
        GROUP BY
            subsystem_code,
            subsystem_category,
            tenant,
            facility,
            source_system
    ) v ON ds.subsystem_code = v.subsystem_code
    AND ds.subsystem_category = v.subsystem_category
    AND ds.tenant = v.tenant
WHEN NOT MATCHED THEN
INSERT
    (
        subsystem_uuid,
        subsystem_code,
        subsystem_category,
        subsystem_name,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
           [ v.tenant,
            v.facility,
            v.source_system,
            v.subsystem_code,
            v.subsystem_category]
        ),
        v.subsystem_code,
        v.subsystem_category,
        '',
        v.tenant,
        v.facility,
        v.source_system,
        @etl_batch_uuid
    );

-- Update tmp_staging_dim_status with subsystem_uuid
UPDATE tmp_staging_dim_status tt
SET
    subsystem_uuid = d.subsystem_uuid
FROM
    (
        select distinct
            subsystem_uuid,
            subsystem_code,
            subsystem_category,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_subsystem`
    ) d
WHERE
    tt.subsystem_code = d.subsystem_code
    AND tt.subsystem_category = d.subsystem_category
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- DIM_Status
MERGE
    `${tenant_id}_oa_curated.dim_status` ds USING (
        SELECT
            status_code,
            subsystem_uuid,
            tenant,
            status_category_code,
            status_locale_code,
            facility,
            source_system,
            max(status_type) AS status_type,
            max(status_locale_name) AS status_locale_name,
            max(dc_tenant_name) AS dc_tenant_name,
            max(status_relevancy_ind) AS status_relevancy_ind,
            max(active_rec_ind) AS active_rec_ind
        FROM
            tmp_staging_dim_status
        GROUP BY
            status_code,
            subsystem_uuid,
            tenant,
            facility,
            source_system,
            status_category_code,
            status_locale_code
    ) v ON ds.status_code = v.status_code
    AND ds.subsystem_uuid = v.subsystem_uuid
    AND ds.tenant = v.tenant
    AND ds.status_category_code = v.status_category_code
    AND ds.status_locale_code = v.status_locale_code
WHEN MATCHED
    AND (
        ds.status_type != v.status_type
        OR ds.status_locale_name != v.status_locale_name
        OR ds.dc_tenant_name != v.dc_tenant_name
        OR ds.status_relevancy_ind != v.status_relevancy_ind
        OR ds.active_rec_ind != v.active_rec_ind
    ) THEN
UPDATE SET
    status_type = COALESCE(v.status_type, ds.status_type),
    status_locale_name = COALESCE(v.status_locale_name, ds.status_locale_name),
    dc_tenant_name = COALESCE(v.dc_tenant_name, ds.dc_tenant_name),
    status_relevancy_ind = COALESCE(v.status_relevancy_ind, ds.status_relevancy_ind),
    active_rec_ind = COALESCE(v.active_rec_ind, ds.active_rec_ind),
    etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
    (
        status_uuid,
        status_code,
        subsystem_uuid,
        status_category_code,
        status_locale_code,
        status_locale_name,
        status_type,
        dc_tenant_name,
        status_relevancy_ind,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.status_code,
            v.subsystem_uuid]
        ),
        v.status_code,
        v.subsystem_uuid,
        v.status_category_code,
        v.status_locale_code,
        v.status_locale_name,
        v.status_type,
        v.dc_tenant_name,
        v.status_relevancy_ind,
        v.tenant,
        v.facility,
        v.source_system,
        v.active_rec_ind,
        @etl_batch_uuid
    );