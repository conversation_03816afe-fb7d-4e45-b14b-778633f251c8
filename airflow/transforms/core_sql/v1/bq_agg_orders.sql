/*
DECLARE etl_batch_uuid STRING DEFAULT STRING(CURRENT_TIMESTAMP);
DECLARE record_timestamp TIMESTAMP DEFAULT TIMESTAMP(CURRENT_TIMESTAMP);
DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2024-04-26 00:00:00');
DECLARE end_query_date TIMESTAMP DEFAULT TIMESTAMP('2024-04-27 02:00:00');
*/

-- Calculate the total pick_order_totals and save them
INSERT INTO `${tenant_id}_oa_curated.agg_orders` (
  etl_batch_id, record_timestamp, tenant, facility , source_system, total_orders, completed_orders, incompleted_orders, percent_completed
)

WITH latest_events AS (
SELECT
    tenant,
    facility,
    source_system,
    pick_order_uuid,
    pick_order_event,
    ROW_NUMBER() OVER (PARTITION BY pick_order_uuid ORDER BY record_timestamp DESC) as rn
  FROM `${tenant_id}_oa_curated.fct_pick_order`
  WHERE record_timestamp BETWEEN TIMESTAMP_TRUNC(TIMESTAMP(@start_query_date), DAY) AND TIMESTAMP(@end_query_date)
),
status_summary AS (
  SELECT
    tenant,
    facility,
    source_system,
    pick_order_uuid,
    CASE
      WHEN pick_order_event IN ('COMPLETE', 'pick_complete') THEN 'Completed'
      ELSE 'Incompleted'
    END AS order_status
  FROM latest_events
  WHERE rn = 1
)
SELECT
  @etl_batch_uuid AS etl_batch_id,
  CURRENT_TIMESTAMP() AS record_timestamp,
  tenant,
  facility,
  source_system,
  COUNT(DISTINCT pick_order_uuid) AS total_orders,
  COUNTIF(order_status = 'Completed') AS completed_orders,
  COUNTIF(order_status = 'Incompleted') AS incompleted_orders,
  ROUND(100 * COUNTIF(order_status = 'Completed') / COUNT(DISTINCT pick_order_uuid), 2) AS percent_completed
FROM status_summary
GROUP BY tenant,facility, source_system ;
