-- DECLARE ltarget_tenant STRING DEFAULT 'the tenant';
-- DECLARE etl_batch_uuid STRING DEFAULT '2023-12-12';
-- DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2021-09-27 00:00:00');
-- DECLARE end_query_date TIMES<PERSON>MP DEFAULT TIMESTAMP('2024-09-27 02:00:00');

CREATE TEMP TABLE tmp_dim_warehouse_subsystem
(
  -- Fields from landing table data
  warehouse_subsystem_code STRING NOT NULL, -- WarehouseSubsystemDimension.id
  facility_code STRING NOT NULL,            -- WarehouseSubsystemDimension.facility
  -- dimension_modification_time TIMESTAMP, -- unused -- WarehouseSubsystemDimension.dimensionModificationTime
  bid STRING,                               -- WarehouseSubsystemDimension.bid
  type STRING,                              -- WarehouseSubsystemDimension.type

  --
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL
);

INSERT INTO tmp_dim_warehouse_subsystem
WITH
  extracted_data AS (
    SELECT
      JSON_VALUE(data, '$.id') AS warehouse_subsystem_code,
      JSON_VALUE(data, '$.facility') AS facility_code,
      JSON_VALUE(data, '$.bid') AS bid,
      JSON_VALUE(data, '$.type') AS type,
      facility,
      source_system,
    FROM `{$tenant_id}_landing.WarehouseSubsystemDimension`
    WHERE ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  normalized_data AS (
    SELECT
      warehouse_subsystem_code,
      facility_code,
      bid,
      type,
      @ltarget_tenant AS tenant,
      facility, 
      source_system,
    FROM extracted_data
  )
SELECT
  warehouse_subsystem_code,
  facility_code,
  bid,
  type,
  tenant,
  facility,
  source_system,
FROM normalized_data;

MERGE INTO `{$tenant_id}_oa_curated.dim_warehouse_subsystem` df
USING (
  SELECT
    warehouse_subsystem_code,
    facility_code,
    bid,
    type,
    tenant,
    facility,
    source_system,
  FROM tmp_dim_warehouse_subsystem
) tt
ON df.warehouse_subsystem_code = tt.warehouse_subsystem_code
WHEN NOT MATCHED THEN
  INSERT (
    warehouse_subsystem_uuid,
    warehouse_subsystem_code,
    facility_code,
    etl_batch_id,
    bid,
    type,
    tenant,
    facility,
    source_system
  ) VALUES (
    `{$tenant_id}_oa_curated.Dimension_PK_Generator`([
      tt.tenant,
      tt.facility,
      tt.source_system,
      tt.warehouse_subsystem_code
    ]),
    tt.warehouse_subsystem_code,
    tt.facility_code,
    @etl_batch_uuid,
    tt.bid,
    tt.type,
    tt.tenant,
    tt.facility,
    tt.source_system
  )

















