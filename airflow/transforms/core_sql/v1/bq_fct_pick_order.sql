/*
**Set variables for running outside of DAG in BQ directly**
declare ltarget_tenant string default 'ict_development';
declare etl_batch_uuid string default '2020-12-12';

declare start_query_date timestamp default timestamp('2023-10-06 00:00:00');
declare end_query_date timestamp default timestamp('2023-10-08 02:00:00');
*/

CREATE TEMP TABLE tmp_staging_pick_order
(
    record_timestamp TIMESTAMP,
    area STRING,
    customer_order_code STRING,
    customer_order_name STRING,
    pick_order_code STRING,
    pick_order_name STRING,
    put_wall_flg INT64,
    pick_order_type STRING,
    pick_order_category STRING,
    operator_code STRING,
    operator_name STRING,
    location_code STRING,
    location_name STRING,
    work_area_code STRING,
    work_area_name STRING,
    module_code STRING,
    module_name STRING,
    bay STRING,
    zone_code STRING,
    wave_code STRING,
    pick_order_event STRING,
    tenant STRING,
    facility STRING,
    source_system STRING,
    reason_code STRING,
    unique_event_identifier STRING,
    event_detail STRING,
    workstation_code STRING,
    pick_order_fulfillment_handling_type STRING,
    pick_order_line_count_expected INT64,
    pick_order_total_qty_expected INT64,
    pick_order_value_added_service STRING,
    pick_order_priority INT64,
    pick_order_packing_requirement STRING,
    pick_order_latest_staging_date_time TIMESTAMP,
    pick_order_order_channel STRING,
    location_uuid STRING DEFAULT NULL,
    module_uuid STRING DEFAULT NULL,
    work_area_uuid STRING DEFAULT NULL,
    operator_uuid STRING DEFAULT NULL,
    reason_uuid STRING DEFAULT NULL,
    wave_uuid STRING DEFAULT NULL,
    zone_uuid STRING DEFAULT NULL,
    pick_order_uuid STRING DEFAULT NULL,
    customer_order_uuid STRING DEFAULT NULL,
    logical_destination_uuid STRING DEFAULT NULL
);
INSERT INTO tmp_staging_pick_order (record_timestamp, area, customer_order_code, customer_order_name, pick_order_code, pick_order_name, put_wall_flg, pick_order_type, pick_order_category, operator_code, operator_name, location_code, location_name, work_area_code, work_area_name, module_code, module_name, bay, zone_code, wave_code, pick_order_event, tenant, facility, source_system, reason_code, unique_event_identifier, event_detail, workstation_code, pick_order_fulfillment_handling_type, pick_order_line_count_expected, pick_order_total_qty_expected, pick_order_value_added_service, pick_order_priority, pick_order_packing_requirement, pick_order_latest_staging_date_time, pick_order_order_channel)

WITH extracted_fct_pick_order_data AS (
    -- Extracting data from JSON fields
    SELECT 
        JSON_VALUE(data, '$.eventTime') AS eventtime,
        JSON_VALUE(data, '$.orderPk') AS order_code,
        JSON_VALUE(data, '$.pickOrderPk') AS pick_order_code_value,
        JSON_VALUE(data, '$.pickOrderType') AS pick_order_type_value,
        JSON_VALUE(data, '$.orderCategory') AS order_category,
        JSON_VALUE(data, '$.userpk') AS userpk_code,
        JSON_VALUE(data, '$.userName') AS user_name,
        JSON_VALUE(data, '$.locationPk') AS locationpk_code,
        JSON_VALUE(data, '$.grouppk') AS grouppk_code,
        JSON_VALUE(data, '$.zonePk') AS zonepk_code,
        JSON_VALUE(data, '$.wavePk') AS wavepk_code,
        JSON_VALUE(data, '$.event') AS event_value,
        JSON_VALUE(data, '$.fulfillmentHandlingType') AS fulfillment_handling_type,
        JSON_VALUE(data, '$.totalLineCt') AS total_line_ct,
        JSON_VALUE(data, '$.totalQtyCt') AS total_qty_ct,
        JSON_VALUE(data, '$.vasRequirement') AS vas_requirement,
        JSON_VALUE(data, '$.urgency') AS urgency_value,
        JSON_VALUE(data, '$.packingRequirement') AS packing_requirement,
        JSON_VALUE(data, '$.latestStagingTime') AS latest_staging_time,
        JSON_VALUE(data, '$.orderChannel') AS order_channel,
        JSON_VALUE(data, '$.putWallPk') AS putwallpk_code,
        ingestion_date,
        facility,
        source_system
    FROM `${tenant_id}_landing.PickOrderFact`
    WHERE tenant = @ltarget_tenant
        AND JSON_VALUE(data, '$.pickOrderPk') IS NOT NULL
        AND TIMESTAMP(ingestion_date) BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
),
transformed_PickOrder AS (
    -- Applying operations on the extracted data
    SELECT 
        `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(eventtime, NULL) AS record_timestamp,
        'picking' AS area,
        COALESCE(
                CASE 
                    WHEN UPPER(order_code) = 'N/A' THEN ''
                    ELSE UPPER(order_code)
                END, 
        '') AS customer_order_code,
        COALESCE(
            CASE 
                WHEN STRPOS(order_code, '#') > 0 THEN 
                    SUBSTR(order_code, LENGTH(order_code) - STRPOS(REVERSE(order_code), '#') + 2) 
                ELSE order_code 
            END, 
        '') AS customer_order_name,
        UPPER(COALESCE(pick_order_code_value, '')) AS pick_order_code,
        COALESCE(
            CASE
                WHEN STRPOS(pick_order_code_value, '#') > 0 THEN 
                    SUBSTR(pick_order_code_value, -STRPOS(REVERSE(pick_order_code_value), '#')+1)
                ELSE 
                    pick_order_code_value
            END, 
        '') AS pick_order_name,
        CASE WHEN LENGTH(COALESCE(putwallpk_code, '')) > 0 THEN 1 ELSE 0 END AS put_wall_flg,
        COALESCE(
                CASE
                    WHEN UPPER(pick_order_type_value) = 'N/A' THEN ''
                    WHEN UPPER(pick_order_type_value) IN ('SHIPPING', 'CUSTOMER', 'STANDARD', 'STANDARDPW', 'CUSTOMPW') THEN 'CUSTOMER'
                    ELSE UPPER(pick_order_type_value)
                END, 
            '') AS pick_order_type,
        COALESCE(order_category, '') AS order_category,
        UPPER(COALESCE(COALESCE(userpk_code, user_name), '')) AS operator_code,
        COALESCE(user_name, '') AS operator_name,
        UPPER(COALESCE(locationpk_code, '')) AS location_code,
        `${tenant_id}_oa_curated.Location_Name_Generator`(locationpk_code) AS location_name,
        COALESCE(
            CASE 
                WHEN STRPOS(locationpk_code, '.') > 0 THEN 
                    UPPER(SUBSTR(locationpk_code, 0, INSTR(locationpk_code, '.', 1, 2)-1))
                ELSE 
                    '' 
            END, 
        '') AS work_area_code,
        CASE 
            WHEN STRPOS(locationpk_code, '.') > 0 THEN 
                UPPER(SPLIT(locationpk_code, '.')[SAFE_OFFSET(1)]) 
            ELSE 
                NULL
        END AS work_area_name,
        COALESCE(
                CASE 
                    WHEN STRPOS(locationpk_code, '.') > 0 THEN 
                    	UPPER(SUBSTR(locationpk_code, 0, INSTR(locationpk_code, '.', 1, 3)-1))
                    ELSE 
                        REPLACE(grouppk_code, '#', '.')
                END, 
        '') AS module_code,
        CASE 
            WHEN STRPOS(locationpk_code, '.') > 0 THEN 
                UPPER(SPLIT(locationpk_code, '.')[SAFE_OFFSET(2)]) 
            WHEN STRPOS(grouppk_code, '#') > 0 THEN 
                UPPER(SUBSTR(grouppk_code, -STRPOS(REVERSE(grouppk_code), '#')+1))
            ELSE 
                REPLACE(grouppk_code, '#', '.')
        END AS module_name,
        CASE 
            WHEN STRPOS(locationpk_code, '.') > 0 THEN 
                UPPER(SPLIT(locationpk_code, '.')[SAFE_OFFSET(3)]) 
            ELSE 
                NULL
        END as bay, 
        UPPER(COALESCE(zonepk_code, '')) AS zone_code,
        UPPER(COALESCE(wavepk_code, '')) AS wave_code,
        COALESCE(event_value, '') AS pick_order_event,
        @ltarget_tenant AS tenant,
        facility,
        source_system,
        '' AS reason_code,
        '' AS unique_event_identifier,
        '' AS event_detail,
        '' AS workstation_code,
        COALESCE(fulfillment_handling_type, '') AS pick_order_fulfillment_handling_type,
        COALESCE(
                SAFE_CAST(
                    CASE 
                        WHEN SAFE_CAST(total_line_ct AS INT64) = 0 THEN NULL 
                        ELSE total_line_ct
                    END AS INT64), 
                0)AS pick_order_line_count_expected,
        COALESCE(SAFE_CAST(total_qty_ct AS INT64), 0) AS pick_order_total_qty_expected,
        COALESCE(vas_requirement, '') AS pick_order_value_added_service,
        COALESCE(SAFE_CAST(urgency_value AS INT64), 0) AS pick_order_priority,
        COALESCE(packing_requirement, '') AS pick_order_packing_requirement,
        COALESCE(TIMESTAMP(latest_staging_time), TIMESTAMP("2020-12-12")) AS pick_order_latest_staging_date_time,
        COALESCE(order_channel, '') AS pick_order_order_channel
    FROM extracted_fct_pick_order_data
),

extracted_fct_pick_order_planning_data AS (
    -- Extracting data from JSON fields
    SELECT 
        SAFE_CAST(JSON_VALUE(data, '$.eventTime') AS TIMESTAMP) AS eventtime,
        SAFE_CAST(JSON_VALUE(data, '$.orderPk') AS STRING) AS order_code,
        SAFE_CAST(JSON_VALUE(data, '$.pickOrderPk') AS STRING) AS pick_order_code_value,
        SAFE_CAST(JSON_VALUE(data, '$.pickOrderType') AS STRING) AS pick_order_type_value,
        SAFE_CAST(JSON_VALUE(data, '$.orderCategory') AS STRING) AS order_category,
        SAFE_CAST(JSON_VALUE(data, '$.locationPk') AS STRING) AS locationpk_code,
        SAFE_CAST(JSON_VALUE(data, '$.grouppk') AS STRING) AS grouppk_code,
        SAFE_CAST(JSON_VALUE(data, '$.zonePk') AS STRING) AS zonepk_code,
        SAFE_CAST(JSON_VALUE(data, '$.event') AS STRING) AS event_value,
        SAFE_CAST(JSON_VALUE(data, '$.reason') AS STRING) AS reason_value,
        SAFE_CAST(JSON_VALUE(data, '$.eventUuid') AS STRING) AS event_uuid,
        SAFE_CAST(JSON_VALUE(data, '$.jsonData') AS STRING) AS json_data,
        SAFE_CAST(JSON_VALUE(data, '$.workstationid') AS STRING) AS workstation_id,
        @ltarget_tenant,
        facility,
        source_system
    FROM `${tenant_id}_landing.PickOrderPlanningFact`
    WHERE tenant = @ltarget_tenant
        AND TIMESTAMP(ingestion_date) BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
        AND JSON_VALUE(data, '$.pickOrderPk') IS NOT NULL
),

transformed_PickOrderPlanning AS (
    -- Applying operations on the extracted data
    SELECT 
        eventtime AS record_timestamp,
        'picking' AS area,
        COALESCE(UPPER(order_code), '') AS customer_order_code,
        COALESCE(
            CASE
                WHEN STRPOS(order_code, '#') > 0 THEN 
                SUBSTR(order_code, LENGTH(order_code) - STRPOS(REVERSE(order_code), '#') + 2) 
                ELSE order_code 
            END, 
        '') AS customer_order_name,
        UPPER(COALESCE(pick_order_code_value, '')) AS pick_order_code,
        COALESCE(
            CASE
                WHEN STRPOS(pick_order_code_value, '#') > 0 THEN 
                SUBSTR(pick_order_code_value, -STRPOS(REVERSE(pick_order_code_value), '#')+1)
                ELSE 
                pick_order_code_value
            END, 
        '') AS pick_order_name,
        0 AS put_wall_flg,
        COALESCE(
            CASE
                WHEN UPPER(pick_order_type_value) = 'N/A' THEN ''
                WHEN UPPER(pick_order_type_value) IN ('SHIPPING', 'CUSTOMER', 'STANDARD', 'STANDARDPW', 'CUSTOMPW') THEN 'CUSTOMER'
                ELSE UPPER(pick_order_type_value)
            END, 
        '') AS pick_order_type,
        COALESCE(order_category, '') AS pick_order_category,
        '' AS operator_code,
        '' AS operator_name,
        UPPER(COALESCE(locationpk_code, '')) AS location_code,
        `${tenant_id}_oa_curated.Location_Name_Generator`(locationpk_code) AS location_name,
        COALESCE(
            CASE 
                WHEN STRPOS(locationpk_code, '.') > 0 THEN 
                    UPPER(SUBSTR(locationpk_code, 0, INSTR(locationpk_code, '.', 1, 2)-1))
                ELSE '' 
            END, 
        '') AS work_area_code,
        CASE 
            WHEN STRPOS(locationpk_code, '.') > 0 THEN 
                UPPER(SPLIT(locationpk_code, '.')[SAFE_OFFSET(1)])
            ELSE NULL
        END AS work_area_name,
        COALESCE(
            CASE 
                WHEN STRPOS(locationpk_code, '.') > 0 THEN 
                    UPPER(SUBSTR(locationpk_code, 0, INSTR(locationpk_code, '.', 1, 3)-1))
                WHEN STRPOS(grouppk_code, '#') > 0 THEN 
                    UPPER(REPLACE(grouppk_code, '#', '.'))
                ELSE 
                    UPPER(grouppk_code)
            END, 
        '') AS module_code,
        CASE 
            WHEN STRPOS(locationpk_code, '.') > 0 THEN 
                UPPER(SPLIT(locationpk_code, '.')[SAFE_OFFSET(2)])
            WHEN STRPOS(grouppk_code, '#') > 0 THEN 
                UPPER(SUBSTR(grouppk_code, - STRPOS(REVERSE(grouppk_code), '#')+1))
            ELSE 
                UPPER(grouppk_code)
        END AS module_name,
        CASE 
            WHEN STRPOS(locationpk_code, '.') > 0 THEN 
            	UPPER(SPLIT(locationpk_code, '.')[SAFE_OFFSET(3)])
            ELSE NULL
        END as bay,
        UPPER(COALESCE(zonepk_code, '')) AS zone_code,
        '' AS wave_code,
        COALESCE(event_value, '') AS pick_order_event,
        @ltarget_tenant AS tenant,
        facility AS facility,
        source_system AS source_system,
        UPPER(COALESCE(reason_value, '')) AS reason_code,
        COALESCE(event_uuid, '') AS unique_event_identifier,
        COALESCE(json_data, '') AS event_detail,
        UPPER(COALESCE(workstation_id, '')) AS workstation_code,
        '' AS pick_order_fulfillment_handling_type,
        NULL AS pick_order_line_count_expected,
        NULL AS pick_order_total_qty_expected,
        '' AS pick_order_value_added_service,
        NULL AS pick_order_priority,
        '' AS pick_order_packing_requirement,
        TIMESTAMP("2020-12-12") AS pick_order_latest_staging_date_time,
        '' AS pick_order_order_channel
    FROM extracted_fct_pick_order_planning_data         
)

-- CTE to merge the results from both transformed tables
SELECT * FROM transformed_PickOrder
UNION ALL
SELECT * FROM transformed_PickOrderPlanning;


-- DIM_Location Merge
MERGE `${tenant_id}_oa_curated.dim_location` DL
USING (
    SELECT DISTINCT location_code, 
       MAX(location_name) as location_name,
       max(bay) as bay,
       tenant, 
       facility, 
       source_system,
       module_code,
       work_area_code
    FROM tmp_staging_pick_order
    GROUP BY location_code, module_code, work_area_code, tenant, facility, source_system
) TPO
ON DL.location_code = TPO.location_code
   AND DL.tenant = TPO.tenant 
   AND DL.facility = TPO.facility
   AND DL.source_system = TPO.source_system
WHEN MATCHED AND (DL.location_name <> TPO.location_name OR DL.bay <> TPO.bay) THEN
    UPDATE
        SET
            DL.location_name = COALESCE(TPO.location_name, DL.location_name),
            DL.bay = COALESCE(TPO.bay, DL.bay),
            etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
    INSERT (location_uuid, location_code, bay, location_name, tenant, facility, source_system, etl_batch_id)
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [TPO.tenant,
            TPO.facility,
            TPO.source_system,
            TPO.location_code]
        ), TPO.location_code, TPO.bay, TPO.location_name, TPO.tenant, TPO.facility, TPO.source_system, @etl_batch_uuid);

-- Update tmp_staging_pick_order with location_uuid
UPDATE tmp_staging_pick_order TPO
SET location_uuid = DL.location_uuid
FROM
    (
        select distinct
            location_uuid,
            location_code,
            tenant,
            facility,
            source_system
        from `${tenant_id}_oa_curated.dim_location`) DL
WHERE
    TPO.location_code = DL.location_code 
    AND TPO.tenant = DL.tenant
    AND TPO.facility = DL.facility
    AND TPO.source_system = DL.source_system;

--DIM_MODULE
MERGE `${tenant_id}_oa_curated.dim_module` dm
USING (
    SELECT DISTINCT
        module_code,
        max(module_name) as module_name,
        facility,
        source_system
    FROM tmp_staging_pick_order
    GROUP BY module_code, facility, source_system
) v
    ON
        dm.module_code = v.module_code
        AND dm.tenant = @ltarget_tenant
        AND dm.facility = v.facility
        AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (module_uuid, module_code, module_name, tenant, facility, source_system, active_rec_ind, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.module_code]
        ), v.module_code, v.module_name, @ltarget_tenant, v.facility, v.source_system, 1, @etl_batch_uuid);

UPDATE tmp_staging_pick_order tt 
SET module_uuid = d.module_uuid
FROM
    (
        select distinct
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        from 
`${tenant_id}_oa_curated.dim_module`) d
WHERE
      d.module_code = tt.module_code 
      AND d.tenant = @ltarget_tenant 
      AND d.facility = tt.facility 
      AND d.source_system = tt.source_system;

--DIM_WORK_AREA
MERGE `${tenant_id}_oa_curated.dim_work_area` dm
USING (
    SELECT DISTINCT
        work_area_code,
        max(work_area_name) as work_area_name,
        facility,
        source_system
    FROM tmp_staging_pick_order 
    GROUP BY work_area_code, facility, source_system
) v
    ON
        dm.work_area_code = v.work_area_code
        AND dm.tenant = @ltarget_tenant
        AND dm.facility = v.facility
        AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (work_area_uuid, work_area_code, work_area_name, tenant, facility, source_system, active_rec_ind, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.work_area_code]
        ), v.work_area_code, v.work_area_name, @ltarget_tenant, v.facility, v.source_system, 1, @etl_batch_uuid);

UPDATE tmp_staging_pick_order tt 
SET work_area_uuid = d.work_area_uuid
FROM
    (
        select distinct
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        from  
`${tenant_id}_oa_curated.dim_work_area`) d
WHERE
      d.work_area_code = tt.work_area_code 
      AND d.tenant = @ltarget_tenant 
      AND d.facility = tt.facility 
      AND d.source_system = tt.source_system;



-- DIM_Operator Merge
MERGE `${tenant_id}_oa_curated.dim_operator` DO
USING (
    SELECT DISTINCT operator_code, max(operator_name) as operator_name, tenant, facility, source_system
    FROM tmp_staging_pick_order group by operator_code, tenant, facility, source_system
) TPO
ON DO.operator_code = TPO.operator_code 
   AND DO.tenant = TPO.tenant 
   AND DO.facility = TPO.facility
   AND DO.source_system = TPO.source_system
WHEN  MATCHED AND (DO.operator_name <> TPO.operator_name) THEN
    UPDATE SET operator_name = TPO.operator_name
WHEN NOT MATCHED THEN
    INSERT (operator_uuid, operator_code, operator_name, tenant, facility, source_system, etl_batch_id)
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            TPO.facility,
            TPO.source_system,
            TPO.operator_code]
        ), TPO.operator_code, TPO.operator_name, TPO.tenant, TPO.facility, TPO.source_system, @etl_batch_uuid);

-- Update tmp_staging_pick_order with operator_uuid
UPDATE tmp_staging_pick_order TPO
SET operator_uuid = DO.operator_uuid
FROM
    (
        select distinct
            operator_uuid,
            operator_code,
            tenant,
            facility,
            source_system
        from  
`${tenant_id}_oa_curated.dim_operator`) DO
WHERE
    TPO.operator_code = DO.operator_code 
    AND TPO.tenant = DO.tenant
    AND TPO.facility = DO.facility
    AND TPO.source_system = DO.source_system;


-- DIM_Reason Merge
MERGE `${tenant_id}_oa_curated.dim_reason` DR
USING (
    SELECT DISTINCT reason_code, tenant, facility, source_system
    FROM tmp_staging_pick_order
    GROUP BY reason_code, tenant, facility, source_system
) V
ON DR.reason_code = V.reason_code 
   AND DR.tenant = V.tenant 
   AND DR.facility = V.facility
   AND DR.source_system = V.source_system
WHEN NOT MATCHED THEN
    INSERT(reason_uuid, reason_code, tenant, facility, source_system, etl_batch_id)
    VALUES(`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [V.tenant,
            V.facility,
            V.source_system,
            V.reason_code]
        ), V.reason_code, V.tenant, V.facility, V.source_system, @etl_batch_uuid);

-- Update tmp_staging_pick_order with reason_uuid
UPDATE tmp_staging_pick_order TPO
SET reason_uuid = DR.reason_uuid
FROM
    (
        select distinct
            reason_uuid,
            reason_code,
            tenant,
            facility,
            source_system
        from  
`${tenant_id}_oa_curated.dim_reason`) DR
WHERE
    TPO.reason_code = DR.reason_code 
    AND TPO.tenant = DR.tenant
    AND TPO.facility = DR.facility
    AND TPO.source_system = DR.source_system;



-- DIM_Wave Merge
MERGE `${tenant_id}_oa_curated.dim_wave` DW
USING (
    SELECT DISTINCT wave_code, tenant, facility, source_system
    FROM tmp_staging_pick_order
    GROUP BY wave_code, tenant, facility, source_system
) v
ON DW.wave_code = v.wave_code 
   AND DW.tenant = v.tenant 
   AND DW.facility = v.facility
   AND DW.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT(wave_uuid, wave_code, tenant, facility, source_system, etl_batch_id)
    VALUES(`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.wave_code]
        ), v.wave_code, v.tenant, v.facility, v.source_system, @etl_batch_uuid);

-- Update tmp_staging_pick_order with wave_uuid

UPDATE tmp_staging_pick_order TPO
SET wave_uuid = DW.wave_uuid
FROM
    (
        select distinct
            wave_uuid,
            wave_code,
            tenant,
            facility,
            source_system
        from   
`${tenant_id}_oa_curated.dim_wave`) DW
WHERE
    TPO.wave_code = DW.wave_code 
    AND TPO.tenant = DW.tenant
    AND TPO.facility = DW.facility
    AND TPO.source_system = DW.source_system;


--DIM_Customer_Order
MERGE `${tenant_id}_oa_curated.dim_customer_order` dco
USING (SELECT DISTINCT customer_order_code, max(customer_order_name) as customer_order_name, 
facility, source_system 
FROM tmp_staging_pick_order 
group by customer_order_code, facility, source_system) v
    ON
        dco.customer_order_code = v.customer_order_code
        AND dco.tenant = @ltarget_tenant
        AND dco.facility = v.facility
        AND dco.source_system = v.source_system
WHEN MATCHED AND (dco.customer_order_name <> v.customer_order_name) THEN
  UPDATE SET 
    customer_order_name = v.customer_order_name,
    etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
    INSERT
        (
            customer_order_uuid,
            customer_order_code,
            customer_order_name,
            tenant,
            facility,
            source_system,
            active_rec_ind,
            etl_batch_id
        )
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.customer_order_code]
        ), v.customer_order_code, v.customer_order_name, @ltarget_tenant, v.facility, v.source_system, 1, @etl_batch_uuid);

UPDATE tmp_staging_pick_order tt SET
    customer_order_uuid = d.customer_order_uuid
FROM
    (
        select distinct
            customer_order_uuid,
            customer_order_code,
            tenant,
            facility,
            source_system
        from   
`${tenant_id}_oa_curated.dim_customer_order`) d
WHERE
    d.tenant = @ltarget_tenant 
    AND d.facility = tt.facility 
    AND d.source_system = tt.source_system 
    AND tt.customer_order_code = d.customer_order_code;


-- DIM_Zone Merge
MERGE `${tenant_id}_oa_curated.dim_zone` DZ
USING (
    SELECT DISTINCT zone_code, tenant, facility, source_system
    FROM tmp_staging_pick_order
    GROUP BY zone_code, tenant, facility, source_system
) TPO
ON DZ.zone_code = TPO.zone_code
   AND DZ.tenant = TPO.tenant
   AND DZ.facility = TPO.facility
   AND DZ.source_system = TPO.source_system
WHEN NOT MATCHED THEN
    INSERT(zone_uuid, zone_code, tenant, facility, source_system, etl_batch_id)
    VALUES(`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [TPO.tenant,
            TPO.facility,
            TPO.source_system,
            TPO.zone_code]
        ), TPO.zone_code, TPO.tenant, TPO.facility, TPO.source_system, @etl_batch_uuid);

-- Update tmp_staging_pick_order with zone_uuid
UPDATE tmp_staging_pick_order TPO
SET zone_uuid = DZ.zone_uuid
FROM
    (
        select distinct
            zone_uuid,
            zone_code,
            tenant,
            facility,
            source_system
        from    
`${tenant_id}_oa_curated.dim_zone`) DZ
WHERE
    TPO.zone_code = DZ.zone_code 
    AND TPO.tenant = DZ.tenant
    AND TPO.facility = DZ.facility
    AND TPO.source_system = DZ.source_system
;

-- DIM_Pick_Order Merge
MERGE `${tenant_id}_oa_curated.dim_pick_order` DP
USING (
SELECT DISTINCT pick_order_code, customer_order_uuid, 
tenant, facility, source_system, 
max(pick_order_name) as pick_order_name, max(put_wall_flg) as put_wall_flg, 
max(pick_order_type) as pick_order_type, max(pick_order_category) as pick_order_category, 
max(pick_order_fulfillment_handling_type) as pick_order_fulfillment_handling_type, 
max(pick_order_value_added_service) as pick_order_value_added_service, max(pick_order_priority) as pick_order_priority, 
max(pick_order_packing_requirement) as pick_order_packing_requirement, 
max(pick_order_latest_staging_date_time) as pick_order_latest_staging_date_time, 
max(pick_order_order_channel) as pick_order_order_channel, max(pick_order_line_count_expected) as pick_order_line_count_expected, 
max(pick_order_total_qty_expected) as pick_order_total_qty_expected FROM tmp_staging_pick_order 
group by pick_order_code, customer_order_uuid, tenant, facility, source_system
) V
ON DP.pick_order_code = V.pick_order_code 
   AND DP.customer_order_uuid = V.customer_order_uuid  
   AND DP.tenant = V.tenant 
   AND DP.facility = V.facility
   AND DP.source_system = V.source_system
WHEN MATCHED THEN
    UPDATE SET 
        pick_order_type = V.pick_order_type,
        pick_order_category = V.pick_order_category,
        pick_order_fulfillment_handling_type = V.pick_order_fulfillment_handling_type,
        pick_order_value_added_service = V.pick_order_value_added_service,
        pick_order_priority = V.pick_order_priority,
        pick_order_packing_requirement = V.pick_order_packing_requirement,
        pick_order_latest_staging_date_time = V.pick_order_latest_staging_date_time,
        pick_order_order_channel = V.pick_order_order_channel,
        pick_order_line_count_expected = V.pick_order_line_count_expected,
        pick_order_total_qty_expected = V.pick_order_total_qty_expected
WHEN NOT MATCHED THEN
    INSERT(
        pick_order_uuid, 
        customer_order_uuid, 
        pick_order_code, 
        pick_order_name, 
        put_wall_flg, 
        pick_order_type, 
        pick_order_category, 
        pick_order_fulfillment_handling_type, 
        pick_order_value_added_service, 
        pick_order_priority, 
        pick_order_packing_requirement, 
        pick_order_latest_staging_date_time, 
        pick_order_order_channel, 
        pick_order_line_count_expected, 
        pick_order_total_qty_expected, 
        tenant, 
        facility, 
        source_system, 
        etl_batch_id
    )
    VALUES(
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [V.tenant,
            V.facility,
            V.source_system,
            V.pick_order_code]
        ), 
        customer_order_uuid, 
        V.pick_order_code, 
        V.pick_order_name, 
        V.put_wall_flg, 
        V.pick_order_type, 
        V.pick_order_category, 
        V.pick_order_fulfillment_handling_type, 
        V.pick_order_value_added_service, 
        V.pick_order_priority, 
        V.pick_order_packing_requirement, 
        V.pick_order_latest_staging_date_time, 
        V.pick_order_order_channel, 
        V.pick_order_line_count_expected, 
        V.pick_order_total_qty_expected, 
        V.tenant, 
        V.facility, 
        V.source_system,
        @etl_batch_uuid
    );
-- Update tmp_staging_pick_order with zone_uuid
UPDATE tmp_staging_pick_order TPO
SET pick_order_uuid = DZ.pick_order_uuid
FROM
    (
        select distinct
            pick_order_uuid,
            pick_order_code,
            customer_order_uuid,
            tenant,
            facility,
            source_system
        from     
`${tenant_id}_oa_curated.dim_pick_order`) DZ
WHERE
    TPO.pick_order_code = DZ.pick_order_code
    AND TPO.customer_order_uuid = DZ.customer_order_uuid
    AND TPO.tenant = DZ.tenant
    AND TPO.facility = DZ.facility
    AND TPO.source_system = DZ.source_system;

-- FINAL FCT Insert
INSERT INTO `${tenant_id}_oa_curated.fct_pick_order` (
    record_timestamp,
    area,
    pick_order_uuid,
    wave_uuid,
    location_uuid,
    module_uuid,
    work_area_uuid,
    operator_uuid,
    zone_uuid,
    reason_uuid,
    pick_order_event,
    unique_event_identifier,
    event_detail,
    tenant,
    facility,
    source_system,
    etl_batch_id
)
SELECT DISTINCT
    record_timestamp,
    MAX(area) AS area,
    pick_order_uuid,
    wave_uuid,
    location_uuid,
    module_uuid,
    work_area_uuid,
    operator_uuid,
    zone_uuid,
    reason_uuid,
    MAX(pick_order_event) AS pick_order_event,
    unique_event_identifier,
    MAX(event_detail) AS event_detail,
    tenant,
    facility,
    source_system,
    @etl_batch_uuid AS etl_batch_id
FROM tmp_staging_pick_order
GROUP BY
    record_timestamp,
    pick_order_uuid,
    wave_uuid,
    location_uuid,
    module_uuid,
    work_area_uuid,
    operator_uuid,
    zone_uuid,
    reason_uuid,
    unique_event_identifier,
    tenant, facility, source_system;
