/*
declare ltarget_tenant string default '${tenant_id}';
declare etl_batch_uuid string default '2020-12-12';

declare start_query_date timestamp default timestamp('2023-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2023-09-27 02:00:00');
*/

CREATE TEMP TABLE tmp_staging_fct_movement
(
    record_timestamp TIMESTAMP,
    move_start_date_time TIMESTAMP,
    load_unit_code STRING,
    source_location_code STRING default '',
    source_work_area_code STRING default '',
    source_work_area_name STRING,
    source_module_code STRING default '',
    source_module_name STRING,
    source_bay_name STRING,
    source_location_name STRING,
    destination_location_code STRING,
    destination_work_area_code STRING,
    destination_work_area_name STRING,
    destination_module_code STRING,
    destination_module_name STRING,
    destination_bay_name STRING,
    destination_location_name STRING,
    device_code STRING,
    physical_device_code STRING,
    movement_type_code STRING,
    item_code STRING,
    status_code STRING,
    transport_reason_code STRING,
    transport_request_id STRING,
    group_transport_request_id STRING,
    move_duration_seconds INT64,
    item_count INT64,
    x_distance_traveled INT64,
    aisles_traveled_count INT64,
    subsystem_code STRING,
    subsystem_category STRING,
    tenant STRING,
    facility STRING,
    source_system STRING,
    item_sku STRING,
    container_type_code STRING,
    load_unit_content_code STRING,
    relative_x_axis STRING,
    relative_y_axis STRING,
    relative_z_axis STRING,
    put_wall_cubby_code STRING,
    workstation_code STRING,
    move_arrive_date_time TIMESTAMP,
    move_leave_date_time TIMESTAMP,
    item_category_code STRING DEFAULT NULL,
    subsystem_uuid STRING DEFAULT NULL,
    device_uuid STRING DEFAULT NULL,
    physical_device_uuid STRING DEFAULT NULL,
    item_uuid STRING DEFAULT NULL,
    container_type_uuid STRING DEFAULT NULL,
    reason_uuid STRING DEFAULT NULL,
    status_uuid STRING DEFAULT NULL,
    source_location_uuid STRING DEFAULT NULL,
    source_module_uuid STRING DEFAULT NULL,
    source_work_area_uuid STRING DEFAULT NULL,
    destination_location_uuid STRING DEFAULT NULL,
    destination_module_uuid STRING DEFAULT NULL,
    destination_work_area_uuid STRING DEFAULT NULL,
    workstation_uuid STRING DEFAULT NULL,
    nok_counter INT64 DEFAULT NULL
);


-- MULTISHUTTLE MOVEMENT
INSERT INTO tmp_staging_fct_movement (
    record_timestamp,
    move_start_date_time,
    load_unit_code,
    source_location_code,
    source_work_area_code,
    source_work_area_name,
    source_module_code,
    source_module_name,
    source_bay_name,
    source_location_name,
    destination_location_code,
    destination_work_area_code,
    destination_work_area_name,
    destination_module_code,
    destination_module_name,
    destination_bay_name,
    destination_location_name,
    device_code,
    physical_device_code,
    movement_type_code,
    item_code,
    status_code,
    transport_reason_code,
    transport_request_id,
    group_transport_request_id,
    move_duration_seconds,
    item_count,
    x_distance_traveled,
    aisles_traveled_count,
    subsystem_code,
    subsystem_category,
    tenant,
    facility,
    source_system,
    item_sku,
    container_type_code,
    load_unit_content_code,
    relative_x_axis,
    relative_y_axis,
    relative_z_axis,
    put_wall_cubby_code,
    workstation_code,
    move_arrive_date_time,
    move_leave_date_time,
    item_category_code,
    nok_counter
)
WITH extracted_fct_ms_movement_data AS (
    SELECT
        JSON_VALUE(data, '$.transportRequestId') AS transportrequestid,
        JSON_VALUE(data, '$.shuttleId') AS shuttleid,
        JSON_VALUE(data, '$.liftId') AS liftid,
        JSON_VALUE(data, '$.sourceLocationId') AS sourcelocationid,
        JSON_VALUE(data, '$.destinationLocationId') AS destinationlocationid,
        JSON_VALUE(data, '$.aisle') AS aisle,
        JSON_VALUE(data, '$.level') AS level,
        COALESCE(JSON_VALUE(data, '$.subsystemId'),JSON_VALUE(data, '$.subsystemId.value')) AS subsystemid,
        COALESCE(JSON_VALUE(data, '$.loadUnitId'), JSON_VALUE(data, '$.loadUnitId.value')) AS loadunitid,
        COALESCE(JSON_VALUE(data, '$.movementType'),JSON_VALUE(data, '$.movementType.value')) AS movementtype,
        COALESCE(JSON_VALUE(data, '$.eventCode'),JSON_VALUE(data, '$.eventCode.value')) AS eventcode,
        COALESCE(JSON_VALUE(data, '$.transportReason'), JSON_VALUE(data, '$.transportReason.value')) AS transportreason,
        SAFE_CAST(JSON_VALUE(data, '$.startTime') AS TIMESTAMP) AS starttime,
        SAFE_CAST(JSON_VALUE(data, '$.endTime') AS TIMESTAMP) AS endtime,
        SAFE_CAST(JSON_VALUE(data, '$.duration') AS INT64) AS duration,
        SAFE_CAST(JSON_VALUE(data, '$.deltax') AS INT64) AS deltax,
        SAFE_CAST(JSON_VALUE(data, '$.deltaAisle') AS INT64) AS deltaaisle,
        JSON_VALUE(data, '$.skuId') AS skuid,
        JSON_VALUE(data, '$.productRangeId') AS productrangeid,
        SAFE_CAST(JSON_VALUE(data, '$.nokCounter') AS INT64) AS nokcounter,
        JSON_VALUE(data, '$.FaultStartTime') AS faultstarttime,
        JSON_VALUE(data, '$.FaultPhysicalShuttleID') AS faultphysicalshuttleid,
        JSON_VALUE(data, '$.FaultSourceLocationID') AS faultsourcelocationid,
        JSON_VALUE(data, '$.FaultCode') AS faultcode,
        JSON_VALUE(data, '$.FaultClassification') AS faultclassification,
        SAFE_CAST(JSON_VALUE(data, '$.FaultAckTime') AS TIMESTAMP)
            AS faultacktime,
        SAFE_CAST(JSON_VALUE(data, '$.FaultDuration') AS INT64)
            AS faultduration,
        SAFE_CAST(JSON_VALUE(data, '$.FaultRepairTime') AS INT64)
            AS faultrepairtime,
        SAFE_CAST(JSON_VALUE(data, '$.FaultEndTime') AS TIMESTAMP)
            AS faultendtime,
        SAFE_CAST(JSON_VALUE(data, '$.Record_Timestamp_Offset') AS TIMESTAMP)
            AS record_timestamp_offset,
        JSON_VALUE(data, '$.hardwareIdentifier') AS hardwareidentifier,
        SAFE_CAST(JSON_VALUE(data, '$.arriveTime') AS TIMESTAMP) AS arrivetime,
        SAFE_CAST(JSON_VALUE(data, '$.leaveTime') AS TIMESTAMP) AS leavetime,
        @ltarget_tenant AS tenant,
        facility AS facility,
        source_system AS source_system
    FROM `${tenant_id}_landing.MultishuttleMovementFact`
    WHERE
        tenant = @ltarget_tenant
        AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(
            @end_query_date
        )
),

transformed_ms_events AS (
    SELECT
        COALESCE(endtime, NULL) AS record_timestamp,
        COALESCE(starttime, NULL) AS move_start_date_time,
        COALESCE(loadunitid, '') AS load_unit_code,
        COALESCE(sourcelocationid, '') AS source_location_code,
        COALESCE(CASE
            WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SUBSTR(sourcelocationid, 0, INSTR(sourcelocationid, '.',1, 2)-1))
            ELSE NULL
        END, '') AS source_work_area_code,
        CASE
            WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SPLIT(sourcelocationid, '.')[SAFE_OFFSET(1)])
            ELSE NULL
        END AS source_work_area_name,
        COALESCE(CASE
            WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SUBSTR(sourcelocationid, 0, INSTR(sourcelocationid, '.',1, 3)-1))
            ELSE NULL
        END, '') AS source_module_code,
        CASE
            WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SPLIT(sourcelocationid, '.')[SAFE_OFFSET(2)])
            ELSE NULL
        END AS source_module_name,
        CASE
            WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SPLIT(sourcelocationid, '.')[SAFE_OFFSET(3)])
            ELSE NULL
        END AS source_bay_name,
        `${tenant_id}_oa_curated.Location_Name_Generator`(sourcelocationid) AS source_location_name,
        COALESCE(destinationlocationid, '') AS destination_location_code,
        COALESCE(CASE
            WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SUBSTR(destinationlocationid, 0, INSTR(destinationlocationid, '.',1, 2)-1))
            WHEN STRPOS(aisle, '#') > 0 THEN UPPER(REPLACE(SUBSTR(aisle, 0, INSTR(aisle, '#',1, 2)-1), '#', '.'))
            ELSE NULL
        END, '') AS destination_work_area_code,
        CASE
            WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SPLIT(destinationlocationid, '.')[SAFE_OFFSET(1)])
            WHEN STRPOS(aisle, '#') > 0 THEN UPPER(SPLIT(aisle, '#')[SAFE_OFFSET(1)])
            ELSE NULL
        END AS destination_work_area_name,
        COALESCE(CASE
            WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SUBSTR(destinationlocationid, 0, INSTR(destinationlocationid, '.',1, 3)-1))
            WHEN STRPOS(aisle, '#') > 0 THEN UPPER(REPLACE(aisle, '#', '.'))
            WHEN aisle IS NOT NULL THEN UPPER(aisle)
            ELSE ''
        END, '') AS destination_module_code,
        CASE
            WHEN STRPOS(aisle, '#') > 0 THEN UPPER(SPLIT(aisle, '#')[SAFE_OFFSET(2)])
            WHEN aisle IS NOT NULL THEN UPPER(aisle)
            WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SPLIT(destinationlocationid, '.')[SAFE_OFFSET(2)])
            ELSE NULL
        END AS destination_module_name,
        CASE
            WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SPLIT(destinationlocationid, '.')[SAFE_OFFSET(3)])
            ELSE level
        END AS destination_bay_name,
        `${tenant_id}_oa_curated.Location_Name_Generator`(destinationlocationid) AS destination_location_name,
        COALESCE(shuttleid, liftid) AS device_code,
        COALESCE(hardwareidentifier, '') AS physical_device_code,
        COALESCE(movementtype, '') AS movement_type_code,
        ${tenant_id}_oa_curated.Item_Code_Generator(skuid, NULL, NULL, NULL) AS item_code,
        COALESCE(eventcode, '') AS status_code,
        COALESCE(transportreason, '') AS transport_reason_code,
        COALESCE(transportrequestid, '') AS transport_request_id,
        COALESCE(transportrequestid, '') AS group_transport_request_id,
        COALESCE(duration, 0) AS move_duration_seconds,
        NULL AS item_count,
        COALESCE(deltax, NULL) AS x_distance_traveled,
        COALESCE(deltaaisle, NULL) AS aisles_traveled_count,
        'MULTISHUTTLE' AS subsystem_code,
        'DEMATIC IQ' AS subsystem_category,
        @ltarget_tenant AS tenant,
        facility,
        source_system,
        COALESCE(CASE
            WHEN skuid LIKE '%EMPTY_SKU%' THEN ''
            ELSE skuid
        END, '') AS item_sku,
        '' AS container_type_code,
        '' AS load_unit_content_code,
        '' AS relative_x_axis,
        '' AS relative_y_axis,
        '' AS relative_z_axis,
        '' AS put_wall_cubby_code,
        '' AS workstation_code,
        CASE
            WHEN
                LEFT(sourcelocationid, 4) = 'MSAI'
                THEN LEFT(sourcelocationid, 6)
            WHEN
                LEFT(destinationlocationid, 4) = 'MSAI'
                THEN LEFT(destinationlocationid, 6)
            ELSE ''
        END AS subsystemid,
        COALESCE(arrivetime, NULL) AS move_arrive_date_time,
        COALESCE(leavetime, NULL) AS move_leave_date_time,
        COALESCE(nokcounter, NULL) AS nok_counter
    FROM
        extracted_fct_ms_movement_data
    WHERE starttime IS NOT NULL AND endtime IS NOT NULL
),

to_lookup_events AS (
    SELECT transformed_ms_events.*
    FROM
        transformed_ms_events
    WHERE transformed_ms_events.device_code IS NULL
),

device_code_lookup AS (
    SELECT
        tmp2.subsystemid,
        tmp2.deviceid
    FROM
        (
            SELECT
                tmp.subsystemid,
                tmp.deviceid,
                ROW_NUMBER()
                    OVER (PARTITION BY tmp.subsystemid ORDER BY tmp.subsystemid)
                    AS rn
            FROM
                (
                    SELECT
                        CASE
                            WHEN
                                LEFT(sourcelocationid, 4) = 'MSAI'
                                THEN LEFT(sourcelocationid, 6)
                            WHEN
                                LEFT(destinationlocationid, 4) = 'MSAI'
                                THEN LEFT(destinationlocationid, 6)
                            ELSE SAFE_CAST(NULL AS STRING)
                        END AS subsystemid,
                        COALESCE(shuttleid, liftid) AS deviceid
                    FROM
                        extracted_fct_ms_movement_data
                    WHERE
                        extracted_fct_ms_movement_data.tenant = @ltarget_tenant
                ) AS tmp
            WHERE tmp.subsystemid IS NOT NULL
            GROUP BY 1, 2
        ) AS tmp2
    WHERE tmp2.rn = 1
)


SELECT
    record_timestamp,
    move_start_date_time,
    load_unit_code,
    source_location_code,
    source_work_area_code,
    max(source_work_area_name) as source_work_area_name,
    source_module_code,
    max(source_module_name) as source_module_name,
    source_bay_name,
    source_location_name,
    destination_location_code,
    destination_work_area_code,
    max(destination_work_area_name) as destination_work_area_name,
    destination_module_code,
    max(destination_module_name) as destination_module_name,
    destination_bay_name,
    destination_location_name,
    device_code,
    physical_device_code,
    movement_type_code,
    item_code,
    status_code,
    transport_reason_code,
    transport_request_id,
    group_transport_request_id,
    move_duration_seconds,
    item_count,
    x_distance_traveled,
    aisles_traveled_count,
    subsystem_code,
    subsystem_category,
    tenant,
    facility AS facility,
    source_system AS source_system,
    item_sku,
    container_type_code,
    load_unit_content_code,
    relative_x_axis,
    relative_y_axis,
    relative_z_axis,
    put_wall_cubby_code,
    workstation_code,
    move_arrive_date_time,
    MIN(move_leave_date_time) AS move_leave_date_time,
    '' AS item_category_code,
    nok_counter AS nok_counter
FROM
    transformed_ms_events
WHERE device_code IS NOT NULL
GROUP BY
    record_timestamp,
    move_start_date_time,
    load_unit_code,
    source_location_code,
    source_work_area_code,
    source_module_code,
    source_bay_name,
    source_location_name,
    destination_location_code,
    destination_work_area_code,
    destination_module_code,
    destination_bay_name,
    destination_location_name,
    device_code,
    physical_device_code,
    movement_type_code,
    item_code,
    status_code,
    transport_reason_code,
    transport_request_id,
    group_transport_request_id,
    move_duration_seconds,
    item_count,
    x_distance_traveled,
    aisles_traveled_count,
    subsystem_code,
    subsystem_category,
    tenant, facility, source_system,
    item_sku,
    container_type_code,
    load_unit_content_code,
    relative_x_axis,
    relative_y_axis,
    relative_z_axis,
    put_wall_cubby_code,
    workstation_code,
    move_arrive_date_time,
    nok_counter


UNION ALL
SELECT DISTINCT
    tle.record_timestamp,
    tle.move_start_date_time,
    tle.load_unit_code,
    tle.source_location_code,
    tle.source_work_area_code,
    tle.source_work_area_name,
    tle.source_module_code,
    tle.source_module_name,
    tle.source_bay_name,
    tle.source_location_name,
    tle.destination_location_code,
    tle.destination_work_area_code,
    tle.destination_work_area_name,
    tle.destination_module_code,
    tle.destination_module_name,
    tle.destination_bay_name,
    tle.destination_location_name,
    COALESCE(dce.deviceid, '') AS device_code,
    tle.physical_device_code,
    tle.movement_type_code,
    tle.item_code,
    tle.status_code,
    tle.transport_reason_code,
    tle.transport_request_id,
    tle.group_transport_request_id,
    tle.move_duration_seconds,
    tle.item_count,
    tle.x_distance_traveled,
    tle.aisles_traveled_count,
    tle.subsystem_code,
    tle.subsystem_category,
    tle.tenant,
    tle.facility,
    tle.source_system,
    tle.item_sku,
    tle.container_type_code,
    tle.load_unit_content_code,
    tle.relative_x_axis,
    tle.relative_y_axis,
    tle.relative_z_axis,
    tle.put_wall_cubby_code,
    tle.workstation_code,
    tle.move_arrive_date_time,
    tle.move_leave_date_time,
    '' AS item_category_code,
    tle.nok_counter AS nok_counter
FROM
    to_lookup_events AS tle
INNER JOIN (
    SELECT
        device_code_lookup.subsystemid,
        device_code_lookup.deviceid
    FROM
        device_code_lookup
) AS dce ON tle.subsystemid = dce.subsystemid
;

-- CONNECTION MOVEMENT
INSERT INTO tmp_staging_fct_movement (
    record_timestamp,
    move_start_date_time,
    load_unit_code,
    source_location_code,
    source_work_area_code,
    source_work_area_name,
    source_module_code,
    source_module_name,
    source_bay_name,
    source_location_name,
    destination_location_code,
    destination_work_area_code,
    destination_work_area_name,
    destination_module_code,
    destination_module_name,
    destination_bay_name,
    destination_location_name,
    device_code,
    physical_device_code,
    movement_type_code,
    item_code,
    status_code,
    transport_reason_code,
    transport_request_id,
    group_transport_request_id,
    move_duration_seconds,
    item_count,
    x_distance_traveled,
    aisles_traveled_count,
    subsystem_code,
    subsystem_category,
    tenant,
    facility,
    source_system,
    item_sku,
    container_type_code,
    load_unit_content_code,
    relative_x_axis,
    relative_y_axis,
    relative_z_axis,
    put_wall_cubby_code,
    workstation_code,
    move_arrive_date_time,
    move_leave_date_time,
    item_category_code,
    nok_counter
)
WITH extracted_fct_connection_movement_data AS (
    SELECT
        COALESCE(JSON_VALUE(data, '$.subsystemId'),JSON_VALUE(data, '$.subsystemId.value')) AS subsystemid,
        COALESCE(JSON_VALUE(data, '$.loadUnitId'), JSON_VALUE(data, '$.loadUnitId.value')) AS loadunitid,
        COALESCE(JSON_VALUE(data, '$.movementType'),JSON_VALUE(data, '$.movementType.value')) AS movementtype,
        COALESCE(JSON_VALUE(data, '$.eventCode'),JSON_VALUE(data, '$.eventCode.value')) AS eventcode,
        COALESCE(JSON_VALUE(data, '$.transportReason'), JSON_VALUE(data, '$.transportReason.value')) AS transportreason,
        JSON_VALUE(data, '$.transportRequestId') AS transportrequestid,
        JSON_VALUE(data, '$.shuttleId') AS shuttleid,
        JSON_VALUE(data, '$.liftId') AS liftid,
        JSON_VALUE(data, '$.sourceLocationId') AS sourcelocationid,
        JSON_VALUE(data, '$.destinationLocationId') AS destinationlocationid,
        JSON_VALUE(data, '$.aisle') AS aisle,
        JSON_VALUE(data, '$.level') AS level,
        SAFE_CAST(JSON_VALUE(data, '$.startTime') AS TIMESTAMP) AS starttime,
        SAFE_CAST(JSON_VALUE(data, '$.endTime') AS TIMESTAMP) AS endtime,
        SAFE_CAST(JSON_VALUE(data, '$.duration') AS INT64) AS duration,
        SAFE_CAST(JSON_VALUE(data, '$.deltax') AS INT64) AS deltax,
        SAFE_CAST(JSON_VALUE(data, '$.deltaAisle') AS INT64) AS deltaaisle,
        JSON_VALUE(data, '$.skuId') AS skuid,
        JSON_VALUE(data, '$.productRangeId') AS productrangeid,
        SAFE_CAST(JSON_VALUE(data, '$.nokCounter') AS INT64) AS nokcounter,
        JSON_VALUE(data, '$.FaultStartTime') AS faultstarttime,
        JSON_VALUE(data, '$.FaultPhysicalShuttleID') AS faultphysicalshuttleid,
        JSON_VALUE(data, '$.FaultSourceLocationID') AS faultsourcelocationid,
        JSON_VALUE(data, '$.FaultCode') AS faultcode,
        JSON_VALUE(data, '$.FaultClassification') AS faultclassification,
        SAFE_CAST(JSON_VALUE(data, '$.FaultAckTime') AS TIMESTAMP)
            AS faultacktime,
        SAFE_CAST(JSON_VALUE(data, '$.FaultDuration') AS INT64)
            AS faultduration,
        SAFE_CAST(JSON_VALUE(data, '$.FaultRepairTime') AS INT64)
            AS faultrepairtime,
        SAFE_CAST(JSON_VALUE(data, '$.FaultEndTime') AS TIMESTAMP)
            AS faultendtime,
        SAFE_CAST(JSON_VALUE(data, '$.Record_Timestamp_Offset') AS TIMESTAMP)
            AS record_timestamp_offset,
        JSON_VALUE(data, '$.hardwareIdentifier') AS hardwareidentifier,
        SAFE_CAST(JSON_VALUE(data, '$.arriveTime') AS TIMESTAMP) AS arrivetime,
        SAFE_CAST(JSON_VALUE(data, '$.leaveTime') AS TIMESTAMP) AS leavetime,
        facility,
        source_system
    FROM `${tenant_id}_landing.ConnectionMovementFact`
    WHERE
        tenant = @ltarget_tenant
        AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(
            @end_query_date
        )
)

SELECT DISTINCT
    COALESCE(endtime, NULL) AS record_timestamp,
    COALESCE(starttime, NULL) AS move_start_date_time,
    COALESCE(loadunitid, '') AS load_unit_code,
    COALESCE(sourcelocationid, '') AS source_location_code,
    COALESCE(CASE
        WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SUBSTR(sourcelocationid, 0, INSTR(sourcelocationid, '.',1, 2)-1))
        ELSE NULL
    END, '') AS source_work_area_code,
    CASE
        WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SPLIT(sourcelocationid, '.')[SAFE_OFFSET(1)])
        ELSE NULL
    END AS source_work_area_name,
    COALESCE(CASE
        WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SUBSTR(sourcelocationid, 0, INSTR(sourcelocationid, '.',1, 3)-1))
        ELSE NULL
    END, '') AS source_module_code,
    CASE
        WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SPLIT(sourcelocationid, '.')[SAFE_OFFSET(2)])
        ELSE NULL
    END AS source_module_name,
    CASE
        WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SPLIT(sourcelocationid, '.')[SAFE_OFFSET(3)])
        ELSE NULL
    END AS source_bay_name,
    `${tenant_id}_oa_curated.Location_Name_Generator`(sourcelocationid) AS source_location_name,
    COALESCE(destinationlocationid, '') AS destination_location_code,
    COALESCE(CASE
        WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SUBSTR(destinationlocationid, 0, INSTR(destinationlocationid, '.',1, 2)-1))
        WHEN STRPOS(aisle, '#') > 0 THEN UPPER(REPLACE(SUBSTR(aisle, 0, INSTR(aisle, '#',1, 2)-1), '#', '.'))
        ELSE NULL
    END, '') AS destination_work_area_code,
    CASE
        WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SPLIT(destinationlocationid, '.')[SAFE_OFFSET(1)])
        WHEN STRPOS(aisle, '#') > 0 THEN UPPER(SPLIT(aisle, '#')[SAFE_OFFSET(1)])
        ELSE NULL
    END AS destination_work_area_name,
    COALESCE(CASE
        WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SUBSTR(destinationlocationid, 0, INSTR(destinationlocationid, '.',1, 3)-1))
        WHEN STRPOS(aisle, '#') > 0 THEN UPPER(REPLACE(aisle, '#', '.'))
        WHEN aisle IS NOT NULL THEN UPPER(aisle)
        ELSE ''
    END, '') AS destination_module_code,
    CASE
        WHEN STRPOS(aisle, '#') > 0 THEN UPPER(SPLIT(aisle, '#')[SAFE_OFFSET(2)])
        WHEN aisle IS NOT NULL THEN UPPER(aisle)
        WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SPLIT(destinationlocationid, '.')[SAFE_OFFSET(2)])
        ELSE NULL
    END AS destination_module_name,
    CASE
        WHEN STRPOS(destinationlocationid, '.') > 0 THEN SPLIT(destinationlocationid, '.')[SAFE_OFFSET(3)]
        ELSE level
    END AS destination_bay_name,
    `${tenant_id}_oa_curated.Location_Name_Generator`(destinationlocationid) AS destination_location_name,
    COALESCE(shuttleid, liftid, '') AS device_code,
    COALESCE(hardwareidentifier, '') AS physical_device_code,
    COALESCE(movementtype, '') AS movement_type_code,
    ${tenant_id}_oa_curated.Item_Code_Generator(skuid, NULL, NULL, NULL) AS item_code,
    COALESCE(eventcode, '') AS status_code,
    COALESCE(transportreason, '') AS transport_reason_code,
    COALESCE(transportrequestid, '') AS transport_request_id,
    COALESCE(transportrequestid, '') AS group_transport_request_id,
    COALESCE(duration, 0) AS move_duration_seconds,
    NULL AS item_count,
    COALESCE(deltax, NULL) AS x_distance_traveled,
    COALESCE(deltaaisle, NULL) AS aisles_traveled_count,
    'CONVEYOR' AS subsystem_code,
    'DEMATIC IQ' AS subsystem_category,
    @ltarget_tenant AS tenant,
    facility,
    source_system,
    COALESCE(CASE
        WHEN skuid LIKE '%EMPTY_SKU%' THEN ''
        ELSE skuid
    END, '') AS item_sku,
    '' AS container_type_code,
    '' AS load_unit_content_code,
    '' AS relative_x_axis,
    '' AS relative_y_axis,
    '' AS relative_z_axis,
    '' AS put_wall_cubby_code,
    '' AS workstation_code,
    COALESCE(arrivetime, NULL) AS move_arrive_date_time,
    COALESCE(leavetime, NULL) AS move_leave_date_time,
    '' AS item_category_code,
    COALESCE(nokcounter, NULL) AS nok_counter
FROM
    `extracted_fct_connection_movement_data`
WHERE
    endtime IS NOT NULL
    AND starttime IS NOT NULL
;

-- VEHICLE MOVEMENT
INSERT INTO tmp_staging_fct_movement (
    record_timestamp,
    move_start_date_time,
    load_unit_code,
    source_location_code,
    source_work_area_code,
    source_work_area_name,
    source_module_code,
    source_module_name,
    source_bay_name,
    source_location_name,
    destination_location_code,
    destination_work_area_code,
    destination_work_area_name,
    destination_module_code,
    destination_module_name,
    destination_bay_name,
    destination_location_name,
    device_code,
    physical_device_code,
    movement_type_code,
    item_code,
    status_code,
    transport_reason_code,
    transport_request_id,
    group_transport_request_id,
    move_duration_seconds,
    item_count,
    x_distance_traveled,
    aisles_traveled_count,
    subsystem_code,
    subsystem_category,
    tenant,
    facility,
    source_system,
    item_sku,
    container_type_code,
    load_unit_content_code,
    relative_x_axis,
    relative_y_axis,
    relative_z_axis,
    put_wall_cubby_code,
    workstation_code,
    move_arrive_date_time,
    move_leave_date_time,
    item_category_code,
    nok_counter
)
WITH extracted_fct_vehicle_movement_data AS (
    SELECT
        COALESCE(JSON_VALUE(data, '$.subsystemId'),JSON_VALUE(data, '$.subsystemId.value')) AS subsystemid,
        COALESCE(JSON_VALUE(data, '$.loadUnitId'), JSON_VALUE(data, '$.loadUnitId.value')) AS loadunitid,
        COALESCE(JSON_VALUE(data, '$.movementType'),JSON_VALUE(data, '$.movementType.value')) AS movementtype,
        COALESCE(JSON_VALUE(data, '$.eventCode'),JSON_VALUE(data, '$.eventCode.value')) AS eventcode,
        COALESCE(JSON_VALUE(data, '$.transportReason'), JSON_VALUE(data, '$.transportReason.value')) AS transportreason,
        SAFE_CAST(JSON_VALUE(data, '$.transportRequestId') AS STRING) AS transportrequestid,
        JSON_VALUE(data, '$.shuttleId') AS shuttleid,
        JSON_VALUE(data, '$.liftId') AS liftid,
        JSON_VALUE(data, '$.sourceLocationId') AS sourcelocationid,
        JSON_VALUE(data, '$.destinationLocationId') AS destinationlocationid,
        JSON_VALUE(data, '$.aisle') AS aisle,
        JSON_VALUE(data, '$.level') AS level,
        SAFE_CAST(JSON_VALUE(data, '$.startTime') AS TIMESTAMP) AS starttime,
        SAFE_CAST(JSON_VALUE(data, '$.endTime') AS TIMESTAMP) AS endtime,
        SAFE_CAST(JSON_VALUE(data, '$.duration') AS INT64) AS duration,
        SAFE_CAST(JSON_VALUE(data, '$.deltax') AS INT64) AS deltax,
        SAFE_CAST(JSON_VALUE(data, '$.deltaAisle') AS INT64) AS deltaaisle,
        JSON_VALUE(data, '$.skuId') AS skuid,
        JSON_VALUE(data, '$.productRangeId') AS productrangeid,
        SAFE_CAST(JSON_VALUE(data, '$.nokCounter') AS INT64) AS nokcounter,
        JSON_VALUE(data, '$.FaultStartTime') AS faultstarttime,
        JSON_VALUE(data, '$.FaultPhysicalShuttleID') AS faultphysicalshuttleid,
        JSON_VALUE(data, '$.FaultSourceLocationID') AS faultsourcelocationid,
        JSON_VALUE(data, '$.FaultCode') AS faultcode,
        JSON_VALUE(data, '$.FaultClassification') AS faultclassification,
        SAFE_CAST(JSON_VALUE(data, '$.FaultAckTime') AS TIMESTAMP)
            AS faultacktime,
        SAFE_CAST(JSON_VALUE(data, '$.FaultDuration') AS INT64)
            AS faultduration,
        SAFE_CAST(JSON_VALUE(data, '$.FaultRepairTime') AS INT64)
            AS faultrepairtime,
        SAFE_CAST(JSON_VALUE(data, '$.FaultEndTime') AS TIMESTAMP)
            AS faultendtime,
        SAFE_CAST(JSON_VALUE(data, '$.Record_Timestamp_Offset') AS TIMESTAMP)
            AS record_timestamp_offset,
        JSON_VALUE(data, '$.hardwareIdentifier') AS hardwareidentifier,
        SAFE_CAST(JSON_VALUE(data, '$.arriveTime') AS TIMESTAMP) AS arrivetime,
        SAFE_CAST(JSON_VALUE(data, '$.leaveTime') AS TIMESTAMP) AS leavetime,
        facility,
        source_system
    FROM `${tenant_id}_landing.VehicleMovementFact`
    WHERE
        tenant = @ltarget_tenant
        AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(
            @end_query_date
        )
)

SELECT DISTINCT
    COALESCE(endtime, NULL) AS record_timestamp,
    COALESCE(starttime, NULL) AS move_start_date_time,
    COALESCE(loadunitid, '') AS load_unit_code,
    COALESCE(sourcelocationid, '') AS source_location_code,
    COALESCE(CASE
        WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SUBSTR(sourcelocationid, 0, INSTR(sourcelocationid, '.',1, 2)-1))
        ELSE NULL
    END, '') AS source_work_area_code,
    CASE
        WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SPLIT(sourcelocationid, '.')[SAFE_OFFSET(1)])
        ELSE NULL
    END AS source_work_area_name,
    COALESCE(CASE
        WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SUBSTR(sourcelocationid, 0, INSTR(sourcelocationid, '.',1, 3)-1))
        ELSE NULL
    END, '') AS source_module_code,
    CASE
        WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SPLIT(sourcelocationid, '.')[SAFE_OFFSET(2)])
        ELSE NULL
    END AS source_module_name,
    CASE
        WHEN STRPOS(sourcelocationid, '.') > 0 THEN UPPER(SPLIT(sourcelocationid, '.')[SAFE_OFFSET(3)])
        ELSE NULL
    END AS source_bay_name,
    `${tenant_id}_oa_curated.Location_Name_Generator`(sourcelocationid) AS source_location_name,
    COALESCE(destinationlocationid, '') AS destination_location_code,
    COALESCE(CASE
        WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SUBSTR(destinationlocationid, 0, INSTR(destinationlocationid, '.',1, 2)-1))
        WHEN STRPOS(aisle, '#') > 0 THEN UPPER(REPLACE(SUBSTR(aisle, 0, INSTR(aisle, '#',1, 2)-1), '#', '.'))
        ELSE NULL
    END, '') AS destination_work_area_code,
    CASE
        WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SPLIT(destinationlocationid, '.')[SAFE_OFFSET(1)])
        WHEN STRPOS(aisle, '#') > 0 THEN UPPER(SPLIT(aisle, '#')[SAFE_OFFSET(1)])
        ELSE NULL
    END AS destination_work_area_name,
    COALESCE(CASE
        WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SUBSTR(destinationlocationid, 0, INSTR(destinationlocationid, '.',1, 3)-1))
        WHEN STRPOS(aisle, '#') > 0 THEN UPPER(REPLACE(aisle, '#', '.'))
        WHEN aisle IS NOT NULL THEN UPPER(aisle)
        ELSE ''
    END, '') AS destination_module_code,
    CASE
        WHEN STRPOS(aisle, '#') > 0 THEN UPPER(SPLIT(aisle, '#')[SAFE_OFFSET(2)])
        WHEN aisle IS NOT NULL THEN UPPER(aisle)
        WHEN STRPOS(destinationlocationid, '.') > 0 THEN UPPER(SPLIT(destinationlocationid, '.')[SAFE_OFFSET(2)])
        ELSE NULL
    END AS destination_module_name,
    CASE
        WHEN STRPOS(destinationlocationid, '.') > 0 THEN SPLIT(destinationlocationid, '.')[SAFE_OFFSET(3)]
        ELSE level
    END AS destination_bay_name,
    `${tenant_id}_oa_curated.Location_Name_Generator`(destinationlocationid) AS destination_location_name,
    COALESCE(shuttleid, liftid, '') AS device_code,
    COALESCE(hardwareidentifier, '') AS physical_device_code,
    COALESCE(movementtype, '') AS movement_type_code,
    ${tenant_id}_oa_curated.Item_Code_Generator(skuid, NULL, NULL, NULL) AS item_code,
    COALESCE(eventcode, '') AS status_code,
    COALESCE(transportreason, '') AS transport_reason_code,
    COALESCE(transportrequestid, '') AS transport_request_id,
    COALESCE(transportrequestid, '') AS group_transport_request_id,
    COALESCE(duration, 0) AS move_duration_seconds,
    NULL AS item_count,
    COALESCE(deltax, NULL) AS x_distance_traveled,
    COALESCE(deltaaisle, NULL) AS aisles_traveled_count,
    'STACKERCRANE' AS subsystem_code,
    'DEMATIC IQ' AS subsystem_category,
    @ltarget_tenant AS tenant,
    facility,
    source_system,
    COALESCE(CASE
        WHEN skuid LIKE '%EMPTY_SKU%' THEN ''
        ELSE skuid
    END, '') AS item_sku,
    '' AS container_type_code,
    '' AS load_unit_content_code,
    '' AS relative_x_axis,
    '' AS relative_y_axis,
    '' AS relative_z_axis,
    '' AS put_wall_cubby_code,
    '' AS workstation_code,
    COALESCE(arrivetime, NULL) AS move_arrive_date_time,
    COALESCE(leavetime, NULL) AS move_leave_date_time,
    '' AS item_category_code,
    COALESCE(nokcounter, NULL) AS nok_counter
FROM
    extracted_fct_vehicle_movement_data
WHERE
    endtime IS NOT NULL
    AND starttime IS NOT NULL
;

-- CONTAINER MOVEMENT
INSERT INTO tmp_staging_fct_movement (
    record_timestamp,
    move_start_date_time,
    load_unit_code,
    tenant,
    facility,
    destination_location_code,
    destination_work_area_code,
    destination_work_area_name,
    destination_module_code,
    destination_module_name,
    destination_bay_name,
    destination_location_name,
    device_code,
    physical_device_code,
    movement_type_code,
    item_code,
    status_code,
    transport_reason_code,
    transport_request_id,
    group_transport_request_id,
    move_duration_seconds,
    item_count,
    x_distance_traveled,
    aisles_traveled_count,
    subsystem_code,
    subsystem_category,
    source_system,
    item_sku,
    container_type_code,
    load_unit_content_code,
    relative_x_axis,
    relative_y_axis,
    relative_z_axis,
    put_wall_cubby_code,
    workstation_code,
    move_arrive_date_time,
    move_leave_date_time,
    item_category_code,
    nok_counter
)
WITH extracted_fct_container_movement AS (
    SELECT
        JSON_VALUE(data, '$.autostore_id') AS autostore_id,
        SAFE_CAST(JSON_VALUE(data, '$.event_time') AS TIMESTAMP) AS event_time,
        JSON_VALUE(data, '$.container_id') AS container_id,
        SAFE_CAST(JSON_VALUE(data, '$.x_position') AS INT64) AS x_position,
        SAFE_CAST(JSON_VALUE(data, '$.y_position') AS INT64) AS y_position,
        JSON_VALUE(data, '$.grid_id') AS grid_id,
        SAFE_CAST(JSON_VALUE(data, '$.depth') AS INT64) AS depth,
        JSON_VALUE(data, '$.container_mode') AS container_mode,
        JSON_VALUE(data, '$.container_content_code') AS container_content_code,
        SAFE_CAST(JSON_VALUE(data, '$.container_height') AS INT64)
            AS container_height,
        JSON_VALUE(data, '$.event_type') AS event_type,
        SAFE_CAST(JSON_VALUE(data, '$.process_epoch_ns') AS INT64)
            AS process_epoch_ns,
        SAFE_CAST(JSON_VALUE(data, '$.transportReason.value') AS STRING) AS transportreason,
        SAFE_CAST(JSON_VALUE(data, '$.duration') AS INT64) AS duration,
        SAFE_CAST(JSON_VALUE(data, '$.nokCounter') AS INT64) AS nokcounter,
        facility,
        source_system
    FROM `${tenant_id}_landing.ContainerMovementFact`
    WHERE
        tenant = @ltarget_tenant
        AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(
            @end_query_date
        )
)

SELECT DISTINCT
    TIMESTAMP_ADD(
        SAFE_CAST(event_time AS TIMESTAMP),
        INTERVAL SAFE_CAST(
            RIGHT(
                SAFE_CAST(
                    COALESCE(SAFE_CAST(process_epoch_ns AS INT64), 0) AS STRING
                ),
                6
            ) AS INT64
        ) MICROSECOND
    ) AS record_timestamp,
    TIMESTAMP_ADD(
        SAFE_CAST(event_time AS TIMESTAMP),
        INTERVAL SAFE_CAST(
            RIGHT(
                SAFE_CAST(
                    COALESCE(SAFE_CAST(process_epoch_ns AS INT64), 0) AS STRING
                ),
                6
            ) AS INT64
        ) MICROSECOND
    ) AS move_start_date_time,
    COALESCE(container_id, '') AS load_unit_code,
    @ltarget_tenant AS tenant,
    facility AS facility,
    COALESCE(
        CASE
            WHEN
                x_position IS NULL
                THEN
                    LAG(
                        CONCAT(
                            COALESCE(autostore_id, ''),
                            '-',
                            COALESCE(grid_id, 'NA'),
                            '-',
                            COALESCE(SAFE_CAST(x_position AS STRING), '*'),
                            '-',
                            COALESCE(SAFE_CAST(y_position AS STRING), '*'),
                            '-',
                            COALESCE(SAFE_CAST(depth AS STRING), '*')
                        ),
                        1
                    ) OVER (PARTITION BY container_id ORDER BY event_time)
            ELSE
                CONCAT(
                    COALESCE(autostore_id, ''),
                    '-',
                    COALESCE(grid_id, 'NA'),
                    '-',
                    COALESCE(SAFE_CAST(x_position AS STRING), '*'),
                    '-',
                    COALESCE(SAFE_CAST(y_position AS STRING), '*'),
                    '-',
                    COALESCE(SAFE_CAST(depth AS STRING), '*')
                )
        END, ''
    ) AS destination_location_code,
    '' as destination_work_area_code,
    CAST(NULL AS STRING) as destination_work_area_name,
    COALESCE(autostore_id, '') AS destination_module_code,
    COALESCE(grid_id, '') AS destination_module_name,
    CAST(NULL AS STRING) AS destination_module_bay,
    COALESCE(
        CASE
            WHEN
                x_position IS NULL
                THEN
                    LAG(
                        CONCAT(
                            COALESCE(autostore_id, ''),
                            '-',
                            COALESCE(grid_id, 'NA'),
                            '-',
                            COALESCE(SAFE_CAST(x_position AS STRING), '*'),
                            '-',
                            COALESCE(SAFE_CAST(y_position AS STRING), '*'),
                            '-',
                            COALESCE(SAFE_CAST(depth AS STRING), '*')
                        ),
                        1
                    ) OVER (PARTITION BY container_id ORDER BY event_time)
            ELSE
                CONCAT(
                    COALESCE(autostore_id, ''),
                    '-',
                    COALESCE(grid_id, 'NA'),
                    '-',
                    COALESCE(SAFE_CAST(x_position AS STRING), '*'),
                    '-',
                    COALESCE(SAFE_CAST(y_position AS STRING), '*'),
                    '-',
                    COALESCE(SAFE_CAST(depth AS STRING), '*')
                )
        END, ''
    ) AS destination_location_name,
    '' AS device_code,
    '' AS physical_device_code,
    'AutoStore Container' AS movement_type_code,
    '' AS item_code,
    CASE
        container_mode
        WHEN 'G' THEN 'G-GRID'
        WHEN 'O' THEN 'O-OPEN'
        WHEN 'T' THEN 'T-PREPARED'
        WHEN 'F' THEN 'F-FORECAST'
        WHEN 'C' THEN 'C-CLOSED'
        WHEN 'P' THEN 'P-PORT'
        WHEN 'R' THEN 'R-TOGRID'
        WHEN 'X' THEN 'X-OUTSIDE'
        ELSE COALESCE(container_mode, '')
    END AS status_code,
    COALESCE(transportreason, '') AS transport_reason_code,
    '' AS transport_request_id,
    '' AS group_transport_request_id,
    COALESCE(duration, 0) AS move_duration_seconds,
    NULL AS item_count,
    NULL AS x_distance_traveled,
    NULL AS aisles_traveled_count,
    'AUTOSTORE' AS subsystem_code,
    'AUTOSTORE' AS subsystem_category,
    facility
    source_system,
    '' AS item_sku,
    COALESCE(SAFE_CAST(container_height AS STRING), '') AS container_type_code,
    COALESCE(container_content_code, '') AS load_unit_content_code,
    SAFE_CAST(x_position AS STRING) AS relative_x_axis,
    SAFE_CAST(y_position AS STRING) AS relative_y_axis,
    SAFE_CAST(depth AS STRING) AS relative_z_axis,
    '' AS put_wall_cubby_code,
    '' AS workstation_code,
    SAFE_CAST(NULL AS TIMESTAMP) AS move_arrive_date_time,
    SAFE_CAST(NULL AS TIMESTAMP) AS move_leave_date_time,
    '' AS item_category_code,
    nokcounter AS nok_counter
FROM
    extracted_fct_container_movement
WHERE event_time IS NOT NULL;


--DIM_Subsystem 
MERGE `${tenant_id}_oa_curated.dim_subsystem` ds
USING
    (SELECT DISTINCT
        subsystem_code,
        subsystem_category,
        tenant,
        facility AS facility,
        source_system AS source_system
    FROM tmp_staging_fct_movement
    GROUP BY
      subsystem_code,
      subsystem_category,
      tenant, facility, source_system) v
    ON
        ds.subsystem_code = v.subsystem_code
        AND ds.subsystem_category = v.subsystem_category
        AND ds.tenant = v.tenant
        AND ds.facility = v.facility
        AND ds.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (subsystem_uuid, subsystem_code, subsystem_category, tenant, facility, source_system, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.subsystem_code, v.subsystem_category]
        ), v.subsystem_code, v.subsystem_category, v.tenant, v.facility, v.source_system, @etl_batch_uuid);

UPDATE tmp_staging_fct_movement tt 
SET subsystem_uuid = d.subsystem_uuid
FROM
    (
        select distinct
            subsystem_uuid,
            subsystem_code,
            subsystem_category,
            tenant,
            facility,
            source_system
        from  
`${tenant_id}_oa_curated.dim_subsystem`) d
WHERE
    tt.subsystem_code = d.subsystem_code 
    AND tt.subsystem_category = d.subsystem_category 
    AND tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system;

--DIM_Device
MERGE `${tenant_id}_oa_curated.dim_device` dd
USING
    (SELECT DISTINCT
        subsystem_uuid,
        device_code,
        tenant,
        facility AS facility,
        source_system AS source_system
    FROM tmp_staging_fct_movement
    GROUP BY
      subsystem_uuid,
      device_code,
      tenant, facility, source_system
    UNION DISTINCT
    SELECT DISTINCT
        subsystem_uuid,
        physical_device_code AS device_code,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_movement) v
    ON
        dd.device_code = v.device_code
        AND dd.subsystem_uuid = v.subsystem_uuid
        AND dd.tenant = v.tenant
        AND dd.facility = v.facility
        AND dd.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (device_uuid, device_code, subsystem_uuid, tenant, facility, source_system, etl_batch_id, device_functional_type) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.device_code, v.subsystem_uuid]
        ), v.device_code, v.subsystem_uuid, v.tenant, v.facility, v.source_system, @etl_batch_uuid, 'MULTISHUTTLE');

UPDATE tmp_staging_fct_movement tt 
SET device_uuid = d.device_uuid
FROM
    (
        select distinct
            device_uuid,
            device_code,
            subsystem_uuid,
            tenant,
            facility,
            source_system
        from   
`${tenant_id}_oa_curated.dim_device`) d
WHERE
    tt.subsystem_uuid = d.subsystem_uuid 
    AND tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system 
    AND tt.device_code = d.device_code;

UPDATE tmp_staging_fct_movement tt 
SET physical_device_uuid = d.device_uuid
FROM
    (
        select distinct
            device_uuid,
            device_code,
            subsystem_uuid,
            tenant,
            facility,
            source_system
        from   
`${tenant_id}_oa_curated.dim_device`) d
WHERE
    tt.subsystem_uuid = d.subsystem_uuid 
    AND tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system 
    AND tt.physical_device_code = d.device_code;

--DIM_Item
MERGE `${tenant_id}_oa_curated.dim_item` di
USING
    (SELECT DISTINCT
        item_code,
        item_category_code,
        MAX(item_sku) AS item_sku,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_movement GROUP BY item_code, item_category_code, tenant, facility, source_system) v
    ON
        di.item_code = v.item_code
        AND di.item_category_code = v.item_category_code
        AND di.tenant = v.tenant
        AND di.facility = v.facility 
        AND di.source_system = v.source_system
WHEN MATCHED THEN
    UPDATE SET item_sku = v.item_sku
WHEN NOT MATCHED THEN
    INSERT (item_uuid, item_code, item_category_code, item_sku, tenant, facility, source_system, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.item_code]
        ), v.item_code, v.item_category_code, v.item_sku, v.tenant, v.facility, v.source_system, @etl_batch_uuid);

UPDATE tmp_staging_fct_movement tt 
SET item_uuid = d.item_uuid
FROM
    (
        select distinct
            item_uuid,
            item_code,
            item_sku,
            item_category_code,
            tenant,
            facility,
            source_system
        from  
`${tenant_id}_oa_curated.dim_item`) d
WHERE
    tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system 
    AND tt.item_code = d.item_code 
    AND tt.item_category_code = d.item_category_code;

--DIM_reason
MERGE `${tenant_id}_oa_curated.dim_reason` dr
USING
    (SELECT DISTINCT
        transport_reason_code,
        tenant,
        facility AS facility,
        source_system AS source_system
    FROM tmp_staging_fct_movement
    GROUP BY
      transport_reason_code,
      tenant, facility, source_system) v
    ON
        dr.reason_code = v.transport_reason_code
        AND dr.tenant = v.tenant
        AND dr.facility = v.facility
        AND dr.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (reason_uuid, reason_code, tenant, facility, source_system, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.transport_reason_code]
        ), v.transport_reason_code, v.tenant, v.facility, v.source_system, @etl_batch_uuid);

UPDATE tmp_staging_fct_movement tt 
SET reason_uuid = d.reason_uuid
FROM
    (
        select distinct
            reason_uuid,
            reason_code,
            tenant,
            facility,
            source_system
        from  
        `${tenant_id}_oa_curated.dim_reason`) d
WHERE
    tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system 
    AND tt.transport_reason_code = d.reason_code;

--DIM_Status
MERGE `${tenant_id}_oa_curated.dim_status` ds
USING
    (SELECT DISTINCT
        status_code,
        subsystem_uuid,
        '' as status_locale_code,
        '' as status_category_code,
        tenant,
        facility AS facility,
        source_system AS source_system,
        MAX(nok_counter) as nok_counter
    FROM tmp_staging_fct_movement
    GROUP BY status_code, subsystem_uuid, tenant, facility, source_system) v
    ON
        ds.status_code = v.status_code
        AND ds.subsystem_uuid = v.subsystem_uuid
        AND ds.status_locale_code = v.status_locale_code
        AND ds.status_category_code = v.status_category_code
        AND ds.tenant = v.tenant
        AND ds.facility = v.facility
        AND ds.source_system = v.source_system
WHEN MATCHED THEN
    UPDATE SET
        ds.status_relevancy_ind = v.nok_counter
WHEN NOT MATCHED THEN
    INSERT
        (
            status_uuid,
            status_code,
            status_locale_code,
            status_category_code,
            status_relevancy_ind,
            subsystem_uuid,
            tenant,
            facility,
            source_system,
            etl_batch_id
        )
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.status_code, v.subsystem_uuid]
        ), v.status_code, v.status_locale_code, v.status_category_code, v.nok_counter, v.subsystem_uuid, v.tenant, v.facility, v.source_system, @etl_batch_uuid);

UPDATE tmp_staging_fct_movement tt 
SET status_uuid = d.status_uuid
FROM
    (
        select distinct
            status_uuid,
            status_code,
            subsystem_uuid,
            tenant,
            facility,
            source_system
        from  
        `${tenant_id}_oa_curated.dim_status`) d
WHERE
    tt.subsystem_uuid = d.subsystem_uuid 
    AND tt.status_code = d.status_code 
    AND tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system;

--DIM_Location
MERGE `${tenant_id}_oa_curated.dim_location` dl
USING (
    SELECT location_code, max(bay) as bay, max(location_name) as location_name, tenant, facility AS facility, source_system AS source_system from 
    (SELECT DISTINCT
        source_location_code AS location_code,
        source_bay_name as bay,
        source_location_name as location_name,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_movement
    UNION DISTINCT
    SELECT DISTINCT
        destination_location_code AS location_code,
        destination_bay_name as bay,
        destination_location_name as location_name,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_movement) t
    GROUP BY location_code,tenant, facility, source_system
    ) v
    ON
        dl.location_code = v.location_code
        AND dl.tenant = v.tenant
        AND dl.facility = v.facility
        AND dl.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (location_uuid, location_code, bay, location_name, tenant, facility, source_system, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.location_code]
        ), v.location_code, v.bay, v.location_name, v.tenant, v.facility, v.source_system, @etl_batch_uuid);

UPDATE tmp_staging_fct_movement tt 
SET source_location_uuid = d.location_uuid
FROM
    (
        select distinct
            location_uuid,
            location_code,
            tenant,
            facility,
            source_system
        from 
`${tenant_id}_oa_curated.dim_location`) d
WHERE
    tt.source_location_code = d.location_code 
    AND tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system;

UPDATE tmp_staging_fct_movement tt 
SET destination_location_uuid = d.location_uuid
FROM
    (
        select distinct
            location_uuid,
            location_code,
            tenant,
            facility,
            source_system
        from  
        `${tenant_id}_oa_curated.dim_location`) d
WHERE
     tt.destination_location_code = d.location_code 
     AND tt.tenant = d.tenant 
     AND tt.facility = d.facility 
     AND tt.source_system = d.source_system;

--DIM_MODULE
MERGE `${tenant_id}_oa_curated.dim_module` dm
USING (
    SELECT DISTINCT module_code, 
        max(module_name) as module_name, 
        tenant, 
        facility, 
        source_system 
    FROM (
        SELECT 
            source_module_code AS module_code,
            MAX(source_module_name) as module_name,
            tenant,
            facility,
            source_system
        FROM tmp_staging_fct_movement
        GROUP BY source_module_code, tenant, facility, source_system
        UNION DISTINCT
        SELECT 
            destination_module_code AS module_code,
            MAX(destination_module_name) as module_name,
            tenant,
            facility,
            source_system
        FROM tmp_staging_fct_movement
        GROUP BY destination_module_code, tenant, facility, source_system
    ) t
    GROUP BY module_code, tenant, facility, source_system
) v
ON dm.module_code = v.module_code AND dm.tenant = v.tenant AND dm.facility = v.facility AND dm.source_system = v.source_system
WHEN MATCHED THEN
    UPDATE SET 
        dm.module_name = v.module_name, 
        dm.facility = v.facility, 
        dm.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (module_uuid, module_code, module_name, tenant, facility, source_system, active_rec_ind, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.module_code]
        ), v.module_code, v.module_name, v.tenant, v.facility, v.source_system, 1, @etl_batch_uuid);

UPDATE tmp_staging_fct_movement tt 
SET source_module_uuid = d.module_uuid
FROM
    (
        select distinct
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        from 
    `${tenant_id}_oa_curated.dim_module`) d
WHERE d.module_code = tt.source_module_code 
AND d.tenant = @ltarget_tenant 
AND d.facility = tt.facility 
AND d.source_system = tt.source_system;

UPDATE tmp_staging_fct_movement tt 
SET destination_module_uuid = d.module_uuid
FROM
    (
        select distinct
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        from  `${tenant_id}_oa_curated.dim_module`) d
WHERE d.module_code = tt.destination_module_code 
AND d.tenant = @ltarget_tenant 
AND d.facility = tt.facility 
AND d.source_system = tt.source_system;

--DIM_WORK_AREA
MERGE `${tenant_id}_oa_curated.dim_work_area` dm
USING (
    SELECT work_area_code, max(work_area_name) as work_area_name, tenant, facility AS facility, source_system AS source_system
    FROM 
    (SELECT DISTINCT
        source_work_area_code AS work_area_code,
        source_work_area_name as work_area_name,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_movement
    UNION DISTINCT
    SELECT DISTINCT
        destination_work_area_code AS work_area_code,
        destination_work_area_name as work_area_name,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_movement) t
    GROUP BY work_area_code,tenant, facility, source_system
) v
    ON
        dm.work_area_code = v.work_area_code
        AND dm.tenant = @ltarget_tenant
        AND dm.facility = v.facility
        AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (work_area_uuid, work_area_code, work_area_name, tenant, facility, source_system, active_rec_ind, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.work_area_code]
        ), v.work_area_code, v.work_area_name, @ltarget_tenant, v.facility, v.source_system, 1, @etl_batch_uuid);

UPDATE tmp_staging_fct_movement tt 
SET source_work_area_uuid = d.work_area_uuid
FROM
    (
        select distinct
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        from   
`${tenant_id}_oa_curated.dim_work_area`) d
WHERE
      d.work_area_code = tt.source_work_area_code 
      AND d.tenant = @ltarget_tenant 
      AND d.facility = tt.facility 
      AND d.source_system = tt.source_system;

UPDATE tmp_staging_fct_movement tt 
SET destination_work_area_uuid = d.work_area_uuid
FROM
    (
        select distinct
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        from  
`${tenant_id}_oa_curated.dim_work_area`) d
WHERE
      d.work_area_code = tt.destination_work_area_code 
      AND d.tenant = @ltarget_tenant 
      AND d.facility = tt.facility 
      AND d.source_system = tt.source_system;

-- DIM_Container_Type
MERGE `${tenant_id}_oa_curated.dim_container_type` dct
USING
    (SELECT DISTINCT
        container_type_code,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_movement) v
    ON
        dct.container_type_code = v.container_type_code
        AND dct.tenant = v.tenant
        AND dct.facility = v.facility
        AND dct.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (container_type_uuid, container_type_code, tenant, facility, source_system, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.container_type_code]
        ), v.container_type_code, v.tenant, v.facility, v.source_system, @etl_batch_uuid);

UPDATE tmp_staging_fct_movement tt 
SET container_type_uuid = d.container_type_uuid
FROM
    (
        select distinct
            container_type_uuid,
            container_type_code,
            tenant,
            facility,
            source_system
        from  
`${tenant_id}_oa_curated.dim_container_type`) d
WHERE
    tt.container_type_code = d.container_type_code 
    AND tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system;

-- DIM_Workstation
MERGE `${tenant_id}_oa_curated.dim_workstation` dw
USING
    (SELECT DISTINCT
        workstation_code,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_movement) v
    ON
        dw.workstation_code = v.workstation_code
        AND dw.tenant = v.tenant
        AND dw.facility = v.facility
        AND dw.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (workstation_uuid, workstation_code, tenant, facility, source_system, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.workstation_code]
        ), v.workstation_code, v.tenant, v.facility, v.source_system, @etl_batch_uuid);

UPDATE tmp_staging_fct_movement tt 
SET workstation_uuid = d.workstation_uuid
FROM
    (
        select distinct
            workstation_uuid,
            workstation_code,
            tenant,
            facility,
            source_system
        from  
`${tenant_id}_oa_curated.dim_workstation`) d
WHERE
    tt.workstation_code = d.workstation_code 
    AND tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system;


-- FINAL FCT Insert
INSERT INTO `${tenant_id}_oa_curated.fct_movement` (
    record_timestamp,
    source_location_uuid,
    source_module_uuid,
    source_work_area_uuid,
    destination_location_uuid,
    destination_module_uuid,
    destination_work_area_uuid,
    status_uuid,
    device_uuid,
    physical_device_uuid,
    item_uuid,
    transport_reason_uuid,
    container_type_uuid,
    workstation_uuid,
    movement_type_code,
    move_start_date_time,
    load_unit_code,
    transport_request_id,
    group_transport_request_id,
    move_duration_seconds,
    item_count,
    x_distance_traveled,
    aisles_traveled_count,
    load_unit_content_code,
    move_arrive_date_time,
    move_leave_date_time,
    tenant,
    facility,
    source_system,
    etl_batch_id
)
SELECT DISTINCT
    record_timestamp,
    source_location_uuid,
    source_module_uuid,
    source_work_area_uuid,
    destination_location_uuid,
    destination_module_uuid,
    destination_work_area_uuid,
    status_uuid,
    device_uuid,
    physical_device_uuid,
    item_uuid,
    reason_uuid AS transport_reason_uuid,
    container_type_uuid,
    workstation_uuid,
    movement_type_code,
    MAX(move_start_date_time) AS move_start_date_time,
    MAX(load_unit_code) AS load_unit_code,
    MAX(transport_request_id) AS transport_request_id,
    MAX(group_transport_request_id) AS group_transport_request_id,
    MAX(move_duration_seconds) AS move_duration_seconds,
    MAX(item_count) AS item_count,
    MAX(x_distance_traveled) AS x_distance_traveled,
    MAX(aisles_traveled_count) AS aisles_traveled_count,
    MAX(load_unit_content_code) AS load_unit_content_code,
    MAX(move_arrive_date_time) AS move_arrive_date_time,
    MAX(move_leave_date_time) AS move_leave_date_time,
    @ltarget_tenant AS tenant,
    facility,
    source_system,
    @etl_batch_uuid AS etl_batch_id
FROM
    tmp_staging_fct_movement
GROUP BY
    record_timestamp,
    source_location_uuid,
    source_module_uuid,
    source_work_area_uuid,
    destination_location_uuid,
    destination_module_uuid,
    destination_work_area_uuid,
    status_uuid,
    device_uuid,
    physical_device_uuid,
    item_uuid,
    reason_uuid,
    container_type_uuid,
    workstation_uuid,
    movement_type_code,
    facility, source_system;
