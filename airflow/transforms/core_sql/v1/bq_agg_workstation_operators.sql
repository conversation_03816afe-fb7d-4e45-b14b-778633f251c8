-- This script populates the agg_workstation_operators table every time the DAG runs
/*
<PERSON><PERSON>ARE ltarget_tenant STRING DEFAULT '${tenant_id}';
DECLARE etl_batch_uuid STRING DEFAULT '2020-12-12';

DECLARE start_query_date TIM<PERSON><PERSON><PERSON> DEFAULT TIMESTAMP('2022-09-27 00:00:00');
DECLARE end_query_date TIMESTAMP DEFAULT TIMESTAMP('2024-09-27 02:00:00');
*/

DECLARE startDate TIMESTAMP;
SET startDate = TIMESTAMP_SUB(@end_query_date, INTERVAL 1 DAY);

CREATE TEMP TABLE tempWH (  
  logged_in_duration INT64,
  event_hour TIMESTAMP,
  event STRING,
  next_event STRING,
  record_timestamp TIMESTAMP,
  next_record_timestamp TIMESTAMP,
  operator_uuid STRING,
  workstation_uuid STRING
  
  );

--- Confirm that the transitional table is empty;
DELETE `${tenant_id}_oa_curated.eval_workstation_history` 
WHERE true;

INSERT INTO `${tenant_id}_oa_curated.eval_workstation_history`
SELECT logged_in_duration, 
    event_hour, 
    NULL AS event, 
    NULL AS next_event, 
    starting_timestamp AS record_timestamp, 
    ending_timestamp AS next_record_timestamp, 
    operator_uuid, 
    workstation_uuid 
    FROM (
      SELECT DISTINCT
        logged_in_duration, 
        event_hour, 
        starting_timestamp, 
        ending_timestamp, 
        operator_uuid, 
        workstation_uuid
      FROM `${tenant_id}_oa_curated.agg_workstation_operators` 
      WHERE event_hour >= startDate
    );

INSERT INTO tempWH (

WITH
logging_times AS (
  SELECT DISTINCT
    UPPER(event) AS event,
    record_timestamp,
    operator_uuid,
    workstation_uuid
  FROM
    `${tenant_id}_oa_curated.fct_pick_activity` 
  WHERE
    record_timestamp > startDate
    AND record_timestamp < @end_query_date
    AND UPPER(event) LIKE 'LOG%'
  ORDER BY
    operator_uuid,
    record_timestamp),

  logging_details AS (
  SELECT
  event,
  LEAD(event) OVER (PARTITION BY operator_uuid, workstation_uuid ORDER BY record_timestamp) AS next_event,
  record_timestamp,
  COALESCE(LEAD(record_timestamp) OVER (PARTITION BY operator_uuid, workstation_uuid ORDER BY record_timestamp), @end_query_date) AS next_record_timestamp,
  operator_uuid,
  workstation_uuid
  FROM logging_times
  ORDER BY
    operator_uuid,
    record_timestamp)

  SELECT
  CASE 
    WHEN TIMESTAMP_TRUNC(record_timestamp, HOUR) = TIMESTAMP_TRUNC(next_record_timestamp, HOUR)
    THEN TIMESTAMP_DIFF(next_record_timestamp, record_timestamp, MINUTE)
    END AS logged_in_duration,
    CASE
    WHEN TIMESTAMP_TRUNC(record_timestamp, HOUR) = TIMESTAMP_TRUNC(next_record_timestamp, HOUR)
    THEN TIMESTAMP_TRUNC(record_timestamp, HOUR)
    END AS event_hour
  ,* FROM logging_details WHERE event = 'LOGON' AND (next_event IS NULL OR event != next_event) 
  AND next_record_timestamp > TIMESTAMP_SUB(@end_query_date, INTERVAL 1 HOUR)
 ORDER BY operator_uuid, record_timestamp
);

 MERGE INTO `${tenant_id}_oa_curated.eval_workstation_history` ewl USING (
  SELECT * FROM tempWH
 ) src ON ewl.operator_uuid = src.operator_uuid
    AND ewl.workstation_uuid = src.workstation_uuid
    AND ewl.next_record_timestamp = src.next_record_timestamp
  WHEN NOT MATCHED THEN INSERT (
      logged_in_duration,
      event_hour,
      event,
      next_event,
      record_timestamp,
      next_record_timestamp,
      operator_uuid,
      workstation_uuid 
    ) VALUES (
      src.logged_in_duration,
      src.event_hour,
      src.event,
      src.next_event,
      src.record_timestamp,
      src.next_record_timestamp,
      src.operator_uuid,
      src.workstation_uuid 
    );

CALL `${tenant_id}_oa_curated.expandOverTime`(@end_query_date)