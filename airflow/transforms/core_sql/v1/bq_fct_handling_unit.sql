-- Creating the temporary staging table for Handling Unit data
CREATE TEMP TABLE tmp_staging_fct_handling_unit (
    record_timestamp TIMESTAMP NOT NULL,
    display_timestamp TIMESTAMP,
    event_type STRING,
    handling_id STRING,
    handling_unit_type STRING,
    workstation_id STRING,
    workstation_uuid STRING,
    tenant STRING,
    facility STRING,
    source_system STRING
);

-- Inserting data into the temporary staging table for Handling Unit Facts
INSERT INTO tmp_staging_fct_handling_unit
SELECT
    `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(JSON_VALUE(data, '$.eventDate'), NULL) AS record_timestamp,
    `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(JSON_VALUE(data, '$.displayTime'), NULL) AS display_timestamp,
    JSON_VALUE(data, '$.eventType') AS event_type,
    JSON_VALUE(data, '$.id') AS handling_id,
    JSON_VALUE(data, '$.type') AS handling_unit_type,
    JSON_VALUE(data, '$.workstationId') AS workstation_id,
    '' AS workstation_uuid,
    tenant,
    facility,
    source_system,
FROM `${tenant_id}_landing.HandlingUnitFact`
WHERE @ltarget_tenant = tenant AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date);

-- DIM_Workstation
MERGE `${tenant_id}_oa_curated.dim_workstation` dw
USING
    (SELECT DISTINCT
        workstation_id,
        tenant,
        facility,
        source_system
    FROM tmp_staging_fct_handling_unit) v
    ON
        dw.workstation_code = v.workstation_id
        AND dw.tenant = v.tenant
        AND dw.facility = v.facility
        AND dw.source_system = v.source_system
WHEN NOT MATCHED THEN
    INSERT (workstation_uuid, workstation_code, tenant, facility, source_system, etl_batch_id) 
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.workstation_id]
        ), v.workstation_id, v.tenant, v.facility, v.source_system, @etl_batch_uuid);

UPDATE tmp_staging_fct_handling_unit tt 
SET workstation_uuid = d.workstation_uuid
FROM
    (
        SELECT DISTINCT
            workstation_uuid,
            workstation_code,
            tenant,
            facility,
            source_system
        from  
`${tenant_id}_oa_curated.dim_workstation`) d
WHERE
    tt.workstation_id = d.workstation_code 
    AND tt.tenant = d.tenant 
    AND tt.facility = d.facility 
    AND tt.source_system = d.source_system;

-- Inserting data into the Target Fact Table
INSERT INTO `${tenant_id}_oa_curated.fct_handling_unit`
SELECT DISTINCT     
    record_timestamp,
    display_timestamp,
    event_type,
    handling_id,
    handling_unit_type,
    workstation_uuid,
    tenant,
    facility,
    source_system,
    @etl_batch_uuid
FROM tmp_staging_fct_handling_unit;