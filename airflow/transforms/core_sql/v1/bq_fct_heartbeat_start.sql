/*
**Set variables for running outside of DAG in BQ directly**
declare tenant_id string default 'your_tenant_id';
declare etl_batch_uuid string default '2020-12-12';
declare start_query_date timestamp default timestamp('2022-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2024-09-27 02:00:00');
*/

-- Inserting data directly into the Target Fact Table for HeartBeat-Start 
INSERT INTO `${tenant_id}_oa_curated.fct_heartbeat`
SELECT
    SAFE_CAST(JSON_VALUE(data, '$.RecordTimestamp') AS TIMESTAMP) AS record_timestamp,
    JSON_VALUE(data, '$.DCHostname') AS dc_hostname,
    JSON_VALUE(data, '$.DCUsername') AS dc_username,
    JSON_VALUE(data, '$.Drive0.driveName') AS drive0_name,
    SAFE_CAST(JSON_VALUE(data, '$.Drive0.driveSpaceGB') AS FLOAT64) AS drive0_space_gb,
    JSON_VALUE(data, '$.Drive1.driveName') AS drive1_name,
    SAFE_CAST(JSON_VALUE(data, '$.Drive1.driveSpaceGB') AS FLOAT64) AS drive1_space_gb,
    SAFE_CAST(JSON_VALUE(data, '$.ErrorBacklogFiles') AS INT64) AS error_backlog_files,
    JSON_VALUE(data, '$.HeartbeatType') AS heartbeat_type,
    SAFE_CAST(JSON_VALUE(data, '$.InSightsBacklogFiles') AS INT64) AS insights_backlog_files,
    JSON_VALUE(data, '$.InSightsTenant') AS insights_tenant,
    SAFE_CAST(JSON_VALUE(data, '$.OsFreeMemGB') AS FLOAT64) AS os_free_mem_gb,
    SAFE_CAST(JSON_VALUE(data, '$.OsMaxMemGB') AS FLOAT64) AS os_max_mem_gb,
    JSON_VALUE(data, '$.OsName') AS os_name,
    SAFE_CAST(JSON_VALUE(data, '$.OsUptimeDays') AS FLOAT64) AS os_uptime_days,
    JSON_VALUE(data, '$.OsVersion') AS os_version,
    JSON_VALUE(data, '$.PowerChainVersion') AS power_chain_version,
    JSON_VALUE(data, '$.PSVersion') AS ps_version,
    SAFE_CAST(JSON_VALUE(data, '$.SprocketBacklogFiles') AS INT64) AS sprocket_backlog_files,
    JSON_VALUE(data, '$.SprocketCustomerID') AS sprocket_customer_id
FROM `${tenant_id}_landing.heartbeat_start`
WHERE ingestion_date BETWEEN @start_query_date AND @end_query_date;