/*
DECLARE days_to_look_back INT64 DEFAULT 7;
DECLARE etl_batch_uuid STRING DEFAULT '';
*/

-- Calculate the useful historical data points about SKUs and save them
INSERT INTO `${tenant_id}_oa_curated.hst_item_by_zone` (
  etl_batch_id, item_uuid, record_timestamp, work_area_code, inventory_qty, avg_picked_qty, avg_picked_orders
)
WITH latest_inventory_timestamp_per_item AS (
  -- get the most recent record_timestamp for each item_uuid
  SELECT item_uuid,
    MAX(record_timestamp) as latest_record_timestamp
  FROM `${tenant_id}_oa_curated.fct_inventory` 
  GROUP BY
    item_uuid
),
latestInventory AS (
  -- calculate the sum of inventory for all records in fct_inventory for the given item_uuid and latest record_timestamp for that item
  SELECT fct.item_uuid,fct.location_uuid, fct.source_system, workarea.work_area_code AS work_area_code, SUM(fct.inventory_qty) as inventory_qty
    FROM `${tenant_id}_oa_curated.fct_inventory` fct 
        INNER JOIN latest_inventory_timestamp_per_item latest on (latest.item_uuid = fct.item_uuid and fct.record_timestamp = latest.latest_record_timestamp)
        JOIN `${tenant_id}_oa_curated.dim_work_area` workarea on workarea.work_area_uuid = fct.work_area_uuid
    GROUP BY
      item_uuid,
      location_uuid,
      fct.source_system,
      workarea.work_area_code
),
inventory AS (
    SELECT  dim_item.item_uuid,
            fct_inventory.work_area_code,
            SUM(COALESCE(fct_inventory.inventory_qty, 0)) AS inventory_qty,
    FROM `${tenant_id}_oa_curated.dim_item` dim_item
    LEFT JOIN latestInventory fct_inventory
        ON dim_item.item_uuid = fct_inventory.item_uuid AND
           dim_item.source_system = fct_inventory.source_system
    GROUP BY 
        dim_item.item_uuid,
        fct_inventory.work_area_code
),
picks AS (
    SELECT  fct_order_line.item_uuid,
            SUM(fct_order_line.picked_qty) / @days_to_look_back as avg_picked_qty,
            COUNT(DISTINCT CONCAT(fct_order_line.pick_order_uuid, ':', fct_order_line.container_physical_code)) / @days_to_look_back as avg_picked_orders  
    FROM `${tenant_id}_oa_curated.fct_order_line` fct_order_line
    JOIN `${tenant_id}_oa_curated.dim_item` dim_item
        ON dim_item.item_uuid = fct_order_line.item_uuid
    WHERE fct_order_line.record_timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL @days_to_look_back DAY)
    GROUP BY fct_order_line.item_uuid
)
SELECT 
    @etl_batch_uuid AS etl_batch_id,
    inventory.item_uuid,
    CURRENT_TIMESTAMP() AS record_timestamp,
    inventory.work_area_code,
    inventory.inventory_qty as inventory_qty,
    COALESCE(picks.avg_picked_qty, 0) as avg_picked_qty,
    COALESCE(picks.avg_picked_orders, 0) as avg_picked_orders
FROM inventory
LEFT JOIN picks
    ON picks.item_uuid = inventory.item_uuid;
