/*
declare ltarget_tenant string default '${tenant_id}';
declare etl_batch_uuid string default '2024-01-02';

declare start_query_date timestamp default timestamp('2022-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2022-09-27 02:00:00');
 */
-- Creating the temporary staging table
CREATE TEMP TABLE
  temp_pack_activity (
    record_timestamp TIMESTAMP NOT NULL,
    user_uuid STRING DEFAULT NULL,
    user_code STRING DEFAULT NULL,
    workstation_uuid STRING NOT NULL,
    workstation_code STRING NOT NULL,
    event STRING NOT NULL,
    event_status STRING NOT NULL,
    technology STRING DEFAULT NULL,
    handling_unit_code STRING DEFAULT NULL,
    handling_unit_type STRING DEFAULT NULL,
    workflow_code STRING DEFAULT NULL,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING NOT NULL
  );

-- Inserting data into the temporary staging table
INSERT INTO
  temp_pack_activity
WITH
  raw_data AS (
    SELECT
      `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp (JSON_VALUE(data, '$.eventTime'), NULL) AS record_timestamp,
      JSON_VALUE(data, '$.userId') AS user_id,
      JSON_VALUE(data, '$.workstationId') AS workstation_id,
      JSON_VALUE(data, '$.event') AS event,
      JSON_VALUE(data, '$.eventStatus') AS event_status,
      JSON_VALUE(data, '$.technology') AS technology,
      JSON_VALUE(data, '$.handlingUnitId') AS handling_unit_code,
      JSON_VALUE(data, '$.handlingUnitType') AS handling_unit_type,
      JSON_VALUE(data, '$.workflowId') AS workflow_code,
      @ltarget_tenant AS tenant,
      facility,
      source_system
    FROM
      `${tenant_id}_landing.PackActivityFact`
    WHERE
      ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  normalized_data AS (
    SELECT
      COALESCE(TIMESTAMP(record_timestamp), TIMESTAMP(NULL)) AS record_timestamp,
      '' AS user_uuid,
      COALESCE(CAST(user_id AS STRING), '') AS user_code,
      '' AS workstation_uuid,
      COALESCE(CAST(workstation_id AS STRING), '') AS workstation_code,
      COALESCE(CAST(event AS STRING), '') AS event,
      COALESCE(CAST(event_status AS STRING), '') AS event_status,
      COALESCE(CAST(technology AS STRING), '') AS technology,
      COALESCE(CAST(handling_unit_code AS STRING), '') AS handling_unit_code,
      COALESCE(CAST(handling_unit_type AS STRING), '') AS handling_unit_type,
      COALESCE(CAST(workflow_code AS STRING), '') AS workflow_code,
      tenant,
      facility,
      source_system,
      @etl_batch_uuid AS etl_batch_id
    FROM
      raw_data
  )
SELECT
  *
FROM
  normalized_data;

/*
Merge the ID fields: 
user_uuid  -> dim_operator
workstation_uuid  ->  dim_workstation
 */
-- user_uuid  -->  dim_operator
MERGE
  `${tenant_id}_oa_curated.dim_operator` dm USING (
    SELECT
      user_uuid,
      user_code,
      tenant,
      facility,
      source_system,
      MAX(etl_batch_id) AS etl_batch_id
    FROM
      temp_pack_activity
    GROUP BY
      user_uuid,
      user_code,
      tenant,
      facility,
      source_system
  ) v ON dm.operator_code = v.user_code
  AND dm.tenant = v.tenant
  AND dm.facility = v.facility
  AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    operator_uuid,
    operator_code,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.user_code]
    ),
    v.user_code,
    v.tenant,
    v.facility,
    v.source_system,
    v.etl_batch_id
  );

UPDATE temp_pack_activity tpt
SET
  tpt.user_uuid = d.operator_uuid
FROM
  (
    select distinct
      operator_uuid,
      operator_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_operator`
  ) d
WHERE
  d.operator_code = tpt.user_code
  AND d.tenant = tpt.tenant
  AND d.facility = tpt.facility
  AND d.source_system = tpt.source_system;

-- workstation_uuid  -->  dim_workstation
MERGE
  `${tenant_id}_oa_curated.dim_workstation` dw USING (
    SELECT DISTINCT
      workstation_code,
      tenant,
      facility,
      source_system,
      MAX(etl_batch_id) AS etl_batch_id
    FROM
      temp_pack_activity
    GROUP BY
      workstation_code,
      tenant,
      facility,
      source_system
  ) v ON dw.workstation_code = v.workstation_code
  AND dw.tenant = v.tenant
  AND dw.facility = v.facility
  AND dw.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    workstation_uuid,
    workstation_code,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.workstation_code]
    ),
    v.workstation_code,
    v.tenant,
    v.facility,
    v.source_system,
    v.etl_batch_id
  );

UPDATE temp_pack_activity tt
SET
  tt.workstation_uuid = d.workstation_uuid
FROM
  (
    select distinct
      workstation_uuid,
      workstation_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_workstation`
  ) d
WHERE
  tt.workstation_code = d.workstation_code
  AND tt.tenant = d.tenant
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

-- FINAL FCT Insert
INSERT INTO
  `${tenant_id}_oa_curated.fct_pack_activity`
SELECT
  *
FROM
  temp_pack_activity;