-- DECLAR<PERSON> ltarget_tenant STRING DEFAULT 'the tenant';
-- DECLARE etl_batch_uuid STRING DEFAULT '2023-12-12';
-- DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2021-09-27 00:00:00');
-- DECLARE end_query_date TIMES<PERSON>MP DEFAULT TIMESTAMP('2024-09-27 02:00:00');

CREATE TEMP TABLE tmp_dim_subgroup
(
  -- Fields from landing table data
  subgroup_code STRING NOT NULL,            -- SubgroupDimension.primaryKey
  subgroup_id STRING NOT NULL,              -- SubgroupDimension.id
  group_code STRING NOT NULL,               -- SubgroupDimension.group
  -- dimension_modification_time TIMESTAMP  -- unused -- SubgroupDimension.dimensionModificationTime
  subgroup_type STRING,                     -- SubgroupDimension.subgroupType
  configuration_status STRING,              -- SubgroupDimension.configurationStatus
  y INT64,                                  -- SubgroupDimension.y -- ???

  --
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL
);

INSERT INTO tmp_dim_subgroup
WITH
  extracted_data AS (
    SELECT
      JSON_VALUE(data, '$.primaryKey') AS subgroup_code,
      JSON_VALUE(data, '$.id') AS subgroup_id,
      JSON_VALUE(data, '$.group') AS group_code,
      JSON_VALUE(data, '$.subgroupType') AS subgroup_type,
      JSON_VALUE(data, '$.configurationStatus') AS configuration_status,
      JSON_VALUE(data, '$.y') AS y,
      facility,
      source_system,
    FROM `${tenant_id}_landing.SubgroupDimension`
    WHERE ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  normalized_data AS (
    SELECT
      subgroup_code,
      subgroup_id,
      group_code,
      subgroup_type,
      configuration_status,
      SAFE_CAST(y AS INT64) AS y,
      @ltarget_tenant AS tenant,
      facility,
      source_system,
    FROM extracted_data
  )
SELECT
  subgroup_code,
  subgroup_id,
  group_code,
  subgroup_type,
  configuration_status,
  y,
  tenant,
  facility,
  source_system,
FROM normalized_data;

MERGE INTO `${tenant_id}_oa_curated.dim_subgroup` ds
USING (
  SELECT
    subgroup_code,
    subgroup_id,
    group_code,
    subgroup_type,
    configuration_status,
    y,
    tenant,
    facility,
    source_system,
  FROM tmp_dim_subgroup
) tt
ON ds.subgroup_code = tt.subgroup_code
WHEN NOT MATCHED THEN
  INSERT (
    subgroup_uuid,
    etl_batch_id,
    subgroup_code,
    subgroup_id,
    group_code,
    subgroup_type,
    configuration_status,
    y,
    tenant,
    facility,
    source_system
  ) VALUES (
    `${tenant_id}_oa_curated.Dimension_PK_Generator`([
      tt.tenant,
      tt.facility,
      tt.source_system,
      tt.subgroup_code
    ]),
    @etl_batch_uuid,
    tt.subgroup_code,
    tt.subgroup_id,
    tt.group_code,
    tt.subgroup_type,
    tt.configuration_status,
    tt.y,
    tt.tenant,
    tt.facility,
    tt.source_system
  )
  


















