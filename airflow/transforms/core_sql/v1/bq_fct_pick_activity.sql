/*
 **Set variables for running outside of DAG in BQ directly**
declare ltarget_tenant string default 'ict_development';
declare etl_batch_uuid string default '2020-12-12';

declare start_query_date timestamp default timestamp('2021-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2024-09-27 02:00:00');
 */
CREATE TEMP TABLE
    tmp_staging_fct_pick_activity (
        record_timestamp TIMESTAMP,
        area STRING,
        operator_code STRING,
        workstation_code STRING,
        zone_code STRING,
        technology_code STRING,
        work_type_code STRING,
        work_type_style STRING,
        technology_vendor STRING,
        container_type_code STRING,
        sku_code STRING,
        module_code STRING,
        module_name STRING,
        work_area_code STRING,
        work_area_name STRING,
        load_unit_code STRING,
        event STRING,
        tenant STRING,
        facility STRING,
        source_system STRING,
        cart_code STRING,
        container_instance_code STRING,
        cart_instance_code STRING,
        induct_type STRING,
        cluster_train_code STRING,
        induction_zone_code STRING,
        load_unit_usage_type_code STRING,
        zone_uuid STRING DEFAULT NULL,
        work_type_uuid STRING DEFAULT NULL,
        container_type_uuid STRING DEFAULT NULL,
        operator_uuid STRING DEFAULT NULL,
        workstation_uuid STRING DEFAULT NULL,
        technology_uuid STRING DEFAULT NULL,
        module_uuid STRING DEFAULT NULL,
        work_area_uuid STRING DEFAULT NULL
    );

INSERT INTO
    tmp_staging_fct_pick_activity (
        record_timestamp,
        area,
        operator_code,
        workstation_code,
        zone_code,
        technology_code,
        work_type_code,
        work_type_style,
        technology_vendor,
        container_type_code,
        sku_code,
        module_code,
        module_name,
        work_area_code,
        work_area_name,
        load_unit_code,
        event,
        tenant,
        facility,
        source_system,
        cart_code,
        container_instance_code,
        cart_instance_code,
        induct_type,
        cluster_train_code,
        induction_zone_code,
        load_unit_usage_type_code
    )
WITH
    extracted_fct_pick_activity_data AS (
        SELECT
            JSON_VALUE(data, '$.pickerId') AS pickerId,
            JSON_VALUE(data, '$.zonePk') AS zonePk,
            JSON_VALUE(data, '$.inductionZonePk') AS inductionZonePk,
            JSON_VALUE(data, '$.groupId') AS groupId,
            JSON_VALUE(data, '$.workstationId') AS workstationId,
            JSON_VALUE(data, '$.technology') AS technology,
            JSON_VALUE(data, '$.loadUnitID') AS loadUnitID,
            JSON_VALUE(data, '$.containerPk') AS containerPk,
            JSON_VALUE(data, '$.cartPk') AS cartPk,
            JSON_VALUE(data, '$.cartId') AS cartId,
            JSON_VALUE(data, '$.skuPk') AS skuPk,
            JSON_VALUE(data, '$.mode') AS mode,
            JSON_VALUE(data, '$.workflowPk') AS workflowPk,
            JSON_VALUE(data, '$.loadUnitType') AS loadUnitType,
            JSON_VALUE(data, '$.inductType') AS inductType,
            JSON_VALUE(data, '$.primaryMergeZonePk') AS primaryMergeZonePk,
            SAFE_CAST(
                JSON_VALUE(data, '$.apportionedMergeZoneDurationInSeconds') AS INT64
            ) AS apportionedMergeZoneDurationInSeconds,
            JSON_VALUE(data, '$.trainId') AS trainId,
            SAFE_CAST(JSON_VALUE(data, '$.mergeStartTime') AS TIMESTAMP) AS mergeStartTime,
            SAFE_CAST(JSON_VALUE(data, '$.mergeEndTime') AS TIMESTAMP) AS mergeEndTime,
            JSON_VALUE(data, '$.tenantName') AS tenantName,
            JSON_VALUE(data, '$.event') AS event,
            `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp (JSON_VALUE(data, '$.eventTime'), NULL) AS eventTime,
            JSON_VALUE(data, '$.Record_Timestamp_Offset') AS Record_Timestamp_Offset,
            JSON_VALUE(data, '$.userPk') AS userPk,
            JSON_VALUE(data, '$.loadUnitUsageType') AS loadUnitUsageType,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.PickActivityFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    transformed_fct_pick_activity AS (
        SELECT
            TIMESTAMP(eventTime) AS record_timestamp,
            'picking' AS area,
            UPPER(COALESCE(COALESCE(userPk, pickerId), '')) AS operator_code,
            UPPER(COALESCE(workstationId, '')) AS workstation_code,
            UPPER(COALESCE(zonePk, '')) AS zone_code,
            UPPER(COALESCE(technology, '')) AS technology_code,
            COALESCE(
                CASE
                    WHEN STRPOS(workflowPk, '#') > 0 THEN UPPER(SPLIT(workflowPk, '#') [SAFE_OFFSET(0)])
                    ELSE workflowPk
                END,
                ''
            ) AS work_type_code,
            COALESCE(
                CASE
                    WHEN STRPOS(workflowPk, '#') > 0 THEN SPLIT(workflowPk, '#') [SAFE_OFFSET(1)]
                    ELSE ''
                END,
                ''
            ) AS work_type_style,
            COALESCE(
                CASE
                    WHEN STRPOS(workflowPk, '#') > 0 THEN ARRAY_REVERSE(SPLIT(workflowPk, '#')) [SAFE_OFFSET(0)]
                    ELSE ''
                END,
                ''
            ) AS technology_vendor,
            UPPER(COALESCE(loadUnitType, '')) AS container_type_code,
            UPPER(COALESCE(skuPk, '')) AS sku_code,
            COALESCE(REPLACE(groupId, '#', '.'), '') AS module_code,
            CASE
                WHEN STRPOS(groupId, '#') > 0 THEN UPPER(
                    ARRAY_REVERSE(SPLIT(groupId, '#')) [SAFE_OFFSET(0)]
                )
                ELSE groupId
            END AS module_name,
            COALESCE(
                CASE
                    WHEN STRPOS(groupId, '#') > 0 THEN UPPER(
                        REPLACE(
                            SUBSTR(groupId, 0, INSTR(groupId, '#', 1, 2) -1),
                            '#',
                            '.'
                        )
                    )
                    ELSE ''
                END,
                ''
            ) AS work_area_code,
            CASE
                WHEN STRPOS(groupId, '#') > 0 THEN UPPER(
                    ARRAY_REVERSE(SPLIT(groupId, '#')) [SAFE_OFFSET(1)]
                )
                ELSE NULL
            END AS work_area_name,
            UPPER(COALESCE(loadUnitId, '')) AS load_unit_code,
            CASE
                WHEN event IN ('sourceLUArrived', 'pickContainerArrived') THEN 'Arrival'
                WHEN event IN ('sourceLUReleased', 'pickContainerDeparted') THEN 'Release'
                WHEN event IN ('sourceLUDeparted') THEN 'Departure'
                ELSE COALESCE(event, '')
            END AS event,
            @ltarget_tenant AS tenant,
            facility AS facility,
            source_system AS source_system,
            UPPER(COALESCE(cartId, '')) as cart_code,
            UPPER(COALESCE(containerPk, '')) as container_instance_code,
            UPPER(COALESCE(cartpk, '')) as cart_instance_code,
            UPPER(COALESCE(inductType, '')) AS induct_type,
            UPPER(COALESCE(trainID, '')) AS cluster_train_code,
            UPPER(COALESCE(inductionZonePk, '')) AS induction_zone_code,
            CASE
                WHEN event LIKE 'sourceLU%'
                OR loadunitusagetype = 'GTPWorkStationDonor' THEN 'Donor'
                WHEN event LIKE 'pickContainer%'
                OR loadunitusagetype = 'GTPWorkStationOrder' THEN 'Order'
                ELSE UPPER(COALESCE(loadunitusagetype, ''))
            END AS load_unit_usage_type_code
        FROM
            extracted_fct_pick_activity_data
    ),
    extracted_fct_workstation_mode AS (
        SELECT
            JSON_VALUE(data, '$.autostore_id') AS autostore_id,
            SAFE_CAST(JSON_VALUE(data, '$.event_time') AS TIMESTAMP) AS event_time,
            JSON_VALUE(data, '$.workstation_id') AS workstation_id,
            JSON_VALUE(data, '$.workstation_mode') AS workstation_mode,
            JSON_VALUE(data, '$.event_type') AS event_type,
            SAFE_CAST(JSON_VALUE(data, '$.process_epoch_ns') AS FLOAT64) AS process_epoch_ns,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.WorkstationModeFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    transformed_fct_workstation_mode AS (
        SELECT
            event_time AS record_timestamp,
            'picking' AS area,
            '' AS operator_code,
            UPPER(COALESCE(workstation_id, '')) AS workstation_code,
            '' AS zone_code,
            'AUTOSTORE' AS technology_code,
            SAFE_CAST('' AS STRING) AS work_type_code,
            SAFE_CAST(
                COALESCE(SAFE_CAST(workstation_mode AS STRING), '') AS STRING
            ) AS work_type_style,
            '' AS technology_vendor,
            '' AS container_type_code,
            '' AS sku_code,
            UPPER(SAFE_CAST(COALESCE(autostore_id, '') AS STRING)) AS module_code,
            UPPER(SAFE_CAST(COALESCE(autostore_id, '') AS STRING)) AS module_name,
            '' AS work_area_code,
            CAST(NULL AS STRING) AS work_area_name,
            '' AS load_unit_code,
            SAFE_CAST(COALESCE(workstation_mode, '') AS STRING) AS event,
            '' AS cart_code,
            '' AS container_instance_code,
            '' AS cart_instance_code,
            '' AS induct_type,
            '' AS cluster_train_code,
            '' AS induction_zone_code,
            '' AS load_unit_usage_type_code,
            @ltarget_tenant AS tenant,
            facility,
            source_system
        FROM
            extracted_fct_workstation_mode
    )
SELECT DISTINCT
    *
FROM
    transformed_fct_pick_activity
UNION ALL
SELECT DISTINCT
    *
FROM
    transformed_fct_workstation_mode;

--DIM Merges
-- DIM_Container_Type
MERGE
    `${tenant_id}_oa_curated.dim_container_type` dct USING (
        SELECT DISTINCT
            container_type_code,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_fct_pick_activity
        GROUP BY
            container_type_code,
            tenant,
            facility,
            source_system
    ) tt ON dct.container_type_code = tt.container_type_code
    AND dct.tenant = tt.tenant
    AND dct.facility = tt.facility
    AND dct.source_system = tt.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        container_type_uuid,
        container_type_code,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [tt.tenant,
            tt.facility,
            tt.source_system,
            tt.container_type_code]
        ),
        tt.container_type_code,
        tt.tenant,
        tt.facility,
        tt.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_pick_activity tt
SET
    container_type_uuid = d.container_type_uuid
FROM
    (
        select distinct
            container_type_uuid,
            container_type_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_container_type`
    ) d
WHERE
    tt.container_type_code = d.container_type_code
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- DIM_Operator
MERGE
    `${tenant_id}_oa_curated.dim_operator` dop USING (
        SELECT DISTINCT
            operator_code,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_fct_pick_activity
        GROUP BY
            operator_code,
            tenant,
            facility,
            source_system
    ) tt ON dop.operator_code = tt.operator_code
    AND dop.tenant = tt.tenant
    AND dop.facility = tt.facility
    AND dop.source_system = tt.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        operator_uuid,
        operator_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [tt.tenant,
            tt.facility,
            tt.source_system,
            tt.operator_code]
        ),
        tt.operator_code,
        tt.tenant,
        tt.facility,
        tt.source_system,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_pick_activity tt
SET
    operator_uuid = d.operator_uuid
FROM
    (
        select distinct
            operator_uuid,
            operator_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_operator`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.operator_code = d.operator_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- DIM_Technology
MERGE
    `${tenant_id}_oa_curated.dim_technology` dt USING (
        SELECT DISTINCT
            technology_code,
            technology_vendor,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_fct_pick_activity
        GROUP BY
            technology_code,
            tenant,
            facility,
            source_system,
            technology_vendor
    ) tt ON dt.technology_code = tt.technology_code
    AND dt.tenant = tt.tenant
    AND dt.facility = tt.facility
    AND dt.source_system = tt.source_system
WHEN MATCHED
    AND (dt.technology_vendor <> tt.technology_vendor) THEN
UPDATE SET
    technology_vendor = COALESCE(tt.technology_vendor, dt.technology_vendor),
    dt.etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
    (
        technology_uuid,
        technology_code,
        technology_vendor,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [tt.tenant,
            tt.facility,
            tt.source_system,
            tt.technology_code,
            tt.technology_vendor]
        ),
        tt.technology_code,
        tt.technology_vendor,
        tt.tenant,
        tt.facility,
        tt.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_pick_activity tt
SET
    technology_uuid = d.technology_uuid
FROM
    (
        select distinct
            technology_uuid,
            technology_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_technology`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.technology_code = d.technology_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- DIM_Work_Type
MERGE
    `${tenant_id}_oa_curated.dim_work_type` dwt USING (
        SELECT DISTINCT
            work_type_code,
            max(work_type_style) as work_type_style,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_fct_pick_activity
        group by
            work_type_code,
            tenant,
            facility,
            source_system
    ) tt ON dwt.work_type_code = tt.work_type_code
    AND dwt.tenant = tt.tenant
    AND dwt.facility = tt.facility
    AND dwt.source_system = tt.source_system
WHEN MATCHED
    AND (dwt.work_type_style <> tt.work_type_style) THEN
UPDATE SET
    work_type_style = COALESCE(tt.work_type_style, dwt.work_type_style)
WHEN NOT MATCHED THEN
INSERT
    (
        work_type_uuid,
        work_type_code,
        work_type_style,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [tt.tenant,
            tt.facility,
            tt.source_system,
            tt.work_type_code]
        ),
        tt.work_type_code,
        tt.work_type_style,
        tt.tenant,
        tt.facility,
        tt.source_system,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_pick_activity tt
SET
    work_type_uuid = d.work_type_uuid
FROM
    (
        select distinct
            work_type_uuid,
            work_type_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_work_type`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.work_type_code = d.work_type_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- DIM_Workstation
MERGE
    `${tenant_id}_oa_curated.dim_workstation` dw USING (
        SELECT DISTINCT
            workstation_code,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_fct_pick_activity
        GROUP BY
            workstation_code,
            tenant,
            facility,
            source_system
    ) tt ON dw.workstation_code = tt.workstation_code
    AND dw.tenant = tt.tenant
    AND dw.facility = tt.facility
    AND dw.source_system = tt.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        workstation_uuid,
        workstation_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [tt.tenant,
            tt.facility,
            tt.source_system,
            tt.workstation_code]
        ),
        tt.workstation_code,
        tt.tenant,
        tt.facility,
        tt.source_system,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_pick_activity tt
SET
    workstation_uuid = d.workstation_uuid
FROM
    (
        select distinct
            workstation_uuid,
            workstation_code,
            tenant,
            facility,
            source_system
        from
    `${tenant_id}_oa_curated.dim_workstation`) d
WHERE
    tt.workstation_code = d.workstation_code
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_MODULE
MERGE
    `${tenant_id}_oa_curated.dim_module` dm USING (
        SELECT DISTINCT
            module_code,
            max(module_name) as module_name,
            facility,
            source_system
        FROM
            tmp_staging_fct_pick_activity
        GROUP BY
            module_code,
            facility,
            source_system
    ) v ON dm.module_code = v.module_code
    AND dm.tenant = @ltarget_tenant
    AND dm.facility = v.facility
    AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        module_uuid,
        module_code,
        module_name,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.module_code]
        ),
        v.module_code,
        v.module_name,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_pick_activity tt
SET
    module_uuid = d.module_uuid
FROM
    (
        select distinct
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        from
    `${tenant_id}_oa_curated.dim_module`) d
WHERE
    d.module_code = tt.module_code
    AND d.tenant = @ltarget_tenant
    AND d.facility = tt.facility
    AND d.source_system = tt.source_system;

--DIM_WORK_AREA
MERGE
    `${tenant_id}_oa_curated.dim_work_area` dwa USING (
        SELECT DISTINCT
            work_area_code,
            max(work_area_name) as work_area_name,
            facility,
            source_system
        FROM
            tmp_staging_fct_pick_activity
        GROUP BY
            work_area_code,
            facility,
            source_system
    ) v ON dwa.work_area_code = v.work_area_code
    AND dwa.tenant = @ltarget_tenant
    AND dwa.facility = v.facility
    AND dwa.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        work_area_uuid,
        work_area_code,
        work_area_name,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.work_area_code]
        ),
        v.work_area_code,
        v.work_area_name,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_pick_activity tt
SET
    work_area_uuid = d.work_area_uuid
FROM
    (
        select distinct
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        from
    `${tenant_id}_oa_curated.dim_work_area`) d
WHERE
    d.work_area_code = tt.work_area_code
    AND d.tenant = @ltarget_tenant
    AND d.facility = tt.facility
    AND d.source_system = tt.source_system;

-- DIM_Zone
MERGE
    `${tenant_id}_oa_curated.dim_zone` dz USING (
        SELECT DISTINCT
            zone_code,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_fct_pick_activity tt
        GROUP BY
            zone_code,
            tenant,
            facility,
            source_system
    ) tt ON dz.zone_code = tt.zone_code
    AND dz.tenant = tt.tenant
    AND dz.facility = tt.facility
    AND dz.source_system = tt.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        zone_uuid,
        zone_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [tt.tenant,
            tt.facility,
            tt.source_system,
            tt.zone_code]
        ),
        tt.zone_code,
        tt.tenant,
        tt.facility,
        tt.source_system,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_pick_activity tt
SET
    zone_uuid = d.zone_uuid
FROM
    (
        select distinct
            zone_uuid,
            zone_code,
            tenant,
            facility,
            source_system
        from
    `${tenant_id}_oa_curated.dim_zone`) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.zone_code = d.zone_code
    AND d.facility = tt.facility
    AND d.source_system = tt.source_system;

-- FINAL FCT Insert
INSERT INTO
    `${tenant_id}_oa_curated.fct_pick_activity` (
        record_timestamp,
        area,
        operator_uuid,
        zone_uuid,
        workstation_uuid,
        technology_uuid,
        work_type_uuid,
        work_area_uuid,
        module_uuid,
        container_type_uuid,
        load_unit_code,
        event,
        sku_code,
        cart_code,
        container_instance_code,
        cart_instance_code,
        induct_type,
        cluster_train_code,
        induction_zone_code,
        load_unit_usage_type_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
SELECT DISTINCT
    record_timestamp,
    MAX(area) AS area,
    operator_uuid,
    zone_uuid,
    workstation_uuid,
    technology_uuid,
    work_type_uuid,
    work_area_uuid,
    module_uuid,
    container_type_uuid,
    load_unit_code,
    event,
    sku_code,
    MAX(cart_code) AS cart_code,
    MAX(container_instance_code) AS container_instance_code,
    MAX(cart_instance_code) AS cart_instance_code,
    MAX(induct_type) AS induct_type,
    MAX(cluster_train_code) AS cluster_train_code,
    MAX(induction_zone_code) AS induction_zone_code,
    MAX(load_unit_usage_type_code) AS load_unit_usage_type_code,
    tenant,
    facility,
    source_system,
    @etl_batch_uuid AS etl_batch_id
FROM
    tmp_staging_fct_pick_activity
GROUP BY
    record_timestamp,
    operator_uuid,
    zone_uuid,
    workstation_uuid,
    technology_uuid,
    work_type_uuid,
    work_area_uuid,
    module_uuid,
    container_type_uuid,
    load_unit_code,
    event,
    sku_code,
    tenant,
    facility,
    source_system;