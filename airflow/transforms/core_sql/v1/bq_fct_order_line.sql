/*
 **Set variables for running outside of DAG in BQ directly**
declare ltarget_tenant string default '${tenant_id}';
declare etl_batch_uuid string default '2023-12-12';

declare start_query_date timestamp default timestamp('2021-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2024-09-27 02:00:00');
 */
CREATE TEMP TABLE
    tmp_staging_fct_orderline (
        record_timestamp TIMESTAMP,
        area STRING,
        location_code STRING,
        operator_code STRING,
        item_code STRING,
        item_sku STRING,
        item_product_code STRING,
        uom STRING,
        client STRING,
        line_item STRING,
        cart_instance_code STRING,
        cart_instance_name STRING,
        technology_code STRING,
        technology_vendor STRING,
        pick_order_line_complete_ind INT64,
        zone_code STRING,
        pick_to_container_physical_code STRING,
        work_request_code STRING,
        pick_order_code STRING,
        container_train_code STRING,
        pick_to_container_type_code STRING,
        work_type_code STRING,
        container_type_code STRING,
        process_type_code STRING,
        reason_code STRING,
        container_physical_code STRING,
        container_instance_code STRING,
        pick_batch_code STRING,
        duration_seconds FLOAT64 DEFAULT NULL,
        picked_qty FLOAT64,
        skipped_qty FLOAT64,
        shorted_qty FLOAT64,
        new_container_qty FLOAT64,
        requested_qty FLOAT64,
        work_type_style STRING,
        operator_pick_time STRING,
        work_area_code STRING,
        work_area_name STRING,
        module_code STRING,
        module_name STRING,
        bay STRING,
        location_name STRING,
        workstation_code STRING,
        customer_order_code STRING,
        location_uuid STRING DEFAULT NULL,
        operator_uuid STRING DEFAULT NULL,
        item_uuid STRING DEFAULT NULL,
        work_type_uuid STRING DEFAULT NULL,
        container_type_uuid STRING DEFAULT NULL,
        pick_to_container_type_uuid STRING DEFAULT NULL,
        reason_uuid STRING DEFAULT NULL,
        technology_uuid STRING DEFAULT NULL,
        customer_order_uuid STRING DEFAULT NULL,
        pick_order_uuid STRING DEFAULT NULL,
        zone_uuid STRING DEFAULT NULL,
        module_uuid STRING DEFAULT NULL,
        work_area_uuid STRING DEFAULT NULL,
        workstation_uuid STRING DEFAULT NULL,
        tenant STRING,
        facility STRING,
        source_system STRING
    );

-- Sort Disposition Events
INSERT INTO
    tmp_staging_fct_orderline (
        record_timestamp,
        area,
        location_code,
        operator_code,
        item_code,
        item_sku,
        item_product_code,
        uom,
        client,
        line_item,
        cart_instance_code,
        cart_instance_name,
        technology_code,
        technology_vendor,
        pick_order_line_complete_ind,
        zone_code,
        pick_to_container_physical_code,
        work_request_code,
        pick_order_code,
        container_train_code,
        pick_to_container_type_code,
        work_type_code,
        container_type_code,
        process_type_code,
        reason_code,
        container_physical_code,
        container_instance_code,
        pick_batch_code,
        duration_seconds,
        picked_qty,
        skipped_qty,
        shorted_qty,
        new_container_qty,
        requested_qty,
        work_type_style,
        operator_pick_time,
        work_area_code,
        work_area_name,
        module_code,
        module_name,
        bay,
        location_name,
        workstation_code,
        customer_order_code,
        tenant,
        facility,
        source_system
    )
WITH
    extracted_pick_activity AS (
        SELECT
            JSON_VALUE(data, '$.pickerId') AS pickerid,
            JSON_VALUE(data, '$.zonePk') AS zonepk,
            JSON_VALUE(data, '$.inductionZonePk') AS inductionzonepk,
            JSON_VALUE(data, '$.groupId') AS groupid,
            JSON_VALUE(data, '$.workstationId') AS workstationid,
            JSON_VALUE(data, '$.technology') AS technology,
            JSON_VALUE(data, '$.loadUnitID') AS loadunitid,
            JSON_VALUE(data, '$.containerPk') AS containerpk,
            JSON_VALUE(data, '$.cartPk') AS cartpk,
            JSON_VALUE(data, '$.cartId') AS cartid,
            JSON_VALUE(data, '$.skuPk') AS skupk,
            JSON_VALUE(data, '$.mode') AS mode,
            JSON_VALUE(data, '$.workflowPk') AS workflowpk,
            JSON_VALUE(data, '$.loadUnitType') AS loadunittype,
            JSON_VALUE(data, '$.inductType') AS inducttype,
            JSON_VALUE(data, '$.primaryMergeZonePk') AS primarymergezonepk,
            SAFE_CAST(
                JSON_VALUE(data, '$.apportionedMergeZoneDurationInSeconds') AS INT64
            ) AS apportionedmergezonedurationinseconds,
            JSON_VALUE(data, '$.trainId') AS trainid,
            SAFE_CAST(JSON_VALUE(data, '$.mergeStartTime') AS TIMESTAMP) AS mergestarttime,
            SAFE_CAST(JSON_VALUE(data, '$.mergeEndTime') AS TIMESTAMP) AS mergeendtime,
            JSON_VALUE(data, '$.tenantName') AS tenantname,
            JSON_VALUE(data, '$.event') AS event,
            SAFE_CAST(JSON_VALUE(data, '$.eventTime') AS TIMESTAMP) AS eventtime,
            JSON_VALUE(data, '$.Record_Timestamp_Offset') AS record_timestamp_offset,
            JSON_VALUE(data, '$.userPk') AS userpk,
            JSON_VALUE(data, '$.loadUnitUsageType') AS loadunitusagetype,
            tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.PickActivityFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    standardized_pick_activity AS (
        SELECT
            eventtime,
            UPPER(COALESCE(userpk, pickerid)) AS pickerid,
            SAFE_CAST(NULL AS STRING) AS orderpk,
            SAFE_CAST(NULL AS STRING) AS pick_order_code,
            SAFE_CAST(NULL AS STRING) AS client,
            SAFE_CAST(NULL AS STRING) AS sourceloadunit,
            loadunitid,
            SAFE_CAST(NULL AS STRING) AS locationpk,
            containerpk,
            CASE
                WHEN skupk = '-' THEN NULL
                ELSE skupk
            END AS skupk,
            zonepk,
            inductionzonepk,
            SAFE_CAST(NULL AS STRING) AS terminalid,
            SAFE_CAST(NULL AS FLOAT64) AS quantitypicked,
            SAFE_CAST(NULL AS FLOAT64) AS quantitytarget,
            SAFE_CAST(NULL AS STRING) AS pickpk,
            SAFE_CAST(NULL AS STRING) AS hostlineid,
            SAFE_CAST(NULL AS STRING) AS pickbatchname,
            SAFE_CAST(NULL AS STRING) AS quantityunit,
            SAFE_CAST(NULL AS STRING) AS sourceloadunittype,
            loadunittype,
            cartpk,
            cartid,
            technology,
            SAFE_CAST(NULL AS STRING) AS processtype,
            UPPER(event) AS pickconfirmationcode,
            workflowpk,
            SAFE_CAST(NULL AS STRING) AS confirmationreason,
            trainid,
            workstationid,
            SAFE_CAST(NULL AS FLOAT64) AS quantityremaining,
            SAFE_CAST(NULL AS STRING) AS productcode,
            tenant,
            facility,
            source_system
        FROM
            extracted_pick_activity --33
    ),
    --FCT_Picking_OrderLine
    extracted_pickfact AS (
        SELECT
            SAFE_CAST(JSON_VALUE(data, '$.eventTime') AS TIMESTAMP) AS eventtime,
            UPPER(
                COALESCE(
                    JSON_VALUE(data, '$.userPk'),
                    JSON_VALUE(data, '$.pickerId')
                )
            ) AS pickerid,
            JSON_VALUE(data, '$.orderPk') AS orderpk,
            JSON_VALUE(data, '$.pickOrderPk') AS pick_order_code,
            JSON_VALUE(data, '$.client') AS client,
            JSON_VALUE(data, '$.sourceLoadUnit') AS sourceloadunit,
            JSON_VALUE(data, '$.loadUnitID') AS loadunitid,
            JSON_VALUE(data, '$.locationPk') AS locationpk,
            JSON_VALUE(data, '$.containerPk') AS containerpk,
            CASE
                WHEN JSON_VALUE(data, '$.skuPk') = '-' THEN NULL
                ELSE JSON_VALUE(data, '$.skuPk')
            END AS skupk,
            JSON_VALUE(data, '$.zonePk') AS zonepk,
            JSON_VALUE(data, '$.inductionZonePk') AS inductionzonepk,
            JSON_VALUE(data, '$.terminalId') AS terminalid,
            SAFE_CAST(JSON_VALUE(data, '$.quantityPicked') AS FLOAT64) AS quantitypicked,
            SAFE_CAST(JSON_VALUE(data, '$.quantityTarget') AS FLOAT64) AS quantitytarget,
            JSON_VALUE(data, '$.pickPk') AS pickpk,
            JSON_VALUE(data, '$.hostLineId') AS hostlineid,
            JSON_VALUE(data, '$.pickBatchName') AS pickbatchname,
            JSON_VALUE(data, '$.quantityUnitPk') AS quantityunit,
            JSON_VALUE(data, '$.sourceLoadUnitType') AS sourceloadunittype,
            JSON_VALUE(data, '$.loadUnitType') AS loadunittype,
            JSON_VALUE(data, '$.cartPk') AS cartpk,
            JSON_VALUE(data, '$.cartId') AS cartid,
            JSON_VALUE(data, '$.technology') AS technology,
            JSON_VALUE(data, '$.processType') AS processtype,
            UPPER(JSON_VALUE(data, '$.confirmationCode')) AS pickconfirmationcode,
            JSON_VALUE(data, '$.workflowPk') AS workflowpk,
            JSON_VALUE(data, '$.confirmationReason') AS confirmationreason,
            JSON_VALUE(data, '$.trainId') AS trainid,
            JSON_VALUE(data, '$.workstationId') AS workstationid,
            SAFE_CAST(
                JSON_VALUE(data, '$.quantityRemaining') AS FLOAT64
            ) AS quantityremaining,
            JSON_VALUE(data, '$.productCode') AS productcode,
            tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.PickFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    standardized_events AS (
        SELECT
            *
        FROM
            extracted_pickfact
        UNION DISTINCT
        SELECT
            *
        FROM
            standardized_pick_activity
        WHERE
            (
                inductionzonepk IS NULL
                OR inductionzonepk = ''
            )
            AND (
                UPPER(COALESCE(pickconfirmationcode, '')) = 'INDUCT'
                AND LENGTH(RTRIM(COALESCE(cartid, ''))) = 0
                OR UPPER(COALESCE(pickconfirmationcode, '')) = 'CART_ACTIVATED'
            )
    ),
    mapped_events AS (
        SELECT
            eventtime AS record_timestamp,
            pickerid AS operator_code,
            pickpk AS line_item,
            technology,
            productcode,
            quantitytarget,
            quantitypicked,
            pickbatchname,
            containerpk,
            confirmationreason,
            processtype,
            sourceloadunit,
            sourceloadunittype,
            loadunittype,
            loadunitid,
            pickconfirmationcode AS event,
            UPPER(COALESCE(zonepk, workstationid)) AS zonepk,
            COALESCE(trainid, '') AS trainid,
            pick_order_code,
            COALESCE(orderpk, '') AS customer_order_code,
            cartpk,
            cartid,
            SUBSTR(skupk, INSTR(skupk, '#') + 1) AS sku_code,
            CASE
                WHEN client IS NOT NULL THEN client
                WHEN STRPOS(skupk, '#') > 0 THEN LEFT(skupk, STRPOS(skupk, '#') - 1)
                ELSE SAFE_CAST(NULL AS STRING)
            END AS client_code,
            CASE
                WHEN STRPOS(workflowpk, '#') > 0 THEN LEFT(workflowpk, STRPOS(workflowpk, '#') - 1)
                ELSE workflowpk
            END AS worktype,
            locationpk AS location_code,
            `${tenant_id}_oa_curated.Location_Name_Generator` (locationpk) AS location_name,
            COALESCE(workstationid, '') AS workstation_code,
            CASE
                WHEN STRPOS(locationpk, '.') > 0 THEN UPPER(
                    SUBSTR(locationpk, 0, INSTR(locationpk, '.', 1, 2) -1)
                )
                ELSE ''
            END AS work_area_code,
            CASE
                WHEN STRPOS(locationpk, '.') > 0 THEN UPPER(SPLIT(locationpk, '.') [SAFE_OFFSET(1)])
                ELSE NULL
            END AS work_area_name,
            CASE
                WHEN STRPOS(locationpk, '.') > 0 THEN UPPER(
                    SUBSTR(locationpk, 0, INSTR(locationpk, '.', 1, 3) -1)
                )
                ELSE ''
            END AS module_code,
            CASE
                WHEN STRPOS(locationpk, '.') > 0 THEN UPPER(SPLIT(locationpk, '.') [SAFE_OFFSET(2)])
                ELSE NULL
            END AS module_name,
            CASE
                WHEN STRPOS(locationpk, '.') > 0 THEN UPPER(SPLIT(locationpk, '.') [SAFE_OFFSET(3)])
                ELSE NULL
            END AS bay_code,
            CASE
                WHEN LENGTH(quantityunit) - LENGTH(REPLACE(quantityunit, '#', '')) > 1 THEN SUBSTR(quantityunit, INSTR(quantityunit, '#', 1, 2) + 1)
                ELSE quantityunit
            END AS uom_code,
            CASE
                WHEN LENGTH(workflowpk) - LENGTH(REPLACE(workflowpk, '#', '')) > 1 THEN SPLIT(workflowpk, '#') [OFFSET(1)]
                ELSE NULL
            END AS worktypestyle,
            CASE
                WHEN LENGTH(COALESCE(technology, '')) > 0
                AND INSTR(workflowpk, '#', 1, 3) > 0 THEN SUBSTR(workflowpk, INSTR(workflowpk, '#', 1, 3) + 1)
                ELSE NULL
            END AS technology_vendor,
            CASE
                WHEN quantityremaining IS NULL THEN COALESCE(quantitytarget, SAFE_CAST(0 AS FLOAT64)) - COALESCE(quantitypicked, SAFE_CAST(0 AS FLOAT64))
                ELSE quantityremaining
            END AS quantityremaining,
            CASE
                WHEN pickconfirmationcode = 'OK' THEN 'Completed'
                WHEN pickconfirmationcode = 'SPLIT' THEN 'Ncar'
                WHEN pickconfirmationcode = 'SHORT'
                AND STRPOS(pick_order_code, '#') > 0 THEN 'ShortCompleted'
                WHEN pickconfirmationcode = 'NOTPICKED'
                AND STRPOS(pick_order_code, '#') > 0 THEN 'ShortCompleted'
                WHEN pickconfirmationcode = 'FULL' THEN 'Ncar'
                WHEN pickconfirmationcode = 'DAMAGE' THEN 'Ncar'
                ELSE pickconfirmationcode
            END AS mapped_event,
            tenant,
            facility,
            source_system
        FROM
            standardized_events
        WHERE
            LENGTH(RTRIM(COALESCE(pickerid, ''))) > 0
            AND UPPER(TRIM(COALESCE(pickconfirmationcode, ''))) != 'DELETED'
            AND UPPER(COALESCE(processtype, '')) != 'PUTWALL_PACK'
            AND LENGTH(RTRIM(COALESCE(pickerid, ''))) > 0
    ),
    process_events AS (
        SELECT
            *,
            CASE
                WHEN UPPER(mapped_event) = 'SKIPPED' THEN SAFE_CAST(quantityremaining AS INT64)
                ELSE 0
            END AS skippedqty,
            CASE
                WHEN UPPER(mapped_event) = 'SHORT'
                OR UPPER(mapped_event) = 'SHORTCOMPLETED' THEN SAFE_CAST(quantityremaining AS INT64)
                ELSE 0
            END AS shortedqty,
            CASE
                WHEN UPPER(mapped_event) = 'SHORTCOMPLETED'
                OR UPPER(mapped_event) = 'COMPLETED'
                OR UPPER(mapped_event) = 'NCAR'
                AND quantityremaining = 0 THEN 1
                ELSE 0
            END AS orderlinecompletedidx,
            CASE
                WHEN UPPER(mapped_event) = 'NCAR' THEN SAFE_CAST(quantityremaining AS INT64)
                ELSE 0
            END AS newcontainerqty,
            SAFE_CAST(NULL AS INT64) AS duration_seconds,
            'picking' AS area
        FROM
            mapped_events
    )
SELECT DISTINCT
    record_timestamp,
    MAX(area) AS area,
    COALESCE(UPPER(location_code), '') AS location_code,
    COALESCE(operator_code, '') AS operator_code,
    `${tenant_id}_oa_curated.Item_Code_Generator` (sku_code, productcode, uom_code, NULL) AS item_code,
    sku_code AS item_sku,
    productcode AS item_product_code,
    uom_code AS uom,
    client_code AS client,
    COALESCE(UPPER(line_item), '') AS line_item,
    COALESCE(COALESCE(cartpk, cartid), '') AS cart_instance_code,
    COALESCE(COALESCE(cartid, cartpk), '') AS cart_instance_name,
    COALESCE(UPPER(technology), '') AS technology_code,
    COALESCE(UPPER(technology_vendor), '') AS technology_vendor,
    orderlinecompletedidx AS pick_order_line_complete_ind,
    COALESCE(UPPER(zonepk), '') AS zone_code,
    COALESCE(UPPER(loadunitid), '') AS pick_to_container_physical_code,
    '' AS work_request_code,
    COALESCE(UPPER(pick_order_code), '') AS pick_order_code,
    COALESCE(UPPER(trainid), '') AS container_train_code,
    COALESCE(UPPER(loadunittype), '') AS pick_to_container_type_code,
    COALESCE(UPPER(worktype), '') AS work_type_code,
    COALESCE(UPPER(sourceloadunittype), '') AS container_type_code,
    COALESCE(UPPER(processtype), '') AS process_type_code,
    COALESCE(UPPER(confirmationreason), '') AS reason_code,
    COALESCE(UPPER(sourceloadunit), '') AS container_physical_code,
    COALESCE(UPPER(containerpk), '') AS container_instance_code,
    COALESCE(pickbatchname, '') AS pick_batch_code,
    SAFE_CAST(NULL AS FLOAT64) AS duration_seconds,
    SUM(quantitypicked) AS picked_qty,
    SUM(skippedqty) AS skipped_qty,
    SUM(shortedqty) AS shorted_qty,
    SUM(newcontainerqty) AS new_container_qty,
    SUM(SAFE_CAST(quantitytarget AS INT64)) AS requested_qty,
    COALESCE(UPPER(worktypestyle), '') AS work_type_style,
    '' AS operator_pick_time,
    COALESCE(work_area_code, '') AS work_area_code,
    MAX(work_area_name) as work_area_name,
    COALESCE(module_code, '') AS module_code,
    MAX(module_name) as module_name,
    bay_code AS bay,
    location_name AS location_name,
    COALESCE(workstation_code, '') AS workstation_code,
    COALESCE(customer_order_code, '') AS customer_order_code,
    tenant,
    facility,
    source_system
FROM
    process_events
GROUP BY
    record_timestamp,
    location_code,
    operator_code,
    sku_code,
    productcode,
    uom_code,
    client_code,
    line_item,
    cartpk,
    cartid,
    technology,
    technology_vendor,
    orderlinecompletedidx,
    zonepk,
    loadunitid,
    pick_order_code,
    trainid,
    loadunittype,
    worktype,
    sourceloadunittype,
    processtype,
    confirmationreason,
    sourceloadunit,
    containerpk,
    pickbatchname,
    worktypestyle,
    work_area_code,
    module_code,
    bay_code,
    location_name,
    workstation_code,
    customer_order_code,
    tenant,
    facility,
    source_system;

-- DIM_Operator
MERGE
    `${tenant_id}_oa_curated.dim_operator` op USING (
        SELECT DISTINCT
            operator_code,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
        GROUP BY
            operator_code,
            facility,
            source_system
    ) sfo ON op.operator_code = sfo.operator_code
    AND op.tenant = @ltarget_tenant
    AND op.facility = sfo.facility
    AND op.source_system = sfo.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        operator_uuid,
        operator_code,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            sfo.facility,
            sfo.source_system,
            sfo.operator_code]
        ),
        sfo.operator_code,
        @ltarget_tenant,
        sfo.facility,
        sfo.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tol
SET
    operator_uuid = d.operator_uuid
FROM
    (
        select distinct
            operator_uuid,
            operator_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_operator`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tol.operator_code = d.operator_code
    AND tol.facility = d.facility
    AND tol.source_system = d.source_system;

--DIM_Item
MERGE
    `${tenant_id}_oa_curated.dim_item` di USING (
        SELECT DISTINCT
            item_code,
            max(item_sku) as item_sku,
            max(item_product_code) as item_product_code,
            max(uom) as uom,
            max(client) as client,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
        GROUP BY
            item_code,
            facility,
            source_system
    ) v ON di.item_code = v.item_code
    AND di.item_category_code = ''
    AND di.tenant = @ltarget_tenant
    AND di.facility = v.facility
    AND di.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        item_uuid,
        item_code,
        item_category_code,
        item_sku,
        item_product_code,
        uom,
        client,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.item_code]
        ),
        v.item_code,
        '',
        v.item_sku,
        v.item_product_code,
        v.uom,
        v.client,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    item_uuid = d.item_uuid
FROM
    (
        select distinct
            item_uuid,
            item_code,
            item_sku,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_item`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.item_code = d.item_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- DIM_Technology
MERGE
    `${tenant_id}_oa_curated.dim_technology` dt USING (
        SELECT DISTINCT
            technology_code,
            technology_vendor,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
        GROUP BY
            technology_code,
            facility,
            source_system,
            technology_vendor
    ) v ON dt.technology_code = v.technology_code
    AND dt.tenant = @ltarget_tenant
    AND dt.facility = v.facility
    AND dt.source_system = v.source_system
WHEN MATCHED
    AND dt.technology_vendor != v.technology_vendor THEN
UPDATE SET
    dt.technology_vendor = COALESCE(v.technology_vendor, dt.technology_vendor),
    dt.etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
    (
        technology_uuid,
        technology_code,
        technology_vendor,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.technology_code,
            v.technology_vendor]
        ),
        v.technology_code,
        v.technology_vendor,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    technology_uuid = d.technology_uuid
FROM
    (
        select distinct
            technology_uuid,
            technology_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_technology`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.technology_code = d.technology_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_Reason
MERGE
    `${tenant_id}_oa_curated.dim_reason` dr USING (
        SELECT DISTINCT
            reason_code,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
        GROUP BY
            reason_code,
            facility,
            source_system
    ) v ON dr.reason_code = v.reason_code
    AND dr.tenant = @ltarget_tenant
    AND dr.facility = v.facility
    AND dr.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        reason_uuid,
        reason_code,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.reason_code]
        ),
        v.reason_code,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    reason_uuid = d.reason_uuid
FROM
    (
        select distinct
            reason_uuid,
            reason_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_reason`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.reason_code = d.reason_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_Work_type
MERGE
    `${tenant_id}_oa_curated.dim_work_type` dwt USING (
        SELECT DISTINCT
            work_type_code,
            max(work_type_style) as work_type_style,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
        GROUP BY
            work_type_code,
            facility,
            source_system
    ) v ON dwt.work_type_code = v.work_type_code
    AND dwt.tenant = @ltarget_tenant
    AND dwt.facility = v.facility
    AND dwt.source_system = v.source_system
WHEN MATCHED
    AND dwt.work_type_style != v.work_type_style THEN
UPDATE SET
    work_type_style = COALESCE(v.work_type_style, dwt.work_type_style),
    dwt.etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
    (
        work_type_uuid,
        work_type_code,
        work_type_style,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.work_type_code]
        ),
        v.work_type_code,
        v.work_type_style,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    work_type_uuid = d.work_type_uuid
FROM
    (
        select distinct
            work_type_uuid,
            work_type_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_work_type`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.work_type_code = d.work_type_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_Container_Type
MERGE
    `${tenant_id}_oa_curated.dim_container_type` dct USING (
        SELECT DISTINCT
            container_type_code,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
        UNION DISTINCT
        SELECT DISTINCT
            pick_to_container_type_code as container_type_code,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
    ) v ON dct.container_type_code = v.container_type_code
    AND dct.tenant = @ltarget_tenant
    AND dct.facility = v.facility
    AND dct.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        container_type_uuid,
        container_type_code,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.container_type_code]
        ),
        v.container_type_code,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    container_type_uuid = d.container_type_uuid
FROM
    (
        select distinct
            container_type_uuid,
            container_type_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_container_type`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.container_type_code = d.container_type_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

UPDATE tmp_staging_fct_orderline tt
SET
    pick_to_container_type_uuid = d.container_type_uuid
FROM
    (
        select distinct
            container_type_uuid,
            container_type_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_container_type`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.pick_to_container_type_code = d.container_type_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_Customer_Order
MERGE
    `${tenant_id}_oa_curated.dim_customer_order` dco USING (
        SELECT DISTINCT
            customer_order_code,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
    ) v ON dco.customer_order_code = v.customer_order_code
    AND dco.tenant = @ltarget_tenant
    AND dco.facility = v.facility
    AND dco.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        customer_order_uuid,
        customer_order_code,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.customer_order_code]
        ),
        v.customer_order_code,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    customer_order_uuid = d.customer_order_uuid
FROM
    (
        select distinct
            customer_order_uuid,
            customer_order_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_customer_order`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system
    AND tt.customer_order_code = d.customer_order_code;

--DIM_Pick_Order
MERGE
    `${tenant_id}_oa_curated.dim_pick_order` dpo USING (
        SELECT DISTINCT
            pick_order_code,
            customer_order_uuid,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
    ) v ON dpo.pick_order_code = v.pick_order_code
    AND dpo.customer_order_uuid = v.customer_order_uuid
    AND dpo.tenant = @ltarget_tenant
    AND dpo.facility = v.facility
    AND dpo.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        pick_order_uuid,
        customer_order_uuid,
        pick_order_code,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.pick_order_code,
            v.customer_order_uuid]
        ),
        v.customer_order_uuid,
        v.pick_order_code,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    pick_order_uuid = d.pick_order_uuid
FROM
    (
        select distinct
            pick_order_uuid,
            pick_order_code,
            customer_order_uuid,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_pick_order`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system
    AND tt.pick_order_code = d.pick_order_code
    AND tt.customer_order_uuid = d.customer_order_uuid;

--DIM_Location
MERGE
    `${tenant_id}_oa_curated.dim_location` dl USING (
        SELECT DISTINCT
            location_code,
            max(location_name) as location_name,
            max(bay) as bay,
            facility,
            source_system
        FROM
            (
                SELECT DISTINCT
                    location_code,
                    location_name,
                    bay,
                    facility,
                    source_system
                FROM
                    tmp_staging_fct_orderline
                UNION DISTINCT
                --logical destination in dim_zone
                SELECT
                    '' AS location_code,
                    NULL AS location_name,
                    NULL AS bay,
                    '' AS facility,
                    '' AS source_system
            ) t
        group by
            location_code,
            facility,
            source_system
    ) v ON dl.location_code = v.location_code
    AND dl.tenant = @ltarget_tenant
    AND dl.facility = v.facility
    AND dl.source_system = v.source_system
WHEN MATCHED
    AND (
        dl.location_name <> v.location_name
        OR dl.bay <> v.bay
    ) THEN
UPDATE SET
    dl.location_name = COALESCE(v.location_name, dl.location_name),
    dl.bay = COALESCE(v.bay, dl.bay),
    etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
    (
        location_uuid,
        location_code,
        bay,
        location_name,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.location_code]
        ),
        v.location_code,
        v.bay,
        v.location_name,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    location_uuid = d.location_uuid
FROM
    (
        select distinct
            location_uuid,
            location_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_location`
    ) d
WHERE
    tt.location_code = d.location_code
    AND d.tenant = @ltarget_tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_MODULE
MERGE
    `${tenant_id}_oa_curated.dim_module` dm USING (
        SELECT DISTINCT
            module_code,
            max(module_name) as module_name,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
        GROUP BY
            module_code,
            facility,
            source_system
    ) v ON dm.module_code = v.module_code
    AND dm.tenant = @ltarget_tenant
    AND dm.facility = v.facility
    AND dm.source_system = v.source_system
WHEN MATCHED THEN
UPDATE SET
    dm.module_name = v.module_name,
    dm.facility = v.facility,
    dm.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        module_uuid,
        module_code,
        module_name,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.module_code]
        ),
        v.module_code,
        v.module_name,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    module_uuid = d.module_uuid
FROM
    (
        select distinct
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_module`
    ) d
WHERE
    d.module_code = tt.module_code
    AND d.tenant = @ltarget_tenant
    AND d.facility = tt.facility
    AND d.source_system = tt.source_system;

--DIM_WORK_AREA
MERGE
    `${tenant_id}_oa_curated.dim_work_area` dwa USING (
        SELECT DISTINCT
            work_area_code,
            max(work_area_name) as work_area_name,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
        GROUP BY
            work_area_code,
            facility,
            source_system
    ) v ON dwa.work_area_code = v.work_area_code
    AND dwa.tenant = @ltarget_tenant
    AND dwa.facility = v.facility
    AND dwa.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        work_area_uuid,
        work_area_code,
        work_area_name,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.work_area_code]
        ),
        v.work_area_code,
        v.work_area_name,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    work_area_uuid = d.work_area_uuid
FROM
    (
        select distinct
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_work_area`
    ) d
WHERE
    d.work_area_code = tt.work_area_code
    AND d.tenant = @ltarget_tenant
    AND d.facility = tt.facility
    AND d.source_system = tt.source_system;

--DIM_Zone
MERGE
    `${tenant_id}_oa_curated.dim_zone` dz USING (
        SELECT DISTINCT
            zone_code,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
        GROUP BY
            zone_code,
            facility,
            source_system
    ) v ON dz.zone_code = v.zone_code
    AND dz.tenant = @ltarget_tenant
    AND dz.facility = v.facility
    AND dz.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        zone_uuid,
        zone_code,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.zone_code]
        ),
        v.zone_code,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    zone_uuid = d.zone_uuid
FROM
    (
        select distinct
            zone_uuid,
            zone_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_zone`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.zone_code = d.zone_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- DIM_Workstation
MERGE
    `${tenant_id}_oa_curated.dim_workstation` ws USING (
        SELECT DISTINCT
            workstation_code,
            facility,
            source_system
        FROM
            tmp_staging_fct_orderline
        GROUP BY
            workstation_code,
            facility,
            source_system
    ) tws ON ws.workstation_code = tws.workstation_code
    AND ws.tenant = @ltarget_tenant
    AND ws.facility = tws.facility
    AND ws.source_system = tws.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        workstation_uuid,
        workstation_code,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            tws.facility,
            tws.source_system,
            tws.workstation_code]
        ),
        tws.workstation_code,
        @ltarget_tenant,
        tws.facility,
        tws.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_fct_orderline tt
SET
    workstation_uuid = dws.workstation_uuid
FROM
    (
        select distinct
            workstation_uuid,
            workstation_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_workstation`
    ) dws
WHERE
    dws.tenant = @ltarget_tenant
    AND tt.workstation_code = dws.workstation_code
    AND tt.facility = dws.facility
    AND tt.source_system = dws.source_system;

--FINAL FCT Insert
INSERT INTO
    `${tenant_id}_oa_curated.fct_order_line` (
        record_timestamp,
        area,
        tenant,
        facility,
        source_system,
        location_uuid,
        module_uuid,
        work_area_uuid,
        workstation_uuid,
        operator_uuid,
        zone_uuid,
        pick_order_uuid,
        item_uuid,
        cart_instance_code,
        cart_instance_name,
        technology_uuid,
        container_train_code,
        pick_to_container_type_uuid,
        line_item,
        work_type_uuid,
        work_request_code,
        container_type_uuid,
        process_type_code,
        reason_uuid,
        pick_order_line_complete_ind,
        container_physical_code,
        pick_to_container_physical_code,
        container_instance_code,
        pick_batch_code,
        duration_seconds,
        picked_qty,
        skipped_qty,
        shorted_qty,
        new_container_qty,
        requested_qty,
        etl_batch_id
    )
SELECT DISTINCT
    record_timestamp,
    MAX(area) AS area,
    @ltarget_tenant AS tenant,
    facility,
    source_system,
    location_uuid,
    module_uuid,
    work_area_uuid,
    workstation_uuid,
    operator_uuid,
    zone_uuid,
    pick_order_uuid,
    item_uuid,
    cart_instance_code,
    MAX(cart_instance_name) AS cart_instance_name,
    technology_uuid,
    container_train_code,
    pick_to_container_type_uuid,
    line_item,
    work_type_uuid,
    work_request_code,
    container_type_uuid,
    process_type_code,
    reason_uuid,
    MAX(pick_order_line_complete_ind) AS pick_order_line_complete_ind,
    MAX(container_physical_code) AS container_physical_code,
    MAX(pick_to_container_physical_code) AS pick_to_container_physical_code,
    MAX(container_instance_code) AS container_instance_code,
    MAX(pick_batch_code) AS pick_batch_code,
    MAX(duration_seconds) AS duration_seconds,
    MAX(picked_qty) AS picked_qty,
    MAX(skipped_qty) AS skipped_qty,
    MAX(shorted_qty) AS shorted_qty,
    MAX(new_container_qty) AS new_container_qty,
    MAX(requested_qty) AS requested_qty,
    @etl_batch_uuid AS etl_batch_id
FROM
    tmp_staging_fct_orderline
GROUP BY
    record_timestamp,
    location_uuid,
    module_uuid,
    work_area_uuid,
    workstation_uuid,
    operator_uuid,
    zone_uuid,
    pick_order_uuid,
    item_uuid,
    cart_instance_code,
    technology_uuid,
    container_train_code,
    pick_to_container_type_uuid,
    line_item,
    work_type_uuid,
    work_request_code,
    container_type_uuid,
    process_type_code,
    reason_uuid,
    facility,
    source_system;