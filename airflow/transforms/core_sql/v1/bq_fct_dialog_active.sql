/*
DECLARE ltarget_tenant STRING DEFAULT 'dematic';
DECLARE etl_batch_uuid STRING DEFAULT '2024-01-02';

DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2022-09-27 00:00:00');
DECLARE end_query_date TIM<PERSON><PERSON><PERSON> DEFAULT TIMESTAMP('2024-09-27 02:00:00');
 */
-- Creating the temporary staging table for fct_dialog_active
CREATE TEMP TABLE
    temp_dialog_active (
        dialog_name STRING,
        dialog_type STRING,
        session_id STRING,
        terminal_id STRING,
        user_id STRING,
        event_date TIMESTAMP,
        operator_uuid STRING,
        record_timestamp TIMESTAMP,
        tenant STRING,
        facility STRING,
        source_system STRING,
        etl_batch_id STRING
    );

-- Inserting data into the temporary staging table
INSERT INTO
    temp_dialog_active
WITH
    raw_data AS (
        SELECT
            JSON_VALUE(data, '$.dialogName') AS dialog_name,
            JSON_VALUE(data, '$.dialogType') AS dialog_type,
            JSON_VALUE(data, '$.uiSessionIdentifier') AS session_id,
            JSON_VALUE(data, '$.terminalId') AS terminal_id,
            JSON_VALUE(data, '$.userSubject') AS user_id,
            `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp (JSON_VALUE(data, '$.eventDate'), NULL) AS event_date,
            '' AS operator_uuid,
            `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp (JSON_VALUE(data, '$.factTime'), NULL) AS record_timestamp,
            @ltarget_tenant AS tenant,
            facility,
            source_system,
            @etl_batch_uuid as etl_batch_id
        FROM
            `${tenant_id}_landing.DialogActiveFact`
        WHERE
            ingestion_date BETWEEN @start_query_date AND @end_query_date
    )
SELECT
    *
FROM
    raw_data;

-- DIM_Operator
MERGE
    `${tenant_id}_oa_curated.dim_operator` dop USING (
        SELECT DISTINCT
            user_id,
            tenant,
            facility,
            source_system
        FROM
            temp_dialog_active
        GROUP BY
            user_id,
            tenant,
            facility,
            source_system
    ) tt ON dop.operator_code = tt.user_id
    AND dop.tenant = tt.tenant
    AND dop.facility = tt.facility
    AND dop.source_system = tt.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        operator_uuid,
        operator_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [tt.tenant,
            tt.facility,
            tt.source_system,
            tt.user_id]
        ),
        tt.user_id,
        tt.tenant,
        tt.facility,
        tt.source_system,
        @etl_batch_uuid
    );

UPDATE temp_dialog_active tt
SET
    operator_uuid = d.operator_uuid
FROM
    (
        select distinct
            operator_uuid,
            operator_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_operator`
    ) d
WHERE
    d.tenant = @ltarget_tenant
    AND tt.user_id = d.operator_code
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- Merging into the Target Fact Table (fct_dialog_active)
-- Inserting data into the Target Fact Table (fct_dialog_active)
INSERT INTO
    `${tenant_id}_oa_curated.fct_dialog_active` (
        dialog_name,
        dialog_type,
        session_id,
        terminal_id,
        user_id,
        event_date,
        operator_uuid,
        record_timestamp,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
SELECT
    dialog_name,
    dialog_type,
    session_id,
    terminal_id,
    user_id,
    event_date,
    operator_uuid,
    record_timestamp,
    tenant,
    facility,
    source_system,
    @etl_batch_uuid
FROM
    temp_dialog_active;