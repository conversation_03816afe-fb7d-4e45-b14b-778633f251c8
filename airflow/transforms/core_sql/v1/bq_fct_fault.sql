/*
declare ltarget_tenant string default 'ict_development';
declare etl_batch_uuid string default STRING(CURRENT_TIMESTAMP);

declare start_query_date timestamp default timestamp('2024-04-19 00:00:00');
declare end_query_date timestamp default timestamp('2024-04-19 12:00:00');
 */
CREATE TEMP TABLE
    preprocessed_fault_events (
        uuid STRING DEFAULT GENERATE_UUID(),
        container_physical_code STRING,
        device_code STRING,
        fault_event STRING,
        fault_acknowledgement_date_time TIMESTAMP,
        fault_end_date_time TIMESTAMP NOT NULL,
        fault_duration_seconds INT64,
        fault_repair_duration_seconds INT64,
        fault_start_date_time TIMESTAMP NOT NULL,
        fault_tag_reason_code STRING,
        item_category_code STRING,
        item_code STRING DEFAULT NULL,
        item_sku STRING,
        location_code STRING,
        module_code STRING,
        operator_code STRING,
        physical_device_code STRING,
        plc_code STRING,
        reason_name STRING,
        record_timestamp TIMESTAMP,
        fault_code STRING,
        condition_name STRING,
        event_category STRING,
        event_type STRING,
        quality STRING,
        node STRING,
        subsystem_category STRING,
        subsystem_code STRING,
        status_category_code STRING DEFAULT '',
        classification STRING DEFAULT '',
        deviceStatus STRING DEFAULT '',
        deviceType STRING DEFAULT '',
        statusId STRING DEFAULT '',
        tenant STRING,
        facility STRING,
        source_system STRING
    );

CREATE TEMP TABLE
    temp_fault_events (
        container_physical_code STRING,
        device_code STRING,
        fault_event STRING,
        fault_acknowledgement_date_time TIMESTAMP,
        fault_end_date_time TIMESTAMP NOT NULL,
        fault_duration_seconds INT64,
        fault_repair_duration_seconds INT64,
        fault_start_date_time TIMESTAMP NOT NULL,
        fault_tag_reason_code STRING,
        item_category_code STRING,
        item_code STRING DEFAULT NULL,
        item_sku STRING,
        location_code STRING,
        module_code STRING,
        module_name STRING,
        work_area_code STRING,
        work_area_name STRING,
        bay STRING,
        location_name STRING,
        operator_code STRING,
        physical_device_code STRING,
        plc_code STRING,
        reason_name STRING,
        record_timestamp TIMESTAMP,
        fault_code STRING,
        subsystem_category STRING,
        subsystem_code STRING,
        status_category_code STRING DEFAULT '',
        location_uuid STRING DEFAULT '',
        module_uuid STRING DEFAULT '',
        work_area_uuid STRING DEFAULT '',
        subsystem_uuid STRING DEFAULT '',
        operator_uuid STRING DEFAULT '',
        item_uuid STRING DEFAULT '',
        device_uuid STRING DEFAULT '',
        physical_device_uuid STRING DEFAULT '',
        status_uuid STRING DEFAULT '',
        reason_uuid STRING DEFAULT '',
        tenant STRING,
        facility STRING,
        source_system STRING,
        condition_name STRING,
        event_category STRING,
        event_type STRING,
        quality STRING,
        node STRING,
        classification STRING DEFAULT '',
        deviceStatus STRING DEFAULT '',
        deviceType STRING DEFAULT '',
        statusId STRING DEFAULT ''        
    );

INSERT INTO
    preprocessed_fault_events (
        device_code, -- FaultEventFact.deviceId
        fault_event, -- FaultEventFact.event
        record_timestamp, -- FaultEventFact.eventTime
        fault_code, -- FaultEventFact.faultId
        physical_device_code, -- FaultEventFact.deviceId
        subsystem_code, -- FaultEventFact.mtsType
        container_physical_code,
        status_category_code,
        fault_start_date_time,
        fault_end_date_time,
        tenant,
        facility,
        source_system
    )
WITH
    extracted_fault_events AS (
        SELECT
            JSON_VALUE(data, '$.deviceId') AS device_code,
            JSON_VALUE(data, '$.event') AS fault_event,
            JSON_VALUE(data, '$.eventTime') AS record_timestamp,
            JSON_VALUE(data, '$.faultId') AS fault_code,
            JSON_VALUE(data, '$.deviceId') AS physical_device_code,
            JSON_VALUE(data, '$.mtsType') AS subsystem_code,
            JSON_VALUE(data, '$.loadUnitId') AS container_physical_code,
            JSON_VALUE(data, '$.availabilityStatus') AS status_category_code,
            facility,
            source_system,
        FROM
            `${tenant_id}_landing.FaultEventFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    normalized_fault_events AS (
        SELECT
            device_code AS device_code,
            fault_event AS fault_event,
            `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp (record_timestamp, NULL) AS record_timestamp,
            fault_code AS fault_code,
            physical_device_code AS physical_device_code,
            subsystem_code AS subsystem_code,
            container_physical_code AS container_physical_code,
            status_category_code AS status_category_code,
            `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp (record_timestamp, NULL) AS fault_start_date_time,
            `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp (record_timestamp, NULL) AS fault_end_date_time,
            @ltarget_tenant AS tenant,
            facility,
            source_system,
        FROM
            extracted_fault_events
    )
SELECT
    device_code,
    fault_event,
    record_timestamp,
    fault_code,
    physical_device_code,
    subsystem_code,
    container_physical_code,
    status_category_code,
    fault_start_date_time,
    fault_end_date_time,
    tenant,
    facility,
    source_system
FROM
    normalized_fault_events;

INSERT INTO
    preprocessed_fault_events (
        container_physical_code,
        device_code,
        fault_acknowledgement_date_time,
        fault_end_date_time,
        fault_duration_seconds,
        fault_repair_duration_seconds,
        fault_start_date_time,
        fault_tag_reason_code,
        item_category_code,
        item_sku,
        location_code,
        module_code,
        operator_code,
        physical_device_code,
        plc_code,
        reason_name,
        record_timestamp,
        fault_code,
        subsystem_category,
        subsystem_code,
        tenant,
        facility,
        source_system
    )
WITH
    extracted_scada_fault AS (
        SELECT
            JSON_VALUE(data, '$.State') AS state,
            JSON_VALUE(data, '$.Date') AS fault_date,
            JSON_VALUE(data, '$.Time') AS fault_time,
            JSON_VALUE(data, '$."Duration[sec]"') AS fault_duration_timer,
            JSON_VALUE(data, '$.Number') AS fault_number,
            JSON_VALUE(data, '$.Subsystem') AS subsystem_code,
            JSON_VALUE(data, '$.PLC') AS plc_code,
            JSON_VALUE(data, '$.Component') AS device_code,
            JSON_VALUE(data, '$.Messagetext') AS messagetext,
            --JSON_VALUE(data, '$.Group') AS group,
            --JSON_VALUE(data, '$.Symbolic') AS symbolic,
            --JSON_VALUE(data, '$.Objecttype') AS objecttype,
            --JSON_VALUE(data, '$.Texttype') AS texttype,
            --JSON_VALUE(data, '$.Priority') AS priority,
            --JSON_VALUE(data, '$.Comment') AS comment,
            tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.SCADAFaultFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    preprocessed_scada_events AS (
        SELECT
            PARSE_TIMESTAMP(
                '%d.%m.%y %H:%M:%S',
                CONCAT(fault_date, '', fault_time)
            ) AS record_timestamp,
            CASE
                WHEN fault_duration_timer IS NULL
                OR fault_duration_timer = '0' THEN 0
                ELSE UNIX_SECONDS(PARSE_TIMESTAMP('%H:%M:%S', fault_duration_timer))
            END AS fault_duration_seconds,
            plc_code,
            CASE
                WHEN fault_number IS NULL THEN state
                ELSE fault_number
            END AS fault_code,
            REPLACE(LEFT(COALESCE(messagetext, ''), 255), r'\"', '') AS fault_message,
            UPPER(subsystem_code) AS subsystem_code,
            LEFT(device_code, 50) AS device_code,
            tenant,
            facility,
            source_system
        FROM
            extracted_scada_fault
        WHERE
            (
                state = '-'
                OR UPPER(state) = 'SERVER RESTART'
            )
            AND fault_date IS NOT NULL
            AND fault_time IS NOT NULL
    ),
    cleaned_scada_events AS (
        SELECT DISTINCT
            SAFE_CAST(NULL AS STRING) AS container_physical_code,
            ppe.device_code,
            SAFE_CAST(NULL AS TIMESTAMP) AS fault_acknowledgement_date_time,
            ppe.record_timestamp AS fault_end_date_time,
            ppe.fault_duration_seconds,
            0 AS fault_repair_duration_seconds,
            TIMESTAMP_SUB(
                ppe.record_timestamp,
                INTERVAL ppe.fault_duration_seconds SECOND
            ) AS fault_start_date_time,
            ppe.fault_message AS fault_tag_reason_code,
            SAFE_CAST(NULL AS STRING) AS item_category_code,
            SAFE_CAST(NULL AS STRING) AS item_sku,
            SAFE_CAST(NULL AS STRING) AS location_code,
            SAFE_CAST(NULL AS STRING) AS module_code,
            SAFE_CAST(NULL AS STRING) AS operator_code,
            SAFE_CAST(NULL AS STRING) AS physical_device_code,
            ppe.plc_code,
            SAFE_CAST(NULL AS STRING) AS reason_name,
            ppe.record_timestamp AS record_timestamp,
            ppe.fault_code,
            'SCADA' AS subsystem_category,
            ppe.subsystem_code,
            @ltarget_tenant AS tenant,
            ppe.facility AS facility,
            ppe.source_system AS source_system
        FROM
            preprocessed_scada_events AS ppe
    )
SELECT DISTINCT
    container_physical_code,
    device_code,
    fault_acknowledgement_date_time,
    fault_end_date_time,
    fault_duration_seconds,
    fault_repair_duration_seconds,
    fault_start_date_time,
    fault_tag_reason_code,
    item_category_code,
    item_sku,
    location_code,
    module_code,
    operator_code,
    physical_device_code,
    plc_code,
    reason_name,
    record_timestamp,
    fault_code,
    subsystem_category,
    subsystem_code,
    tenant,
    tenant,
    source_system
FROM
    cleaned_scada_events;

INSERT INTO
    preprocessed_fault_events (
        container_physical_code,
        device_code,
        fault_acknowledgement_date_time,
        fault_end_date_time,
        fault_duration_seconds,
        fault_repair_duration_seconds,
        fault_start_date_time,
        fault_tag_reason_code,
        item_category_code,
        item_sku,
        location_code,
        module_code,
        operator_code,
        physical_device_code,
        plc_code,
        reason_name,
        record_timestamp,
        fault_code,
        subsystem_category,
        subsystem_code,
        classification,
        deviceStatus,
        deviceType,
        statusId,
        tenant,
        facility,
        source_system
    )
WITH
    extracted_nok_events AS (
        SELECT
            JSON_VALUE(data, '$.tuId') AS container_physical_code,
            SAFE_CAST(JSON_VALUE(data, '$.startTime') AS TIMESTAMP) AS start_time,
            SAFE_CAST(JSON_VALUE(data, '$.endTime') AS TIMESTAMP) AS end_time,
            SAFE_CAST(
                JSON_VALUE(data, '$.AcknowledgeTime') AS TIMESTAMP
            ) AS acknowledge_time,
            SAFE_CAST(
                COALESCE(
                    JSON_VALUE(data, '$.faultRepairTime'),
                    JSON_VALUE(data, '$.faultRepairTime.value')
                ) AS INT64
            ) AS fault_repair_time,
            SAFE_CAST(
                COALESCE(
                    JSON_VALUE(data, '$.faultDuration'),
                    JSON_VALUE(data, '$.faultDuration.value')
                ) AS INT64
            ) AS faultDuration,
            JSON_VALUE(data, '$.faultCode') AS faultcode,
            JSON_VALUE(data, '$.deviceId') AS device_code,
            COALESCE(
                JSON_VALUE(data, '$.classification'),
                JSON_VALUE(data, '$.classification.value')
            ) AS classification,
            JSON_VALUE(data, '$.locationId') AS locationId,
            SAFE_CAST(JSON_VALUE(data, '$.relevance') AS INT64) AS relevance,
            SAFE_CAST(JSON_VALUE(data, '$.weightFactor') AS FLOAT64) AS weightFactor,
            JSON_VALUE(data, '$.UserComment') AS user_comment,
            COALESCE(
                JSON_VALUE(data, '$.mtsType'),
                JSON_VALUE(data, '$.mtsType.value')
            ) AS subsystem_code,
            JSON_VALUE(data, '$.hardwareIdentifier') AS physical_device_code,
            JSON_VALUE(data, '$.skuId') AS skuid,
            JSON_VALUE(data, '$.deviceStatus') AS deviceStatus,
            JSON_VALUE(data, '$.deviceType') AS deviceType,
            JSON_VALUE(data, '$.statusId') AS statusId,
            @ltarget_tenant AS tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.NokIntervalFact`
        WHERE
            ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
            and JSON_VALUE(data, '$.startTime') is not null
            and JSON_VALUE(data, '$.endTime') is not null
    ),
    preprocessed_nok_events AS (
        SELECT
            container_physical_code,
            COALESCE(start_time, end_time) AS fault_start_date_time,
            end_time AS fault_end_date_time,
            end_time AS record_timestamp,
            acknowledge_time AS fault_acknowledgement_date_time,
            SAFE_CAST(ROUND(DIV(fault_repair_time, 1000), 1) AS INT64) AS fault_repair_duration_seconds,
            COALESCE(
                SAFE_CAST(ROUND(DIV(faultduration, 1000), 1) AS INT64),
                DATETIME_DIFF(end_time, start_time, SECOND)
            ) AS fault_duration_seconds,
            CASE
                WHEN faultcode IS NULL
                OR faultcode = '0' THEN classification
                ELSE faultcode
            END AS fault_code,
            classification,
            device_code,
            locationid AS location_code,
            UPPER(subsystem_code) AS subsystem_code,
            physical_device_code,
            UPPER(
                CASE
                    WHEN COALESCE(skuid, 'EMPTY_SKU') = 'EMPTY_SKU' THEN NULL
                    ELSE skuid
                END
            ) AS item_sku,
            deviceStatus,
            deviceType,
            statusId,
            tenant,
            facility,
            source_system
        FROM
            extracted_nok_events
    ),
    cleaned_nok_events AS (
        SELECT
            ppe.container_physical_code,
            ppe.device_code,
            CASE
                WHEN UPPER(ppe.classification) = 'PLANNED_DOWNTIME'
                OR UPPER(ppe.classification) = 'PLANNED_MAINTENANCE' THEN ppe.fault_end_date_time
                WHEN UPPER(ppe.classification) = 'FAULT'
                AND ppe.fault_repair_duration_seconds < ppe.fault_duration_seconds THEN DATE_ADD(
                    ppe.fault_end_date_time,
                    INTERVAL ppe.fault_repair_duration_seconds * -1 SECOND
                )
                WHEN UPPER(ppe.classification) = 'FAULT'
                AND ppe.fault_repair_duration_seconds = ppe.fault_duration_seconds THEN ppe.fault_end_date_time
                ELSE ppe.fault_acknowledgement_date_time
            END AS fault_acknowledgement_date_time,
            ppe.fault_end_date_time,
            ppe.fault_duration_seconds,
            CASE
                WHEN UPPER(ppe.classification) = 'PLANNED_DOWNTIME'
                OR UPPER(ppe.classification) = 'PLANNED_MAINTENANCE' THEN 0
                WHEN ppe.fault_acknowledgement_date_time IS NULL THEN COALESCE(ppe.fault_repair_duration_seconds, 0)
                ELSE DATETIME_DIFF(
                    ppe.fault_end_date_time,
                    ppe.fault_acknowledgement_date_time,
                    SECOND
                )
            END AS fault_repair_duration_seconds,
            ppe.fault_start_date_time,
            SAFE_CAST(NULL AS STRING) AS fault_tag_reason_code,
            SAFE_CAST(NULL AS STRING) AS item_category_code,
            ppe.item_sku,
            ppe.location_code,
            SAFE_CAST(NULL AS STRING) AS module_code,
            SAFE_CAST(NULL AS STRING) AS operator_code,
            ppe.physical_device_code,
            SAFE_CAST(NULL AS STRING) AS plc_code,
            SAFE_CAST(NULL AS STRING) AS reason_name,
            ppe.record_timestamp,
            ppe.fault_code,
            'DEMATIC IQ' AS subsystem_category,
            ppe.subsystem_code,
            ppe.classification,
            ppe.deviceStatus,
            ppe.deviceType,
            ppe.statusId,
            ppe.tenant,
            ppe.facility AS facility,
            ppe.source_system AS source_system
        FROM
            preprocessed_nok_events AS ppe
    )
SELECT DISTINCT
    container_physical_code,
    device_code,
    fault_acknowledgement_date_time,
    fault_end_date_time,
    fault_duration_seconds,
    fault_repair_duration_seconds,
    fault_start_date_time,
    fault_tag_reason_code,
    item_category_code,
    item_sku,
    location_code,
    module_code,
    operator_code,
    physical_device_code,
    plc_code,
    reason_name,
    record_timestamp,
    fault_code,
    subsystem_category,
    subsystem_code,
    classification,
    deviceStatus,
    deviceType,
    statusId,
    tenant,
    facility,
    source_system
FROM
    cleaned_nok_events;

INSERT INTO
    preprocessed_fault_events (
        container_physical_code,
        device_code,
        fault_acknowledgement_date_time,
        fault_end_date_time,
        fault_duration_seconds,
        fault_repair_duration_seconds,
        fault_start_date_time,
        fault_tag_reason_code,
        item_category_code,
        item_sku,
        location_code,
        module_code,
        operator_code,
        physical_device_code,
        plc_code,
        reason_name,
        record_timestamp,
        fault_code,
        condition_name,
        event_category,
        event_type,
        quality,
        node,
        subsystem_category,
        subsystem_code,
        tenant,
        facility,
        source_system
    )
WITH
    extracted_viz_events AS (
        SELECT
            SAFE_CAST(JSON_VALUE(data, '$.ChangeMask') AS STRING) AS changemask,
            SAFE_CAST(JSON_VALUE(data, '$.ActiveTime') AS TIMESTAMP) AS fault_start_date_time,
            /*
            
            SAFE_CAST(
            JSON_VALUE(data, '$.Severity') AS FLOAT64
            ) AS Severity,
            JSON_VALUE(data, '$.ActorID') AS ActorID,
            JSON_VALUE(data, '$.AckComment') AS AckComment,
            SAFE_CAST(
            JSON_VALUE(data, '$.CV') AS INT64
            ) AS CV,
             */
            JSON_VALUE(data, '$.OBJ_NAME') AS plc_code,
            JSON_VALUE(data, '$.Area') AS area,
            JSON_VALUE(data, '$.Info') AS device_code,
            JSON_VALUE(data, '$.FLT_CODE') AS fault_code,
            --JSON_VALUE(data, '$.FLT_DESC') AS FLT_DESC,
            JSON_VALUE(data, '$.TUID1') AS container_physical_code,
            JSON_VALUE(data, '$.TUID2') AS container_physical_code2,
            /*
            SAFE_CAST(
            JSON_VALUE(data, '$.XY_POS') AS FLOAT64
            ) AS XY_POS,
            SAFE_CAST(
            JSON_VALUE(data, '$.XY_CMD') AS FLOAT64
            ) AS XY_CMD,
            SAFE_CAST(
            JSON_VALUE(data, '$.LZ_POS') AS FLOAT64
            ) AS LZ_POS,
            SAFE_CAST(
            JSON_VALUE(data, '$.LZ_CMD') AS FLOAT64
            ) AS LZ_CMD,
            
            SAFE_CAST(JSON_VALUE(data, '$._TimeUTC') AS TIMESTAMP)
            AS _TimeUTC,
             */
            SAFE_CAST(JSON_VALUE(data, '$.EventTime') AS TIMESTAMP) AS eventtime,
            SAFE_CAST(JSON_VALUE(data, '$.ms') AS INT64) AS ms,
            JSON_VALUE(data, '$.Tag') AS fault_tag_reason_code,
            /*
            JSON_VALUE(data, '$.Acked') AS Acked,
            JSON_VALUE(data, '$.ConditionActive') AS ConditionActive,
             */
            JSON_VALUE(data, '$.ConditionName') AS condition_name,
            JSON_VALUE(data, '$.EventCategory') AS event_category,
            JSON_VALUE(data, '$.EventType') AS event_type,
            JSON_VALUE(data, '$.Quality') AS quality,
            JSON_VALUE(data, '$.Node') AS node,
            JSON_VALUE(data, '$.Message') AS reason_name,
            @ltarget_tenant AS tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.viz_EventLogFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    preprocessed_viz_events AS (
        SELECT
            container_physical_code,
            container_physical_code2,
            device_code,
            DATETIME_ADD(eventtime, INTERVAL COALESCE(ms, 0) MILLISECOND) AS fault_end_date_time,
            fault_start_date_time,
            fault_tag_reason_code,
            plc_code,
            reason_name,
            fault_code,
            condition_name,
            event_category,
            event_type,
            quality,
            node,
            CASE
                WHEN STRPOS(area, '\\') > 0 THEN SPLIT(SUBSTR(area, STRPOS(area, '\\') + 1), ';') [SAFE_OFFSET(0)]
                ELSE SAFE_CAST(NULL AS STRING)
            END AS location_code,
            SPLIT(area, '\\') [SAFE_OFFSET(0)] AS module_code,
            CASE
                WHEN area IS NULL THEN SAFE_CAST(NULL AS STRING)
                WHEN LEFT(UPPER(area), 3) = 'DMS' THEN 'MULTISHUTTLE'
                WHEN STRPOS(UPPER(area), 'SORTER') > 0 THEN 'SORTER'
                WHEN STRPOS(UPPER(area), 'CROSSBELT') > 0 THEN 'SORTER'
                ELSE 'OTHER'
            END AS subsystem_code,
            tenant,
            facility,
            source_system
        FROM
            extracted_viz_events
        WHERE
            changemask = '1'
    ),
    deduplicated_viz_events AS (
        SELECT DISTINCT
            ppe.container_physical_code,
            ppe.device_code,
            grp.fault_end_date_time,
            ppe.fault_start_date_time,
            grp.fault_tag_reason_code,
            ppe.plc_code,
            ppe.reason_name,
            ppe.fault_code,
            ppe.condition_name,
            ppe.event_category,
            ppe.event_type,
            ppe.quality,
            ppe.node,
            ppe.location_code,
            ppe.module_code,
            ppe.subsystem_code,
            ppe.tenant AS tenant,
            ppe.facility AS facility,
            ppe.source_system AS source_system
        FROM
            preprocessed_viz_events AS ppe
            INNER JOIN (
                SELECT
                    preprocessed_viz_events.fault_start_date_time,
                    MAX(preprocessed_viz_events.fault_end_date_time) AS fault_end_date_time,
                    preprocessed_viz_events.fault_tag_reason_code
                FROM
                    preprocessed_viz_events
                GROUP BY
                    1,
                    3
            ) AS grp ON ppe.fault_start_date_time = grp.fault_start_date_time
            AND ppe.fault_tag_reason_code = grp.fault_tag_reason_code
    ),
    cleaned_viz_events AS (
        SELECT
            container_physical_code,
            LEFT(device_code, 50) AS device_code,
            SAFE_CAST(NULL AS TIMESTAMP) AS fault_acknowledgement_date_time,
            fault_end_date_time,
            DATETIME_DIFF(
                fault_end_date_time,
                fault_start_date_time,
                SECOND
            ) AS fault_duration_seconds,
            NULL AS fault_repair_duration_seconds,
            fault_start_date_time,
            fault_tag_reason_code,
            SAFE_CAST(NULL AS STRING) AS item_category_code,
            SAFE_CAST(NULL AS STRING) AS item_sku,
            LEFT(location_code, 50) AS location_code,
            LEFT(module_code, 50) AS module_code,
            SAFE_CAST(NULL AS STRING) AS operator_code,
            SAFE_CAST(NULL AS STRING) AS physical_device_code,
            plc_code,
            reason_name,
            fault_end_date_time AS record_timestamp,
            fault_code,
            condition_name,
            event_category,
            event_type,
            quality,
            node,
            'VISUALIZATION' AS subsystem_category,
            UPPER(LEFT(subsystem_code, 50)) AS subsystem_code,
            tenant,
            facility,
            source_system
        FROM
            deduplicated_viz_events
    )
SELECT DISTINCT
    container_physical_code,
    device_code,
    fault_acknowledgement_date_time,
    fault_end_date_time,
    fault_duration_seconds,
    fault_repair_duration_seconds,
    fault_start_date_time,
    fault_tag_reason_code,
    item_category_code,
    item_sku,
    location_code,
    module_code,
    operator_code,
    physical_device_code,
    plc_code,
    reason_name,
    record_timestamp,
    fault_code,
    condition_name,
    event_category,
    event_type,
    quality,
    node,
    subsystem_category,
    subsystem_code,
    tenant,
    facility,
    source_system
FROM
    cleaned_viz_events;

INSERT INTO
    preprocessed_fault_events (
        container_physical_code,
        device_code,
        fault_acknowledgement_date_time,
        fault_end_date_time,
        fault_duration_seconds,
        fault_repair_duration_seconds,
        fault_start_date_time,
        fault_tag_reason_code,
        item_category_code,
        item_sku,
        location_code,
        module_code,
        operator_code,
        physical_device_code,
        plc_code,
        reason_name,
        record_timestamp,
        fault_code,
        subsystem_category,
        subsystem_code,
        tenant,
        facility,
        source_system
    )
WITH
    extracted_fct_conn_mov_fault_data AS (
        SELECT
            SAFE_CAST(JSON_VALUE(data, '$.endTime') AS TIMESTAMP) AS fault_end_date_time,
            COALESCE(
                JSON_VALUE(data, '$.loadUnitId'),
                JSON_VALUE(data, '$.loadUnitId.value')
            ) AS container_physical_code,
            JSON_VALUE(data, '$.shuttleId') AS shuttleid,
            JSON_VALUE(data, '$.liftId') AS liftid,
            SAFE_CAST(JSON_VALUE(data, '$.duration') AS INT64) AS duration,
            SAFE_CAST(JSON_VALUE(data, '$.startTime') AS TIMESTAMP) AS fault_start_date_time,
            JSON_VALUE(data, '$.destinationLocationId') AS location_code,
            JSON_VALUE(data, '$.hardwareIdentifier') AS physical_device_code,
            JSON_VALUE(data, '$.skuId') AS item_sku,
            COALESCE(
                JSON_VALUE(data, '$.eventCode'),
                JSON_VALUE(data, '$.eventCode.value')
            ) AS fault_code,
            'CONVEYOR' AS Subsystem_Code,
            facility AS conn_mov_facility,
            source_system AS conn_mov_source_system
        FROM
            `${tenant_id}_landing.ConnectionMovementFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    extracted_fct_ms_move_fault_data AS (
        SELECT
            SAFE_CAST(JSON_VALUE(data, '$.endTime') AS TIMESTAMP) AS fault_end_date_time,
            COALESCE(
                JSON_VALUE(data, '$.loadUnitId'),
                JSON_VALUE(data, '$.loadUnitId.value')
            ) AS container_physical_code,
            JSON_VALUE(data, '$.shuttleId') AS shuttleid,
            JSON_VALUE(data, '$.liftId') AS liftid,
            SAFE_CAST(JSON_VALUE(data, '$.duration') AS INT64) AS duration,
            SAFE_CAST(JSON_VALUE(data, '$.startTime') AS TIMESTAMP) AS fault_start_date_time,
            JSON_VALUE(data, '$.destinationLocationId') AS location_code,
            JSON_VALUE(data, '$.hardwareIdentifier') AS physical_device_code,
            JSON_VALUE(data, '$.skuId') AS item_sku,
            COALESCE(
                JSON_VALUE(data, '$.eventCode'),
                JSON_VALUE(data, '$.eventCode.value')
            ) AS fault_code,
            'MULTISHUTTLE' AS Subsystem_Code,
            facility AS ms_move_facility,
            source_system AS ms_move_source_system
        FROM
            `${tenant_id}_landing.MultishuttleMovementFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    extracted_fct_vehicle_move_fault_data AS (
        SELECT
            SAFE_CAST(JSON_VALUE(data, '$.endTime') AS TIMESTAMP) AS fault_end_date_time,
            COALESCE(
                JSON_VALUE(data, '$.loadUnitId'),
                JSON_VALUE(data, '$.loadUnitId.value')
            ) AS container_physical_code,
            JSON_VALUE(data, '$.shuttleId') AS shuttleid,
            JSON_VALUE(data, '$.liftId') AS liftid,
            SAFE_CAST(JSON_VALUE(data, '$.duration') AS INT64) AS duration,
            SAFE_CAST(JSON_VALUE(data, '$.startTime') AS TIMESTAMP) AS fault_start_date_time,
            JSON_VALUE(data, '$.destinationLocationId') AS location_code,
            JSON_VALUE(data, '$.hardwareIdentifier') AS physical_device_code,
            JSON_VALUE(data, '$.skuId') AS item_sku,
            COALESCE(
                JSON_VALUE(data, '$.eventCode'),
                JSON_VALUE(data, '$.eventCode.value')
            ) AS fault_code,
            'STACKERCRANE' AS Subsystem_Code,
            facility AS vehicle_move_facility,
            source_system AS vehicle_move_source_system
        FROM
            `${tenant_id}_landing.VehicleMovementFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    extracted_fct_crane_move_fault_data AS (
        SELECT
            SAFE_CAST(JSON_VALUE(data, '$.endTime') AS TIMESTAMP) AS fault_end_date_time,
            COALESCE(
                JSON_VALUE(data, '$.loadUnitId'),
                JSON_VALUE(data, '$.loadUnitId.value')
            ) AS container_physical_code,
            JSON_VALUE(data, '$.shuttleId') AS shuttleid,
            JSON_VALUE(data, '$.liftId') AS liftid,
            SAFE_CAST(JSON_VALUE(data, '$.duration') AS INT64) AS duration,
            SAFE_CAST(JSON_VALUE(data, '$.startTime') AS TIMESTAMP) AS fault_start_date_time,
            JSON_VALUE(data, '$.destinationLocationId') AS location_code,
            JSON_VALUE(data, '$.hardwareIdentifier') AS physical_device_code,
            JSON_VALUE(data, '$.skuId') AS item_sku,
            COALESCE(
                JSON_VALUE(data, '$.eventCode'),
                JSON_VALUE(data, '$.eventCode.value')
            ) AS fault_code,
            'STACKERCRANE' AS Subsystem_Code,
            facility AS crane_move_facility,
            source_system AS crane_move_source_system
        FROM
            `${tenant_id}_landing.StackercraneMovementFact`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    cleaned_movement_faults AS (
        SELECT
            *,
            COALESCE(shuttleid, liftid) AS device_code,
            SAFE_CAST(
                COALESCE(
                    TIMESTAMP_DIFF(
                        fault_end_date_time,
                        fault_start_date_time,
                        SECOND
                    ),
                    ROUND(duration / 1000, 1)
                ) AS INT64
            ) AS fault_duration_seconds,
            COALESCE(
                ARRAY_REVERSE(SPLIT(location_Code, '.')) [SAFE_OFFSET(2)],
                ''
            ) AS module_code,
            Fault_End_Date_Time AS record_timestamp,
            SAFE_CAST(NULL AS STRING) AS plc_code,
            SAFE_CAST(NULL AS STRING) AS reason_name,
            'DEMATIC IQ' AS subsystem_category,
            SAFE_CAST(NULL AS STRING) AS item_category_code,
            SAFE_CAST(NULL AS STRING) AS fault_tag_reason_code,
            SAFE_CAST(NULL AS INT64) AS fault_repair_duration_seconds,
            SAFE_CAST(NULL AS TIMESTAMP) AS fault_acknowledgement_date_time,
            SAFE_CAST(NULL AS STRING) AS operator_code,
            conn_mov_facility AS facility,
            conn_mov_source_system AS source_system
        FROM
            (
                SELECT DISTINCT
                    *
                FROM
                    extracted_fct_conn_mov_fault_data
                WHERE
                    fault_code <> 'OK'
                UNION DISTINCT
                SELECT DISTINCT
                    *
                FROM
                    extracted_fct_crane_move_fault_data
                WHERE
                    fault_code <> 'OK'
                UNION DISTINCT
                SELECT DISTINCT
                    *
                FROM
                    extracted_fct_vehicle_move_fault_data
                WHERE
                    fault_code <> 'OK'
                UNION DISTINCT
                SELECT DISTINCT
                    *
                FROM
                    extracted_fct_ms_move_fault_data
                WHERE
                    fault_code <> 'OK'
            ) AS all_events
    )
SELECT DISTINCT
    container_physical_code,
    device_code,
    fault_acknowledgement_date_time,
    fault_end_date_time,
    fault_duration_seconds,
    fault_repair_duration_seconds,
    fault_start_date_time,
    fault_tag_reason_code,
    item_category_code,
    item_sku,
    location_code,
    module_code,
    operator_code,
    physical_device_code,
    plc_code,
    reason_name,
    record_timestamp,
    fault_code,
    subsystem_category,
    subsystem_code,
    @ltarget_tenant AS tenant,
    cleaned_movement_faults.facility AS facility,
    cleaned_movement_faults.source_system AS source_system
FROM
    cleaned_movement_faults;

-- lookup item_code based on device code 
MERGE INTO
    preprocessed_fault_events fe USING (
        WITH
            empty_item_events AS (
                SELECT DISTINCT
                    uuid,
                    fault_start_date_time,
                    fault_end_date_time,
                    device_code,
                    condition_name,
                    event_category,
                    event_type,
                    quality,
                    node,
                    facility,
                    source_system
                FROM
                    preprocessed_fault_events
                WHERE
                    item_sku IS NULL
                    AND subsystem_code = 'MULTISHUTTLE'
            ),
            item_lookup_on_device AS (
                SELECT DISTINCT
                    uuid,
                    fault_start_date_time,
                    fault_end_date_time,
                    eie.device_code,
                    idt.move_start_date_time,
                    idt.item_code,
                    idt.item_sku,
                    eie.facility AS facility,
                    eie.source_system AS source_system,
                    ROW_NUMBER() OVER (
                        PARTITION BY
                            eie.fault_end_date_time,
                            eie.device_code
                        ORDER BY
                            idt.move_start_date_time DESC
                    ) AS rp
                FROM
                    empty_item_events AS eie
                    LEFT OUTER JOIN (
                        SELECT DISTINCT
                            DATE_ADD(record_timestamp, INTERVAL 3 SECOND) AS move_end_date_time,
                            move_start_date_time,
                            item_code,
                            item_sku,
                            device_code,
                            m.facility AS facility,
                            m.source_system AS source_system
                        FROM
                            `${tenant_id}_oa_curated.fct_movement` AS m
                            INNER JOIN `${tenant_id}_oa_curated.dim_item` AS it ON m.item_uuid = it.item_uuid
                            INNER JOIN `${tenant_id}_oa_curated.dim_device` AS d ON m.device_uuid = d.device_uuid
                            INNER JOIN `${tenant_id}_oa_curated.dim_subsystem` AS s ON d.subsystem_uuid = s.subsystem_uuid
                        WHERE
                            s.subsystem_code = 'MULTISHUTTLE'
                            AND s.tenant = @ltarget_tenant
                            AND m.record_timestamp >= @start_query_date
                            AND m.record_timestamp <= @end_query_date
                    ) AS idt ON idt.device_code = eie.device_code
                    AND eie.device_code IS NOT NULL
                    AND eie.fault_start_date_time >= idt.move_start_date_time
                    AND eie.fault_start_date_time <= idt.move_end_date_time
            )
        SELECT
            * EXCEPT (rp)
        FROM
            item_lookup_on_device
        WHERE
            item_code IS NULL
            AND rp = 1
    ) ti ON ti.uuid = fe.uuid
WHEN MATCHED THEN
UPDATE SET
    item_code = ti.item_code,
    item_sku = ti.item_sku;

-- lookup item_code based on container code
MERGE INTO
    preprocessed_fault_events fe USING (
        WITH
            empty_item_events AS (
                SELECT DISTINCT
                    uuid,
                    fault_start_date_time,
                    fault_end_date_time,
                    container_physical_code
                FROM
                    preprocessed_fault_events
                WHERE
                    item_sku IS NULL
                    AND subsystem_code = 'MULTISHUTTLE'
            )
        SELECT DISTINCT
            uuid,
            fault_start_date_time,
            fault_end_date_time,
            eie.container_physical_code,
            ict.move_start_date_time,
            ict.item_code,
            ict.item_sku,
            ROW_NUMBER() OVER (
                PARTITION BY
                    eie.fault_end_date_time,
                    eie.container_physical_code
                ORDER BY
                    ict.move_start_date_time DESC
            ) AS rp
        FROM
            empty_item_events AS eie
            LEFT OUTER JOIN (
                SELECT DISTINCT
                    DATE_ADD(record_timestamp, INTERVAL 3 SECOND) AS move_end_date_time,
                    move_start_date_time,
                    item_code,
                    item_sku,
                    load_unit_code AS container_physical_code
                FROM
                    `${tenant_id}_oa_curated.fct_movement` AS m
                    INNER JOIN `${tenant_id}_oa_curated.dim_item` AS it ON m.item_uuid = it.item_uuid
                    INNER JOIN `${tenant_id}_oa_curated.dim_status` AS st ON m.status_uuid = st.status_uuid
                    INNER JOIN `${tenant_id}_oa_curated.dim_subsystem` AS s ON st.subsystem_uuid = s.subsystem_uuid
                WHERE
                    s.subsystem_code = 'MULTISHUTTLE'
                    AND s.tenant = @ltarget_tenant
                    AND m.record_timestamp >= @start_query_date
                    AND m.record_timestamp <= @end_query_date
            ) AS ict ON ict.container_physical_code = eie.container_physical_code
            AND eie.container_physical_code IS NOT NULL
            AND eie.fault_start_date_time >= ict.move_start_date_time
            AND eie.fault_start_date_time <= ict.move_end_date_time
    ) ti ON ti.uuid = fe.uuid
WHEN MATCHED THEN
UPDATE SET
    item_code = ti.item_code,
    item_sku = ti.item_sku;

INSERT INTO
    temp_fault_events (
        container_physical_code,
        device_code,
        fault_event,
        fault_acknowledgement_date_time,
        fault_end_date_time,
        fault_duration_seconds,
        fault_repair_duration_seconds,
        fault_start_date_time,
        fault_tag_reason_code,
        item_category_code,
        item_sku,
        item_code,
        location_code,
        work_area_code,
        work_area_name,
        module_code,
        module_name,
        bay,
        location_name,
        operator_code,
        physical_device_code,
        plc_code,
        reason_name,
        record_timestamp,
        fault_code,
        condition_name,
        event_category,
        event_type,
        quality,
        node,
        subsystem_category,
        subsystem_code,
        classification,
        deviceStatus,
        deviceType,
        statusId,
        tenant,
        facility,
        source_system
    )
WITH
    deduplicated_fct_fault AS (
        SELECT DISTINCT
            uuid,
            UPPER(COALESCE(container_physical_code, '')) AS container_physical_code,
            UPPER(COALESCE(device_code, '')) AS device_code,
            fault_event,
            fault_acknowledgement_date_time,
            fault_end_date_time,
            fault_duration_seconds,
            fault_repair_duration_seconds,
            fault_start_date_time,
            UPPER(COALESCE(fault_tag_reason_code, '')) AS fault_tag_reason_code,
            UPPER(COALESCE(item_category_code, '')) AS item_category_code,
            IF(
                LENGTH(TRIM(item_sku)) > 0,
                item_sku,
                SAFE_CAST(NULL AS STRING)
            ) AS item_sku,
            IFNULL(
                item_code,
                `${tenant_id}_oa_curated.Item_Code_Generator` (item_sku, NULL, NULL, NULL)
            ) AS item_code,
            UPPER(COALESCE(location_code, '')) AS location_code,
            UPPER(
                COALESCE(
                    module_code,
                    CASE
                        WHEN STRPOS(location_code, '.') > 0 THEN UPPER(
                            SUBSTR(
                                location_code,
                                0,
                                INSTR(location_code, '.', 1, 3) -1
                            )
                        )
                        ELSE ''
                    END
                )
            ) AS module_code,
            UPPER(
                COALESCE(
                    module_code,
                    CASE
                        WHEN STRPOS(location_code, '.') > 0 THEN SPLIT(location_code, '.') [SAFE_OFFSET(2)]
                        ELSE NULL
                    END
                )
            ) AS module_name,
            CASE
                WHEN STRPOS(location_code, '.') > 0 THEN UPPER(
                    SUBSTR(
                        location_code,
                        0,
                        INSTR(location_code, '.', 1, 2) -1
                    )
                )
                ELSE ''
            END AS work_area_code,
            CASE
                WHEN STRPOS(location_code, '.') > 0 THEN UPPER(SPLIT(location_code, '.') [SAFE_OFFSET(1)])
                ELSE NULL
            END AS work_area_name,
            CASE
                WHEN STRPOS(location_code, '.') > 0 THEN UPPER(SPLIT(location_code, '.') [SAFE_OFFSET(3)])
                ELSE NULL
            END AS bay,
            `${tenant_id}_oa_curated.Location_Name_Generator` (location_code) AS location_name,
            UPPER(COALESCE(operator_code, '')) AS operator_code,
            UPPER(COALESCE(physical_device_code, '')) AS physical_device_code,
            UPPER(COALESCE(plc_code, '')) AS plc_code,
            reason_name,
            record_timestamp,
            UPPER(COALESCE(fault_code, '')) AS fault_code,
            COALESCE(subsystem_category, '') AS subsystem_category,
            UPPER(COALESCE(subsystem_code, '')) AS subsystem_code,
            condition_name,
            event_category,
            event_type,
            quality,
            node,
            classification,
            deviceStatus,
            deviceType,
            statusId,
            @ltarget_tenant,
            facility,
            source_system
        FROM
            preprocessed_fault_events
    )
SELECT DISTINCT
    container_physical_code,
    device_code,
    fault_event,
    fault_acknowledgement_date_time,
    fault_end_date_time,
    fault_duration_seconds,
    fault_repair_duration_seconds,
    fault_start_date_time,
    fault_tag_reason_code,
    item_category_code,
    item_sku,
    item_code,
    location_code,
    work_area_code,
    work_area_name,
    module_code,
    module_name,
    bay,
    location_name,
    operator_code,
    physical_device_code,
    plc_code,
    reason_name,
    record_timestamp,
    fault_code,
    condition_name,
    event_category,
    event_type,
    quality,
    node,
    subsystem_category,
    subsystem_code,
    classification,
    deviceStatus,
    deviceType,
    statusId,
    @ltarget_tenant AS tenant,
    facility,
    source_system
FROM
    deduplicated_fct_fault;

--DIM_Item
MERGE
    `${tenant_id}_oa_curated.dim_item` di USING (
        SELECT DISTINCT
            item_code,
            MAX(item_sku) AS item_sku,
            item_category_code,
            tenant,
            facility,
            source_system
        FROM
            temp_fault_events
        GROUP BY
            item_code,
            item_category_code,
            tenant,
            facility,
            source_system
    ) v ON di.item_code = v.item_code
    AND di.item_category_code = v.item_category_code
    AND di.tenant = @ltarget_tenant
    AND di.facility = v.facility
    AND di.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        item_uuid,
        item_code,
        item_category_code,
        item_sku,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [
                @ltarget_tenant,
                v.facility,
                v.source_system,
                v.item_code
            ]
        ),
        v.item_code,
        v.item_category_code,
        v.item_sku,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE temp_fault_events tt
SET
    item_uuid = d.item_uuid
FROM
    (
        SELECT DISTINCT
            item_uuid,
            item_sku,
            item_category_code,
            tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_oa_curated.dim_item`
    ) d
WHERE
    tt.item_code = d.item_sku
    AND tt.item_category_code = d.item_category_code
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_Reason
MERGE
    `${tenant_id}_oa_curated.dim_reason` dr USING (
        SELECT DISTINCT
            fault_tag_reason_code,
            MAX(reason_name) AS reason_name,
            tenant,
            facility,
            source_system
        FROM
            temp_fault_events
        GROUP BY
            fault_tag_reason_code,
            tenant,
            facility,
            source_system
    ) v ON dr.reason_code = v.fault_tag_reason_code
    AND dr.tenant = @ltarget_tenant
    AND dr.facility = v.facility
    AND dr.source_system = v.source_system
WHEN MATCHED
    AND (dr.reason_name <> v.reason_name) THEN
UPDATE SET
    dr.reason_name = COALESCE(v.reason_name, dr.reason_name),
    dr.etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
    (
        reason_uuid,
        reason_code,
        reason_name,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [
                v.tenant,
                v.facility,
                v.source_system,
                v.fault_tag_reason_code
            ]
        ),
        v.fault_tag_reason_code,
        v.reason_name,
        v.tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE temp_fault_events tt
SET
    reason_uuid = d.reason_uuid
FROM
    (
        SELECT DISTINCT
            reason_uuid,
            reason_code,
            tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_oa_curated.dim_reason`
    ) d
WHERE
    tt.fault_tag_reason_code = d.reason_code
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_Location
MERGE
    `${tenant_id}_oa_curated.dim_location` dl USING (
        SELECT DISTINCT
            location_code,
            MAX(location_name) AS location_name,
            MAX(bay) AS bay,
            facility,
            source_system
        FROM
            temp_fault_events
        GROUP BY
            location_code,
            facility,
            source_system
    ) v ON dl.location_code = v.location_code
    AND dl.tenant = @ltarget_tenant
    AND dl.facility = v.facility
    AND dl.source_system = v.source_system
WHEN MATCHED
    AND (
        dl.location_name <> v.location_name
        OR dl.bay <> v.bay
    ) THEN
UPDATE SET
    dl.location_name = COALESCE(v.location_name, dl.location_name),
    dl.bay = COALESCE(v.bay, dl.bay),
    etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
    (
        location_uuid,
        location_code,
        bay,
        location_name,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [
                @ltarget_tenant,
                v.facility,
                v.source_system,
                v.location_code
            ]
        ),
        v.location_code,
        v.bay,
        v.location_name,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE temp_fault_events tt
SET
    location_uuid = d.location_uuid
FROM
    (
        SELECT DISTINCT
            location_uuid,
            location_code,
            tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_oa_curated.dim_location`
    ) d
WHERE
    tt.location_code = d.location_code
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_MODULE
MERGE
    `${tenant_id}_oa_curated.dim_module` dm USING (
        SELECT DISTINCT
            module_code,
            MAX(module_name) AS module_name,
            facility,
            source_system
        FROM
            temp_fault_events
        GROUP BY
            module_code,
            facility,
            source_system
    ) v ON dm.module_code = v.module_code
    AND dm.tenant = @ltarget_tenant
    AND dm.facility = v.facility
    AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        module_uuid,
        module_code,
        module_name,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [
                @ltarget_tenant,
                v.facility,
                v.source_system,
                v.module_code
            ]
        ),
        v.module_code,
        v.module_name,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE temp_fault_events tt
SET
    module_uuid = d.module_uuid
FROM
    (
        SELECT DISTINCT
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_oa_curated.dim_module`
    ) d
WHERE
    d.module_code = tt.module_code
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_WORK_AREA
MERGE
    `${tenant_id}_oa_curated.dim_work_area` dwa USING (
        SELECT DISTINCT
            work_area_code,
            MAX(work_area_name) AS work_area_name,
            tenant,
            facility,
            source_system
        FROM
            temp_fault_events
        GROUP BY
            work_area_code,
            tenant,
            facility,
            source_system
    ) v ON dwa.work_area_code = v.work_area_code
    AND dwa.tenant = @ltarget_tenant
    AND dwa.facility = v.facility
    AND dwa.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        work_area_uuid,
        work_area_code,
        work_area_name,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [
                @ltarget_tenant,
                v.facility,
                v.source_system,
                v.work_area_code
            ]
        ),
        v.work_area_code,
        v.work_area_name,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE temp_fault_events tt
SET
    work_area_uuid = d.work_area_uuid
FROM
    (
        SELECT DISTINCT
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_oa_curated.dim_work_area`
    ) d
WHERE
    d.work_area_code = tt.work_area_code
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--DIM_Subsystem 
MERGE
    `${tenant_id}_oa_curated.dim_subsystem` ds USING (
        SELECT DISTINCT
            subsystem_code,
            subsystem_category,
            tenant,
            facility,
            source_system
        FROM
            temp_fault_events
        GROUP BY
            subsystem_code,
            subsystem_category,
            tenant,
            facility,
            source_system
    ) v ON ds.subsystem_code = v.subsystem_code
    AND ds.subsystem_category = v.subsystem_category
    AND ds.tenant = @ltarget_tenant
    AND ds.facility = v.facility
    AND ds.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        subsystem_uuid,
        subsystem_code,
        subsystem_category,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [
                @ltarget_tenant,
                v.facility,
                v.source_system,
                v.subsystem_code,
                v.subsystem_category
            ]
        ),
        v.subsystem_code,
        v.subsystem_category,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        @etl_batch_uuid
    );

UPDATE temp_fault_events tt
SET
    subsystem_uuid = d.subsystem_uuid
FROM
    (
        SELECT DISTINCT
            subsystem_uuid,
            subsystem_code,
            subsystem_category,
            @ltarget_tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_oa_curated.dim_subsystem`
    ) d
WHERE
    tt.subsystem_code = d.subsystem_code
    AND tt.subsystem_category = d.subsystem_category
    AND tt.tenant = @ltarget_tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- DIM_Operator
MERGE
    `${tenant_id}_oa_curated.dim_operator` op USING (
        SELECT DISTINCT
            operator_code,
            @ltarget_tenant,
            facility,
            source_system
        FROM
            temp_fault_events
        GROUP BY
            operator_code,
            tenant,
            facility,
            source_system
    ) pfe ON op.operator_code = pfe.operator_code
    AND op.tenant = @ltarget_tenant
    AND op.facility = pfe.facility
    AND op.source_system = pfe.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        operator_uuid,
        operator_code,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [
                @ltarget_tenant,
                pfe.facility,
                pfe.source_system,
                pfe.operator_code
            ]
        ),
        pfe.operator_code,
        @ltarget_tenant,
        pfe.facility,
        pfe.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE temp_fault_events tt
SET
    operator_uuid = d.operator_uuid
FROM
    (
        SELECT DISTINCT
            operator_uuid,
            operator_code,
            @ltarget_tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_oa_curated.dim_operator`
    ) d
WHERE
    tt.operator_code = d.operator_code
    AND tt.tenant = @ltarget_tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- TODO: status_category_code defines uniqueness as a incremental. At the moment, we do not have a way of identifying uniqueness without this status_category_code
/*
--DIM_Status
MERGE
`${tenant_id}_oa_curated.dim_status` ds USING (
SELECT DISTINCT
fault_code,
'' AS status_locale_code,
status_category_code AS status_category_code,
subsystem_uuid,
facility,
source_system
FROM
temp_fault_events
GROUP BY
fault_code,
status_category_code,
subsystem_uuid,
facility,
source_system
) v ON ds.status_code = v.fault_code
AND ds.status_locale_code = v.status_locale_code
AND ds.status_category_code = v.status_category_code
AND ds.subsystem_uuid = v.subsystem_uuid
AND ds.tenant = @ltarget_tenant
AND ds.facility = v.facility
AND ds.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
(
status_uuid,
status_code,
status_locale_code,
status_category_code,
subsystem_uuid,
tenant,
facility,
source_system,
etl_batch_id
)
VALUES
(
`${tenant_id}_oa_curated.Dimension_PK_Generator` (
[
@ltarget_tenant,
v.facility,
v.source_system,
v.fault_code,
v.subsystem_uuid
]
),
v.fault_code,
v.status_locale_code,
SAFE_CAST(v.status_category_code AS STRING),
v.subsystem_uuid,
@ltarget_tenant,
v.facility,
v.source_system,
@etl_batch_uuid
);

-- Update the original tmp_fault_events table with the new status_uuid from dim_status
UPDATE temp_fault_events tt
SET
status_uuid = d.status_uuid
FROM
(
SELECT DISTINCT
status_uuid,
subsystem_uuid,
status_code,
status_category_code,
tenant,
facility,
source_system
FROM
`${tenant_id}_oa_curated.dim_status`
) d
WHERE
tt.subsystem_uuid = d.subsystem_uuid
AND tt.fault_code = d.status_code
AND d.status_category_code = tt.status_category_code
AND d.tenant = @ltarget_tenant;
 */
--DIM_Device
MERGE
    `${tenant_id}_oa_curated.dim_device` dd USING (
        SELECT DISTINCT
            subsystem_uuid,
            device_code,
            tenant,
            facility,
            source_system
        FROM
            (
                SELECT
                    subsystem_uuid,
                    device_code,
                    tenant,
                    facility,
                    source_system
                FROM
                    temp_fault_events
                UNION DISTINCT
                SELECT
                    subsystem_uuid,
                    physical_device_code AS device_code,
                    tenant,
                    facility,
                    source_system
                FROM
                    temp_fault_events
            ) v
        GROUP BY
            subsystem_uuid,
            device_code,
            tenant,
            facility,
            source_system
    ) v ON dd.device_code = v.device_code
    AND dd.subsystem_uuid = v.subsystem_uuid
    AND dd.tenant = @ltarget_tenant
    AND dd.facility = v.facility
    AND dd.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        device_uuid,
        device_code,
        subsystem_uuid,
        tenant,
        facility,
        source_system,
        etl_batch_id,
        device_functional_type
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [
                @ltarget_tenant,
                v.facility,
                v.source_system,
                v.device_code,
                v.subsystem_uuid
            ]
        ),
        v.device_code,
        v.subsystem_uuid,
        @ltarget_tenant,
        v.facility,
        v.source_system,
        @etl_batch_uuid,
        'MULTISHUTTLE'
    );

UPDATE temp_fault_events tt
SET
    tt.device_uuid = d.device_uuid
FROM
    (
        SELECT DISTINCT
            device_uuid,
            subsystem_uuid,
            device_code,
            tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_oa_curated.dim_device`
    ) d
WHERE
    tt.subsystem_uuid = d.subsystem_uuid
    AND tt.device_code = d.device_code
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

--FCT_FAULT
MERGE INTO
    `${tenant_id}_oa_curated.fct_fault` fct USING (
        SELECT DISTINCT
            record_timestamp,
            @ltarget_tenant AS tenant,
            facility AS facility,
            source_system AS source_system,
            fault_start_date_time,
            device_uuid,
            physical_device_uuid,
            location_uuid,
            module_uuid,
            work_area_uuid,
            subsystem_uuid,
            '' AS status_uuid,
            item_uuid,
            operator_uuid,
            reason_uuid AS fault_tag_reason_uuid,
            container_physical_code,
            plc_code,
            MAX(fault_duration_seconds) AS fault_duration_seconds,
            MAX(fault_repair_duration_seconds) AS fault_repair_duration_seconds,
            MAX(fault_acknowledgement_date_time) AS fault_acknowledgement_date_time,
            @etl_batch_uuid AS etl_batch_id,
            fault_event,
            fault_code,
            condition_name,
            event_category,
            event_type,
            quality,
            node,
            device_code,
            fault_tag_reason_code,
            item_category_code,
            item_code,
            location_code,
            module_code,
            operator_code,
            physical_device_code,
            subsystem_code,
            status_category_code,
            classification,
            deviceStatus,
            deviceType,
            statusId
        FROM
            temp_fault_events
        GROUP BY
            record_timestamp,
            fault_start_date_time,
            device_uuid,
            physical_device_uuid,
            location_uuid,
            module_uuid,
            work_area_uuid,
            subsystem_uuid,
            item_uuid,
            operator_uuid,
            reason_uuid,
            container_physical_code,
            plc_code,
            facility,
            source_system,
            fault_event,
            fault_code,
            condition_name,
            event_category,
            event_type,
            quality,
            node,
            device_code,
            fault_tag_reason_code,
            item_category_code,
            item_code,
            location_code,
            module_code,
            operator_code,
            physical_device_code,
            subsystem_code,
            status_category_code,
            classification,
            deviceStatus,
            deviceType,
            statusId
    ) src ON fct.record_timestamp = src.record_timestamp
    AND fct.fault_start_date_time = src.fault_start_date_time
    AND fct.device_uuid = src.device_uuid
    AND fct.physical_device_uuid = src.physical_device_uuid
    AND fct.location_uuid = src.location_uuid
    AND fct.module_uuid = src.module_uuid
    AND fct.work_area_uuid = src.work_area_uuid
    AND fct.subsystem_uuid = src.subsystem_uuid
    AND fct.item_uuid = src.item_uuid
    AND fct.operator_uuid = src.operator_uuid
    AND fct.container_physical_code = src.container_physical_code
WHEN NOT MATCHED BY TARGET
    AND TIMESTAMP_TRUNC(record_timestamp, DAY) BETWEEN DATE_SUB(
        TIMESTAMP_TRUNC(@start_query_date, DAY),
        INTERVAL 1 DAY
    ) AND TIMESTAMP_TRUNC(@end_query_date, DAY)  THEN
INSERT
    (
        record_timestamp,
        tenant,
        facility,
        source_system,
        fault_start_date_time,
        device_uuid,
        physical_device_uuid,
        location_uuid,
        module_uuid,
        work_area_uuid,
        subsystem_uuid,
        status_uuid,
        item_uuid,
        operator_uuid,
        fault_tag_reason_uuid,
        container_physical_code,
        plc_code,
        fault_duration_seconds,
        fault_repair_duration_seconds,
        fault_acknowledgement_date_time,
        etl_batch_id,
        fault_event,
        fault_code,
        condition_name,
        event_category,
        event_type,
        quality,
        node,
        device_code,
        fault_tag_reason_code,
        item_category_code,
        item_code,
        location_code,
        module_code,
        operator_code,
        physical_device_code,
        subsystem_code,
        status_category_code,
        classification,
        deviceStatus,
        deviceType,
        statusId
    )
VALUES
    (
        record_timestamp,
        tenant,
        facility,
        source_system,
        fault_start_date_time,
        device_uuid,
        physical_device_uuid,
        location_uuid,
        module_uuid,
        work_area_uuid,
        subsystem_uuid,
        status_uuid,
        item_uuid,
        operator_uuid,
        fault_tag_reason_uuid,
        container_physical_code,
        plc_code,
        fault_duration_seconds,
        fault_repair_duration_seconds,
        fault_acknowledgement_date_time,
        etl_batch_id,
        fault_event,
        fault_code,
        condition_name,
        event_category,
        event_type,
        quality,
        node,
        device_code,
        fault_tag_reason_code,
        item_category_code,
        item_code,
        location_code,
        module_code,
        operator_code,
        physical_device_code,
        subsystem_code,
        status_category_code,
        classification,
        deviceStatus,
        deviceType,
        statusId
    );