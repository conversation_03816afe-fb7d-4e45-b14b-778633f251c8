-- declare ltarget_tenant string default 'ict_development';
-- declare etl_batch_uuid string default '2020-12-12';
-- declare start_query_date timestamp default timestamp('2023-09-27 00:00:00');
-- declare end_query_date timestamp default timestamp('2025-09-27 02:00:00');
 
CREATE TEMP TABLE tmp_dim_location (
  location_uuid STRING NOT NULL,
  location_type_code STRING,
  location_code STRING NOT NULL,
  location_name STRING,
  relative_x_axis STRING,
  relative_y_axis STRING,
  relative_z_axis STRING,
  side_position STRING,
  slot_position STRING,
  location_group STRING,
  bay STRING,
  weight_limit INT64,
  location_size STRING,
  hazardous_material_allowed INT64,
  bay_width INT64,
  effective_begin_date TIMESTAMP,
  effective_end_date TIMESTAMP,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  active_rec_ind INT64,
  etl_batch_id STRING
);

INSERT INTO tmp_dim_location (
  location_uuid,
  location_type_code,
  location_code,
  location_name,
  relative_x_axis,
  relative_y_axis,
  relative_z_axis,
  side_position,
  slot_position,
  location_group,
  bay,
  weight_limit,
  location_size,
  hazardous_material_allowed,
  bay_width,
  effective_begin_date,
  effective_end_date,
  tenant,
  facility,
  source_system,
  active_rec_ind,
  etl_batch_id
)
WITH raw_data AS (
  SELECT
    JSON_VALUE(data, '$.areHazardousMaterialsAllowed') AS areHazardousMaterialsAllowed,
    JSON_VALUE(data, '$.area') AS area,
    JSON_VALUE(data, '$.coordinate') AS coordinate,
    JSON_VALUE(data, '$.dimensionModificationTime') AS dimensionModificationTime,
    JSON_VALUE(data, '$.displayID') AS displayId,
    JSON_VALUE(data, '$.group') AS `group`,
    JSON_VALUE(data, '$.id') AS id,
    JSON_VALUE(data, '$.primaryKey') AS primaryKey,
    JSON_VALUE(data, '$.sequenceNumber') AS sequenceNumber,
    JSON_VALUE(data, '$.side') AS side,
    JSON_VALUE(data, '$.subGroup') AS subGroup,
    JSON_VALUE(data, '$.usageType') AS usageType,
    JSON_VALUE(data, '$.velocityClassification') AS velocityClassification,
    JSON_VALUE(data, '$.weightLimit') AS weightLimit,
    JSON_VALUE(data, '$.x') AS x,
    JSON_VALUE(data, '$.y') AS y,
    tenant,
    facility,
    source_system,
    @etl_batch_uuid AS etl_batch_id
  FROM `${tenant_id}_landing.LocationDimension`
  WHERE
    tenant = @ltarget_tenant
    AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
),
processedData AS (
  SELECT
    SAFE_CAST(areHazardousMaterialsAllowed AS INT64) AS areHazardousMaterialsAllowed,
    area,
    coordinate,
    `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(dimensionModificationTime, NULL) AS record_timestamp,
    displayId,
    `group`,
    id,
    primaryKey,
    sequenceNumber,
    side,
    subGroup,
    usageType,
    velocityClassification,
    SAFE_CAST(weightLimit AS INT64) AS weightLimit,
    x,
    y,
    tenant,
    facility,
    source_system,
    etl_batch_id
  FROM raw_data
)
SELECT
  `${tenant_id}_oa_curated.Dimension_PK_Generator`(
    [
      tenant,
      facility,
      source_system,
      id
    ]
  ),                            -- location_uuid
  '',                           -- location_type_code
  id,                           -- location_code
  displayId,                    -- location_name
  x,                            -- relative_x_axis
  y,                            -- relative_y_axis
  '',                           -- relative_z_axis
  side,                         -- side_position
  '',                           -- slot_position
  `group`,                      -- location_group
  '',                           -- bay
  weightLimit,                  -- weightLimit
  '',                           -- location_size
  areHazardousMaterialsAllowed, -- hazardous_materials_allowed
  null,                         -- bay_width
  record_timestamp,             -- effective_begin_date
  record_timestamp,             -- effective_end_date
  tenant,                       -- tenant
  facility,                     -- facility
  source_system,                -- source_system
  null,                         -- active_rec_ind
  etl_batch_id                  -- etl_batch_id
FROM processedData;

MERGE `${tenant_id}_oa_curated.dim_location` dl
USING (SELECT * FROM tmp_dim_location) tt
ON dl.location_uuid = tt.location_uuid
  AND dl.location_code = tt.location_code
  AND dl.tenant = tt.tenant
  AND dl.facility = tt.facility
  AND dl.source_system = tt.source_system
WHEN NOT MATCHED THEN INSERT
(
  location_uuid,
  location_type_code,
  location_code,
  location_name,
  relative_x_axis,
  relative_y_axis,
  relative_z_axis,
  side_position,
  slot_position,
  location_group,
  bay,
  weight_limit,
  location_size,
  hazardous_material_allowed,
  bay_width,
  effective_begin_date,
  effective_end_date,
  tenant,
  facility,
  source_system,
  active_rec_ind,
  etl_batch_id
) VALUES (
  tt.location_uuid,
  tt.location_type_code,
  tt.location_code,
  tt.location_name,
  tt.relative_x_axis,
  tt.relative_y_axis,
  tt.relative_z_axis,
  tt.side_position,
  tt.slot_position,
  tt.location_group,
  tt.bay,
  tt.weight_limit,
  tt.location_size,
  tt.hazardous_material_allowed,
  tt.bay_width,
  tt.effective_begin_date,
  tt.effective_end_date,
  tt.tenant,
  tt.facility,
  tt.source_system,
  tt.active_rec_ind,
  tt.etl_batch_id
);
