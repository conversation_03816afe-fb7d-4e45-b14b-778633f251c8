-- declare @ltarget_tenant string default '${tenant_id}';
-- declare etl_batch_uuid string default '2024-01-02';
-- declare start_query_date timestamp default timestamp('2022-09-27 00:00:00');
-- declare end_query_date timestamp default timestamp('2024-09-27 02:00:00');

-- Creating the temporary staging table
CREATE TEMP TABLE
    temp_pack_task (
        record_timestamp TIMESTAMP,
        pack_task_uuid STRING DEFAULT NULL,
        pack_task_code STRING DEFAULT NULL,
        facility_order_uuid STRING DEFAULT NULL,
        facility_order_code STRING DEFAULT NULL,
        workstation_uuid STRING DEFAULT NULL,
        workstation_code STRING DEFAULT NULL,
        event STRING DEFAULT NULL,
        service_qualifier STRING DEFAULT NULL,
        shipping_label_barcode STRING DEFAULT NULL,
        destination_uuid STRING DEFAULT NULL,
        destination_code STRING DEFAULT NULL,
        preferred_handling_unit_type_uuid STRING DEFAULT NULL,
        preferred_handling_unit_type_code STRING DEFAULT NULL,
        packing_document_type STRING DEFAULT NULL,
        vas_requirement STRING DEFAULT NULL,
        vas_instructions STRING DEFAULT NULL,
        priority INT64 DEFAULT NULL,
        vas_sequence STRING DEFAULT NULL,
        order_channel STRING DEFAULT NULL,
        packing_documents STRING DEFAULT NULL,
        tenant STRING NOT NULL,
        facility STRING NOT NULL,
        source_system STRING NOT NULL,
        etl_batch_id STRING NOT NULL,
    );

-- Inserting data into the temporary staging table
INSERT INTO
    temp_pack_task
WITH
    raw_data AS (
        SELECT
            `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp (JSON_VALUE(data, '$.eventTime'), NULL) AS record_timestamp,
            JSON_VALUE(data, '$.packTaskId') AS pack_task_id,
            JSON_VALUE(data, '$.facilityOrderId') AS facility_order_id,
            JSON_VALUE(data, '$.workstationId') AS workstation_id,
            JSON_VALUE(data, '$.event') AS event,
            JSON_VALUE(data, '$.serviceQualifier') AS service_qualifier,
            JSON_VALUE(data, '$.shippingLabelBarcode') AS shipping_label_barcode,
            JSON_VALUE(data, '$.destinationId') AS destination_id,
            JSON_VALUE(data, '$.preferredHandlingUnitTypeId') AS preferred_handling_unit_type_id,
            JSON_VALUE(data, '$.packingDocumentType') AS packing_document_type,
            JSON_VALUE(data, '$.vasRequrirment') AS vas_requirement,
            JSON_VALUE(data, '$.vasInstructions') AS vas_instructions,
            JSON_VALUE(data, '$.priority') AS priority,
            JSON_VALUE(data, '$.vasSequence') AS vas_sequence,
            JSON_VALUE(data, '$.orderChannel') AS order_channel,
            JSON_VALUE(data, '$.packingDocuments') AS packing_documents,
            @ltarget_tenant AS tenant,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.PackTaskFact`
        WHERE
            ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    normalized_data AS (
        SELECT
            COALESCE(TIMESTAMP(record_timestamp), TIMESTAMP(NULL)) AS record_timestamp,
            '' AS pack_task_uuid,
            COALESCE(CAST(pack_task_id AS STRING), '') AS pack_task_code,
            '' AS facility_order_uuid,
            COALESCE(CAST(facility_order_id AS STRING), '') AS facility_order_code,
            '' AS workstation_uuid,
            COALESCE(CAST(workstation_id AS STRING), '') AS workstation_code,
            COALESCE(CAST(event AS STRING), '') AS event,
            COALESCE(CAST(service_qualifier AS STRING), '') AS service_qualifier,
            COALESCE(CAST(shipping_label_barcode AS STRING), '') AS shipping_label_barcode,
            '' AS destination_uuid,
            COALESCE(CAST(destination_id AS STRING), '') AS destination_code,
            '' AS preferred_handling_unit_type_uuid,
            COALESCE(
                CAST(preferred_handling_unit_type_id AS STRING),
                ''
            ) AS preferred_handling_unit_type_code,
            COALESCE(CAST(packing_document_type AS STRING), '') AS packing_document_type,
            COALESCE(CAST(vas_requirement AS STRING), '') AS vas_requirement,
            COALESCE(CAST(vas_instructions AS STRING), '') AS vas_instructions,
            COALESCE(CAST(priority AS INT64), 0) AS priority,
            COALESCE(CAST(vas_sequence AS STRING), '') AS vas_sequence,
            COALESCE(CAST(order_channel AS STRING), '') AS order_channel,
            COALESCE(CAST(packing_documents AS STRING), '') AS packing_documents,
            tenant,
            facility,
            source_system,
            @etl_batch_uuid AS etl_batch_id
        FROM
            raw_data
    )
SELECT
    *
FROM
    normalized_data;

-- Merge the ID fields: facility_order_uuid, workstation_uuid, destination_uuid
-- facility_order_uuid  -->  dim_facility_order
MERGE
    `${tenant_id}_oa_curated.dim_facility_order` dm USING (
        SELECT
            facility_order_uuid,
            facility_order_code,
            tenant,
            facility,
            source_system,
            MAX(etl_batch_id) AS etl_batch_id
        FROM
            temp_pack_task
        GROUP BY
            facility_order_code,
            facility_order_uuid,
            tenant,
            facility,
            source_system
    ) v ON dm.facility_order_code = v.facility_order_code
    AND dm.tenant = v.tenant
    AND dm.facility = v.facility
    AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        facility_order_uuid,
        facility_order_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.facility_order_code]
        ),
        v.facility_order_code,
        v.tenant,
        v.facility,
        v.source_system,
        v.etl_batch_id
    );

UPDATE temp_pack_task tpt
SET
    tpt.facility_order_uuid = d.facility_order_uuid
FROM
    (
        select distinct
            facility_order_uuid,
            facility_order_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_facility_order`
    ) d
WHERE
    d.facility_order_code = tpt.facility_order_code
    AND d.tenant = tpt.tenant
    AND d.facility = tpt.facility
    AND d.source_system = tpt.source_system;

-- workstation_uuid  -->  dim_workstation
MERGE
    `${tenant_id}_oa_curated.dim_workstation` dw USING (
        SELECT DISTINCT
            workstation_code,
            tenant,
            facility,
            source_system,
            MAX(etl_batch_id) AS etl_batch_id
        FROM
            temp_pack_task
        GROUP BY
            workstation_code,
            tenant,
            facility,
            source_system
    ) v ON dw.workstation_code = v.workstation_code
    AND dw.tenant = v.tenant
    AND dw.facility = v.facility
    AND dw.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        workstation_uuid,
        workstation_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.workstation_code]
        ),
        v.workstation_code,
        v.tenant,
        v.facility,
        v.source_system,
        v.etl_batch_id
    );

UPDATE temp_pack_task tt
SET
    tt.workstation_uuid = d.workstation_uuid
FROM
    (
        select distinct
            workstation_uuid,
            workstation_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_workstation`
    ) d
WHERE
    tt.workstation_code = d.workstation_code
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- destination_uuid  -->  dim_location
MERGE
    `${tenant_id}_oa_curated.dim_location` dl USING (
        SELECT
            destination_code,
            tenant,
            facility,
            source_system,
            MAX(etl_batch_id) AS etl_batch_id
        FROM
            temp_pack_task
        GROUP BY
            destination_code,
            tenant,
            facility,
            source_system
    ) v ON dl.location_code = v.destination_code
    AND dl.tenant = v.tenant
    AND dl.facility = v.facility
    AND dl.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        location_uuid,
        location_code,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.destination_code]
        ),
        v.destination_code,
        v.tenant,
        v.facility,
        v.source_system,
        v.etl_batch_id
    );

UPDATE temp_pack_task tt
SET
    tt.destination_uuid = d.location_uuid
FROM
    (
        select distinct
            location_uuid,
            location_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_location`
    ) d
WHERE
    tt.destination_code = d.location_code
    AND tt.tenant = d.tenant
    AND tt.facility = d.facility
    AND tt.source_system = d.source_system;

-- pack_task_uuid  -->  dim_pack_task
MERGE
    `${tenant_id}_oa_curated.dim_pack_task` dim USING (
        SELECT
            pack_task_code,
            facility_order_uuid,
            facility_order_code,
            workstation_uuid,
            workstation_code,
            service_qualifier,
            destination_uuid,
            destination_code,
            preferred_handling_unit_type_uuid,
            preferred_handling_unit_type_code,
            packing_document_type,
            vas_requirement,
            vas_instructions,
            priority,
            vas_sequence,
            order_channel,
            packing_documents,
            record_timestamp,
            event,
            tenant,
            facility,
            source_system,
            etl_batch_id,
        FROM
            (
                SELECT
                    *,
                    ROW_NUMBER() OVER (PARTITION BY pack_task_code ORDER BY record_timestamp DESC) AS row_num
                FROM temp_pack_task
            ) AS sq
        WHERE row_num = 1
    ) v ON dim.pack_task_code = v.pack_task_code
    AND dim.tenant = v.tenant
    AND dim.facility = v.facility
    AND dim.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
    (
        pack_task_uuid,
        pack_task_code,
        facility_order_uuid,
        facility_order_code,
        workstation_uuid,
        workstation_code,
        service_qualifier,
        destination_uuid,
        destination_code,
        preferred_handling_unit_type_uuid,
        preferred_handling_unit_type_code,
        packing_document_type,
        vas_requirement,
        vas_instructions,
        priority,
        vas_sequence,
        order_channel,
        packing_documents,
        last_updated_timestamp,
        task_status,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.pack_task_code]
        ),
        v.pack_task_code,
        v.facility_order_uuid,
        v.facility_order_code,
        v.workstation_uuid,
        v.workstation_code,
        v.service_qualifier,
        v.destination_uuid,
        v.destination_code,
        v.preferred_handling_unit_type_uuid,
        v.preferred_handling_unit_type_code,
        v.packing_document_type,
        v.vas_requirement,
        v.vas_instructions,
        v.priority,
        v.vas_sequence,
        v.order_channel,
        v.packing_documents,
        v.record_timestamp,
        v.event,
        v.tenant,
        v.facility,
        v.source_system,
        v.etl_batch_id
    )
WHEN MATCHED
    AND v.record_timestamp > dim.last_updated_timestamp THEN
    -- Update the record in the dimension table
UPDATE SET
    pack_task_code = COALESCE(v.pack_task_code, dim.pack_task_code),
    facility_order_uuid = COALESCE(v.facility_order_uuid, dim.facility_order_uuid),
    facility_order_code = COALESCE(v.facility_order_code, dim.facility_order_code),
    workstation_uuid = COALESCE(v.workstation_uuid, dim.workstation_uuid),
    workstation_code = COALESCE(v.workstation_code, dim.workstation_code),
    service_qualifier = COALESCE(v.service_qualifier, dim.service_qualifier),
    destination_uuid = COALESCE(v.destination_uuid, dim.destination_uuid),
    destination_code = COALESCE(v.destination_code, dim.destination_code),
    preferred_handling_unit_type_uuid = COALESCE(
        v.preferred_handling_unit_type_uuid,
        dim.preferred_handling_unit_type_uuid
    ),
    preferred_handling_unit_type_code = COALESCE(
        v.preferred_handling_unit_type_code,
        dim.preferred_handling_unit_type_code
    ),
    packing_document_type = COALESCE(
        v.packing_document_type,
        dim.packing_document_type
    ),
    vas_requirement = COALESCE(v.vas_requirement, dim.vas_requirement),
    vas_instructions = COALESCE(v.vas_instructions, dim.vas_instructions),
    priority = COALESCE(v.priority, dim.priority),
    vas_sequence = COALESCE(v.vas_sequence, dim.vas_sequence),
    order_channel = COALESCE(v.order_channel, dim.order_channel),
    packing_documents = COALESCE(v.packing_documents, dim.packing_documents),
    last_updated_timestamp = COALESCE(v.record_timestamp, dim.last_updated_timestamp),
    task_status = COALESCE(v.event, dim.task_status),
    tenant = COALESCE(v.tenant, dim.tenant),
    facility = COALESCE(v.facility, dim.facility),
    source_system = COALESCE(v.source_system, dim.source_system),
    etl_batch_id = COALESCE(v.etl_batch_id, dim.etl_batch_id);

-- Update the temp table with the new or existing UUID for pack task
UPDATE temp_pack_task tpt
SET
    tpt.pack_task_uuid = dim.pack_task_uuid
FROM
    `${tenant_id}_oa_curated.dim_pack_task` dim
WHERE
    tpt.pack_task_code = dim.pack_task_code
    AND tpt.tenant = dim.tenant
    AND tpt.facility = dim.facility
    AND tpt.source_system = dim.source_system;

-- FINAL FCT Insert
MERGE INTO `${tenant_id}_oa_curated.fct_pack_task` ft USING (
    SELECT * FROM temp_pack_task
) tt
ON tt.pack_task_code = ft.pack_task_code
    AND tt.event = ft.event
    AND tt.tenant = ft.tenant
    AND tt.facility = ft.facility
    AND tt.source_system = ft.source_system
WHEN NOT MATCHED THEN INSERT
(
    record_timestamp,
    pack_task_uuid,
    pack_task_code,
    facility_order_uuid,
    facility_order_code,
    workstation_uuid,
    workstation_code,
    event,
    service_qualifier,
    shipping_label_barcode,
    destination_uuid,
    destination_code,
    preferred_handling_unit_type_uuid,
    preferred_handling_unit_type_code,
    packing_document_type,
    vas_requirement,
    vas_instructions,
    priority,
    vas_sequence,
    order_channel,
    packing_documents,
    tenant,
    facility,
    source_system,
    etl_batch_id
) VALUES (
    tt.record_timestamp,
    tt.pack_task_uuid,
    tt.pack_task_code,
    tt.facility_order_uuid,
    tt.facility_order_code,
    tt.workstation_uuid,
    tt.workstation_code,
    tt.event,
    tt.service_qualifier,
    tt.shipping_label_barcode,
    tt.destination_uuid,
    tt.destination_code,
    tt.preferred_handling_unit_type_uuid,
    tt.preferred_handling_unit_type_code,
    tt.packing_document_type,
    tt.vas_requirement,
    tt.vas_instructions,
    tt.priority,
    tt.vas_sequence,
    tt.order_channel,
    tt.packing_documents,
    tt.tenant,
    tt.facility,
    tt.source_system,
    tt.etl_batch_id
);
