/*
**Set variables for running outside of DAG in BQ directly**
declare ltarget_tenant string default 'ict_development';
declare etl_batch_uuid string default '2020-12-12';
declare start_query_date timestamp default timestamp('2023-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2023-09-27 02:00:00');
*/
CREATE TEMP TABLE tmp_staging_dim_operator (
    operator_code STRING,
    operator_name STRING,
    operator_description STRING,
    dc_tenant_name STRING,
    operator_full_name STRING,
    tenant STRING,
    facility STRING,
    source_system STRING,
    active_rec_ind INT64,
    start_date TIMESTAMP,
    end_date TIMESTAMP
);

INSERT INTO
    tmp_staging_dim_operator
WITH
    extracted_dim_operator AS (
        SELECT
            JSON_VALUE(data, '$.userName') AS operator_code,
            JSON_VALUE(data, '$.description') AS operator_description,
            JSON_VALUE(data, '$.tenantName') AS dc_tenant_name,
            JSON_VALUE(data, '$.fullName') AS operator_full_name,
            SAFE_CAST(
                JSON_VALUE(data, '$.dimensionModificationTime') AS TIMESTAMP
            ) AS dimensionmodificationtime,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.UserDimension`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    ordered_stage_operator AS (
        SELECT
            UPPER(COALESCE(operator_code, '')) AS operator_code,
            operator_code AS operator_name,
            operator_description,
            COALESCE(UPPER(dc_tenant_name), '') AS dc_tenant_name,
            operator_full_name,
            1 AS active_rec_ind,
            LENGTH(
                RTRIM(
                    CONCAT(
                        COALESCE(operator_full_name, ''),
                        COALESCE(operator_description, '')
                    )
                )
            ) AS fs_len,
            ROW_NUMBER() OVER (
                PARTITION BY
                    operator_code,
                    dc_tenant_name
                ORDER BY
                    dimensionmodificationtime DESC
            ) AS rnk,
            extracted_dim_operator.facility AS facility,
            extracted_dim_operator.source_system AS source_system
        FROM
            extracted_dim_operator
    ),
    operators AS (
        SELECT
            ordered_stage_operator.*
        FROM
            ordered_stage_operator
        WHERE
            ordered_stage_operator.rnk = 1
    ),
    refined_operators AS (
        SELECT DISTINCT
            operators.operator_code AS operator_code,
            operators.operator_name,
            CASE
                WHEN db_operators.db_len >= operators.fs_len THEN db_operators.operator_description_exist
                ELSE COALESCE(operators.operator_description, '')
            END AS operator_description,
            operators.dc_tenant_name,
            operators.operator_full_name,
            @ltarget_tenant AS tenant,
            facility AS facility,
            source_system AS source_system,
            operators.active_rec_ind,
            COALESCE(
                db_operators.start_date_exist,
                CURRENT_TIMESTAMP()
            ) AS start_date,
            COALESCE(db_operators.end_date_exist, CURRENT_TIMESTAMP()) AS end_date
        FROM
            operators
            LEFT OUTER JOIN (
                SELECT
                    LENGTH(
                        RTRIM(
                            CONCAT(
                                COALESCE(
                                    `${tenant_id}_oa_curated.dim_operator`.operator_full_name,
                                    ''
                                ),
                                COALESCE(
                                    `${tenant_id}_oa_curated.dim_operator`.operator_description,
                                    ''
                                )
                            )
                        )
                    ) AS db_len,
                    `${tenant_id}_oa_curated.dim_operator`.operator_description AS operator_description_exist,
                    `${tenant_id}_oa_curated.dim_operator`.operator_code,
                    `${tenant_id}_oa_curated.dim_operator`.active_rec_ind AS active_rec_ind_exists,
                    `${tenant_id}_oa_curated.dim_operator`.start_date AS start_date_exist,
                    `${tenant_id}_oa_curated.dim_operator`.end_date AS end_date_exist
                FROM
                    `${tenant_id}_oa_curated.dim_operator`
                WHERE
                    `${tenant_id}_oa_curated.dim_operator`.operator_code IS NOT NULL
                    AND `${tenant_id}_oa_curated.dim_operator`.tenant = @ltarget_tenant
            ) AS db_operators ON operators.operator_code = db_operators.operator_code
    )
SELECT
    operator_code,
    operator_name,
    operator_description,
    dc_tenant_name,
    operator_full_name,
    tenant,
    facility,
    source_system,
    active_rec_ind,
    start_date,
    end_date
FROM
    refined_operators;

-- DIM_Operator
MERGE
    `${tenant_id}_oa_curated.dim_operator` op USING (
        SELECT
            operator_code,
            tenant,
            max(operator_name) as operator_name,
            max(operator_full_name) as operator_full_name,
            max(operator_description) as operator_description,
            max(start_date) as start_date,
            max(end_date) as end_date,
            max(active_rec_ind) as active_rec_ind,
            facility,
            source_system
        FROM
            tmp_staging_dim_operator
        GROUP BY
            operator_code,
            tenant,
            facility,
            source_system
    ) sdo ON op.operator_code = sdo.operator_code
        AND op.tenant = sdo.tenant
        AND op.facility = sdo.facility
        AND op.source_system = sdo.source_system
WHEN MATCHED THEN
UPDATE SET
    operator_name = sdo.operator_name,
    operator_full_name = sdo.operator_full_name,
    operator_description = sdo.operator_description,
    start_date = sdo.start_date,
    end_date = sdo.end_date,
    active_rec_ind = sdo.active_rec_ind,
    etl_batch_id = etl_batch_id
WHEN NOT MATCHED THEN
INSERT
    (
        operator_uuid,
        operator_code,
        operator_name,
        operator_full_name,
        operator_description,
        start_date,
        end_date,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            facility,
            source_system,
            operator_code]
        ),
        operator_code,
        operator_name,
        operator_full_name,
        operator_description,
        start_date,
        end_date,
        @ltarget_tenant,
        facility,
        source_system,
        active_rec_ind,
        @etl_batch_uuid
    );