/*
 **Set variables for running outside of DAG in BQ directly**
declare @ltarget_tenant string default 'ict_development';
declare @etl_batch_uuid string default '2020-12-12';

declare @start_query_date timestamp default timestamp('2021-09-27 00:00:00');
declare @end_query_date timestamp default timestamp('2024-09-27 02:00:00');
 */
CREATE TEMP TABLE
    tmp_staging_dim_module (
        module_code STRING,
        module_name STRING,
        module_type STRING,
        work_area_code STRING,
        work_area_name STRING,
        tenant STRING,
        facility STRING,
        source_system STRING,
        active_rec_ind INT64,
        module_uuid STRING DEFAULT NULL,
        work_area_uuid STRING DEFAULT NULL
    );

INSERT INTO
    tmp_staging_dim_module (
        module_code,
        module_name,
        module_type,
        work_area_code,
        work_area_name,
        tenant,
        facility,
        source_system,
        active_rec_ind
    )
WITH
    staging_group_dim AS (
        SELECT
            JSON_VALUE(data, '$.primaryKey') AS primarykey,
            SAFE_CAST(
                JSON_VALUE(data, '$.dimensionModificationTime') AS TIMESTAMP
            ) AS dimensionmodificationtime,
            JSON_VALUE(data, '$.area') AS area,
            JSON_VALUE(data, '$.id') AS id,
            JSON_VALUE(data, '$.configurationStatus') AS configurationStatus,
            JSON_VALUE(data, '$.tenantName') AS tenantname,
            JSON_VALUE(data, '$.groupType') AS groupType,
            facility,
            source_system
        FROM
            `${tenant_id}_landing.GroupDimension`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    latest_group_dim AS (
        SELECT
            *,
            ROW_NUMBER() OVER (
                PARTITION BY
                    primarykey
                ORDER BY
                    dimensionmodificationtime DESC
            ) AS rnk
        FROM
            staging_group_dim
    )
SELECT
    COALESCE(REPLACE(primaryKey, '#', '.'), '') AS module_code,
    CASE
        WHEN id IS NOT NULL THEN id
        WHEN STRPOS(primaryKey, '#') > 0 THEN UPPER(SPLIT(primaryKey, '#') [SAFE_OFFSET(2)])
        ELSE primaryKey
    END AS module_name,
    groupType as module_type,
    COALESCE(
        CASE
            WHEN STRPOS(area, '#') > 0 THEN UPPER(REPLACE(area, '#', '.'))
            WHEN STRPOS(primaryKey, '#') > 0 THEN UPPER(
                REPLACE(
                    SUBSTR(primaryKey, 0, INSTR(primaryKey, '#', 1, 2) -1),
                    '#',
                    '.'
                )
            )
            ELSE area
        END,
        ''
    ) AS work_area_code,
    CASE
        WHEN STRPOS(area, '#') > 0 THEN UPPER(SPLIT(area, '#') [SAFE_OFFSET(1)])
        WHEN STRPOS(primaryKey, '#') > 0 THEN UPPER(SPLIT(primaryKey, '#') [SAFE_OFFSET(1)])
        ELSE area
    END AS work_area_name,
    @ltarget_tenant AS tenant,
    staging_group_dim.facility AS facility,
    staging_group_dim.source_system AS source_system,
    1 as active_rec_ind
FROM
    latest_group_dim
WHERE
    rnk = 1;

--DIM_WORK_AREA
MERGE
    `${tenant_id}_oa_curated.dim_work_area` dm USING (
        SELECT
            work_area_code,
            max(work_area_name) as work_area_name,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_dim_module
        GROUP BY
            work_area_code,
            tenant,
            facility,
            source_system
    ) v ON dm.work_area_code = v.work_area_code
    AND dm.tenant = v.tenant
WHEN MATCHED THEN
UPDATE SET
    work_area_name = v.work_area_name
WHEN NOT MATCHED THEN
INSERT
    (
        work_area_uuid,
        work_area_code,
        work_area_name,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.work_area_code]
        ),
        v.work_area_code,
        v.work_area_name,
        v.tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_dim_module tt
SET
    work_area_uuid = d.work_area_uuid
FROM
    (
        select distinct
            work_area_uuid,
            work_area_code,
            tenant,
            facility,
            source_system
        from
            `${tenant_id}_oa_curated.dim_work_area`
    ) d
WHERE
    d.work_area_code = tt.work_area_code
    AND d.tenant = tt.tenant
    AND d.facility = tt.facility
    AND d.source_system = tt.source_system;

--DIM_MODULE
MERGE
    `${tenant_id}_oa_curated.dim_module` dm USING (
        SELECT
            module_code,
            max(module_name) as module_name,
            max(module_type) as module_type,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_dim_module
        GROUP BY
            module_code,
            tenant,
            facility,
            source_system
    ) v ON dm.module_code = v.module_code
    AND dm.tenant = v.tenant
WHEN MATCHED THEN
UPDATE SET
    module_name = v.module_name,
    module_type = v.module_type
WHEN NOT MATCHED THEN
INSERT
    (
        module_uuid,
        module_code,
        module_name,
        module_type,
        tenant,
        facility,
        source_system,
        active_rec_ind,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.module_code]
        ),
        v.module_code,
        v.module_name,
        v.module_type,
        v.tenant,
        v.facility,
        v.source_system,
        1,
        @etl_batch_uuid
    );

UPDATE tmp_staging_dim_module tt
SET
    module_uuid = d.module_uuid
FROM
    (
        select distinct
            module_uuid,
            module_code,
            tenant,
            facility,
            source_system
        from
    `${tenant_id}_oa_curated.dim_module`) d
WHERE
    d.module_code = tt.module_code
    AND d.tenant = tt.tenant
    AND d.facility = tt.facility
    AND d.source_system = tt.source_system;