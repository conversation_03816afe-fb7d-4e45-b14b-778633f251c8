-- declare @ltarget_tenant string default '${tenant_id}';
-- declare etl_batch_uuid string default '2024-01-02';
-- declare start_query_date timestamp default timestamp('2022-09-27 00:00:00');
-- declare end_query_date timestamp default timestamp('2024-09-27 02:00:00');

-- Creating the temporary staging table
CREATE TEMP TABLE
  temp_pack_task_line (
    record_timestamp TIMESTAMP NOT NULL,
    pack_task_line_uuid STRING NOT NULL,
    pack_task_line_code STRING NOT NULL,
    pack_task_uuid STRING NOT NULL,
    pack_task_code STRING NOT NULL,
    facility_order_uuid STRING DEFAULT NULL,
    facility_order_code STRING DEFAULT NULL,
    facility_order_line_uuid STRING DEFAULT NULL,
    facility_order_line_code STRING DEFAULT NULL,
    source_handling_unit_code STRING DEFAULT NULL,
    destination_handling_unit_code STRING DEFAULT NULL,
    workstation_uuid STRING DEFAULT NULL,
    workstation_code STRING DEFAULT NULL,
    source_handling_unit_type STRING DEFAULT NULL,
    destination_handling_unit_type STRING DEFAULT NULL,
    destination_preferred_handling_unit_type STRING DEFAULT NULL,
    technology STRING DEFAULT NULL,
    area_uuid STRING DEFAULT NULL,
    area_code STRING DEFAULT NULL,
    group_uuid STRING DEFAULT NULL,
    group_code STRING DEFAULT NULL,
    sku_uuid STRING NOT NULL,
    sku_code STRING NOT NULL,
    u_o_m STRING NOT NULL,
    product_code STRING NOT NULL,
    quantity_target INT64 NOT NULL,
    quantity_packed INT64 DEFAULT NULL,
    user_uuid STRING DEFAULT NULL,
    user_code STRING DEFAULT NULL,
    event STRING NOT NULL,
    confirmation_code STRING DEFAULT NULL,
    vas_instructions_text STRING DEFAULT NULL,
    vas_language STRING DEFAULT NULL,
    vas_sequence STRING DEFAULT NULL,
    expiration_date TIMESTAMP,
    batch_id STRING DEFAULT NULL,
    reason STRING DEFAULT NULL,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING NOT NULL,
    item_category_code STRING NOT NULL,
  );

-- Inserting data into the temporary staging table
INSERT INTO
  temp_pack_task_line
WITH
  raw_data AS (
    SELECT
      `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp (JSON_VALUE(data, '$.eventTime'), NULL) AS record_timestamp,
      JSON_VALUE(data, '$.packTaskLineId') AS pack_task_line_id,
      JSON_VALUE(data, '$.packTaskId') AS pack_task_id,
      JSON_VALUE(data, '$.facilityOrderId') AS facility_order_id,
      JSON_VALUE(data, '$.facilityOrderLineId') AS facility_order_line_id,
      JSON_VALUE(data, '$.sourceHandlingUnitId') AS source_handling_unit_id,
      JSON_VALUE(data, '$.destinationHandlingUnitId') AS destination_handling_unit_id,
      JSON_VALUE(data, '$.workstationId') AS workstation_id,
      JSON_VALUE(data, '$.sourceHandlingUnitType') AS source_handling_unit_type,
      JSON_VALUE(data, '$.destinationHandlingUnitType') AS destination_handling_unit_type,
      JSON_VALUE(data, '$.destinationPreferredHandlingUnitType') AS destination_preferred_handling_unit_type,
      JSON_VALUE(data, '$.technology') AS technology,
      JSON_VALUE(data, '$.areaId') AS area_id,
      JSON_VALUE(data, '$.groupId') AS group_id,
      JSON_VALUE(data, '$.skuId') AS sku_id,
      JSON_VALUE(data, '$.UOM') AS u_o_m,
      JSON_VALUE(data, '$.productCode') AS product_code,
      JSON_VALUE(data, '$.quantityTarget') AS quantity_target,
      JSON_VALUE(data, '$.quantityPacked') AS quantity_packed,
      JSON_VALUE(data, '$.userId') AS user_id,
      JSON_VALUE(data, '$.event') AS event,
      JSON_VALUE(data, '$.confirmationCode') AS confirmation_code,
      JSON_VALUE(data, '$.vasInstructionsText') AS vas_instructions_text,
      JSON_VALUE(data, '$.vasLanguage') AS vas_language,
      JSON_VALUE(data, '$.vasSequence') AS vas_sequence,
      JSON_VALUE(data, '$.expirationDate') AS expiration_date,
      JSON_VALUE(data, '$.batchId') AS batch_id,
      JSON_VALUE(data, '$.reason') AS reason,
      @ltarget_tenant AS tenant,
      facility,
      source_system
    FROM
      `${tenant_id}_landing.PackTaskLineFact`
    WHERE
      ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  normalized_data AS (
    SELECT
      COALESCE(TIMESTAMP(record_timestamp), TIMESTAMP(NULL)) AS record_timestamp,
      '' AS pack_task_line_uuid,
      COALESCE(CAST(pack_task_line_id AS STRING), '') AS pack_task_line_code,
      '' AS pack_task_uuid,
      COALESCE(CAST(pack_task_id AS STRING), '') AS pack_task_code,
      '' AS facility_order_uuid,
      COALESCE(CAST(facility_order_id AS STRING), '') AS facility_order_code,
      '' AS facility_order_line_uuid,
      COALESCE(CAST(facility_order_line_id AS STRING), '') AS facility_order_line_code,
      COALESCE(CAST(source_handling_unit_id AS STRING), '') AS source_handling_unit_code,
      COALESCE(CAST(destination_handling_unit_id AS STRING), '') AS destination_handling_unit_code,
      '' AS workstation_uuid,
      COALESCE(CAST(workstation_id AS STRING), '') AS workstation_code,
      COALESCE(CAST(source_handling_unit_type AS STRING), '') AS source_handling_unit_type,
      COALESCE(
        CAST(destination_handling_unit_type AS STRING),
        ''
      ) AS destination_handling_unit_type,
      COALESCE(
        CAST(
          destination_preferred_handling_unit_type AS STRING
        ),
        ''
      ) AS destination_preferred_handling_unit_type,
      COALESCE(CAST(technology AS STRING), '') AS technology,
      '' AS area_uuid,
      COALESCE(CAST(area_id AS STRING), '') AS area_code,
      '' AS group_uuid,
      COALESCE(CAST(group_id AS STRING), '') AS group_code,
      '' AS sku_uuid,
      COALESCE(CAST(sku_id AS STRING), '') AS sku_code,
      COALESCE(CAST(u_o_m AS STRING), '') AS u_o_m,
      COALESCE(CAST(product_code AS STRING), '') AS product_code,
      COALESCE(CAST(quantity_target AS INT64), 0) AS quantity_target,
      COALESCE(CAST(quantity_packed AS INT64), 0) AS quantity_packed,
      '' as user_uuid,
      COALESCE(CAST(user_id AS STRING), '') AS user_code,
      COALESCE(CAST(event AS STRING), '') AS event,
      COALESCE(CAST(confirmation_code AS STRING), '') AS confirmation_code,
      COALESCE(CAST(vas_instructions_text AS STRING), '') AS vas_instructions_text,
      COALESCE(CAST(vas_language AS STRING), '') AS vas_language,
      COALESCE(CAST(vas_sequence AS STRING), '') AS vas_sequence,
      COALESCE(TIMESTAMP(expiration_date), TIMESTAMP(NULL)) AS expiration_date,
      COALESCE(CAST(batch_id AS STRING), '') AS batch_id,
      COALESCE(CAST(reason AS STRING), '') AS reason,
      tenant,
      facility,
      source_system,
      @etl_batch_uuid AS etl_batch_id,
      '' AS item_category_code,
    FROM
      raw_data
  )
SELECT
  *
FROM
  normalized_data;

/*
-- Merge the ID fields: 
facility_order_uuid  ->  dim_facility_order 
workstation_uuid --> dim_workstation
pack_task_uuid  ->  dim_pack_task 
area_uuid  --> dim_work_area
sku_uuid  ->  dim_item
user_uuid   ->  dim_operator
group_uuid  -> dim_module
 */
-- facility_order_uuid  -->  dim_facility_order
MERGE
  `${tenant_id}_oa_curated.dim_facility_order` dm USING (
    SELECT
      facility_order_uuid,
      facility_order_code,
      tenant,
      facility,
      source_system,
      MAX(etl_batch_id) AS etl_batch_id
    FROM
      temp_pack_task_line
    GROUP BY
      facility_order_code,
      facility_order_uuid,
      tenant,
      facility,
      source_system
  ) v ON dm.facility_order_code = v.facility_order_code
  AND dm.tenant = v.tenant
  AND dm.facility = v.facility
  AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    facility_order_uuid,
    facility_order_code,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.facility_order_code]
    ),
    v.facility_order_code,
    v.tenant,
    v.facility,
    v.source_system,
    v.etl_batch_id
  );

UPDATE temp_pack_task_line tpt
SET
  tpt.facility_order_uuid = d.facility_order_uuid
FROM
  (
    select distinct
      facility_order_uuid,
      facility_order_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_facility_order`
  ) d
WHERE
  d.facility_order_code = tpt.facility_order_code
  AND d.tenant = tpt.tenant
  AND d.facility = tpt.facility
  AND d.source_system = tpt.source_system;

-- workstation_uuid  -->  dim_workstation
MERGE
  `${tenant_id}_oa_curated.dim_workstation` dw USING (
    SELECT DISTINCT
      workstation_code,
      tenant,
      facility,
      source_system,
      MAX(etl_batch_id) AS etl_batch_id
    FROM
      temp_pack_task_line
    GROUP BY
      workstation_code,
      tenant,
      facility,
      source_system
  ) v ON dw.workstation_code = v.workstation_code
  AND dw.tenant = v.tenant
  AND dw.facility = v.facility
  AND dw.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    workstation_uuid,
    workstation_code,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.workstation_code]
    ),
    v.workstation_code,
    v.tenant,
    v.facility,
    v.source_system,
    v.etl_batch_id
  );

UPDATE temp_pack_task_line tt
SET
  tt.workstation_uuid = d.workstation_uuid
FROM
  (
    select distinct
      workstation_uuid,
      workstation_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_workstation`
  ) d
WHERE
  tt.workstation_code = d.workstation_code
  AND tt.tenant = d.tenant
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

-- pack_task_uuid  -->  dim_pack_task
MERGE
  `${tenant_id}_oa_curated.dim_pack_task` dim USING (
    SELECT
      record_timestamp,
      pack_task_uuid,
      pack_task_code,
      facility_order_uuid,
      facility_order_code,
      tenant,
      facility,
      source_system,
      etl_batch_id,
    FROM
      (
        SELECT
          *,
          ROW_NUMBER() OVER (PARTITION BY pack_task_code ORDER BY record_timestamp DESC) AS row_num
          FROM temp_pack_task_line
      ) AS sq
    WHERE row_num = 1
  ) v ON dim.pack_task_code = v.pack_task_code
  AND dim.tenant = v.tenant
  AND dim.facility = v.facility
  AND dim.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    pack_task_uuid,
    pack_task_code,
    facility_order_uuid,
    facility_order_code,
    last_updated_timestamp,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.pack_task_code]
    ),
    v.pack_task_code,
    v.facility_order_uuid,
    v.facility_order_code,
    v.record_timestamp,
    v.tenant,
    v.facility,
    v.source_system,
    v.etl_batch_id
  )
WHEN MATCHED
  AND v.record_timestamp > dim.last_updated_timestamp THEN
  -- Update the record in the dimension table
UPDATE SET
  pack_task_code = COALESCE(v.pack_task_code, dim.pack_task_code),
  last_updated_timestamp = COALESCE(v.record_timestamp, dim.last_updated_timestamp),
  tenant = COALESCE(v.tenant, dim.tenant),
  facility = COALESCE(v.facility, dim.facility),
  source_system = COALESCE(v.source_system, dim.source_system),
  etl_batch_id = COALESCE(v.etl_batch_id, dim.etl_batch_id);

-- Update the temp table with the new or existing UUID for pack task
UPDATE temp_pack_task_line tpt
SET
  tpt.pack_task_uuid = dim.pack_task_uuid
FROM
  `${tenant_id}_oa_curated.dim_pack_task` dim
WHERE
  tpt.pack_task_code = dim.pack_task_code
  AND tpt.tenant = dim.tenant
  AND tpt.facility = dim.facility
  AND tpt.source_system = dim.source_system;

-- area_uuid  -->  dim_work_area
MERGE
  `${tenant_id}_oa_curated.dim_work_area` dm USING (
    SELECT
      area_uuid,
      area_code,
      tenant,
      facility,
      source_system,
      MAX(etl_batch_id) AS etl_batch_id
    FROM
      temp_pack_task_line
    GROUP BY
      area_uuid,
      area_code,
      tenant,
      facility,
      source_system
  ) v ON dm.work_area_code = v.area_code
  AND dm.tenant = v.tenant
  AND dm.facility = v.facility
  AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    work_area_uuid,
    work_area_code,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.area_code]
    ),
    v.area_code,
    v.tenant,
    v.facility,
    v.source_system,
    v.etl_batch_id
  );

UPDATE temp_pack_task_line tpt
SET
  tpt.area_uuid = d.work_area_uuid
FROM
  (
    select distinct
      work_area_uuid,
      work_area_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_work_area`
  ) d
WHERE
  d.work_area_code = tpt.area_code
  AND d.tenant = tpt.tenant
  AND d.facility = tpt.facility
  AND d.source_system = tpt.source_system;

-- sku_uuid  -->  dim_item
MERGE
  `${tenant_id}_oa_curated.dim_item` dm USING (
    SELECT
      sku_uuid,
      sku_code,
      item_category_code,
      tenant,
      facility,
      source_system,
      MAX(etl_batch_id) AS etl_batch_id
    FROM
      temp_pack_task_line
    GROUP BY
      sku_uuid,
      sku_code,
      item_category_code,
      tenant,
      facility,
      source_system
  ) v ON dm.item_code = v.sku_code
  AND dm.tenant = v.tenant
  AND dm.facility = v.facility
  AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    item_uuid,
    item_code,
    item_category_code,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.sku_code]
    ),
    v.sku_code,
    v.item_category_code,
    v.tenant,
    v.facility,
    v.source_system,
    v.etl_batch_id
  );

UPDATE temp_pack_task_line tpt
SET
  tpt.sku_uuid = d.item_uuid
FROM
  (
    select distinct
      item_uuid,
      item_sku,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_item`
  ) d
WHERE
  d.item_sku = tpt.sku_code
  AND d.tenant = tpt.tenant
  AND d.facility = tpt.facility
  AND d.source_system = tpt.source_system;

-- user_uuid  -->  dim_operator
MERGE
  `${tenant_id}_oa_curated.dim_operator` dm USING (
    SELECT
      user_uuid,
      user_code,
      tenant,
      facility,
      source_system,
      MAX(etl_batch_id) AS etl_batch_id
    FROM
      temp_pack_task_line
    GROUP BY
      user_uuid,
      user_code,
      tenant,
      facility,
      source_system
  ) v ON dm.operator_code = v.user_code
  AND dm.tenant = v.tenant
  AND dm.facility = v.facility
  AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    operator_uuid,
    operator_code,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.user_code]
    ),
    v.user_code,
    v.tenant,
    v.facility,
    v.source_system,
    v.etl_batch_id
  );

UPDATE temp_pack_task_line tpt
SET
  tpt.user_uuid = d.operator_uuid
FROM
  (
    select distinct
      operator_uuid,
      operator_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_operator`
  ) d
WHERE
  d.operator_code = tpt.user_code
  AND d.tenant = tpt.tenant
  AND d.facility = tpt.facility
  AND d.source_system = tpt.source_system;

-- group_uuid  -->  dim_module
MERGE
  `${tenant_id}_oa_curated.dim_module` dm USING (
    SELECT
      group_uuid,
      group_code,
      tenant,
      facility,
      source_system,
      MAX(etl_batch_id) AS etl_batch_id
    FROM
      temp_pack_task_line
    GROUP BY
      group_uuid,
      group_code,
      tenant,
      facility,
      source_system
  ) v ON dm.module_code = v.group_code
  AND dm.tenant = v.tenant
  AND dm.facility = v.facility
  AND dm.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    module_uuid,
    module_code,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.group_code]
    ),
    v.group_code,
    v.tenant,
    v.facility,
    v.source_system,
    v.etl_batch_id
  );

UPDATE temp_pack_task_line tpt
SET
  tpt.group_uuid = d.module_uuid
FROM
  (
    select distinct
      module_uuid,
      module_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_module`
  ) d
WHERE
  d.module_code = tpt.group_code
  AND d.tenant = tpt.tenant
  AND d.facility = tpt.facility
  AND d.source_system = tpt.source_system;

-- FINAL FCT Insert
INSERT INTO
  `${tenant_id}_oa_curated.fct_pack_task_line`
SELECT
  *
FROM
  temp_pack_task_line;
