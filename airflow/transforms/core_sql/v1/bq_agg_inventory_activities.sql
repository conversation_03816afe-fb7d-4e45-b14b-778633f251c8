-- ATTENTION!! AS OF 9/18/2024, THIS VIEW IS NOT IN USE.  PLEASE DO NOT BLINDLY ADD IT BACK TO ANY DAGS

-- This script populates the agg_inventory_activities table every time the DAG runs
/*
DECLARE ltarget_tenant STRING DEFAULT '${tenant_id}';
DECLARE etl_batch_uuid STRING DEFAULT '2020-12-12';

DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2022-09-27 00:00:00');
DECLARE end_query_date TIMESTAMP DEFAULT TIMESTAMP('2024-09-27 02:00:00');
*/


INSERT INTO ${tenant_id}_oa_curated.agg_inventory_activities (
  record_timestamp, item_uuid, item_sku, location_uuid, location_name, process_type_code, tenant, etl_batch_id
)

-- CTE to get location data
WITH location_data AS (
  SELECT
    location_uuid,
    location_name
  FROM
    `${tenant_id}_oa_curated.dim_location`
),
-- CTE to get item data
item_data AS (
  SELECT
    item_uuid,
    item_sku
  FROM
    `${tenant_id}_oa_curated.dim_item`
)
SELECT 
  i.record_timestamp AS record_timestamp,
  i.item_uuid AS item_uuid, 
  MAX(it.item_sku) AS item_sku,
  i.location_uuid,
  MAX(l.location_name) AS location_name,
  MAX(i.process_type_code) AS process_type_code,
  MAX(@etl_batch_uuid) AS etl_batch_id,
  MAX(@ltarget_tenant) AS tenant
FROM 
  `${tenant_id}_oa_curated.fct_order_line` i
JOIN
  location_data l
ON
  i.location_uuid = l.location_uuid
JOIN
  item_data it
ON
  i.item_uuid = it.item_uuid
WHERE
  i.record_timestamp BETWEEN @start_query_date AND @end_query_date
GROUP BY
  i.record_timestamp, 
  i.item_uuid, 
  i.location_uuid
