/*
 **Set variables for running outside of DAG in BQ directly**
declare ltarget_tenant string default 'ict_development';
declare etl_batch_uuid string default '2020-12-12';

declare start_query_date timestamp default timestamp('2021-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2024-09-27 02:00:00');
 */
CREATE TEMP TABLE
    tmp_staging_dim_work_area (
        work_area_code STRING,
        work_area_name STRING,
        work_area_type STRING,
        work_area_description STRING,
        work_area_operation_mode STRING,
        tenant STRING,
        facility STRING,
        source_system STRING,
        active_rec_ind INT64,
        work_area_uuid STRING DEFAULT NULL
    );

INSERT INTO
    tmp_staging_dim_work_area (
        work_area_code,
        work_area_name,
        work_area_type,
        work_area_description,
        work_area_operation_mode,
        tenant,
        facility,
        source_system,
        active_rec_ind
    )
WITH
    staging_area_dim AS (
        SELECT
            JSON_VALUE(data, '$.id') AS id,
            JSON_VALUE(data, '$.name') AS work_area_name,
            JSON_VALUE(data, '$.areaType') AS work_area_type,
            JSON_VALUE(data, '$.operationMode') AS work_area_operation_mode,
            JSON_VALUE(data, '$.description') AS work_area_description,
            JSON_VALUE(data, '$.primaryKey') AS primarykey,
            SAFE_CAST(
                JSON_VALUE(data, '$.dimensionModificationTime') AS TIMESTAMP
            ) AS dimensionmodificationtime
        FROM
            `${tenant_id}_landing.AreaDimension`
        WHERE
            tenant = @ltarget_tenant
            AND facility = @ltarget_facility
            AND source_system = @lsource_system
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    latest_area_dim AS (
        SELECT
            *,
            ROW_NUMBER() OVER (
                PARTITION BY
                    primarykey
                ORDER BY
                    dimensionmodificationtime DESC
            ) AS rnk
        FROM
            staging_area_dim
    )
SELECT
    COALESCE(REPLACE(primarykey, '#', '.'), '') AS work_area_code,
    CASE
        WHEN id IS NOT NULL THEN id
        WHEN work_area_name IS NOT NULL THEN work_area_name
        WHEN STRPOS(primarykey, '#') > 0 THEN UPPER(SPLIT(primarykey, '#') [SAFE_OFFSET(1)])
        ELSE primarykey
    END AS work_area_name,
    work_area_type,
    work_area_operation_mode,
    work_area_description,
    @ltarget_tenant AS tenant,
    @ltarget_facility AS facility,
    @lsource_system AS source_system,
    1 as active_rec_ind
FROM
    latest_area_dim
WHERE
    rnk = 1;

--DIM_WORK_AREA
MERGE
    `${tenant_id}_oa_curated.dim_work_area` dm USING (
        SELECT
            work_area_code,
            max(work_area_name) as work_area_name,
            max(work_area_type) as work_area_type,
            max(work_area_operation_mode) as work_area_operation_mode,
            max(work_area_description) as work_area_description,
            tenant,
            facility,
            source_system
        FROM
            tmp_staging_dim_work_area
        GROUP BY
            work_area_code,
            tenant,
            facility,
            source_system
    ) v ON dm.work_area_code = v.work_area_code
    AND dm.tenant = v.tenant
    AND dm.facility = v.facility
    AND dm.source_system = v.source_system
WHEN MATCHED THEN
UPDATE SET
    work_area_name = v.work_area_name,
    work_area_type = v.work_area_type,
    work_area_operation_mode = v.work_area_operation_mode,
    work_area_description = v.work_area_description,
    etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
    (
        work_area_uuid,
        work_area_code,
        work_area_name,
        work_area_type,
        work_area_operation_mode,
        work_area_description,
        active_rec_ind,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [v.tenant,
            v.facility,
            v.source_system,
            v.work_area_code]
        ),
        v.work_area_code,
        v.work_area_name,
        v.work_area_type,
        v.work_area_operation_mode,
        v.work_area_description,
        1,
        v.tenant,
        v.facility,
        v.source_system,
        @etl_batch_uuid
    );