
/*
declare ltarget_tenant string default 'dematic';
declare etl_batch_uuid string default '2020-12-12';

declare start_query_date timestamp default timestamp('2022-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2024-09-27 02:00:00');
*/

-- Creating the temporary staging table
CREATE TEMP TABLE tmp_staging_fct_bin_utilization
(
    record_timestamp TIMESTAMP,
    aisle INT64,
    area STRING,
    empty_locations INT64,
    empty_openings INT64,
    faulted_locations INT64,
    percent_empty_openings FLOAT64,
    percent_empty_storage_locs FLOAT64,
    total_locations INT64,
    total_openings INT64,
    unavailable_locations INT64,
    tenant STRING,
    facility STRING,
    source_system STRING
);
INSERT INTO tmp_staging_fct_bin_utilization
-- Inserting data into the temporary staging table
WITH raw_data AS (
    SELECT
        SAFE_CAST(JSON_VALUE(data, '$.recordTime') AS TIMESTAMP) AS record_timestamp,
        SAFE_CAST(JSON_VALUE(data, '$.Aisle') AS INT64) AS aisle,
        SAFE_CAST(JSON_VALUE(data, '$.area') AS STRING) AS area,
        SAFE_CAST(JSON_VALUE(data, '$.EmptyLocations') AS INT64) AS empty_locations,
        SAFE_CAST(JSON_VALUE(data, '$.EmptyOpenings') AS INT64) AS empty_openings,
        SAFE_CAST(JSON_VALUE(data, '$.FaultedLocations') AS INT64) AS faulted_locations,
        SAFE_CAST(JSON_VALUE(data, '$.PercentEmptyOpenings') AS FLOAT64) AS percent_empty_openings,
        SAFE_CAST(JSON_VALUE(data, '$.PercentEmptyStorageLocs') AS FLOAT64) AS percent_empty_storage_locs,
        SAFE_CAST(JSON_VALUE(data, '$.TotalLocations') AS INT64) AS total_locations,
        SAFE_CAST(JSON_VALUE(data, '$.TotalOpenings') AS INT64) AS total_openings,
        SAFE_CAST(JSON_VALUE(data, '$.UnavailableLocations') AS INT64) AS unavailable_locations,
        @ltarget_tenant AS tenant,
        facility,
        source_system
    FROM `${tenant_id}_landing.BinUtilization`
    WHERE ingestion_date BETWEEN @start_query_date AND @end_query_date
)
SELECT
    record_timestamp,
    aisle,
    area,
    empty_locations,
    empty_openings,
    faulted_locations,
    percent_empty_openings,
    percent_empty_storage_locs,
    total_locations,
    total_openings,
    unavailable_locations,
    tenant,
    facility,
    source_system
FROM raw_data;

-- Inserting data into the Target Fact Table
INSERT INTO `${tenant_id}_oa_curated.fct_bin_utilization`
SELECT
    aisle,
    area,
    empty_locations,
    empty_openings,
    faulted_locations,
    percent_empty_openings,
    percent_empty_storage_locs,
    record_timestamp,
    total_locations,
    total_openings,
    unavailable_locations,
    tenant,
    facility,
    source_system,
    @etl_batch_uuid
FROM tmp_staging_fct_bin_utilization;

