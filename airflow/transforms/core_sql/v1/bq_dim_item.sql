/*
 **Set variables for running outside of DAG in BQ directly**
declare ltarget_tenant string default 'ict_development';
declare etl_batch_uuid string default '2020-12-12';

declare start_query_date timestamp default timestamp('2023-09-27 00:00:00');
declare end_query_date timestamp default timestamp('2023-09-27 02:00:00');
 */
CREATE TEMP TABLE
    tmp_staging_dim_item (
        item_code STRING,
        item_category_code STRING,
        item_name STRING,
        item_sku STRING,
        item_product_code STRING,
        uom STRING,
        client STRING,
        total_weight FLOAT64,
        total_volume FLOAT64,
        length FLOAT64,
        width FLOAT64,
        height FLOAT64,
        palletizing_mode STRING,
        max_units_per_pick INT64,
        item_shelf_life_in_days INT64,
        preferred_container_type STRING,
        velocity_classification STRING,
        hazard_classification STRING,
        supplier_code STRING,
        base_uom STRING,
        default_pick_uom INT64,
        preferred_fulfillment_handling STRING,
        amcap_qualification_ind STRING,
        toppling_rate INT64,
        tenant STRING,
        facility STRING,
        source_system STRING,
        active_rec_ind INT64
    );

INSERT INTO
    tmp_staging_dim_item (
        item_code,
        item_category_code,
        item_name,
        item_sku,
        item_product_code,
        uom,
        client,
        total_weight,
        total_volume,
        length,
        width,
        height,
        palletizing_mode,
        max_units_per_pick,
        item_shelf_life_in_days,
        preferred_container_type,
        velocity_classification,
        hazard_classification,
        supplier_code,
        base_uom,
        default_pick_uom,
        preferred_fulfillment_handling,
        amcap_qualification_ind,
        toppling_rate,
        tenant,
        facility,
        source_system,
        active_rec_ind
    )
WITH
    staging_sku_prod_dim AS (
        SELECT
            JSON_VALUE(data, '$.dimPrimaryKey') AS dimprimarykey,
            SAFE_CAST(
                JSON_VALUE(data, '$.dimensionModificationTime') AS TIMESTAMP
            ) AS dimensionmodificationtime,
            JSON_VALUE(data, '$.primaryKey') AS primarykey,
            JSON_VALUE(data, '$.productCode') AS productcode,
            JSON_VALUE(data, '$.quantityUnit') AS quantityunit,
            JSON_VALUE(data, '$.tenantName') AS tenantname,
            @ltarget_tenant AS tenant,
            facility AS facility,
            source_system AS source_system
        FROM
            `${tenant_id}_landing.SKUProductCodeDimension`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    ordered_sku_prod_code AS (
        SELECT
            *,
            CASE
                WHEN STRPOS(primarykey, '#') > 0 THEN SPLIT(primarykey, '#') [OFFSET(0)]
                ELSE primarykey
            END AS client,
            CASE
                WHEN STRPOS(primarykey, '#') > 0 THEN SPLIT(primarykey, '#') [OFFSET(2)]
                ELSE primarykey
            END AS uom,
            CASE
                WHEN STRPOS(primarykey, '#') > 0 THEN SPLIT(primarykey, '#') [OFFSET(1)]
                ELSE primarykey
            END AS skuid,
            ROW_NUMBER() OVER (
                PARTITION BY
                    primarykey
                ORDER BY
                    dimensionmodificationtime DESC
            ) AS rnk
        FROM
            staging_sku_prod_dim
    ),
    sku_prod_code AS (
        SELECT
            * EXCEPT (rnk)
        FROM
            ordered_sku_prod_code
        WHERE
            ordered_sku_prod_code.rnk = 1
    ),
    staging_sku_quantity_unit_dim AS (
        SELECT
            JSON_VALUE(data, '$.amcapQualification') AS amcapqualification,
            SAFE_CAST(
                JSON_VALUE(data, '$.dimensionModificationTime') AS TIMESTAMP
            ) AS dimensionmodificationtime,
            SAFE_CAST(
                JSON_VALUE(data, '$.defaultPickQuantityUnit') AS BOOL
            ) AS defaultpickquantityunit,
            SAFE_CAST(JSON_VALUE(data, '$.factorToBaseQU') AS NUMERIC) AS factortobasequ,
            SAFE_CAST(JSON_VALUE(data, '$.height') AS FLOAT64) AS height,
            SAFE_CAST(JSON_VALUE(data, '$.length') AS FLOAT64) AS length,
            JSON_VALUE(data, '$.preferredFulfillmentHandlingType') AS preferredfulfillmenthandlingtype,
            JSON_VALUE(data, '$.primaryKey') AS primarykey,
            JSON_VALUE(data, '$.quantityUnitId') AS quantityunitid,
            JSON_VALUE(data, '$.sku') AS sku,
            JSON_VALUE(data, '$.tenantName') AS tenantname,
            JSON_VALUE(data, '$.client') AS s_client,
            SAFE_CAST(JSON_VALUE(data, '$.totalVolume') AS FLOAT64) AS totalvolume,
            SAFE_CAST(JSON_VALUE(data, '$.totalWeight') AS FLOAT64) AS totalweight,
            SAFE_CAST(JSON_VALUE(data, '$.width') AS FLOAT64) AS width,
            @ltarget_tenant AS tenant,
            facility AS facility,
            source_system AS source_system
        FROM
            `${tenant_id}_landing.SKUQuantityUnitDimension`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    ordered_sku_quantity_unit_dim AS (
        SELECT
            *,
            COALESCE(
                s_client,
                CASE
                    WHEN STRPOS(primarykey, '#') > 0 THEN SPLIT(primarykey, '#') [OFFSET(0)]
                    ELSE primarykey
                END
            ) AS client,
            COALESCE(
                quantityunitid,
                CASE
                    WHEN STRPOS(primarykey, '#') > 0 THEN SPLIT(primarykey, '#') [OFFSET(2)]
                    ELSE primarykey
                END
            ) AS uom,
            CASE
                WHEN STRPOS(primarykey, '#') > 0 THEN SPLIT(primarykey, '#') [OFFSET(1)]
                ELSE primarykey
            END AS skuid,
            ROW_NUMBER() OVER (
                PARTITION BY
                    primarykey
                ORDER BY
                    dimensionmodificationtime DESC
            ) AS rnk
        FROM
            staging_sku_quantity_unit_dim
    ),
    skuuom AS (
        SELECT
            * EXCEPT (rnk, s_client)
        FROM
            ordered_sku_quantity_unit_dim
        WHERE
            rnk = 1
    ),
    staging_sku_dim AS (
        SELECT
            JSON_VALUE(data, '$.baseQuantityUnit') AS basequantityunit,
            JSON_VALUE(data, '$.client') AS client,
            JSON_VALUE(data, '$.description') AS description,
            JSON_VALUE(data, '$.dimPrimaryKey') AS dimprimarykey,
            JSON_VALUE(data, '$.dispatchPackingGroup') AS dispatchpackinggroup,
            JSON_VALUE(data, '$.hazardClassification') AS hazardclassification,
            JSON_VALUE(data, '$.loadUnitType') AS loadunittype,
            JSON_VALUE(data, '$.mainProductRangeId') AS mainproductrangeid,
            SAFE_CAST(
                JSON_VALUE(data, '$.maximumUnitsPerPick') AS INT64
            ) AS maximumunitsperpick,
            JSON_VALUE(data, '$.owner') AS owner,
            JSON_VALUE(data, '$.palletizingMode') AS palletizingmode,
            JSON_VALUE(data, '$.preferredLoadUnitType') AS preferredloadunittype,
            JSON_VALUE(data, '$.primaryKey') AS primarykey,
            JSON_VALUE(data, '$.productCode') AS productcode,
            SAFE_CAST(JSON_VALUE(data, '$.shelfLife') AS INT64) AS shelflife,
            JSON_VALUE(data, '$.status') AS status,
            JSON_VALUE(data, '$.supplierId') AS supplierid,
            JSON_VALUE(data, '$.velocityClassification') AS velocityclassification,
            SAFE_CAST(
                JSON_VALUE(data, '$.dimensionModificationTime') AS TIMESTAMP
            ) AS dimensionmodificationtime,
            @ltarget_tenant AS tenant,
            facility AS facility,
            source_system AS source_system
        FROM
            `${tenant_id}_landing.SKUDimension`
        WHERE
            tenant = @ltarget_tenant
            AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
    ),
    ordered_sku AS (
        SELECT
            *,
            CASE
                WHEN STRPOS(primarykey, '#') > 0 THEN ARRAY_REVERSE(SPLIT(primarykey, '#')) [OFFSET(0)]
                ELSE primarykey
            END AS skuid,
            ROW_NUMBER() OVER (
                PARTITION BY
                    primarykey
                ORDER BY
                    dimensionmodificationtime DESC
            ) AS rnk
        FROM
            staging_sku_dim
    ),
    sku AS (
        SELECT
            * EXCEPT (rnk)
        FROM
            ordered_sku
        WHERE
            rnk = 1
    )
SELECT
    `${tenant_id}_oa_curated.Item_Code_Generator` (
        item_dim.item_sku,
        item_dim.item_product_code,
        item_dim.uom,
        NULL
    ) AS item_code,
    MAX(item_dim.item_category_code) AS item_category_code,
    item_dim.item_name AS item_name,
    item_dim.item_sku AS item_sku,
    item_dim.item_product_code AS item_product_code,
    item_dim.uom AS uom,
    item_dim.client AS client,
    MAX(item_dim.total_weight) AS total_weight,
    MAX(item_dim.total_volume) AS total_volume,
    MAX(item_dim.length) AS length,
    MAX(item_dim.width) AS width,
    MAX(item_dim.height) AS height,
    item_dim.palletizing_mode AS palletizing_mode,
    item_dim.max_units_per_pick AS max_units_per_pick,
    item_dim.item_shelf_life_in_days AS item_shelf_life_in_days,
    item_dim.preferred_container_type AS preferred_container_type,
    item_dim.velocity_classification AS velocity_classification,
    item_dim.hazard_classification AS hazard_classification,
    item_dim.supplier_code AS supplier_code,
    item_dim.base_uom AS base_uom,
    item_dim.default_pick_uom,
    item_dim.preferred_fulfillment_handling AS preferred_fulfillment_handling,
    item_dim.amcap_qualification_ind AS amcap_qualification_ind,
    item_dim.toppling_rate AS toppling_rate,
    @ltarget_tenant AS tenant,
    item_dim.facility,
    item_dim.source_system,
    item_dim.active_rec_ind
FROM
    (
        SELECT
            COALESCE(item.item_category_code, '') AS item_category_code,
            item.description AS item_name,
            item.skuid AS item_sku,
            item.productcode AS item_product_code,
            item.uom AS uom,
            item.client AS client,
            item.totalweight AS total_weight,
            item.totalvolume AS total_volume,
            item.length AS length,
            item.width AS width,
            item.height AS height,
            item.palletizingmode AS palletizing_mode,
            item.maximumunitsperpick AS max_units_per_pick,
            item.shelflife AS item_shelf_life_in_days,
            item.preferredloadunittype AS preferred_container_type,
            item.velocityclassification AS velocity_classification,
            item.hazardclassification AS hazard_classification,
            item.supplierid AS supplier_code,
            item.basequantityunit AS base_uom,
            item.defaultpickquantityunit,
            CASE
                WHEN item.defaultpickquantityunit IS false THEN 0
                ELSE 1
            END AS default_pick_uom,
            item.preferredfulfillmenthandlingtype AS preferred_fulfillment_handling,
            item.amcapqualification AS amcap_qualification_ind,
            null AS toppling_rate,
            facility AS facility,
            source_system AS source_system,
            1 AS active_rec_ind
        FROM
            (
                SELECT
                    COALESCE(
                        sku.skuid,
                        COALESCE(sku_prod_code.skuid, skuuom.skuid)
                    ) AS skuid,
                    COALESCE(
                        sku.client,
                        COALESCE(sku_prod_code.client, skuuom.client)
                    ) AS client,
                    COALESCE(sku_prod_code.uom, skuuom.uom) AS uom,
                    sku_prod_code.productcode,
                    sku.basequantityunit,
                    sku.description,
                    sku.dimprimarykey,
                    sku.dispatchpackinggroup AS item_category_code,
                    sku.hazardclassification,
                    sku.loadunittype,
                    sku.mainproductrangeid,
                    sku.maximumunitsperpick,
                    owner AS const_sku_owner,
                    sku.palletizingmode,
                    sku.preferredloadunittype,
                    sku.shelflife,
                    sku.status,
                    sku.supplierid,
                    sku.velocityclassification,
                    sku_prod_code.quantityunit,
                    skuuom.amcapqualification,
                    skuuom.defaultpickquantityunit,
                    skuuom.factortobasequ,
                    skuuom.height,
                    skuuom.length,
                    skuuom.preferredfulfillmenthandlingtype,
                    skuuom.sku,
                    skuuom.totalvolume,
                    skuuom.totalweight,
                    skuuom.width,
                    sku_prod_code.facility AS facility,
                    sku_prod_code.source_system AS source_system
                FROM
                    sku_prod_code
                    LEFT JOIN skuuom ON sku_prod_code.skuid = skuuom.skuid
                    AND sku_prod_code.client = skuuom.client
                    AND sku_prod_code.uom = skuuom.uom
                    LEFT JOIN sku ON sku.skuid = skuuom.skuid
                    AND sku.client = skuuom.client
            ) AS item
    ) AS item_dim
GROUP BY
    item_dim.item_sku,
    item_dim.item_name,
    item_dim.uom,
    item_dim.client,
    25,
    item_dim.item_product_code,
    item_dim.palletizing_mode,
    item_dim.active_rec_ind,
    item_dim.max_units_per_pick,
    item_dim.item_shelf_life_in_days,
    item_dim.preferred_container_type,
    item_dim.velocity_classification,
    item_dim.hazard_classification,
    item_dim.supplier_code,
    item_dim.base_uom,
    item_dim.preferred_fulfillment_handling,
    item_dim.amcap_qualification_ind,
    item_dim.toppling_rate,
    25,
    21,
    item_dim.defaultpickquantityunit,
    item_dim.facility,
    item_dim.source_system;

-- DIM_Item
MERGE
    `${tenant_id}_oa_curated.dim_item` di USING (
        SELECT
            item_code,
            item_category_code,
            max(item_name) as item_name,
            max(item_sku) as item_sku,
            max(item_product_code) item_product_code,
            max(uom) as uom,
            max(client) as client,
            max(total_weight) as total_weight,
            max(total_volume) as total_volume,
            max(length) as length,
            max(width) as width,
            max(height) as height,
            max(palletizing_mode) as palletizing_mode,
            max(max_units_per_pick) as max_units_per_pick,
            max(item_shelf_life_in_days) as item_shelf_life_in_days,
            max(preferred_container_type) as preferred_container_type,
            max(velocity_classification) as velocity_classification,
            max(hazard_classification) as hazard_classification,
            max(supplier_code) as supplier_code,
            max(base_uom) as base_uom,
            max(default_pick_uom) as default_pick_uom,
            max(preferred_fulfillment_handling) as preferred_fulfillment_handling,
            max(amcap_qualification_ind) as amcap_qualification_ind,
            max(toppling_rate) as toppling_rate,
            max(active_rec_ind) as active_rec_ind,
            facility,
            source_system
        FROM
            tmp_staging_dim_item
        GROUP by
            item_code,
            item_category_code,
            facility,
            source_system
    ) ti ON di.item_code = ti.item_code
    AND di.item_category_code = ti.item_category_code
    AND di.tenant = @ltarget_tenant
WHEN MATCHED THEN
UPDATE SET
    item_name = COALESCE(ti.item_name, di.item_name),
    item_sku = COALESCE(ti.item_sku, di.item_sku),
    item_product_code = COALESCE(ti.item_product_code, di.item_product_code),
    uom = COALESCE(ti.uom, di.uom),
    client = COALESCE(ti.client, di.client),
    total_weight = COALESCE(ti.total_weight, di.total_weight),
    total_volume = COALESCE(ti.total_volume, di.total_volume),
    length = COALESCE(ti.length, di.length),
    width = COALESCE(ti.width, di.width),
    height = COALESCE(ti.height, di.height),
    palletizing_mode = COALESCE(ti.palletizing_mode, di.palletizing_mode),
    max_units_per_pick = COALESCE(ti.max_units_per_pick, di.max_units_per_pick),
    item_shelf_life_in_days = COALESCE(
        ti.item_shelf_life_in_days,
        di.item_shelf_life_in_days
    ),
    preferred_container_type = COALESCE(
        ti.preferred_container_type,
        di.preferred_container_type
    ),
    velocity_classification = COALESCE(
        ti.velocity_classification,
        di.velocity_classification
    ),
    hazard_classification = COALESCE(
        ti.hazard_classification,
        di.hazard_classification
    ),
    supplier_code = COALESCE(ti.supplier_code, di.supplier_code),
    base_uom = COALESCE(ti.base_uom, di.base_uom),
    default_pick_uom = COALESCE(ti.default_pick_uom, di.default_pick_uom),
    preferred_fulfillment_handling = COALESCE(
        ti.preferred_fulfillment_handling,
        di.preferred_fulfillment_handling
    ),
    amcap_qualification_ind = COALESCE(
        ti.amcap_qualification_ind,
        di.amcap_qualification_ind
    ),
    toppling_rate = COALESCE(ti.toppling_rate, di.toppling_rate),
    active_rec_ind = ti.active_rec_ind,
    etl_batch_id = @etl_batch_uuid
WHEN NOT MATCHED THEN
INSERT
    (
        item_uuid,
        item_code,
        item_category_code,
        item_name,
        item_sku,
        item_product_code,
        uom,
        client,
        total_weight,
        total_volume,
        length,
        width,
        height,
        palletizing_mode,
        max_units_per_pick,
        item_shelf_life_in_days,
        preferred_container_type,
        velocity_classification,
        hazard_classification,
        supplier_code,
        base_uom,
        default_pick_uom,
        preferred_fulfillment_handling,
        amcap_qualification_ind,
        toppling_rate,
        active_rec_ind,
        tenant,
        facility,
        source_system,
        etl_batch_id
    )
VALUES
    (
        `${tenant_id}_oa_curated.Dimension_PK_Generator` (
           [ @ltarget_tenant,
            facility,
            source_system,
            item_code]
            
        ),
        item_code,
        item_category_code,
        item_name,
        item_sku,
        item_product_code,
        uom,
        client,
        total_weight,
        total_volume,
        length,
        width,
        height,
        palletizing_mode,
        max_units_per_pick,
        item_shelf_life_in_days,
        preferred_container_type,
        velocity_classification,
        hazard_classification,
        supplier_code,
        base_uom,
        default_pick_uom,
        preferred_fulfillment_handling,
        amcap_qualification_ind,
        toppling_rate,
        active_rec_ind,
        @ltarget_tenant,
        facility,
        source_system,
        @etl_batch_uuid
    );