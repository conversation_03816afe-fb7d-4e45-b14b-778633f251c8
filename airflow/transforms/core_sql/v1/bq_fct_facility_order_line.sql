/* **Set variables for running outside of DAG in BQ directly** */
/* declare ltarget_tenant string default '${tenant_id}' ; */
/* declare etl_batch_uuid string default '2023-12-12' ; */
/* declare start_query_date timestamp default timestamp ( '2021-09-27 00:00:00' ) ; */
/* declare end_query_date timestamp default timestamp ( '2024-09-27 02:00:00' ) ; */
CREATE TEMP TABLE
  tmp_staging_fct_order_line (
    record_timestamp TIMESTAMP NOT NULL,
    facility_order_code STRING,
    facility_order_line_code STRING,
    requested_item_id STRING,
    item_id STRING,
    uom STRING,
    facility_order_line_event STRING,
    reason_code STRING,
    batch_number STRING,
    completed_qty INT64,
    requested_qty INT64,
    facility_order_owner STRING,
    facility_order_uuid STRING DEFAULT '',
    requested_item_uuid STRING DEFAULT '',
    item_uuid STRING DEFAULT '',
    reason_uuid STRING DEFAULT '',
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING NOT NULL,
  );

INSERT INTO
  tmp_staging_fct_order_line (
    record_timestamp,
    facility_order_code,
    facility_order_line_code,
    requested_item_id,
    item_id,
    uom,
    facility_order_line_event,
    reason_code,
    batch_number,
    completed_qty,
    requested_qty,
    facility_order_owner,
    facility_order_uuid,
    requested_item_uuid,
    item_uuid,
    reason_uuid,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
WITH
  extracted_order_lines AS (
    SELECT
      SAFE_CAST(JSON_VALUE(DATA, '$.eventDateTime') AS TIMESTAMP) AS record_timestamp,
      JSON_VALUE(DATA, '$.facilityOrderId') AS facility_order_code,
      JSON_VALUE(DATA, '$.facilityOrderLineId') AS facility_order_line_code,
      JSON_VALUE(DATA, '$.skuRequested') AS requested_item_id,
      JSON_VALUE(DATA, '$.skuCompleted') AS item_id,
      JSON_VALUE(DATA, '$.skuQuantityUnit') AS uom,
      JSON_VALUE(DATA, '$.event') AS facility_order_line_event,
      JSON_VALUE(DATA, '$.reason') AS reason_code,
      JSON_VALUE(DATA, '$.batchId') AS batch_number,
      SAFE_CAST(JSON_VALUE(DATA, '$.quantityCompleted') AS INT64) AS completed_qty,
      SAFE_CAST(JSON_VALUE(DATA, '$.quantityRequested') AS INT64) AS requested_qty,
      JSON_VALUE(DATA, '$.owner') AS facility_order_owner,
      @ltarget_tenant,
      facility,
      source_system,
      @etl_batch_uuid
    FROM
      `${tenant_id}_landing.FacilityOrderLineFact`
    WHERE
      tenant = @ltarget_tenant
      AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  )
SELECT DISTINCT
  *
FROM
  tmp_staging_fct_order_line;

--DIM_Facility_Order
MERGE
  `${tenant_id}_oa_curated.dim_facility_order` dm USING (
    SELECT DISTINCT
      facility_order_uuid,
      facility_order_code,
      facility_order_owner,
      tenant,
      facility,
      source_system
    FROM
      tmp_staging_fct_order_line
    GROUP BY
      facility_order_code,
      facility_order_owner,
      facility_order_uuid,
      tenant,
      facility,
      source_system
  ) v ON dm.facility_order_code = v.facility_order_code
  AND dm.facility_order_owner = v.facility_order_owner
  AND dm.tenant = v.tenant
  AND dm.facility = v.facility
  AND dm.source_system = v.source_system
WHEN MATCHED THEN
UPDATE SET
  facility_order_uuid = v.facility_order_uuid
WHEN NOT MATCHED THEN
INSERT
  (
    facility_order_uuid,
    facility_order_code,
    facility_order_owner,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [v.tenant,
      v.facility,
      v.source_system,
      v.facility_order_code]
    ),
    v.facility_order_code,
    v.facility_order_owner,
    v.tenant,
    v.facility,
    v.source_system,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_order_line tt
SET
  facility_order_uuid = d.facility_order_uuid
FROM
  (
    select distinct
      facility_order_uuid,
      facility_order_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_facility_order`
  ) d
WHERE
  d.facility_order_code = tt.facility_order_code
  AND d.tenant = tt.tenant
  AND d.facility = tt.facility
  AND d.source_system = tt.source_system;

--DIM_Reason
MERGE
  `${tenant_id}_oa_curated.dim_reason` dr USING (
    SELECT DISTINCT
      reason_code,
      facility,
      source_system
    FROM
      tmp_staging_fct_order_line
    GROUP BY
      reason_code,
      facility,
      source_system
  ) v ON dr.reason_code = v.reason_code
  AND dr.tenant = @ltarget_tenant
  AND dr.facility = v.facility
  AND dr.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    reason_uuid,
    reason_code,
    tenant,
    facility,
    source_system,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.reason_code]
    ),
    v.reason_code,
    @ltarget_tenant,
    v.facility,
    v.source_system,
    1,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_order_line tt
SET
  reason_uuid = d.reason_uuid
FROM
  (
    select distinct
      reason_uuid,
      reason_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_reason`
  ) d
WHERE
  d.tenant = @ltarget_tenant
  AND tt.reason_code = d.reason_code
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

--DIM_Item REQUESTED ITEM
MERGE
  `${tenant_id}_oa_curated.dim_item` di USING (
    SELECT DISTINCT
      requested_item_id,
      MAX(uom) AS uom,
      MAX(facility_order_owner) AS facility_order_owner,
      facility,
      source_system
    FROM
      tmp_staging_fct_order_line
    GROUP BY
      requested_item_id,
      facility,
      source_system
  ) v ON di.item_sku = v.requested_item_id
  AND di.uom = v.uom
  AND di.tenant = @ltarget_tenant
  AND di.facility = v.facility
  AND di.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    item_uuid,
    item_sku,
    uom,
    client,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.requested_item_id]
    ),
    v.requested_item_id,
    v.uom,
    v.facility_order_owner,
    @ltarget_tenant,
    v.facility,
    v.source_system,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_order_line tt
SET
  item_uuid = d.item_uuid
FROM
  (
    select distinct
      item_uuid,
      item_sku,
      uom,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_item`
  ) d
WHERE
  d.tenant = @ltarget_tenant
  AND tt.requested_item_id = d.item_sku
  AND tt.uom = d.uom
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

--DIM_Item RECEIVED ITEM
MERGE
  `${tenant_id}_oa_curated.dim_item` di USING (
    SELECT DISTINCT
      item_id,
      MAX(uom) AS uom,
      MAX(facility_order_owner) AS facility_order_owner,
      facility,
      source_system
    FROM
      tmp_staging_fct_order_line
    GROUP BY
      item_id,
      facility,
      source_system
  ) v ON di.item_sku = v.item_id
  AND di.uom = v.uom
  AND di.tenant = @ltarget_tenant
  AND di.facility = v.facility
  AND di.source_system = v.source_system
  AND di.client = v.facility_order_owner
WHEN NOT MATCHED THEN
INSERT
  (
    item_uuid,
    item_sku,
    uom,
    client,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.item_id]
    ),
    v.item_id,
    v.uom,
    v.facility_order_owner,
    @ltarget_tenant,
    v.facility,
    v.source_system,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_order_line tt
SET
  requested_item_uuid = d.item_uuid
FROM
  (
    select distinct
      item_uuid,
      item_sku,
      uom,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_item`
  ) d
WHERE
  d.tenant = @ltarget_tenant
  AND tt.requested_item_id = d.item_sku
  AND tt.uom = d.uom
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

-- Merging INTO the TARGET Fact TABLE (fct_facility_order_line) 
-- Inserting DATA INTO the TARGET Fact TABLE (fct_facility_order_line)
INSERT INTO
  `${tenant_id}_oa_curated.fct_facility_order_line` (
    record_timestamp,
    facility_order_uuid,
    requested_item_uuid,
    item_uuid,
    reason_uuid,
    facility_order_line_code,
    facility_order_line_event,
    batch_number,
    completed_qty,
    requested_qty,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
SELECT
  record_timestamp,
  facility_order_uuid,
  requested_item_uuid,
  item_uuid,
  reason_uuid,
  facility_order_line_code,
  facility_order_line_event,
  batch_number,
  completed_qty,
  requested_qty,
  tenant,
  facility,
  source_system,
  @etl_batch_uuid
FROM
  tmp_staging_fct_order_line;