-- DECLARE etl_batch_uuid STRING DEFAULT STRING(CURRENT_TIMESTAMP);
-- DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2022-09-27 00:00:00');
-- DECLARE end_query_date TIMES<PERSON>MP DEFAULT TIMESTAMP('2024-09-27 02:00:00');
CREATE TEMP TABLE
  tmp_fct_workstation_workflow (
    record_timestamp TIMESTAMP NOT NULL, -- WorkstationworkflowEntryFact.eventTime
    workstation_code STRING NOT NULL, -- WorkstationworkflowEntryFact.workstationPk
    tenant_name STRING, -- WorkstationworkflowEntryFact.tenantName
    mod_user STRING, -- WorkstationworkflowEntryFact.modUser  
    workflow_status STRING, -- WorkstationworkflowEntryFact.workFlowStatus
    workstation_status STRING, -- WorkstationworkflowEntryFact.workStationStatus
    workflow STRING, -- WorkstationworkflowEntryFact.workflow
    workstation_uuid STRING NOT NULL,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL
  );

INSERT INTO
  tmp_fct_workstation_workflow
WITH
  extracted_data AS (
    SELECT
      JSON_VALUE(data, '$.eventTime') AS event_time,
      JSON_VALUE(data, '$.workstationPk') AS workstation_code,
      JSON_VALUE(data, '$.tenantName') AS tenant_name,
      JSON_VALUE(data, '$.modUser') AS mod_user,
      JSON_VALUE(data, '$.workFlowStatus') AS workflow_status,
      JSON_VALUE(data, '$.workStationStatus') AS workstation_status,
      JSON_VALUE(data, '$.workflow') AS workflow,
      facility,
      source_system,
    FROM
      `${tenant_id}_landing.WorkstationworkFlowEntryFact`
    WHERE
      TIMESTAMP(ingestion_date) BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  normalized_data AS (
    SELECT
      `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp (event_time, NULL) AS record_timestamp,
      workstation_code AS workstation_code,
      COALESCE(tenant_name, '') AS tenant_name,
      COALESCE(mod_user, '') AS mod_user,
      COALESCE(workflow_status, '') AS workflow_status,
      COALESCE(workstation_status, '') AS workstation_status,
      COALESCE(workflow, '') AS workflow,
      '' AS workstation_uuid,
      @ltarget_tenant AS tenant,
      facility AS facility,
      source_system AS source_system,
    FROM
      extracted_data
  )
SELECT
  *
FROM
  normalized_data;

-- Insert new dim data
MERGE INTO
  `${tenant_id}_oa_curated.dim_workstation` dw USING (
    SELECT
      workstation_code,
      tenant,
      facility,
      source_system,
    FROM
      tmp_fct_workstation_workflow
  ) tt ON dw.tenant = tt.tenant
  AND dw.facility = tt.facility
  AND dw.source_system = tt.source_system
  AND dw.workstation_code = tt.workstation_code
WHEN NOT MATCHED THEN
INSERT
  (
    workstation_uuid,
    workstation_code,
    tenant,
    facility,
    source_system
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [
        tt.tenant,
        tt.facility,
        tt.source_system,
        tt.workstation_code
      ]
    ),
    tt.workstation_code,
    tt.tenant,
    tt.facility,
    tt.source_system
  );

-- Update temp table with workstation_uuids
UPDATE tmp_fct_workstation_workflow tt
SET
  workstation_uuid = dw.workstation_uuid
FROM
  (
    select distinct
      workstation_uuid,
      workstation_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_workstation`
  ) dw
WHERE
  dw.workstation_code = tt.workstation_code
  AND dw.facility = tt.facility
  AND dw.source_system = tt.source_system
  AND dw.tenant = tt.tenant;

-- Insert temp table into its fact table
INSERT INTO
  `${tenant_id}_oa_curated.fct_workstation_workflow` (
    workstation_uuid,
    etl_batch_id,
    record_timestamp,
    workstation_code,
    tenant_name,
    mod_user,
    workflow_status,
    workstation_status,
    workflow,
    tenant,
    facility,
    source_system
  )
SELECT
  workstation_uuid,
  @etl_batch_uuid,
  record_timestamp,
  workstation_code,
  tenant_name,
  mod_user,
  workflow_status,
  workstation_status,
  workflow,
  tenant,
  facility,
  source_system
FROM
  tmp_fct_workstation_workflow;