/*
DECLARE start_query_date TIM<PERSON><PERSON><PERSON> DEFAULT TIMESTAMP_TRUNC(TIMESTAMP('2024-03-04 19:12:14.001276'), DAY);
DECLARE end_query_date TIMESTAMP DEFAULT TIMESTAMP('2024-03-04 19:12:14.001276');
DECLARE ltarget_tenant STRING DEFAULT 'qa_manual';
declare etl_batch_uuid string default '2020-12-12';
*/

-- Create a temporary table with predefined columns
CREATE TEMPORARY TABLE temp_operators_online (
    online_operators INT64,
    tenant STRING,
    etl_batch_id STRING,
    record_timestamp TIMESTAMP,
    area STRING,
    facility STRING,
    source_system STRING
);
-- Insert results from the provided query into the temporary table
INSERT INTO temp_operators_online
-- CTEs 
WITH OperatorLogEvents AS (
  SELECT
    operator_uuid,
    area,
    event,
    record_timestamp,
    @ltarget_tenant AS tenant,
    facility,
    source_system
  FROM
    `${tenant_id}_oa_curated.fct_pick_activity`
  WHERE
-- Operators likely to be logged in beyond the ETL interval so consider all records for the current day.
    UPPER(event) LIKE '%LOG%'
    AND record_timestamp BETWEEN TIMESTAMP_TRUNC(@end_query_date, DAY) AND @end_query_date
    AND tenant = @ltarget_tenant
),
LogStatus AS (
  SELECT
    operator_uuid,
    area,
    event,
    record_timestamp,
    tenant,
    facility,
    source_system,
-- This will be diluted down to the last LOGON/LOGOFF so any record of logon can be "1" 
-- and everything else will be discarded in the final two subqueries
    CASE
      WHEN UPPER(event) = 'LOGON' THEN 1
      ELSE 0
    END AS log_status_flag
  FROM
    OperatorLogEvents
),
MostRecentLogStatus AS (
  SELECT
    operator_uuid,
    area,
    log_status_flag,
    tenant,
    facility,
    source_system,
    ROW_NUMBER() OVER (PARTITION BY operator_uuid ORDER BY record_timestamp DESC) AS rn
  FROM
    LogStatus
),
OnlineOperators AS (
  SELECT
    log_status_flag,
    area,
    tenant,
    facility,
    source_system
  FROM
    MostRecentLogStatus
  WHERE
    rn = 1
)

-- Final step: Calculate the sum of operators who are online
SELECT
    COUNT(*) AS online_operators,
    tenant,
    @etl_batch_uuid AS etl_batch_id,
    CURRENT_TIMESTAMP() AS record_timestamp,
    area,
    facility,
    MAX(source_system) AS source_system
FROM OnlineOperators
WHERE
    log_status_flag = 1
GROUP BY
    area,
    tenant,
    facility,
    etl_batch_id;

-- Insert results from the temporary table to the destination table
INSERT INTO `${tenant_id}_oa_curated.agg_operators_online`
SELECT * FROM temp_operators_online;