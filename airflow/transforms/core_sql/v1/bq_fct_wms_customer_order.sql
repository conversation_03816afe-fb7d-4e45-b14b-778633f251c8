/* **Set variables for running outside of DAG in BQ directly** */
/* declare ltarget_tenant string default '${tenant_id}' ; */
/* declare etl_batch_uuid string default '2023-12-12' ; */
/* declare start_query_date timestamp default timestamp ( '2021-09-27 00:00:00' ) ; */
/* declare end_query_date timestamp default timestamp ( '2024-09-27 02:00:00' ) ; */
CREATE TEMP TABLE tmp_staging_fct_wms_order ( 
    date_added TIMESTAMP,
    carrier_id STRING,
    delivery_date TIMESTAMP,
    date_edited TIMESTAMP,
    effective_date TIMESTAMP,
    record_timestamp TIMESTAMP NOT NULL,
    external_id STRING,
    order_date TIMESTAMP,
    order_id STRING,
    order_type STRING,
    priority INT64,
    ship_date TIMESTAMP,
    ship_no_later_date TIMESTAMP,
    short_flag STRING,
    state STRING,
    sts STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING NOT NULL) ;
INSERT INTO tmp_staging_fct_wms_order ( 
    date_added,
    carrier_id,
    delivery_date,
    date_edited,
    effective_date,
    record_timestamp,
    external_id,
    order_date,
    order_id,
    order_type,
    priority,
    ship_date,
    ship_no_later_date,
    short_flag,
    state,
    sts,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
WITH
  extracted_order_info AS (
  SELECT
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.ADDDATE')) AS date_added,
    JSON_VALUE(DATA, '$.CARRIERID') AS carrier_id,
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.DELIVERYDATE')) AS delivery_date,
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.EDITDATE')) AS date_edited,
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.EFFECTIVEDATE')) AS effective_date,
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.EDITDATE')) AS record_timestamp,
    JSON_VALUE(DATA, '$.EXTERNALID') AS external_id,
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.ORDERDATE')) AS order_date,
    JSON_VALUE(DATA, '$.ORDERID') AS order_id,
    JSON_VALUE(DATA, '$.ORDERTYPE') AS order_type,
    SAFE_CAST(JSON_VALUE(DATA, '$.PRIORITY') AS INT64) AS priority,
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.SHIPDATE')) AS ship_date,
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.SHIPNOLATER')) AS ship_no_later_date,
    JSON_VALUE(DATA, '$.SHORTFLG') AS short_flag,
    JSON_VALUE(DATA, '$.STATE') AS state,
    JSON_VALUE(DATA, '$.STS') AS sts,
    @ltarget_tenant,
    facility,
    source_system,
    @etl_batch_uuid
  FROM
    `${tenant_id}_landing.WMSCustomerOrderFact`
  WHERE
    tenant = @ltarget_tenant
    AND ingestion_date BETWEEN TIMESTAMP(@start_query_date)
    AND TIMESTAMP(@end_query_date))
SELECT
  DISTINCT *
FROM
  extracted_order_info;

-- FINAL FCT Insert  
MERGE INTO `${tenant_id}_oa_curated.fct_wms_customer_order` fct USING (
  SELECT * FROM tmp_staging_fct_wms_order
) src 
ON fct.date_added = src.date_added
  AND fct.carrier_id = src.carrier_id
  AND fct.delivery_date = src.delivery_date
  AND fct.date_edited = src.date_edited
  AND fct.effective_date = src.effective_date
  AND fct.record_timestamp = src.record_timestamp
  AND fct.external_id = src.external_id
  AND fct.order_date = src.order_date
  AND fct.order_id = src.order_id
  AND fct.order_type = src.order_type
  AND fct.priority = src.priority
  AND fct.ship_date = src.ship_date
  AND fct.ship_no_later_date = src.ship_no_later_date
  AND fct.short_flag = src.short_flag
  AND fct.state = src.state
  AND fct.sts = src.sts
  AND fct.tenant = src.tenant
  AND fct.facility = src.facility
  AND fct.source_system = src.source_system 
WHEN NOT MATCHED BY TARGET
THEN INSERT
(
    date_added,
    carrier_id,
    delivery_date,
    date_edited,
    effective_date,
    record_timestamp,
    external_id,
    order_date,
    order_id,
    order_type,
    priority,
    ship_date,
    ship_no_later_date,
    short_flag,
    state,
    sts,
    tenant,
    facility,
    source_system,
    etl_batch_id
) VALUES (
    src.date_added,
    src.carrier_id,
    src.delivery_date,
    src.date_edited,
    src.effective_date,
    src.record_timestamp,
    src.external_id,
    src.order_date,
    src.order_id,
    src.order_type,
    src.priority,
    src.ship_date,
    src.ship_no_later_date,
    src.short_flag,
    src.state,
    src.sts,
    src.tenant,
    src.facility,
    src.source_system,
    src.etl_batch_id
);