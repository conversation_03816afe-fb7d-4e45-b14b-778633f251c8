-- DECLARE etl_batch_uuid STRING DEFAULT STRING(CURRENT_TIMESTAMP);
-- DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2022-09-27 00:00:00');
-- DECLARE end_query_date TIMES<PERSON>MP DEFAULT TIMESTAMP('2024-09-27 02:00:00');

CREATE TEMP TABLE tmp_fct_customer_order
(
  -- Fields to match Insights OA.FCT_Customer_Order table
  record_timestamp TIMESTAMP NOT NULL,  -- OrderCreatedFact.eventDate
  customer_order_id STRING NOT NULL,    -- OrderCreatedFact.orderId
  expected_order_line_count INT64,      -- OrderCreatedFact.numberOfLines
  actual_order_line_count INT64,        -- OrderCompletedFact.exactlyDeliveredLines
  actual_container_count INT64,         -- OrderCompletedFact.numberOfLoadUnits
  undelivered_line_count INT64,         -- OrderCompletedFact.notDeliveredLines
  over_delivered_line_count INT64,      -- OrderCompletedFact.overDeliveredLines
  under_delivered_line_count INT64,     -- OrderCompletedFact.underDeliveredLines
  order_complete_date_time TIMESTAMP,   -- OrderCompletedFact.eventDate

  -- Extra fields just for us
  customer_order_uuid STRING NOT NULL,
  tenant STRING,
  facility STRING,
  source_system STRING,
  customer_order_type STRING
);

-- Combined OrderCreatedFact and OrderCompletedFact data
INSERT INTO tmp_fct_customer_order
WITH
  created_data AS (
    SELECT
      TIMESTAMP(JSON_VALUE(data, '$.eventDate')) AS record_timestamp,
      COALESCE(JSON_VALUE(data, '$.orderId'), '') AS customer_order_id,
      -- COALESCE(JSON_VALUE(data, '$.clientId'), '') AS client_id, -- unused
      -- COALESCE(JSON_VALUE(data, '$.tenantName'), '') AS tenant_name, -- unused
      COALESCE(JSON_VALUE(data, '$.orderType'), '') AS customer_order_type,
      SAFE_CAST(JSON_VALUE(data, '$.numberOfLines') AS INT64) AS expected_order_line_count,
      facility,
      source_system,
    FROM `${tenant_id}_landing.OrderCreatedFact`
    WHERE ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  completed_data AS (
    SELECT
      TIMESTAMP(JSON_VALUE(data, '$.eventDate')) AS order_complete_date_time,
      COALESCE(JSON_VALUE(data, '$.orderId'), '') AS customer_order_id,
      -- COALESCE(JSON_VALUE(data, '$.clientId'), '') AS client_id, -- unused
      -- COALESCE(JSON_VALUE(data, '$.tenantName'), '') AS tenant_name, -- unused
      COALESCE(JSON_VALUE(data, '$.orderType'), '') AS customer_order_type,
      SAFE_CAST(JSON_VALUE(data, '$.overdeliveredLines') AS INT64) AS over_delivered_line_count,
      SAFE_CAST(JSON_VALUE(data, '$.notDeliveredLines') AS INT64) AS undelivered_line_count,
      SAFE_CAST(JSON_VALUE(data, '$.underdeliveredLines') AS INT64) AS under_delivered_line_count,
      SAFE_CAST(JSON_VALUE(data, '$.exactlyDeliveredLines') AS INT64) AS actual_order_line_count,
      SAFE_CAST(JSON_VALUE(data, '$.numberOfLoadUnits') AS INT64) AS actual_container_count,
      facility,
      source_system
    FROM `${tenant_id}_landing.OrderCompletedFact`
    WHERE ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  )
SELECT
  cre.record_timestamp,
  cre.customer_order_id,
  cre.expected_order_line_count,
  com.actual_order_line_count,
  com.actual_container_count,
  com.undelivered_line_count,
  com.over_delivered_line_count,
  com.under_delivered_line_count,
  com.order_complete_date_time,
  '' AS customer_order_uuid,
  @ltarget_tenant AS tenant,
  COALESCE(cre.facility, com.facility, '') AS facility,
  COALESCE(cre.source_system, com.source_system, '') AS source_system,
  COALESCE(cre.customer_order_type, com.customer_order_type, '') AS customer_order_type,
FROM created_data cre
JOIN completed_data com
  ON cre.customer_order_id = com.customer_order_id;

-- Insert new dim data
MERGE `${tenant_id}_oa_curated.dim_customer_order` d
USING (
  SELECT
    customer_order_id,
    tenant,
    facility,
    source_system,
    customer_order_type,
  FROM tmp_fct_customer_order
) tt
ON d.customer_order_code = tt.customer_order_id
  AND d.tenant = tt.tenant
  AND d.facility = tt.facility
  AND d.source_system = tt.source_system
  AND d.customer_order_type = tt.customer_order_type
WHEN NOT MATCHED THEN
  INSERT (
    customer_order_uuid,
    etl_batch_id,
    customer_order_code,
    tenant,
    facility,
    source_system,
    customer_order_type
  ) VALUES (
    `${tenant_id}_oa_curated.Dimension_PK_Generator`([
      tt.tenant,
      tt.facility,
      tt.source_system,
      tt.customer_order_id
    ]),
    @etl_batch_uuid,
    tt.customer_order_id,
    tt.tenant,
    tt.facility,
    tt.source_system,
    tt.customer_order_type
  );

-- Update temp table with customer_order_uuids
UPDATE tmp_fct_customer_order tt
SET customer_order_uuid = d.customer_order_uuid
FROM `${tenant_id}_oa_curated.dim_customer_order` d
WHERE d.customer_order_code = tt.customer_order_id
  AND d.tenant = tt.tenant
  AND d.facility = tt.facility
  AND d.source_system = tt.source_system
  AND d.customer_order_type = tt.customer_order_type;

-- Insert temp table into its fact table
INSERT INTO `${tenant_id}_oa_curated.fct_customer_order`
(
  customer_order_uuid,
  etl_batch_id,
  record_timestamp,
  customer_order_code,
  expected_order_line_count,
  actual_order_line_count,
  actual_container_count,
  undelivered_line_count,
  over_delivered_line_count,
  under_delivered_line_count,
  order_complete_date_time
)
SELECT
  customer_order_uuid,
  @etl_batch_uuid,
  record_timestamp,
  customer_order_id,
  expected_order_line_count,
  actual_order_line_count,
  actual_container_count,
  undelivered_line_count,
  over_delivered_line_count,
  under_delivered_line_count,
  order_complete_date_time,
FROM tmp_fct_customer_order;
