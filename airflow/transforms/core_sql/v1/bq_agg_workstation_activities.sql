-- This script populates the agg_workstation_activities table every time the DAG runs
/*
<PERSON><PERSON><PERSON>E ltarget_tenant STRING DEFAULT '${tenant_id}';
DECLARE etl_batch_uuid STRING DEFAULT '2020-12-12';
DECLARE start_query_date TIM<PERSON><PERSON><PERSON> DEFAULT TIMESTAMP('2022-09-27 00:00:00');
DECLARE end_query_date TIMES<PERSON>MP DEFAULT TIMESTAMP('2024-09-27 02:00:00');
*/
CREATE TEMP TABLE temp_workstation_combined_activities
(
    starvation_time INT64,
    blocking_time INT64,
    event STRING,
    next_event STRING,
    load_unit_code STRING,
    next_load_unit_code STRING,
    load_unit_type STRING,
    next_load_unit_type STRING,
    record_timestamp TIMESTAMP,
    next_record_timestamp TIMESTAMP,
    workstation_uuid STRING,
    next_workstation_uuid STRING,
    operator_uuid STRING,
    facility STRING,
    source_system STRING,
    tenant STRING,
    etl_batch_id STRING
);

CREATE TEMP TABLE temp_workstation_donor_activities
(
    starvation_time INT64,
    blocking_time INT64,
    event STRING,
    next_event STRING,
    load_unit_code STRING,
    next_load_unit_code STRING,
    load_unit_type STRING,
    next_load_unit_type STRING,
    record_timestamp TIMESTAMP,
    next_record_timestamp TIMESTAMP,
    workstation_uuid STRING,
    next_workstation_uuid STRING,
    operator_uuid STRING,
    facility STRING,
    source_system STRING,
    tenant STRING,
    etl_batch_id STRING
);

CREATE TEMP TABLE temp_workstation_order_activities
(
    starvation_time INT64,
    blocking_time INT64,
    event STRING,
    next_event STRING,
    load_unit_code STRING,
    next_load_unit_code STRING,
    load_unit_type STRING,
    next_load_unit_type STRING,
    record_timestamp TIMESTAMP,
    next_record_timestamp TIMESTAMP,
    workstation_uuid STRING,
    next_workstation_uuid STRING,
    operator_uuid STRING,
    facility STRING,
    source_system STRING,
    tenant STRING,
    etl_batch_id STRING
);

CREATE TEMP TABLE temp_activities
(
    event STRING,
    load_unit_code STRING,
    load_unit_type STRING,
    record_timestamp TIMESTAMP,
    workstation_uuid STRING,
    operator_uuid STRING,
    facility STRING,
    source_system STRING,
    tenant STRING,
    etl_batch_id STRING
);


INSERT INTO temp_activities(

WITH 
--- Establish the listing of load units and determine their purpose from FCT_Order_Line
loadUnitTypes as (
  SELECT container_physical_code as source_load_unit, 
  pick_to_container_physical_code as destination_load_unit, 
  record_timestamp, 
  workstation_uuid, 
  operator_uuid 
  FROM `${tenant_id}_oa_curated.fct_order_line` 
  WHERE record_timestamp > TIMESTAMP_SUB(@end_query_date, INTERVAL 2 HOUR) AND record_timestamp < @end_query_date), 

loadUnitActivity as (
--- Identify the load unit type associated with the load unit and event happening
  SELECT UPPER(event) as event,  
  CASE 
    WHEN load_unit_code IS NOT NULL AND load_unit_code != '' THEN load_unit_code
    WHEN container_instance_code IS NOT NULL AND container_instance_code != '' THEN container_instance_code
    ELSE '' 
  END AS load_unit_code,
  CASE 
    WHEN UPPER(load_unit_usage_type_code) = 'DONOR' THEN 'DONOR'
    WHEN UPPER(load_unit_usage_type_code) = 'ORDER' THEN 'ORDER'
    WHEN load_unit_code != '' AND load_unit_code 
      IN (SELECT source_load_unit FROM loadUnitTypes WHERE source_load_unit IS NOT NULL ORDER BY record_timestamp) 
      THEN 'DONOR'
    WHEN load_unit_code != '' AND load_unit_code 
      IN (SELECT destination_load_unit FROM loadUnitTypes WHERE destination_load_unit IS NOT NULL ORDER BY record_timestamp) 
      THEN 'ORDER'
    ELSE ''
  END AS load_unit_type,
  record_timestamp, 
  workstation_uuid,
  operator_uuid, 
  facility,
  source_system,
  @ltarget_tenant AS tenant,
  @etl_batch_uuid AS etl_batch_id
  FROM (
    SELECT DISTINCT 
      record_timestamp, 
      operator_uuid, 
      module_uuid, 
      work_area_uuid, 
      zone_uuid, 
      workstation_uuid, 
      technology_uuid, 
      work_type_uuid, 
      container_type_uuid, 
      load_unit_code, 
      event, 
      sku_code,
      cart_code, 
      container_instance_code, 
      cart_instance_code, 
      induct_type, 
      cluster_train_code, 
      induction_zone_code, 
      load_unit_usage_type_code, 
      tenant, 
      facility, 
      source_system 
    FROM `${tenant_id}_oa_curated.fct_pick_activity` 
  WHERE record_timestamp > TIMESTAMP_SUB(@end_query_date, INTERVAL 1 HOUR) AND record_timestamp < @end_query_date))
  SELECT * FROM loadUnitActivity);

INSERT INTO temp_workstation_donor_activities (

WITH donorLUActivity as (
  SELECT event,
  COALESCE(LEAD(event) OVER (PARTITION BY workstation_uuid ORDER BY record_timestamp), '') as next_event,
  load_unit_code,
  COALESCE(LEAD(load_unit_code) OVER (PARTITION BY workstation_uuid ORDER BY record_timestamp),'') as next_load_unit_code,
  load_unit_type,
  COALESCE(LEAD(load_unit_type) OVER (PARTITION BY workstation_uuid ORDER BY record_timestamp), '') as next_load_unit_type,
  record_timestamp,
  LEAD(record_timestamp) OVER (PARTITION BY workstation_uuid ORDER BY record_timestamp) as next_record_timestamp,
  workstation_uuid,
  COALESCE(LEAD(workstation_uuid) OVER (PARTITION BY workstation_uuid ORDER BY record_timestamp), '') as next_workstation_uuid,
  operator_uuid,
  facility,
  source_system,
  tenant,
  etl_batch_id
  FROM temp_activities 
  WHERE load_unit_type = 'DONOR' OR event IN ('LOGON', 'LOGOFF')),

loadUnitFlow AS (
  SELECT
--- Starvation time: Operator is at a GTP workstation, there is no work available (no donor tote at their station, no empty order tote at the station)
  CASE
    WHEN
    (event = 'LOGON' AND next_event = 'ARRIVAL')  OR
    (event = 'DEPARTURE' AND next_event IN ('LOGOFF', 'ARRIVAL')) OR
    (event = 'DEPARTURE' AND (next_event IS NULL OR next_event = ''))
    THEN LEAST(15, TIMESTAMP_DIFF(COALESCE(next_record_timestamp, @end_query_date), record_timestamp, MINUTE))
  ELSE 0
END AS starvation_time,

--- Blocking time: Donor tote has been completed at the GTP workstation, the operator has indicated the tote is complete, but the tote cannot leave the station
  CASE
    WHEN 
    (event = 'LOGON' AND next_event = 'DEPARTURE') OR
    (event = 'RELEASE'AND next_event IN ('LOGOFF', 'ARRIVAL'))  OR
    (event = 'RELEASE'AND next_event = 'DEPARTURE' AND load_unit_code = next_load_unit_code) OR
    (event = 'RELEASE' AND (next_event IS NULL OR next_event = ''))
    THEN LEAST(15, TIMESTAMP_DIFF(COALESCE(next_record_timestamp, @end_query_date), record_timestamp, MINUTE))
  ELSE 0
END AS blocking_time, 
    event,
    next_event,
    load_unit_code,
    next_load_unit_code,
    CASE 
      WHEN event LIKE 'LOG%' THEN 'DONOR'
      ELSE load_unit_type
    END AS load_unit_type,
    CASE 
      WHEN next_event LIKE 'LOG%' THEN 'DONOR'
      ELSE next_load_unit_type
    END AS next_load_unit_type,
    record_timestamp,
    next_record_timestamp,
    workstation_uuid,
    next_workstation_uuid,
    operator_uuid,
    facility,
    source_system,
    tenant,
    etl_batch_id 
  FROM donorLUActivity)

SELECT DISTINCT * FROM loadUnitFlow);

INSERT INTO temp_workstation_order_activities (

--- Track load unit events for order LU's
WITH 
orderLUActivity as (
  SELECT event,
  COALESCE(LEAD(event) OVER (PARTITION BY workstation_uuid ORDER BY record_timestamp), '') as next_event,
  load_unit_code,
  COALESCE(LEAD(load_unit_code) OVER (PARTITION BY workstation_uuid ORDER BY record_timestamp), '') as next_load_unit_code,
  load_unit_type,
  COALESCE(LEAD(load_unit_type) OVER (PARTITION BY workstation_uuid ORDER BY record_timestamp), '') as next_load_unit_type,
  record_timestamp,
  LEAD(record_timestamp) OVER (PARTITION BY workstation_uuid ORDER BY record_timestamp) as next_record_timestamp,
  workstation_uuid,
  COALESCE(LEAD(workstation_uuid) OVER (PARTITION BY workstation_uuid ORDER BY record_timestamp), '') as next_workstation_uuid,
  operator_uuid,
  facility,
  source_system,
  tenant,
  etl_batch_id
  FROM temp_activities 
  WHERE load_unit_type = 'ORDER' OR event IN ('LOGON', 'LOGOFF')),

loadUnitFlow AS (
  SELECT
--- Starvation time: Operator is at a GTP workstation, there is no work available (no donor tote at their station, no empty order tote at the station)
  CASE
    WHEN
    (event = 'LOGON' AND next_event = 'ARRIVAL')  OR
    (event = 'DEPARTURE' AND next_event IN ('LOGOFF', 'ARRIVAL')) OR
    (event = 'DEPARTURE' AND (next_event IS NULL OR next_event = ''))
    THEN LEAST(15, TIMESTAMP_DIFF(COALESCE(next_record_timestamp, @end_query_date), record_timestamp, MINUTE))
  ELSE 0
END AS starvation_time,

--- Blocking time: Applies only to DONOR totes
  0 AS blocking_time, 
    event,
    next_event,
    load_unit_code,
    next_load_unit_code,
    CASE 
      WHEN event LIKE 'LOG%' THEN 'ORDER'
      ELSE load_unit_type
    END AS load_unit_type,
    CASE 
      WHEN next_event LIKE 'LOG%' THEN 'ORDER'
      ELSE next_load_unit_type
    END AS next_load_unit_type,
    record_timestamp,
    next_record_timestamp,
    workstation_uuid,
    next_workstation_uuid,
    operator_uuid,
    facility,
    source_system,
    tenant,
    etl_batch_id FROM orderLUActivity)

SELECT DISTINCT * FROM loadUnitFlow);

INSERT INTO temp_workstation_combined_activities 
  SELECT DISTINCT * FROM temp_workstation_donor_activities UNION ALL SELECT DISTINCT * FROM temp_workstation_order_activities;

MERGE INTO
    `${tenant_id}_oa_curated.agg_workstation_activities` agg USING (
        SELECT DISTINCT
            starvation_time,
            blocking_time,
            event,
            next_event,
            load_unit_code,
            next_load_unit_code,
            load_unit_type,
            next_load_unit_type,
            record_timestamp,
            next_record_timestamp,
            workstation_uuid,
            next_workstation_uuid,
            operator_uuid,
            facility,
            source_system,
            @ltarget_tenant AS tenant,
            etl_batch_id
        FROM
            temp_workstation_combined_activities
    ) src ON agg.event = src.event
    AND agg.load_unit_code = src.load_unit_code
    AND agg.load_unit_type = src.load_unit_type
    AND agg.record_timestamp = src.record_timestamp
    AND agg.workstation_uuid = src.workstation_uuid
    AND agg.operator_uuid = src.operator_uuid
    AND agg.facility = src.facility
    AND agg.source_system = src.source_system
    AND agg.tenant = src.tenant
    WHEN NOT MATCHED THEN
INSERT
    (
        starvation_time,
        blocking_time,
        event,
        next_event,
        load_unit_code,
        next_load_unit_code,
        load_unit_type,
        next_load_unit_type,
        record_timestamp,
        next_record_timestamp,
        workstation_uuid,
        next_workstation_uuid,
        operator_uuid,
        facility,
        source_system,
        tenant,
        etl_batch_id
    )
VALUES
    (
        src.starvation_time,
        src.blocking_time,
        src.event,
        src.next_event,
        src.load_unit_code,
        src.next_load_unit_code,
        src.load_unit_type,
        src.next_load_unit_type,
        src.record_timestamp,
        src.next_record_timestamp,
        src.workstation_uuid,
        src.next_workstation_uuid,
        src.operator_uuid,
        src.facility,
        src.source_system,
        src.tenant,
        src.etl_batch_id
    )
WHEN MATCHED AND (
    agg.next_event != src.next_event
    OR agg.starvation_time != src.starvation_time
    OR agg.blocking_time != src.blocking_time
    OR agg.next_load_unit_code != src.next_load_unit_code
    OR agg.next_load_unit_type != src.next_load_unit_type
    OR agg.next_workstation_uuid != src.next_workstation_uuid
  )
THEN
UPDATE SET
    agg.starvation_time = src.starvation_time,
    agg.blocking_time = src.blocking_time,
    agg.next_event = src.next_event,
    agg.next_load_unit_code = src.next_load_unit_code,
    agg.next_load_unit_type = src.next_load_unit_type,
    agg.next_record_timestamp = src.next_record_timestamp,
    agg.next_workstation_uuid = src.next_workstation_uuid,
    agg.etl_batch_id = src.etl_batch_id
;