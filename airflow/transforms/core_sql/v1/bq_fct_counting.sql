-- declare ltarget_tenant string default '${tenant_id}';
-- declare etl_batch_uuid string default '2024-01-02';
-- declare start_query_date timestamp default timestamp('2022-09-27 00:00:00');
-- declare end_query_date timestamp default timestamp('2024-09-27 02:00:00');

CREATE TEMP TABLE tmp_fct_counting (
  -- Fact Fields
  record_timestamp TIMESTAMP,             -- CountingFact.eventDate
  expected_quantity INT64,                -- CountingFact.expectedQuantity
  handling_unit_b_id STRING,              -- CountingFact.handlingUnitBid
  invalid BOOL,                           -- CountingFact.invalid
  inventory_audit_execution_type STRING,  -- CountingFact.inventoryAuditExecutionType
  owner_b_id STRING,                      -- CountingFact.ownerBid
  item_code STRING,                       -- CountingFact.skuBidPart
  workstation_b_id STRING,                -- CountingFact.workstationBid

  item_category_code STRING,

  -- Relational Fields
  item_uuid STRING,

  -- Meta data
  tenant STRING,
  facility STRING,
  source_system STRING,
  etl_batch_id STRING,
);

INSERT INTO tmp_fct_counting
WITH
  extracted_data AS (
    SELECT
      JSON_VALUE(data, '$.eventDate') AS record_timestamp,
      JSON_VALUE(data, '$.expectedQuantity') AS expected_quantity,
      JSON_VALUE(data, '$.handlingUnitBid') AS handling_unit_b_id,
      JSON_VALUE(data, '$.invalid') AS invalid,
      JSON_VALUE(data, '$.inventoryAuditExecutionType') AS inventory_audit_execution_type,
      JSON_VALUE(data, '$.ownerBid') AS owner_b_id,
      JSON_VALUE(data, '$.skuBidPart') AS item_code,
      JSON_VALUE(data, '$.workstationBid') AS workstation_b_id,
      facility,
      source_system,
    FROM `${tenant_id}_landing.CountingFact`
    WHERE ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  normalized_data AS (
    SELECT
      `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(record_timestamp, NULL) AS record_timestamp,
      SAFE_CAST(expected_quantity AS INT64) AS expected_quantity,
      handling_unit_b_id,
      SAFE_CAST(invalid AS BOOL) AS invalid,
      inventory_audit_execution_type,
      owner_b_id,
      item_code,
      COALESCE(workstation_b_id, '') AS workstation_b_id,
      '' AS item_uuid,
      '' AS item_category_code,
      facility,
      source_system,
    FROM extracted_data
  )
SELECT
  record_timestamp,
  expected_quantity,
  handling_unit_b_id,
  invalid,
  inventory_audit_execution_type,
  owner_b_id,
  item_code,
  workstation_b_id,
  item_uuid,
  item_category_code,
  @ltarget_tenant AS tenant,
  facility,
  source_system,
  @etl_batch_uuid AS etl_batch_id,
FROM normalized_data;

-- Insert new dim_item data
MERGE `${tenant_id}_oa_curated.dim_item` di
USING (
  SELECT
    item_category_code,
    item_code,
    tenant,
    facility,
    source_system,
    etl_batch_id,
  FROM (
    SELECT
      *,
      -- Remove duplicate item_codes, keeping only the most recent record
      ROW_NUMBER() OVER (PARTITION BY item_code ORDER BY record_timestamp DESC) AS row_num
    FROM tmp_fct_counting
  ) AS sq
  WHERE row_num = 1
) tt
ON di.item_category_code = tt.item_category_code
  AND di.item_code = tt.item_code
  AND di.tenant = tt.tenant
  AND di.facility = tt.facility
  AND di.source_system = tt.source_system
WHEN NOT MATCHED THEN
  INSERT (
    item_uuid,
    etl_batch_id,
    item_category_code,
    item_code,
    tenant,
    facility,
    source_system
  ) VALUES (
    `${tenant_id}_oa_curated.Dimension_PK_Generator`([
      tt.tenant,
      tt.facility,
      tt.source_system,
      tt.item_code
    ]),
    tt.etl_batch_id,
    tt.item_category_code,
    tt.item_code,
    tt.tenant,
    tt.facility,
    tt.source_system
  );

-- Update temp table with item_uuids
UPDATE tmp_fct_counting tt
SET item_uuid = di.item_uuid
FROM `${tenant_id}_oa_curated.dim_item` di
WHERE di.item_category_code = tt.item_category_code
  AND di.item_code = tt.item_code
  AND di.tenant = tt.tenant
  AND di.facility = tt.facility
  AND di.source_system = tt.source_system;

-- Insert new counting facts from temp table into fct_counting
MERGE INTO `${tenant_id}_oa_curated.fct_counting` fc
USING (SELECT * FROM tmp_fct_counting) tt
ON fc.item_category_code = tt.item_category_code
  AND fc.item_code = tt.item_code
  AND fc.tenant = tt.tenant
  AND fc.facility = tt.facility
  AND fc.source_system = tt.source_system
WHEN NOT MATCHED THEN INSERT
(
  record_timestamp,
  expected_quantity,
  handling_unit_b_id,
  invalid,
  inventory_audit_execution_type,
  owner_b_id,
  item_code,
  workstation_b_id,
  item_uuid,
  item_category_code,
  tenant,
  facility,
  source_system,
  etl_batch_id
) VALUES (
  tt.record_timestamp,
  tt.expected_quantity,
  tt.handling_unit_b_id,
  tt.invalid,
  tt.inventory_audit_execution_type,
  tt.owner_b_id,
  tt.item_code,
  tt.workstation_b_id,
  tt.item_uuid,
  tt.item_category_code,
  tt.tenant,
  tt.facility,
  tt.source_system,
  tt.etl_batch_id
)
