/*
DECLARE ltarget_tenant STRING DEFAULT 'dematic';
DECLARE etl_batch_uuid STRING DEFAULT '2020-12-12';
DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2023-09-27 00:00:00');
DECLARE end_query_date TIM<PERSON><PERSON><PERSON> DEFAULT TIMESTAMP('2024-09-27 02:00:00');
*/

/* Creating the temporary staging table for AdviceLineBasedCreateInventoryFact data */
CREATE TEMP TABLE tmp_staging_fct_advice_line_inventory
(
    record_timestamp TIMESTAMP,
    receiving_advice_owner STRING,
    receiving_advice_code STRING,
    advice_line_code STRING,
    originator STRING,
    receiving_advice_type STRING,
    supplier STRING,
    handling_unit_code STRING,
    handling_unit_type STRING,
    quantity INT64,
    sku_code STRING,
    packaging_level_code STRING,
    source_system STRING,
    etl_batch_id STRING,
    tenant STRING,
    facility STRING,
    receiving_advice_uuid STRING,
    container_type_uuid STRING,
    item_uuid STRING,
    start_time TIMESTAMP DEFAULT SAFE_CAST(NULL AS TIMESTAMP),
    active_rec_ind INT64,
    item_category_code STRING
);

/* Inserting data into the temporary staging table */
INSERT INTO tmp_staging_fct_advice_line_inventory
SELECT *,
       MAX(record_timestamp) OVER (PARTITION BY receiving_advice_code) AS start_time,
       1 AS active_rec_ind,
       '' AS item_category_code
FROM (
SELECT
    CASE
        WHEN JSON_VALUE(data, '$.eventTime') IS NOT NULL
            THEN `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(JSON_VALUE(data, '$.eventTime'), NULL)
        WHEN JSON_VALUE(data, '$.eventDate') IS NOT NULL
            THEN `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(JSON_VALUE(data, '$.eventDate'), NULL)
        ELSE SAFE_CAST('' AS TIMESTAMP) -- Fallback if neither field is present
    END AS record_timestamp,
    COALESCE(JSON_VALUE(data, '$.ownerBid'), '') AS receiving_advice_owner,
    COALESCE(JSON_VALUE(data, '$.adviceBid'), '') AS receiving_advice_code,
    COALESCE(JSON_VALUE(data, '$.adviceLineBid'), '') AS advice_line_code,
    COALESCE(JSON_VALUE(data, '$.originator'), '') AS originator,
    COALESCE(JSON_VALUE(data, '$.adviceType'), '') AS receiving_advice_type,
    COALESCE(JSON_VALUE(data, '$.supplierId'), '') AS supplier,
    COALESCE(JSON_VALUE(data, '$.handlingUnitId'), '') AS handling_unit_code,
    COALESCE(JSON_VALUE(data, '$.handlingUnitType'), '') AS handling_unit_type,
    SAFE_CAST(JSON_VALUE(data, '$.quantity') AS INT64) AS quantity,
    COALESCE(JSON_VALUE(data, '$.skuBidPart'), '') AS sku_code,
    COALESCE(JSON_VALUE(data, '$.packagingLevel'), '') AS packaging_level_code,
    source_system,
    @etl_batch_uuid AS etl_batch_id,
    @ltarget_tenant AS tenant,
    facility,
    '' AS receiving_advice_uuid,
    '' AS  container_type_uuid,
    '' AS item_uuid
FROM `${tenant_id}_landing.AdviceLineBasedCreateInventoryFact`
WHERE ingestion_date BETWEEN @start_query_date AND @end_query_date
AND tenant = @ltarget_tenant
);

/* DIM_Receiving_Advice Merge */
MERGE INTO `${tenant_id}_oa_curated.dim_receiving_advice` dra 
USING (
    SELECT DISTINCT 
        receiving_advice_uuid,
        receiving_advice_code,
        receiving_advice_owner,
        receiving_advice_type,
        supplier,
        source_system,
        facility,
        MAX(start_time) AS start_time,
        @ltarget_tenant AS tenant,
        active_rec_ind
    FROM tmp_staging_fct_advice_line_inventory
    GROUP BY receiving_advice_uuid,
             receiving_advice_code,
             receiving_advice_owner,
             receiving_advice_type,
             supplier,
             active_rec_ind,
             tenant, facility, source_system
) AS v
ON  dra.receiving_advice_code = v.receiving_advice_code
AND dra.receiving_advice_owner = v.receiving_advice_owner
AND dra.receiving_advice_type = v.receiving_advice_type
AND dra.supplier = v.supplier
AND dra.tenant = v.tenant
AND dra.facility = v.facility
AND dra.source_system = v.source_system
WHEN MATCHED THEN 
    UPDATE SET
        dra.active_rec_ind = v.active_rec_ind
WHEN NOT MATCHED THEN 
    INSERT (receiving_advice_uuid, receiving_advice_code, receiving_advice_owner, receiving_advice_type, supplier, source_system, active_rec_ind, start_time, etl_batch_id, tenant, facility)
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.receiving_advice_code]
        ),v.receiving_advice_code, v.receiving_advice_owner,v.receiving_advice_type,v.supplier, v.source_system, v.active_rec_ind, v.start_time, @etl_batch_uuid, v.tenant, v.facility);

/* Update UUIDs in the staging table for DIM_Receiving_Advice */
UPDATE tmp_staging_fct_advice_line_inventory tt
SET tt.receiving_advice_uuid = dra.receiving_advice_uuid
FROM `${tenant_id}_oa_curated.dim_receiving_advice` dra
WHERE tt.receiving_advice_code = dra.receiving_advice_code
AND tt.receiving_advice_owner = dra.receiving_advice_owner
AND tt.receiving_advice_type = dra.receiving_advice_type
AND tt.supplier = dra.supplier
AND tt.active_rec_ind = dra.active_rec_ind
AND tt.tenant = dra.tenant
AND tt.facility = dra.facility
AND tt.source_system = dra.source_system;

/* Merge into DIM_Container_Type */
MERGE INTO `${tenant_id}_oa_curated.dim_container_type` dct 
USING (
    SELECT DISTINCT 
        container_type_uuid,
        handling_unit_type AS container_type_code,
        source_system,
        @etl_batch_uuid AS etl_batch_id,
        @ltarget_tenant AS tenant,
        facility
    FROM tmp_staging_fct_advice_line_inventory
    GROUP BY handling_unit_type, container_type_uuid, facility, source_system
) AS v
ON dct.container_type_code = v.container_type_code
AND dct.facility = v.facility
AND dct.source_system = v.source_system
WHEN NOT MATCHED THEN 
    INSERT (container_type_uuid, container_type_code, source_system, etl_batch_id, tenant, facility)
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.container_type_code]
        ), v.container_type_code, v.source_system, @etl_batch_uuid, v.tenant, v.facility);

/* Update UUIDs in the staging table for DIM_Container_Type */
UPDATE tmp_staging_fct_advice_line_inventory tt
SET tt.container_type_uuid = dct.container_type_uuid
FROM
    (
        select distinct
            container_type_uuid,
            container_type_code,
            tenant,
            facility,
            source_system
        from   
        `${tenant_id}_oa_curated.dim_container_type`) dct
WHERE tt.handling_unit_type = dct.container_type_code
AND tt.tenant = dct.tenant
AND tt.facility = dct.facility
AND tt.source_system = dct.source_system;

/* Merge into DIM_Item */
MERGE INTO `${tenant_id}_oa_curated.dim_item` di
USING (
    SELECT DISTINCT
        item_uuid,
        item_category_code, 
        sku_code AS item_code,
        source_system,
        @etl_batch_uuid AS etl_batch_id,
        @ltarget_tenant AS tenant,
        facility
    FROM tmp_staging_fct_advice_line_inventory
    GROUP BY sku_code, item_uuid, item_category_code, facility, source_system
) AS v
ON di.item_code = v.item_code
AND di.tenant = @ltarget_tenant
AND di.facility = v.facility
AND di.source_system = v.source_system
WHEN NOT MATCHED THEN 
    INSERT (item_uuid, item_code, item_category_code, source_system, etl_batch_id, tenant, facility)
    VALUES (`${tenant_id}_oa_curated.Dimension_PK_Generator` (
            [@ltarget_tenant,
            v.facility,
            v.source_system,
            v.item_code]
        ), v.item_code, v.item_category_code, v.source_system, @etl_batch_uuid, v.tenant, v.facility);

/* Update UUIDs in the staging table for DIM_Item */
UPDATE tmp_staging_fct_advice_line_inventory tt
SET tt.item_uuid = di.item_uuid
FROM
    (
        select distinct
            item_uuid,
            item_sku,
            item_code,
            tenant,
            facility,
            source_system
        from 
         `${tenant_id}_oa_curated.dim_item`) di
WHERE tt.sku_code = di.item_code
AND @ltarget_tenant = di.tenant
AND tt.facility = di.facility
AND tt.source_system = di.source_system;

-- FINAL FCT Insert
INSERT INTO `${tenant_id}_oa_curated.fct_receiving_advice_line` (
    receiving_advice_uuid,
    receiving_advice_line_code,
    item_uuid,
    handling_unit_code,
    container_type_uuid,
    receiving_advice_qty,
    originator,
    packaging_level_code,
    source_system,
    etl_batch_id,
    tenant,
    facility,
    record_timestamp,
    supplier_id,
    sku_id
)
SELECT DISTINCT
    receiving_advice_uuid,
    advice_line_code AS receiving_advice_line_code,
    item_uuid,
    handling_unit_code,
    container_type_uuid,
    quantity AS receiving_advice_qty,
    originator,
    packaging_level_code,
    source_system,
    @etl_batch_uuid AS etl_batch_id,
    tenant,
    facility,
    record_timestamp,
    supplier,
    sku_code
FROM tmp_staging_fct_advice_line_inventory;
