/*
DECLARE tenant_id STRING DEFAULT 'dematic';
DECLARE etl_batch_uuid STRING DEFAULT STRING(CURRENT_TIMESTAMP);
DECLARE start_query_date TIMESTAMP DEFAULT TIMESTAMP('2022-09-27 00:00:00');
DECLARE end_query_date TIM<PERSON><PERSON><PERSON> DEFAULT TIMESTAMP('2022-09-27 02:00:00');
*/

-- Creating the temporary staging table for Power Chain Telemetry data
CREATE TEMP TABLE tmp_staging_fct_power_chain_telemetry
(
    fact_type STRING,
    file STRING,
    telemetry_type STRING,
    tel_incident_id STRING,
    tel_type_processing_ms FLOAT64,
    tel_type_size_bytes INT64,
    tel_type_size_lines INT64,
    record_timestamp TIMESTAMP,
    tenant STRING,
    facility STRING,
    source_system STRING,
    etl_batch_id STRING
);

-- Inserting data into the temporary staging table for Power Chain Telemetry
INSERT INTO tmp_staging_fct_power_chain_telemetry
SELECT
    JSON_VALUE(data, '$.facttype') AS fact_type, 
    JSON_VALUE(data, '$.file') AS file,
    JSON_VALUE(data, '$.telemetryType') AS telemetry_type,
    JSON_VALUE(data, '$.telIncidentId') AS tel_incident_id,
    SAFE_CAST(JSON_VALUE(data, '$.telType_processing_ms') AS FLOAT64) AS tel_type_processing_ms,
    SAFE_CAST(JSON_VALUE(data, '$.telType_size_bytes') AS INT64) AS tel_type_size_bytes,
    SAFE_CAST(JSON_VALUE(data, '$.telType_size_lines') AS INT64) AS tel_type_size_lines,
    SAFE_CAST(JSON_VALUE(data, '$.timestamp') AS TIMESTAMP) AS record_timestamp,
    @ltarget_tenant AS tenant,
    facility,
    source_system,
    @etl_batch_uuid AS etl_batch_id
FROM `${tenant_id}_landing.PowerChainTelemetry`
WHERE ingestion_date BETWEEN @start_query_date AND @end_query_date
and JSON_VALUE(data, '$.facttype') is not null;

-- FINAL FCT Insert
INSERT INTO `${tenant_id}_oa_curated.fct_power_chain_telemetry`
SELECT * FROM tmp_staging_fct_power_chain_telemetry;
