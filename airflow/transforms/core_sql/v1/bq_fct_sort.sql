
/*
declare ltarget_tenant string default 'ict_development';
declare etl_batch_uuid string default '2020-12-12';

declare start_query_date timestamp default timestamp('2024-04-17 00:00:00');
declare end_query_date timestamp default timestamp('2024-04-18 02:00:00');
*/ 
CREATE TEMP TABLE
  tmp_staging_fct_sort (
    record_timestamp TIMESTAMP,
    scanned_code STRING,
    intended_logical_destination_code STRING,
    actual_logical_destination_code STRING,
    intended_physical_destination_code STRING,
    actual_physical_destination_code STRING,
    dispatch_status_code STRING,
    disposition_status_code STRING,
    wave_code STRING,
    tracking_code STRING,
    operator_code STRING,
    reason_code STRING,
    dispatch_request_timestamp TIMESTAMP,
    dispatch_assignment_timestamp TIMESTAMP,
    disposition_bit_status INT64,
    induction_lane_code STRING,
    terminal_code STRING,
    dispatch_success_count INT64,
    dispatch_error_count INT64,
    dispatch_total_count INT64,
    disposition_success_count INT64,
    disposition_error_count INT64,
    disposition_total_count INT64,
    recirc_destination_ind INT64,
    reject_destination_ind INT64,
    recirculation_count INT64,
    qc_ind INT64,
    dc_tenant_name STRING,
    sort_device_code STRING,
    subsystem_code STRING,
    subsystem_category STRING,
    scanner_code STRING,
    actual_logical_destination_type STRING,
    actual_physical_destination_type STRING,
    status_category_code STRING,
    disposition_reason_code STRING,
    actual_physical_destination_name STRING,
    container_type_code STRING,
    unit_sorter_carrier_code STRING,
    intended_physical_destination_name STRING,
    subsystem_uuid STRING DEFAULT NULL,
    device_uuid STRING DEFAULT NULL,
    disposition_status_uuid STRING DEFAULT NULL,
    dispatch_status_uuid STRING DEFAULT NULL,
    operator_uuid STRING DEFAULT NULL,
    reason_uuid STRING DEFAULT NULL,
    disposition_reason_uuid STRING DEFAULT NULL,
    wave_uuid STRING DEFAULT NULL,
    scanner_uuid STRING DEFAULT NULL,
    container_type_uuid STRING DEFAULT NULL,
    actual_logical_destination_uuid STRING DEFAULT NULL,
    intended_logical_destination_uuid STRING DEFAULT NULL,
    actual_physical_destination_uuid STRING DEFAULT NULL,
    intended_physical_destination_uuid STRING DEFAULT NULL,
    facility STRING,
    source_system STRING
  );

INSERT INTO
  tmp_staging_fct_sort (
    record_timestamp,
    scanned_code,
    intended_logical_destination_code,
    actual_logical_destination_code,
    intended_physical_destination_code,
    actual_physical_destination_code,
    dispatch_status_code,
    disposition_status_code,
    wave_code,
    tracking_code,
    operator_code,
    reason_code,
    dispatch_request_timestamp,
    dispatch_assignment_timestamp,
    disposition_bit_status,
    induction_lane_code,
    terminal_code,
    dispatch_success_count,
    dispatch_error_count,
    dispatch_total_count,
    disposition_success_count,
    disposition_error_count,
    disposition_total_count,
    recirc_destination_ind,
    reject_destination_ind,
    recirculation_count,
    qc_ind,
    dc_tenant_name,
    sort_device_code,
    subsystem_code,
    subsystem_category,
    scanner_code,
    actual_logical_destination_type,
    actual_physical_destination_type,
    status_category_code,
    disposition_reason_code,
    actual_physical_destination_name,
    container_type_code,
    unit_sorter_carrier_code,
    intended_physical_destination_name,
    facility,
    source_system
  )
WITH
  extracted_fct_sort_disposition_data AS (
    SELECT
      SAFE_CAST(JSON_VALUE(data, '$.requestTime') AS TIMESTAMP) AS request_time,
      SAFE_CAST(JSON_VALUE(data, '$.responseTime') AS TIMESTAMP) AS response_time,
      SAFE_CAST(
        JSON_VALUE(data, '$.dispositionTime') AS TIMESTAMP
      ) AS disposition_time,
      JSON_VALUE(data, '$.requestLocation') AS request_location,
      JSON_VALUE(data, '$.sorterId') AS sorter_id,
      JSON_VALUE(data, '$.trackingId') AS tracking_id,
      JSON_VALUE(data, '$.matchCode') AS match_code,
      JSON_VALUE(data, '$.plannedDestination') AS planned_destination,
      JSON_VALUE(data, '$.plannedPhysicalDestination') AS planned_physical_destination,
      JSON_VALUE(data, '$.actualPhysicalDestination') AS actual_physical_destination,
      JSON_VALUE(data, '$.dispatchEventCode') AS dispatch_event_code,
      JSON_VALUE(data, '$.dispositionEventCode') AS disposition_event_code,
      SAFE_CAST(
        JSON_VALUE(data, '$.recirculationCounter') AS INT64
      ) AS recirculation_counter,
      JSON_VALUE(data, '$.recirculationReason') AS recirculation_reason,
      JSON_VALUE(data, '$.inductStation') AS induct_station,
      JSON_VALUE(data, '$.dispositionDischargeCode') AS disposition_discharge_code,
      JSON_VALUE(data, '$.requestId') AS request_id,
      JSON_VALUE(data, '$.type') AS container_type,
      JSON_VALUE(data, '$.carrierId') AS carrier_id,
      SAFE_CAST(
        JSON_VALUE(data, '$.Record_Timestamp_Offset') AS TIMESTAMP
      ) AS record_timestamp_offset,
      tenant,
      facility,
      source_system
    FROM
      `${tenant_id}_landing.SorterDispositionFact`
    WHERE
      tenant = @ltarget_tenant
      AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  fct_sort_disposition_normalized as (
    select
      disposition_time as record_timestamp,
      match_code as scanned_code,
      planned_destination as intended_logical_destination_code,
      safe_cast(null as string) as actual_logical_destination_code,
      actual_physical_destination as actual_physical_destination_name,
      case
        when strpos(actual_physical_destination, '.') > 0 then array_reverse(split(actual_physical_destination, '.')) [safe_offset(0)]
        else ifnull(actual_physical_destination, '')
      end as actual_physical_destination_code,
      case
        when strpos(planned_physical_destination, '.') > 0 then array_reverse(split(planned_physical_destination, '.')) [safe_offset(0)]
        else ifnull(planned_physical_destination, '')
      end as intended_physical_destination_code,
      planned_physical_destination as intended_physical_destination_name,
      dispatch_event_code as dispatch_status_code,
      disposition_event_code as disposition_status_code,
      safe_cast(null as string) as wave_code,
      tracking_id as tracking_code,
      safe_cast(null as string) as operator_code,
      recirculation_reason as reason_code,
      request_time as dispatch_request_timestamp,
      response_time as dispatch_assignment_timestamp,
      induct_station as induction_lane_code,
      safe_cast(null as string) as terminal_code,
      recirculation_counter as recirculation_count,
      0 as qc_ind,
      safe_cast(null as string) as dc_tenant_name,
      sorter_id as sort_device_code,
      'DEMATIC IQ' as subsystem_category,
      request_location as scanner_code,
      request_location as disposition_reason_code,
      container_type as container_type_code,
      carrier_id as unit_sorter_carrier_code,
      'LOGICAL' as intended_destination_type,
      safe_cast(null as string) as disposition_bit_status_str,
      'MFC_SORTER' as subsystem_code,
      facility,
      source_system
    from
      extracted_fct_sort_disposition_data
    where
      (
        disposition_event_code is not null
        or disposition_event_code != ''
      )
      and disposition_time is not null
  ),
  extracted_fct_sort_data AS (
    SELECT
      SAFE_CAST(
        JSON_VALUE(data, '$.dispatchRequestTime') AS TIMESTAMP
      ) AS dispatch_request_time,
      SAFE_CAST(
        JSON_VALUE(data, '$.dispatchAssignmentTime') AS TIMESTAMP
      ) AS dispatch_assignment_time,
      SAFE_CAST(
        JSON_VALUE(data, '$.dispositionTime') AS TIMESTAMP
      ) AS disposition_time,
      JSON_VALUE(data, '$.scannerId') AS scanner_id,
      JSON_VALUE(data, '$.sortDeviceId') AS sort_device_id,
      JSON_VALUE(data, '$.trackingId') AS tracking_id,
      JSON_VALUE(data, '$.scannedCode') AS scanned_code,
      JSON_VALUE(data, '$.scannedCode2') AS scanned_code2,
      JSON_VALUE(data, '$.intendedDestinationID') AS intended_destination_id,
      JSON_VALUE(data, '$.intendedDestinationType') AS intended_destination_type,
      JSON_VALUE(data, '$.intendedPhysicalDestinationId') AS intended_physical_destination_id,
      JSON_VALUE(data, '$.actualLogicalDestinationId') AS actual_logical_destination_id,
      JSON_VALUE(data, '$.actualPhysicalDestinationId') AS actual_physical_destination_id,
      JSON_VALUE(data, '$.dispatchAssignmentStatus') AS dispatch_assignment_status,
      JSON_VALUE(data, '$.dispositionStatus') AS disposition_status,
      JSON_VALUE(data, '$.dispositionBitStatus') AS disposition_bit_status_str,
      JSON_VALUE(data, '$.originator') AS originator,
      JSON_VALUE(data, '$.qc_Ind') AS qc_ind,
      JSON_VALUE(data, '$.tenantName') AS tenant_name,
      SAFE_CAST(
        JSON_VALUE(data, '$.recirculationCounter') AS INT64
      ) AS recirculation_counter,
      JSON_VALUE(data, '$.recirculationReason') AS recirculation_reason,
      JSON_VALUE(data, '$.orderId') AS order_id,
      JSON_VALUE(data, '$.waveId') AS wave_id,
      JSON_VALUE(data, '$.inductionLaneId') AS induction_lane_id,
      JSON_VALUE(data, '$.terminalID') AS terminal_id,
      JSON_VALUE(data, '$.userId') AS user_id,
      JSON_VALUE(data, '$.dispositionStatusCategory') AS disposition_status_category,
      SAFE_CAST(
        JSON_VALUE(data, '$.Record_Timestamp_Offset') AS TIMESTAMP
      ) AS record_timestamp_offset,
      facility,
      source_system
    FROM
      `${tenant_id}_landing.SortFact`
    WHERE
      tenant = @ltarget_tenant
      AND ingestion_date BETWEEN TIMESTAMP(@start_query_date) AND TIMESTAMP(@end_query_date)
  ),
  fct_sort_normalized AS (
    select
      disposition_time as record_timestamp,
      scanned_code as scanned_code,
      intended_destination_id as intended_logical_destination_code,
      actual_logical_destination_id as actual_logical_destination_code,
      safe_cast(null as string) as actual_physical_destination_name,
      actual_physical_destination_id as actual_physical_destination_code,
      intended_physical_destination_id as intended_physical_destination_code,
      intended_physical_destination_id as intended_physical_destination_name,
      dispatch_assignment_status as dispatch_status_code,
      disposition_status as disposition_status_code,
      wave_id as wave_code,
      tracking_id as tracking_code,
      user_id as operator_code,
      recirculation_reason as reason_code,
      dispatch_request_time as dispatch_request_timestamp,
      dispatch_assignment_time as dispatch_assignment_timestamp,
      induction_lane_id as induction_lane_code,
      terminal_id as terminal_code,
      recirculation_counter as recirculation_count,
      case
        when upper(qc_ind) = 'false'
        or qc_ind = '0' then 0
        else 1
      end as qc_ind,
      tenant_name as dc_tenant_name,
      sort_device_id as sort_device_code,
      'DEMATIC IQ' as subsystem_category,
      scanner_id as scanner_code,
      safe_cast(null as string) as disposition_reason_code,
      safe_cast(null as string) as container_type_code,
      safe_cast(null as string) as unit_sorter_carrier_code,
      coalesce(intended_destination_type, 'LOGICAL') as intended_destination_type,
      disposition_bit_status_str,
      originator as subsystem_code,
      extracted_fct_sort_data.facility AS facility,
      extracted_fct_sort_data.source_system AS source_system
    from
      extracted_fct_sort_data
    where
      (
        disposition_status is not null
        or disposition_status != ''
      )
      and disposition_time is not null
  ),
  all_sort_event as (
    SELECT DISTINCT
      *
    FROM
      fct_sort_disposition_normalized
    UNION DISTINCT
    SELECT DISTINCT
      *
    FROM
      fct_sort_normalized
  ),
  raw_sort_events AS (
    SELECT
      dispatch_status_code,
      CASE
        WHEN upper(subsystem_code) = 'EMS'
        OR subsystem_code IS NULL THEN 'SORTER'
        ELSE subsystem_code
      END AS subsystem_code,
      CASE container_type_code
        WHEN 'BARCODE' THEN 'Load Unit Barcode'
        WHEN 'PRODUCT' THEN 'Item Barcode'
        ELSE coalesce(container_type_code, '')
      END AS container_type_code,
      CASE
        WHEN upper(intended_destination_type) = 'LOGICAL' THEN intended_logical_destination_code
        ELSE ''
      END AS intended_logical_destination_code,
      qc_ind,
      disposition_bit_status_str,
      disposition_status_code,
      scanner_code,
      scanned_code,
      actual_logical_destination_code,
      actual_physical_destination_code,
      actual_physical_destination_name,
      recirculation_count,
      record_timestamp,
      dc_tenant_name,
      sort_device_code,
      wave_code,
      tracking_code,
      operator_code,
      reason_code,
      dispatch_request_timestamp,
      dispatch_assignment_timestamp,
      induction_lane_code,
      terminal_code,
      disposition_reason_code,
      unit_sorter_carrier_code,
      intended_physical_destination_code,
      intended_physical_destination_name,
      subsystem_category,
      all_sort_event.facility AS facility,
      all_sort_event.source_system AS source_system
    FROM
      all_sort_event
  ),
  br_status_lookup_events as (
    SELECT
      status_lookup_cache.status_name AS disposition_status_name,
      psle.dispatch_status_code AS dispatch_status_code,
      psle.subsystem_code AS subsystem_code,
      psle.container_type_code AS container_type_code,
      psle.intended_logical_destination_code AS intended_logical_destination_code,
      psle.qc_ind AS qc_ind,
      psle.disposition_status_code AS disposition_status_code,
      scanner_code,
      scanned_code,
      actual_logical_destination_code,
      actual_physical_destination_code,
      actual_physical_destination_name,
      recirculation_count,
      record_timestamp,
      dc_tenant_name,
      sort_device_code,
      disposition_reason_code,
      wave_code,
      tracking_code,
      operator_code,
      reason_code,
      dispatch_request_timestamp,
      dispatch_assignment_timestamp,
      terminal_code,
      induction_lane_code,
      unit_sorter_carrier_code,
      intended_physical_destination_code,
      intended_physical_destination_name,
      disposition_bit_status_str,
      psle.subsystem_category AS subsystem_category,
      1 AS dispatch_total_count,
      CASE
        WHEN upper(psle.dispatch_status_code) IN (
          'SORTER INDUCT',
          'ROUTED',
          'STRAIGHT',
          'SHIPPING SORTER INDUCT',
          'CASE',
          'NONE',
          'SENT STRAIGHT'
        ) THEN 1
        ELSE 0
      END AS dispatch_success_count,
      CASE
        WHEN upper(status_lookup_cache.status_name) = 'DIVERTED'
        OR upper(status_lookup_cache.status_name) = 'SUCCESSFUL DIVERT' THEN 1
        ELSE 0
      END AS disposition_success_count,
      coalesce(
        CASE
          WHEN length(rtrim(coalesce(disposition_bit_status_str, ''))) = 0
          AND upper(status_lookup_cache.status_name) NOT IN (
            'SUCCESSFUL DIVERT',
            'DIVERTED',
            'DIVERTED TO LAN'
          ) THEN 1
          WHEN length(rtrim(coalesce(disposition_bit_status_str, ''))) = 0 THEN 0
          ELSE length(
            rtrim(
              CAST(
                coalesce(disposition_bit_status_str, '0') as STRING
              )
            )
          ) - length(
            rtrim(replace(disposition_bit_status_str, ':', ''))
          )
        END,
        0
      ) AS disposition_error_count,
      psle.facility AS facility,
      psle.source_system AS source_system
    FROM
      (
        SELECT
          *
        FROM
          raw_sort_events
        WHERE
          upper(left(raw_sort_events.disposition_status_code, 4)) = 'BR00'
      ) AS psle
      LEFT OUTER JOIN (
        SELECT
          s.status_uuid,
          upper(coalesce(s.status_code, '0')) AS status_code,
          coalesce(s.status_locale_name, s.status_code) AS status_name,
          upper(coalesce(ss.subsystem_category, '')) AS subsystem_category,
          upper(coalesce(ss.subsystem_code, '0')) AS subsystem_code,
          upper(coalesce(s.dc_tenant_name, '')) AS tenant_name
        FROM
          `${tenant_id}_oa_curated.dim_status` AS s
          INNER JOIN `${tenant_id}_oa_curated.dim_subsystem` AS ss ON s.subsystem_uuid = ss.subsystem_uuid
        WHERE
          s.tenant = @ltarget_tenant
          AND s.status_locale_code = 'en_US'
      ) AS status_lookup_cache ON status_lookup_cache.status_code = psle.disposition_status_code
      AND status_lookup_cache.subsystem_code = psle.subsystem_code
      AND status_lookup_cache.subsystem_category = 'DEMATIC IQ'
  )
  -- calculate disposition and dispatch error and success counts for BR00 events
,
  br_error_calculation_events as (
    SELECT
      *,
      br_status_lookup_events.disposition_success_count + br_status_lookup_events.disposition_error_count AS disposition_total_count,
      CASE
        WHEN br_status_lookup_events.dispatch_success_count = 1 THEN 0
        ELSE 1
      END AS dispatch_error_count,
      br_status_lookup_events.facility AS br_facility,
      br_status_lookup_events.source_system AS br_source_system
    FROM
      br_status_lookup_events
  )
  -- calculate disposition and dispatch error and success counts for NONE BR00 events
,
  non_br_calculation_events as (
    SELECT
      tmp.*,
      coalesce(tmp.disposition_success_count, 0) + coalesce(tmp.disposition_error_count, 0) AS disposition_total_count,
      CASE
        WHEN tmp.dispatch_success_count = 1 THEN 0
        ELSE 1
      END AS dispatch_error_count,
      tmp.disposition_status_code AS disposition_status_name,
      tmp.raw_facility AS non_br_facility,
      tmp.raw_source_system AS non_br_source_system
    FROM
      (
        SELECT
          *,
          1 AS dispatch_total_count,
          CASE
            WHEN upper(raw_sort_events.dispatch_status_code) IN (
              'NORMAL',
              'ROUTED',
              'STRAIGHT',
              'NONE',
              'SORTER INDUCT',
              'SHIPPING SORTER INDUCT',
              'CASE',
              'SENT STRAIGHT',
              'OK',
              'IR'
            ) THEN 1
            ELSE 0
          END AS dispatch_success_count,
          CASE
            WHEN upper(raw_sort_events.disposition_status_code) IN (
              'SUCCESS',
              'DIVERTED',
              'SUCCESSFUL DIVERT',
              'DIVERTED TO LANE',
              'OK'
            )
            OR length(rtrim(raw_sort_events.disposition_status_code)) <= 0 THEN 1
            ELSE 0
          END AS disposition_success_count,
          coalesce(
            CASE
              WHEN length(
                rtrim(
                  coalesce(raw_sort_events.disposition_bit_status_str, '')
                )
              ) = 0
              AND upper(raw_sort_events.disposition_status_code) NOT IN (
                'SUCCESS',
                'OK',
                'DIVERTED',
                'SUCCESSFUL DIVERT',
                'DIVERTED TO LANE'
              ) THEN 1
              WHEN length(
                rtrim(
                  coalesce(raw_sort_events.disposition_bit_status_str, '')
                )
              ) = 0 THEN 0
              ELSE length(
                rtrim(
                  CAST(
                    coalesce(raw_sort_events.disposition_bit_status_str, '0') as STRING
                  )
                )
              ) - length(
                rtrim(
                  replace(
                    CAST(
                      coalesce(raw_sort_events.disposition_bit_status_str, '0') as STRING
                    ),
                    ':',
                    ''
                  )
                )
              )
            END,
            0
          ) AS disposition_error_count,
          raw_sort_events.facility AS raw_facility,
          raw_sort_events.source_system AS raw_source_system
        FROM
          raw_sort_events
        WHERE
          upper(left(raw_sort_events.disposition_status_code, 4)) <> 'BR00'
      ) AS tmp
  )
  -- merge back the BR00 and None BR00 events into same table
,
  post_calculations_events AS (
    SELECT DISTINCT
      scanned_code,
      intended_physical_destination_name,
      intended_physical_destination_code,
      unit_sorter_carrier_code,
      induction_lane_code,
      terminal_code,
      wave_code,
      tracking_code,
      operator_code,
      reason_code,
      dispatch_request_timestamp,
      dispatch_assignment_timestamp,
      sort_device_code,
      dc_tenant_name,
      record_timestamp,
      recirculation_count,
      disposition_reason_code,
      actual_physical_destination_code,
      actual_physical_destination_name,
      actual_logical_destination_code,
      scanner_code,
      disposition_bit_status_str,
      dispatch_status_code,
      subsystem_code,
      container_type_code,
      intended_logical_destination_code,
      qc_ind,
      disposition_status_code,
      subsystem_category,
      dispatch_total_count,
      dispatch_success_count,
      disposition_success_count,
      disposition_error_count,
      disposition_total_count,
      dispatch_error_count,
      disposition_status_name,
      br_error_calculation_events.br_facility AS facility,
      br_error_calculation_events.br_source_system AS source_system
    from
      br_error_calculation_events
    union DISTINCT
    select DISTINCT
      scanned_code,
      intended_physical_destination_name,
      intended_physical_destination_code,
      unit_sorter_carrier_code,
      induction_lane_code,
      terminal_code,
      wave_code,
      tracking_code,
      operator_code,
      reason_code,
      dispatch_request_timestamp,
      dispatch_assignment_timestamp,
      sort_device_code,
      dc_tenant_name,
      record_timestamp,
      recirculation_count,
      disposition_reason_code,
      actual_physical_destination_code,
      actual_physical_destination_name,
      actual_logical_destination_code,
      scanner_code,
      disposition_bit_status_str,
      dispatch_status_code,
      subsystem_code,
      container_type_code,
      intended_logical_destination_code,
      qc_ind,
      disposition_status_code,
      subsystem_category,
      dispatch_total_count,
      dispatch_success_count,
      disposition_success_count,
      disposition_error_count,
      disposition_total_count,
      dispatch_error_count,
      disposition_status_name,
      non_br_calculation_events.facility AS facility,
      non_br_calculation_events.source_system AS source_system
    from
      non_br_calculation_events
  )
  -- do a lookup of physical and logical destination type 
,
  destination_type_lookup AS (
    SELECT
      intended_physical_destination_name,
      intended_physical_destination_code,
      unit_sorter_carrier_code,
      induction_lane_code,
      terminal_code,
      CASE
        WHEN length(rtrim(disposition_bit_status_str)) - length(
          rtrim(replace(disposition_bit_status_str, ';', ''))
        ) = 1
        AND upper(disposition_status_code) <> 'SUCCESS'
        AND upper(disposition_status_name) NOT IN ('DIVERTED', 'SUCCESSFUL DIVERT')
        OR length(rtrim(disposition_bit_status_str)) - length(
          rtrim(replace(disposition_bit_status_str, ';', ''))
        ) > 1 THEN `${tenant_id}_oa_curated.DispositionCalculatorFunction` (split(disposition_bit_status_str, ';'))
        ELSE coalesce(safe_cast(disposition_bit_status_str as int64), 0)
      END AS disposition_bit_status,
      scanned_code,
      wave_code,
      tracking_code,
      operator_code,
      reason_code,
      dispatch_request_timestamp,
      dispatch_assignment_timestamp,
      sort_device_code,
      dc_tenant_name,
      record_timestamp,
      recirculation_count,
      disposition_reason_code,
      actual_physical_destination_code,
      actual_physical_destination_name,
      actual_logical_destination_code,
      pce.scanner_code,
      dispatch_status_code,
      subsystem_code,
      container_type_code,
      intended_logical_destination_code,
      qc_ind,
      disposition_status_code,
      subsystem_category,
      dispatch_total_count,
      dispatch_success_count,
      disposition_success_count,
      disposition_error_count,
      disposition_total_count,
      dispatch_error_count,
      disposition_status_name,
      pd.physical_destination_type AS actual_physical_destination_type,
      ld.logical_destination_type AS actual_logical_destination_type,
      facility,
      source_system
    FROM
      post_calculations_events AS pce
      LEFT OUTER JOIN (
        SELECT DISTINCT
          upper(coalesce(physical_destination_code, '0')) AS physical_destination_code,
          dpd.physical_destination_type,
          upper(coalesce(scanner_code, '0')) AS scanner_code
        FROM
          `${tenant_id}_oa_curated.dim_physical_destination` AS dpd
          INNER JOIN `${tenant_id}_oa_curated.dim_scanner` AS s ON dpd.scanner_uuid = s.scanner_uuid
        WHERE
          dpd.tenant = @ltarget_tenant
          AND dpd.facility = s.facility
          AND dpd.source_system = s.source_system
      ) AS pd ON pce.scanner_code = pd.scanner_code
      AND pce.actual_physical_destination_code = pd.physical_destination_code
      LEFT OUTER JOIN (
        SELECT DISTINCT
          upper(coalesce(logical_destination_code, '0')) AS logical_destination_code,
          upper(coalesce(dld.dc_tenant_name, '')) AS tenant_name,
          logical_destination_type,
          upper(coalesce(s.scanner_code, '0')) AS scanner_code
        FROM
          `${tenant_id}_oa_curated.dim_logical_destination` AS dld
          INNER JOIN `${tenant_id}_oa_curated.dim_scanner` AS s ON dld.scanner_uuid = s.scanner_uuid
        WHERE
          dld.tenant = @ltarget_tenant
      ) AS ld ON pce.scanner_code = pd.scanner_code
      AND pce.actual_logical_destination_code = ld.logical_destination_code
  )
SELECT
  record_timestamp,
  ifnull(scanned_code, '') as scanned_code,
  ifnull(intended_logical_destination_code, '') as intended_logical_destination_code,
  ifnull(actual_logical_destination_code, '') as actual_logical_destination_code,
  ifnull(intended_physical_destination_code, '') as intended_physical_destination_code,
  ifnull(actual_physical_destination_code, '') as actual_physical_destination_code,
  ifnull(dispatch_status_code, '') as dispatch_status_code,
  ifnull(disposition_status_code, '') as disposition_status_code,
  ifnull(wave_code, '') as wave_code,
  ifnull(tracking_code, '') as tracking_code,
  ifnull(operator_code, '') as operator_code,
  ifnull(reason_code, '') as reason_code,
  dispatch_request_timestamp,
  dispatch_assignment_timestamp,
  disposition_bit_status,
  ifnull(induction_lane_code, '') as induction_lane_code,
  terminal_code,
  dispatch_success_count,
  dispatch_error_count,
  dispatch_total_count,
  disposition_success_count,
  disposition_error_count,
  disposition_total_count,
  case
    when upper(
      coalesce(
        actual_physical_destination_type,
        actual_logical_destination_type,
        ''
      )
    ) = 'recirc' then 1
    when ifnull(recirculation_count, 0) > 0
    and ifnull(disposition_reason_code, '') = '' then 1
    else 0
  end as recirc_destination_ind,
  case
    when upper(
      coalesce(
        actual_physical_destination_type,
        actual_logical_destination_type,
        ''
      )
    ) = 'reject' then 1
    when upper(disposition_reason_code) in ('rd', 'pb', 'pw', 'te', 'nr') then 1
    else 0
  end as reject_destination_ind,
  recirculation_count,
  qc_ind,
  dc_tenant_name,
  ifnull(sort_device_code, '') as sort_device_code,
  ifnull(subsystem_code, '') as subsystem_code,
  ifnull(subsystem_category, '') as subsystem_category,
  ifnull(scanner_code, '') as scanner_code,
  ifnull(actual_logical_destination_type, '') as actual_logical_destination_type,
  ifnull(actual_physical_destination_type, '') as actual_physical_destination_type,
  '' as status_category_code,
  ifnull(disposition_reason_code, '') as disposition_reason_code,
  actual_physical_destination_name,
  ifnull(container_type_code, '') as container_type_code,
  ifnull(unit_sorter_carrier_code, '') as unit_sorter_carrier_code,
  intended_physical_destination_name,
  facility,
  source_system
FROM
  destination_type_lookup;

--DIM_Subsystem 
MERGE
  `${tenant_id}_oa_curated.dim_subsystem` ds USING (
    SELECT DISTINCT
      subsystem_code,
      subsystem_category,
      @ltarget_tenant as tenant,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      subsystem_code,
      subsystem_category,
      tenant,
      facility,
      source_system
  ) v ON ds.subsystem_code = v.subsystem_code
  AND ds.subsystem_category = v.subsystem_category
  AND ds.tenant = v.tenant
  AND ds.facility = v.facility
  AND ds.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    subsystem_uuid,
    subsystem_code,
    subsystem_category,
    subsystem_name,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.subsystem_code,
      v.subsystem_category]
    ),
    v.subsystem_code,
    v.subsystem_category,
    '',
    v.tenant,
    v.facility,
    v.source_system,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_sort tt
SET
  subsystem_uuid = d.subsystem_uuid
FROM
  (
    select distinct
      subsystem_uuid,
      subsystem_code,
      subsystem_category,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_subsystem`
  ) d
WHERE
  tt.subsystem_code = d.subsystem_code
  AND tt.subsystem_category = d.subsystem_category
  AND d.tenant = @ltarget_tenant
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

--DIM_Device
MERGE
  `${tenant_id}_oa_curated.dim_device` dd USING (
    SELECT DISTINCT
      subsystem_uuid,
      sort_device_code AS device_code,
      @ltarget_tenant as tenant,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      subsystem_uuid,
      device_code,
      facility,
      source_system
  ) v ON dd.device_code = v.device_code
  AND dd.subsystem_uuid = v.subsystem_uuid
  AND dd.tenant = v.tenant
  AND dd.facility = v.facility
  AND dd.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    device_uuid,
    device_code,
    subsystem_uuid,
    tenant,
    facility,
    source_system,
    etl_batch_id,
    device_functional_type
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.device_code,
      v.subsystem_uuid]
    ),
    v.device_code,
    v.subsystem_uuid,
    v.tenant,
    v.facility,
    v.source_system,
    @etl_batch_uuid,
    'SORTER'
  );

UPDATE tmp_staging_fct_sort tt
SET
  device_uuid = d.device_uuid
FROM
  (
    select distinct
      subsystem_uuid,
      device_uuid,
      device_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_device`
  ) d
WHERE
  tt.subsystem_uuid = d.subsystem_uuid
  AND tt.sort_device_code = d.device_code
  AND d.tenant = @ltarget_tenant
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

--DIM_Status
MERGE
  `${tenant_id}_oa_curated.dim_status` ds USING (
    SELECT DISTINCT
      disposition_status_code as status_code,
      subsystem_uuid,
      status_category_code,
      @ltarget_tenant as tenant,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      status_code,
      subsystem_uuid,
      status_category_code,
      tenant,
      facility,
      source_system
    UNION DISTINCT
    SELECT DISTINCT
      dispatch_status_code as status_code,
      subsystem_uuid,
      status_category_code,
      @ltarget_tenant as tenant,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      status_code,
      subsystem_uuid,
      status_category_code,
      tenant,
      facility,
      source_system
  ) v ON ds.status_code = v.status_code
  AND ds.subsystem_uuid = v.subsystem_uuid
  AND ds.status_category_code = v.status_category_code
  AND ds.tenant = v.tenant
  AND ds.status_locale_code = ''
  AND ds.facility = v.facility
  AND ds.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    status_uuid,
    status_code,
    status_locale_code,
    status_category_code,
    subsystem_uuid,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.status_code,
      v.subsystem_uuid]
    ),
    v.status_code,
    '',
    status_category_code,
    v.subsystem_uuid,
    v.tenant,
    v.facility,
    v.source_system,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_sort tt
SET
  disposition_status_uuid = d.status_uuid
FROM
  (
    select distinct
      status_uuid,
      status_code,
      status_category_code,
      subsystem_uuid,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_status`
  ) d
WHERE
  tt.subsystem_uuid = d.subsystem_uuid
  AND tt.disposition_status_code = d.status_code
  AND tt.status_category_code = d.status_category_code
  AND d.tenant = @ltarget_tenant
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

UPDATE tmp_staging_fct_sort tt
SET
  dispatch_status_uuid = d.status_uuid
FROM
  `${tenant_id}_oa_curated.dim_status` d
WHERE
  tt.subsystem_uuid = d.subsystem_uuid
  AND tt.dispatch_status_code = d.status_code
  AND tt.status_category_code = d.status_category_code
  AND d.tenant = @ltarget_tenant
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

-- DIM_Operator
MERGE
  `${tenant_id}_oa_curated.dim_operator` op USING (
    SELECT DISTINCT
      operator_code,
      @ltarget_tenant as tenant,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      operator_code,
      tenant,
      facility,
      source_system
  ) v ON op.operator_code = v.operator_code
  AND op.tenant = v.tenant
  AND op.facility = v.facility
  AND op.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    operator_uuid,
    operator_code,
    tenant,
    facility,
    source_system,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.operator_code]
    ),
    v.operator_code,
    v.tenant,
    v.facility,
    v.source_system,
    1,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_sort tt
SET
  operator_uuid = d.operator_uuid
FROM
  (
    select distinct
      operator_uuid,
      operator_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_operator`
  ) d
WHERE
  tt.operator_code = d.operator_code
  AND d.tenant = @ltarget_tenant
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

--DIM_Reason
MERGE
  `${tenant_id}_oa_curated.dim_reason` dr USING (
    SELECT DISTINCT
      reason_code as reason_code,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      reason_code,
      facility,
      source_system
    UNION DISTINCT
    SELECT DISTINCT
      disposition_reason_code as reason_code,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      reason_code,
      facility,
      source_system
  ) v ON dr.reason_code = v.reason_code
  AND dr.tenant = @ltarget_tenant
  AND dr.facility = v.facility
  AND dr.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    reason_uuid,
    reason_code,
    tenant,
    facility,
    source_system,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.reason_code]
    ),
    v.reason_code,
    @ltarget_tenant,
    v.facility,
    v.source_system,
    1,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_sort tt
SET
  reason_uuid = d.reason_uuid
FROM
  (
    select distinct
      reason_uuid,
      reason_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_reason`
  ) d
WHERE
  tt.reason_code = d.reason_code
  AND d.tenant = @ltarget_tenant
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

UPDATE tmp_staging_fct_sort tt
SET
  disposition_reason_uuid = d.reason_uuid
FROM
  (
    select distinct
      reason_uuid,
      reason_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_reason`
  ) d
WHERE
  tt.disposition_reason_code = d.reason_code
  AND d.tenant = @ltarget_tenant
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

-- DIM_Wave Merge
MERGE
  `${tenant_id}_oa_curated.dim_wave` DW USING (
    SELECT DISTINCT
      wave_code,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      wave_code,
      facility,
      source_system
  ) V ON DW.wave_code = V.wave_code
  AND DW.tenant = @ltarget_tenant
  AND DW.facility = V.facility
  AND DW.source_system = V.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    wave_uuid,
    wave_code,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.wave_code]
    ),
    V.wave_code,
    @ltarget_tenant,
    facility,
    source_system,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_sort t
SET
  wave_uuid = d.wave_uuid
FROM
  (
    select distinct
      wave_uuid,
      wave_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_wave`
  ) d
WHERE
  t.wave_code = d.wave_code
  AND d.tenant = @ltarget_tenant
  AND t.facility = d.facility
  AND t.source_system = d.source_system;

-- DIM_Container_Type
MERGE
  `${tenant_id}_oa_curated.dim_container_type` dct USING (
    SELECT DISTINCT
      container_type_code,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      container_type_code,
      facility,
      source_system
  ) v ON dct.container_type_code = v.container_type_code
  AND dct.tenant = @ltarget_tenant
  AND dct.facility = v.facility
  AND dct.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    container_type_uuid,
    container_type_code,
    tenant,
    facility,
    source_system,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.container_type_code]
    ),
    v.container_type_code,
    @ltarget_tenant,
    v.facility,
    v.source_system,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_sort tt
SET
  container_type_uuid = d.container_type_uuid
FROM
  (
    select distinct
      container_type_uuid,
      container_type_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_container_type`
  ) d
WHERE
  tt.container_type_code = d.container_type_code
  AND d.tenant = @ltarget_tenant
  AND tt.facility = d.facility
  AND tt.source_system = d.source_system;

-- dim_scanner merge
MERGE
  `${tenant_id}_oa_curated.dim_scanner` ds USING (
    SELECT DISTINCT
      scanner_code,
      device_uuid,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      scanner_code,
      device_uuid,
      facility,
      source_system
  ) v ON ds.scanner_code = v.scanner_code
  AND ds.device_uuid = v.device_uuid
  AND ds.tenant = @ltarget_tenant
  AND ds.facility = v.facility
  AND ds.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    scanner_uuid,
    scanner_code,
    device_uuid,
    tenant,
    facility,
    source_system,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.scanner_code,
      v.device_uuid]
    ),
    v.scanner_code,
    v.device_uuid,
    @ltarget_tenant,
    v.facility,
    v.source_system,
    1,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_sort t
SET
  scanner_uuid = d.scanner_uuid
FROM
  (
    select distinct
      scanner_uuid,
      scanner_code,
      device_uuid,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_scanner`
  ) d
WHERE
  t.scanner_code = d.scanner_code
  AND t.device_uuid = d.device_uuid
  AND d.tenant = @ltarget_tenant
  AND t.facility = d.facility
  AND t.source_system = d.source_system;

-- dim_logical_destination merge
MERGE
  `${tenant_id}_oa_curated.dim_logical_destination` dld USING (
    SELECT DISTINCT
      scanner_uuid,
      scanner_code,
      intended_logical_destination_code as logical_destination_code,
      '' as logical_destination_type,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      scanner_uuid,
      scanner_code,
      logical_destination_code,
      logical_destination_code,
      facility,
      source_system
    UNION DISTINCT
    SELECT DISTINCT
      scanner_uuid,
      scanner_code,
      actual_logical_destination_code as logical_destination_code,
      actual_logical_destination_type as logical_destination_type,
      facility,
      source_system
    FROM
      tmp_staging_fct_sort
    GROUP BY
      scanner_uuid,
      scanner_code,
      logical_destination_code,
      logical_destination_type,
      facility,
      source_system
  ) v ON dld.scanner_uuid = v.scanner_uuid
  AND dld.logical_destination_code = v.logical_destination_code
  AND dld.logical_destination_type = v.logical_destination_type
  AND dld.tenant = @ltarget_tenant
  AND dld.facility = v.facility
  AND dld.source_system = v.source_system
WHEN NOT MATCHED THEN
INSERT
  (
    logical_destination_uuid,
    scanner_uuid,
    logical_destination_code,
    logical_destination_type,
    tenant,
    facility,
    source_system,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
      v.scanner_code,
      v.logical_destination_code]
    ),
    v.scanner_uuid,
    v.logical_destination_code,
    v.logical_destination_type,
    @ltarget_tenant,
    v.facility,
    v.source_system,
    1,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_sort t
SET
  intended_logical_destination_uuid = d.logical_destination_uuid
FROM
  (
    select distinct
      logical_destination_uuid,
      scanner_uuid,
      logical_destination_code,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_logical_destination`
  ) d
WHERE
  t.scanner_uuid = d.scanner_uuid
  AND t.intended_logical_destination_code = d.logical_destination_code
  AND d.tenant = @ltarget_tenant
  AND t.facility = d.facility
  AND t.source_system = d.source_system;

UPDATE tmp_staging_fct_sort t
SET
  actual_logical_destination_uuid = d.logical_destination_uuid
FROM
  `${tenant_id}_oa_curated.dim_logical_destination` d
WHERE
  t.scanner_uuid = d.scanner_uuid
  AND t.actual_logical_destination_code = d.logical_destination_code
  AND t.actual_logical_destination_type = d.logical_destination_type
  AND d.tenant = @ltarget_tenant
  AND t.facility = d.facility
  AND t.source_system = d.source_system;

-- dim_physical_destination merge
MERGE
  `${tenant_id}_oa_curated.dim_physical_destination` dpd USING (
    SELECT DISTINCT
      scanner_uuid,
      physical_destination_code,
      physical_destination_type,
      MAX(physical_destination_name) as physical_destination_name,
      facility,
      source_system
    FROM
      (
        SELECT
          scanner_uuid,
          intended_physical_destination_code as physical_destination_code,
          '' as physical_destination_type,
          intended_physical_destination_name as physical_destination_name,
          facility,
          source_system
        FROM
          tmp_staging_fct_sort
        GROUP BY
          scanner_uuid,
          physical_destination_code,
          physical_destination_type,
          physical_destination_name,
          facility,
          source_system
        UNION DISTINCT
        SELECT
          scanner_uuid,
          actual_physical_destination_code as logical_destination_code,
          actual_physical_destination_type as physical_destination_type,
          actual_physical_destination_name as physical_destination_name,
          facility,
          source_system
        FROM
          tmp_staging_fct_sort
        GROUP BY
          scanner_uuid,
          logical_destination_code,
          physical_destination_type,
          physical_destination_name,
          facility,
          source_system
      ) t
    group by
      scanner_uuid,
      physical_destination_code,
      physical_destination_type,
      facility,
      source_system
  ) v ON dpd.scanner_uuid = v.scanner_uuid
  AND dpd.physical_destination_code = v.physical_destination_code
  AND dpd.physical_destination_type = v.physical_destination_type
  AND dpd.tenant = @ltarget_tenant
  AND dpd.facility = v.facility
  AND dpd.source_system = v.source_system
WHEN MATCHED
  AND v.physical_destination_name != dpd.physical_destination_name THEN
UPDATE SET
  physical_destination_name = COALESCE(
    v.physical_destination_name,
    dpd.physical_destination_name
  )
WHEN NOT MATCHED THEN
INSERT
  (
    physical_destination_uuid,
    scanner_uuid,
    physical_destination_code,
    physical_destination_type,
    physical_destination_name,
    tenant,
    facility,
    source_system,
    active_rec_ind,
    etl_batch_id
  )
VALUES
  (
    `${tenant_id}_oa_curated.Dimension_PK_Generator` (
      [@ltarget_tenant,
      v.facility,
      v.source_system,
       v.scanner_uuid,
      v.physical_destination_code]
    ),
    v.scanner_uuid,
    v.physical_destination_code,
    v.physical_destination_type,
    v.physical_destination_name,
    @ltarget_tenant,
    v.facility,
    v.source_system,
    1,
    @etl_batch_uuid
  );

UPDATE tmp_staging_fct_sort t
SET
  intended_physical_destination_uuid = d.physical_destination_uuid
FROM
  (
    select distinct
      physical_destination_uuid,
      scanner_uuid,
      physical_destination_code,
      physical_destination_type,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_physical_destination`
  ) d
WHERE
  t.scanner_uuid = d.scanner_uuid
  AND t.intended_physical_destination_code = d.physical_destination_code
  AND '' = d.physical_destination_type
  AND d.tenant = @ltarget_tenant
  AND t.facility = d.facility
  AND t.source_system = d.source_system;

UPDATE tmp_staging_fct_sort t
SET
  actual_physical_destination_uuid = d.physical_destination_uuid
FROM
  (
    select distinct
      physical_destination_uuid,
      scanner_uuid,
      physical_destination_code,
      physical_destination_type,
      tenant,
      facility,
      source_system
    from
      `${tenant_id}_oa_curated.dim_physical_destination`
  ) d
WHERE
  t.scanner_uuid = d.scanner_uuid
  AND t.actual_physical_destination_code = d.physical_destination_code
  AND t.actual_physical_destination_type = d.physical_destination_type
  AND d.tenant = @ltarget_tenant
  AND t.facility = d.facility
  and t.source_system = d.source_system;

-- FINAL FCT Insert
INSERT INTO
  `${tenant_id}_oa_curated.fct_sort` (
    record_timestamp,
    scanned_code,
    intended_logical_destination_uuid,
    actual_logical_destination_uuid,
    intended_physical_destination_uuid,
    actual_physical_destination_uuid,
    dispatch_status_uuid,
    disposition_status_uuid,
    wave_uuid,
    tracking_code,
    operator_uuid,
    reason_uuid,
    disposition_reason_uuid,
    container_type_uuid,
    dispatch_request_timestamp,
    dispatch_assignment_timestamp,
    disposition_bit_status,
    induction_lane_code,
    terminal_code,
    dispatch_success_count,
    dispatch_error_count,
    dispatch_total_count,
    disposition_success_count,
    disposition_error_count,
    disposition_total_count,
    recirc_destination_ind,
    reject_destination_ind,
    recirculation_count,
    qc_ind,
    unit_sorter_carrier_code,
    facility,
    source_system,
    etl_batch_id,
    tenant,
    subsystem_code,
    status_category_code,
    operator_code,
    reason_code,
    wave_code,
    container_type_code,
    scanner_code,
    intended_logical_destination_code,
    actual_logical_destination_code,
    intended_physical_destination_code,
    actual_physical_destination_code,
    dispatch_status_code,
    disposition_status_code,
    sort_device_code,
    subsystem_category,
    actual_logical_destination_type,
    actual_physical_destination_type,
    actual_physical_destination_name,
    intended_physical_destination_name
  )
SELECT DISTINCT
  record_timestamp,
  scanned_code,
  intended_logical_destination_uuid,
  actual_logical_destination_uuid,
  intended_physical_destination_uuid,
  actual_physical_destination_uuid,
  dispatch_status_uuid,
  disposition_status_uuid,
  wave_uuid,
  tracking_code,
  operator_uuid,
  reason_uuid,
  disposition_reason_uuid,
  container_type_uuid,
  MAX(dispatch_request_timestamp),
  MAX(dispatch_assignment_timestamp),
  MAX(disposition_bit_status),
  MAX(induction_lane_code),
  MAX(terminal_code),
  MAX(dispatch_success_count),
  MAX(dispatch_error_count),
  MAX(dispatch_total_count),
  MAX(disposition_success_count),
  MAX(disposition_error_count),
  MAX(disposition_total_count),
  MAX(recirc_destination_ind),
  MAX(reject_destination_ind),
  MAX(recirculation_count),
  MAX(qc_ind),
  MAX(unit_sorter_carrier_code),
  facility,
  source_system,
  @etl_batch_uuid AS etl_batch_id,
  @ltarget_tenant AS tenant,
  subsystem_code,
  status_category_code,
  operator_code,
  reason_code,
  wave_code,
  container_type_code,
  scanner_code,
  intended_logical_destination_code,
  actual_logical_destination_code,
  intended_physical_destination_code,
  actual_physical_destination_code,
  dispatch_status_code,
  disposition_status_code,
  sort_device_code,
  subsystem_category,
  actual_logical_destination_type,
  actual_physical_destination_type,
  actual_physical_destination_name,
  intended_physical_destination_name
FROM
  tmp_staging_fct_sort
GROUP BY
  record_timestamp,
  scanned_code,
  intended_logical_destination_uuid,
  actual_logical_destination_uuid,
  intended_physical_destination_uuid,
  actual_physical_destination_uuid,
  dispatch_status_uuid,
  disposition_status_uuid,
  wave_uuid,
  tracking_code,
  operator_uuid,
  reason_uuid,
  disposition_reason_uuid,
  container_type_uuid,
  facility,
  source_system,
  subsystem_code,
  status_category_code,
  operator_code,
  reason_code,
  wave_code,
  container_type_code,
  scanner_code,
  intended_logical_destination_code,
  actual_logical_destination_code,
  intended_physical_destination_code,
  actual_physical_destination_code,
  dispatch_status_code,
  disposition_status_code,
  sort_device_code,
  subsystem_category,
  actual_logical_destination_type,
  actual_physical_destination_type,
  actual_physical_destination_name,
  intended_physical_destination_name;
