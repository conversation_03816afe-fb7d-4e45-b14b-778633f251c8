/*
DECLARE days_to_look_back INT64 DEFAULT 7;
DECLARE etl_batch_uuid STRING DEFAULT '';
*/

-- Calculate the useful historical data points about SKUs and save them
INSERT INTO `${tenant_id}_oa_curated.hst_item` (
  etl_batch_id, item_uuid, record_timestamp, avg_picked_qty, avg_picked_orders, days_on_hand, distribution_status
)
WITH latest_inventory_timestamp_per_item AS (
  -- get the most recent record_timestamp for each item_uuid
  SELECT item_uuid,
    MAX(record_timestamp) as latest_record_timestamp
  FROM `${tenant_id}_oa_curated.fct_inventory` 
  GROUP BY
    item_uuid
),
latestInventory AS (
  -- calculate the sum of inventory for all records in fct_inventory for the given item_uuid and latest record_timestamp for that item
  SELECT fct.item_uuid,fct.location_uuid, fct.source_system, SUM(fct.inventory_qty) as inventory_qty
    FROM `${tenant_id}_oa_curated.fct_inventory` fct 
      INNER JOIN latest_inventory_timestamp_per_item latest on (latest.item_uuid = fct.item_uuid and fct.record_timestamp = latest.latest_record_timestamp)
    GROUP BY
      item_uuid,
      location_uuid,
      fct.source_system
),
inventory AS (
    SELECT  dim_item.item_uuid,
            SUM(COALESCE(fct_inventory.inventory_qty, 0)) AS inventory_qty,
    FROM `${tenant_id}_oa_curated.dim_item` dim_item
    LEFT JOIN latestInventory fct_inventory
        ON dim_item.item_uuid = fct_inventory.item_uuid AND
           dim_item.source_system = fct_inventory.source_system
    GROUP BY dim_item.item_uuid
),
picks AS (
    SELECT  fct_order_line.item_uuid,
            SUM(fct_order_line.picked_qty) / @days_to_look_back as avg_picked_qty,
            COUNT(DISTINCT CONCAT(fct_order_line.pick_order_uuid, ':', fct_order_line.container_physical_code)) / @days_to_look_back as avg_picked_orders  
    FROM `${tenant_id}_oa_curated.fct_order_line` fct_order_line
    JOIN `${tenant_id}_oa_curated.dim_item` dim_item
        ON dim_item.item_uuid = fct_order_line.item_uuid
    WHERE fct_order_line.record_timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL @days_to_look_back DAY)
    GROUP BY fct_order_line.item_uuid
)
SELECT 
    @etl_batch_uuid AS etl_batch_id,
    inventory.item_uuid,
    CURRENT_TIMESTAMP() AS record_timestamp,
    COALESCE(picks.avg_picked_qty, 0) as avg_picked_qty,
    COALESCE(picks.avg_picked_orders, 0) as avg_picked_orders,
    COALESCE(SAFE_DIVIDE(inventory.inventory_qty, picks.avg_picked_qty), 0) AS days_on_hand,
    CASE
        WHEN COALESCE(SAFE_DIVIDE(inventory.inventory_qty, picks.avg_picked_qty), 0) > 25 THEN 'over'
        WHEN 
            COALESCE(SAFE_DIVIDE(inventory.inventory_qty, picks.avg_picked_qty), 0) >= 11 
            AND COALESCE(SAFE_DIVIDE(inventory.inventory_qty, picks.avg_picked_qty), 0) <= 25 
                THEN 'at'
        WHEN COALESCE(SAFE_DIVIDE(inventory.inventory_qty, picks.avg_picked_qty), 0) > 0 
            AND COALESCE(SAFE_DIVIDE(inventory.inventory_qty, picks.avg_picked_qty), 0) < 11
                THEN 'under'
        ELSE 'no'
    END AS distribution_status
FROM inventory
LEFT JOIN picks
    ON picks.item_uuid = inventory.item_uuid;

-- record the most current inventory aggregation historical data point
INSERT INTO `${tenant_id}_oa_curated.agg_hst_inventory` (
  etl_batch_id, record_timestamp, over_inventory_count, at_inventory_count, under_inventory_count, no_inventory_count, total_inventory_count, over_inventory_percent, at_inventory_percent, under_inventory_percent, no_inventory_percent
)
WITH latest_item_history AS (
    SELECT agg.hst_item.*
    FROM 
    (
        SELECT item_uuid, 
        ARRAY_AGG(STRUCT(hst_item) ORDER BY record_timestamp DESC)[SAFE_OFFSET(0)] agg 
        FROM `${tenant_id}_oa_curated.hst_item` hst_item GROUP BY item_uuid
    )
),
inventory_distribution_counts AS (
    SELECT
        COALESCE(SUM(CASE WHEN distribution_status = 'over' THEN 1 ELSE 0 END), 0) AS over_inventory_count,
        COALESCE(SUM(CASE WHEN distribution_status = 'at' THEN 1 ELSE 0 END), 0) AS at_inventory_count,
        COALESCE(SUM(CASE WHEN distribution_status = 'under' THEN 1 ELSE 0 END), 0) AS under_inventory_count,
        COALESCE(SUM(CASE WHEN distribution_status = 'no' THEN 1 ELSE 0 END), 0) AS no_inventory_count,
        (SELECT COUNT(*) FROM `${tenant_id}_oa_curated.dim_item` dim_item) 
            AS total_inventory_count
    FROM latest_item_history
)
SELECT 
    @etl_batch_uuid AS etl_batch_id,
    CURRENT_TIMESTAMP() AS record_timestamp,
    over_inventory_count,
    at_inventory_count,
    under_inventory_count,
    no_inventory_count,
    total_inventory_count,
    CASE WHEN total_inventory_count > 0 THEN over_inventory_count / total_inventory_count * 100 ELSE 0 END AS over_inventory_percent,
    CASE WHEN total_inventory_count > 0 THEN at_inventory_count / total_inventory_count * 100 ELSE 0 END AS at_inventory_percent,
    CASE WHEN total_inventory_count > 0 THEN under_inventory_count / total_inventory_count * 100 ELSE 0 END AS under_inventory_percent,
    CASE WHEN total_inventory_count > 0 THEN no_inventory_count / total_inventory_count * 100 ELSE 0 END AS no_inventory_percent
FROM inventory_distribution_counts;