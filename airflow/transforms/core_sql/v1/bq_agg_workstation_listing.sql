INSERT INTO `${tenant_id}_oa_curated.agg_workstation_listing` (
      
    WITH workstationList AS (
    SELECT 
        workstation_uuid, 
        workstation_code 
    FROM `${tenant_id}_oa_curated.dim_workstation` 
    WHERE workstation_code IS NOT NULL AND workstation_code != ''
    ),

--- Presume a workstation will have been used within the past month, could be shorter if facts are sent more often.
    workflowFacts AS (
    SELECT *, 
        ROW_NUMBER() OVER (PARTITION BY workstation_code ORDER BY record_timestamp DESC) AS row_num
    FROM
        `${tenant_id}_oa_curated.fct_workstation_workflow`
    WHERE
        record_timestamp > TIMESTAMP_SUB (@end_query_date, INTERVAL 30 DAY)
    ),
--- If available, get workstation information.
    workflowDetails AS (
    SELECT
        workstation_uuid,
        workstation_code as workstation,
        CASE
            WHEN workflow_status = 'ENABLED' THEN workflow
            ELSE 'OTHER'
        END AS workMode,
        CASE
            WHEN workstation_status = 'AVAILABLE' THEN TIMESTAMP_DIFF (@end_query_date, record_timestamp, SECOND)
            ELSE 0
        END AS activeTime,
        record_timestamp,
        workstation_status AS status,
        workflow_status AS workflowStatus
    FROM workflowFacts
    WHERE
        row_num = 1
    ORDER BY workstation_code
    ),

    --- All rates based upon last 15 minutes of performance. ---
    --- Calculate known downtime by workstation for previous interval.
    downTime AS (
    SELECT
        SUM(blocking_time) * 60 AS blockedTime, ---in seconds
        SUM(starvation_time) * 60 AS starvedTime, ---in seconds
        workstation_uuid
    FROM
        `${tenant_id}_oa_curated.agg_workstation_activities`
    WHERE
        record_timestamp > TIMESTAMP_SUB (@end_query_date, INTERVAL 15 MINUTE)
        AND record_timestamp < @end_query_date
    GROUP BY
        workstation_uuid
),
    --- Retrieve distinct records to avoid duplicated date.
    distinctRecords AS (
    SELECT DISTINCT
        record_timestamp,
        location_uuid,
        work_area_uuid,
        module_uuid,
        operator_uuid,
        zone_uuid,
        pick_order_uuid,
        item_uuid,
        cart_instance_code,
        cart_instance_name,
        technology_uuid,
        container_train_code,
        pick_to_container_type_uuid,
        line_item,
        fol.work_type_uuid,
        work_request_code,
        container_type_uuid,
        process_type_code,
        reason_uuid,
        pick_order_line_complete_ind,
        container_physical_code,
        pick_to_container_physical_code,
        container_instance_code,
        pick_batch_code,
        duration_seconds,
        picked_qty,
        skipped_qty,
        shorted_qty,
        new_container_qty,
        requested_qty,
        workstation_uuid,
        area
    FROM
        `${tenant_id}_oa_curated.fct_order_line` fol
        JOIN `${tenant_id}_oa_curated.dim_work_type` dwt 
        ON fol.work_type_uuid = dwt.work_type_uuid
    WHERE
        record_timestamp > TIMESTAMP_SUB (@end_query_date, INTERVAL 15 MINUTE)
        AND record_timestamp < @end_query_date
        AND UPPER(work_type_code) = 'PICK'
    ORDER BY record_timestamp
    ),
    --- Picks/Hr, Lines/Hr - the number of quantity/hr completed at the workstation over the most recent 15 minute window.
    throughPutRates AS (
    SELECT
        COUNT(*) AS linesPerHour,
        SUM(picked_qty) AS picksPerHour,
        workstation_uuid
    FROM distinctRecords
    WHERE
        record_timestamp > TIMESTAMP_SUB (@end_query_date, INTERVAL 15 MINUTE)
    GROUP BY
        workstation_uuid
    ),

    --- Count donor totes from aggregate table (from fct pick activity).
    donorRates AS (
    SELECT
        load_unit_code,
        record_timestamp,
        workstation_uuid
    FROM
        `${tenant_id}_oa_curated.agg_workstation_activities`
    WHERE
        record_timestamp > TIMESTAMP_SUB (@end_query_date, INTERVAL 15 MINUTE)
        AND record_timestamp < @end_query_date
        AND event = 'ARRIVAL'
        AND load_unit_type = 'DONOR'
    ),

    handlingUnitFacts AS (
    SELECT 
        handling_id AS load_unit_code,
        -- Group by minute to cut down on duplicate arrival scans
        TIMESTAMP_TRUNC(record_timestamp, MINUTE) AS record_timestamp,
        workstation_uuid
    FROM 
        `${tenant_id}_oa_curated.fct_handling_unit`
    WHERE 
        record_timestamp > TIMESTAMP_SUB (@end_query_date, INTERVAL 15 MINUTE)
        AND record_timestamp < @end_query_date
        AND event_type = 'ARRIVED' 
        AND handling_unit_type = 'DONOR'
    GROUP BY 
        load_unit_code, 
        workstation_uuid, 
        TIMESTAMP_TRUNC(record_timestamp, MINUTE)
    ),

    --- Count donor totes from distinct records table (from fct order line).
    inferredDonorRates AS (
    SELECT
        pick_to_container_physical_code AS load_unit_code,
        MIN(record_timestamp) AS record_timestamp,
        workstation_uuid
    FROM distinctRecords
    WHERE
        record_timestamp > TIMESTAMP_SUB (@end_query_date, INTERVAL 15 MINUTE)
        AND record_timestamp < @end_query_date
    GROUP BY
        workstation_uuid,
        pick_to_container_physical_code 
    ),

    allDonorTotes AS (
    SELECT * FROM donorRates 
    UNION DISTINCT 
    SELECT * FROM handlingUnitFacts 
    UNION DISTINCT 
    SELECT * FROM inferredDonorRates
    ),
    --- Put them all together.
    combinedDonorRates AS (
    SELECT 
        COUNT( * ) AS donorTotesPerHour, 
        workstation_uuid 
    FROM 
        allDonorTotes 
    GROUP BY
        workstation_uuid
    ),

    --- Count order totes from aggregate table (from fct pick activity).
    orderRates AS (
    SELECT
        load_unit_code,
        record_timestamp,
        workstation_uuid
    FROM
        `${tenant_id}_oa_curated.agg_workstation_activities`
    WHERE
        record_timestamp > TIMESTAMP_SUB (@end_query_date, INTERVAL 15 MINUTE)
        AND record_timestamp < @end_query_date
        AND event = 'ARRIVAL'
        AND load_unit_type = 'ORDER'
    ),

    --- Count order totes from distinct records table (from fct order line).
    inferredOrderRates AS (
    SELECT
        container_physical_code AS load_unit_code,
        MIN(record_timestamp) AS record_timestamp,
        workstation_uuid
    FROM distinctRecords
    WHERE
        record_timestamp > TIMESTAMP_SUB (@end_query_date, INTERVAL 15 MINUTE)
        AND record_timestamp < @end_query_date
    GROUP BY
        workstation_uuid,
        container_physical_code
    ),

    --- Put them all together.
    allOrderTotes AS (
    SELECT * FROM orderRates
    UNION DISTINCT 
    SELECT * FROM inferredOrderRates
    ),

    combinedOrderRates AS (
    SELECT 
        COUNT ( * ) AS orderTotesPerHour,
        workstation_uuid 
    FROM 
        allOrderTotes
    GROUP BY 
        workstation_uuid
    ),

    --- If there's logon/logoff data for operators, use that for current operator. Based upon any starting time 'today' up to no ending time yet.
    operatorsToday AS (
    SELECT DISTINCT
        operator_uuid,
        workstation_uuid,
        ROW_NUMBER() OVER (PARTITION BY workstation_uuid ORDER BY ending_timestamp) AS row_num
    FROM
        `${tenant_id}_oa_curated.agg_workstation_operators`
    WHERE
        starting_timestamp > TIMESTAMP_TRUNC (@end_query_date, DAY)
        AND ending_timestamp <= TIMESTAMP_SUB (@end_query_date, INTERVAL 15 MINUTE)
    ),

    latestOperators AS (
    SELECT *
    FROM operatorsToday
    WHERE
        row_num = 1
    ),

    --- The agg_workstation_operators table recalculates end times every 5 minutes and automatically assigns starting time to within the past hour for logged in operators.
    workstationOperators AS (
    SELECT
        operator_uuid,
        workstation_uuid,
        logged_in_duration,
        starting_timestamp,
        ending_timestamp,
        event_hour,
        update_time,
        ROW_NUMBER() OVER (PARTITION BY workstation_uuid ORDER BY starting_timestamp DESC) AS row_num
    FROM
        `${tenant_id}_oa_curated.agg_workstation_operators`
    WHERE
        starting_timestamp >= TIMESTAMP_TRUNC (TIMESTAMP_SUB (@end_query_date, INTERVAL 1 HOUR), HOUR)
        AND starting_timestamp <= @end_query_date
    ),

    --- Calculate logged in time over the 15 minute timespan. 
    loggedInTimeHelper AS (
      SELECT 
        CASE
            -- To get the minutes worked in the previous 15 minutes when within the same hour with a real logout time
            WHEN event_hour = TIMESTAMP_TRUNC(@end_query_date, HOUR)
                AND TIMESTAMP_TRUNC(@end_query_date, HOUR) = 
                  TIMESTAMP_TRUNC(TIMESTAMP_SUB(@end_query_date, INTERVAL 15 MINUTE), HOUR) 
                --When logout time is real, not default
                AND ending_timestamp != update_time
                THEN
                -- Find the time difference between 
                -- the [current time - 15 minutes] OR the starting time depending on which is most recent
                -- AND the current time OR the ending timestamp depending on which is earlier.  
                TIMESTAMP_DIFF(LEAST(@end_query_date, ending_timestamp), 
                  GREATEST(TIMESTAMP_SUB(@end_query_date, INTERVAL 15 MINUTE), starting_timestamp), MINUTE)

            -- To get the minutes worked in the previous 15 minutes when within the same hour with a default logout time
            WHEN event_hour = TIMESTAMP_TRUNC(@end_query_date, HOUR)
                AND TIMESTAMP_TRUNC(@end_query_date, HOUR) = 
                TIMESTAMP_TRUNC(TIMESTAMP_SUB(@end_query_date, INTERVAL 15 MINUTE), HOUR) 
                --When logout time is default
                AND ending_timestamp = update_time
                THEN
                -- Find the time difference between 
                -- the [current time - 15 minutes] OR the starting time depending on which is most recent
                -- AND the current time.  
                TIMESTAMP_DIFF(@end_query_date, GREATEST(TIMESTAMP_SUB(@end_query_date, INTERVAL 15 MINUTE), 
                   starting_timestamp), MINUTE)

            -- To get the minutes worked in the previous 15 minutes over a split hour with a default logout time
            WHEN event_hour = TIMESTAMP_TRUNC(@end_query_date, HOUR)
                AND TIMESTAMP_TRUNC(@end_query_date, HOUR) != 
                TIMESTAMP_TRUNC(TIMESTAMP_SUB(@end_query_date, INTERVAL 15 MINUTE), HOUR) 
                --When logout time is default
                AND ending_timestamp = update_time
                THEN
                -- Find the time difference between 
                -- the starting time and the current time.  
                TIMESTAMP_DIFF(@end_query_date, starting_timestamp, MINUTE)

            -- To get the minutes worked in the previous 15 minutes over a split hour with a real logout time
            WHEN event_hour = TIMESTAMP_TRUNC(@end_query_date, HOUR)
                AND TIMESTAMP_TRUNC(@end_query_date, HOUR) != 
                TIMESTAMP_TRUNC(TIMESTAMP_SUB(@end_query_date, INTERVAL 15 MINUTE), HOUR) 
                --When logout time is default
                AND ending_timestamp != update_time
                THEN
                -- Find the time difference between 
                -- the starting time and the logout time.  
                TIMESTAMP_DIFF(LEAST(@end_query_date, ending_timestamp), starting_timestamp, MINUTE)

            END AS logged_in_time_hour_one,
      
        CASE 
            WHEN event_hour = TIMESTAMP_SUB(TIMESTAMP_TRUNC(@end_query_date, HOUR), INTERVAL 1 HOUR)
            AND TIMESTAMP_TRUNC(@end_query_date, HOUR) != 
                TIMESTAMP_TRUNC(TIMESTAMP_SUB(@end_query_date, INTERVAL 15 MINUTE), HOUR)
            AND (starting_timestamp >= TIMESTAMP_SUB(@end_query_date, INTERVAL 15 MINUTE) 
            OR ending_timestamp >= TIMESTAMP_SUB(@end_query_date, INTERVAL 15 MINUTE))
            -- Find the time difference between 
            -- the [current time - 15 minutes] OR the starting time depending on which is most recent
            -- AND the logout time.  
            THEN TIMESTAMP_DIFF(ending_timestamp, 
            GREATEST(TIMESTAMP_SUB(@end_query_date, INTERVAL 15 MINUTE), starting_timestamp),  MINUTE)
        END AS logged_in_time_hour_two,
        workstation_uuid 
    FROM workstationOperators
    GROUP BY workstation_uuid, logged_in_duration, event_hour, starting_timestamp, ending_timestamp, update_time),

    totalLoggedInTime AS (
    SELECT
        CASE
            WHEN MAX(logged_in_time_hour_one) IS NULL
                AND MAX(logged_in_time_hour_two) IS NOT NULL 
                THEN MAX(logged_in_time_hour_two)
            WHEN MAX(logged_in_time_hour_two) IS NULL
                AND MAX(logged_in_time_hour_one) IS NOT NULL 
                THEN MAX(logged_in_time_hour_one)
            ELSE MAX(logged_in_time_hour_one) + MAX(logged_in_time_hour_two)
        END AS logged_in_duration,
        workstation_uuid
    FROM loggedInTimeHelper
    GROUP BY
        workstation_uuid
    ),
    --- In case the logged in time isn't complete (logon loggoff records aren't always sent), 
    --- count the minutes within the timespan that a picking related activity occured. 
    --- This will not account for time that may have been blocked/starved preventing activity at the station
    inferredOperatorTime AS (
    SELECT
        COUNT(*) AS logged_in_time,
        workstation_uuid
    FROM (
            SELECT COUNT(*), workstation_uuid
            FROM distinctRecords
            GROUP BY
                TIMESTAMP_TRUNC (record_timestamp, MINUTE), workstation_uuid
        )
    GROUP BY
        workstation_uuid
    ),
    --- Correlate data to use for weighted calculations
    weightedSummary AS (
    --- Use the higher value from prior queries for logged in time
    SELECT
        CASE
            WHEN logged_in_duration IS NULL
                AND logged_in_time IS NOT NULL 
                THEN logged_in_time
            WHEN logged_in_time IS NULL
                AND logged_in_duration IS NOT NULL 
                THEN logged_in_duration
            ELSE GREATEST(
                logged_in_duration,
                logged_in_time
            )
        END AS logged_in_period,
        COALESCE((blockedTime + starvedTime), 0) AS totalDownTime,
        throughPutRates.workstation_uuid
    FROM
        throughPutRates
        LEFT JOIN totalLoggedInTime 
            ON throughPutRates.workstation_uuid = totalLoggedInTime.workstation_uuid
        LEFT JOIN inferredOperatorTime 
            ON throughPutRates.workstation_uuid = inferredOperatorTime.workstation_uuid
        LEFT JOIN downTime 
            ON throughPutRates.workstation_uuid = downTime.workstation_uuid
    ),

    finalCompilation AS (
    SELECT 
        COALESCE(workstation_code, workstation) AS workstation,
        MAX(status) AS status, 
        MAX(workMode) AS work_mode, 
        MAX(workflowStatus) AS workflow_status,
        CASE WHEN MAX(logged_in_period) IS NOT NULL AND MAX(logged_in_period) > 0 THEN
            MAX(operator_uuid) 
        END AS operator_id,
        MAX(logged_in_period) AS logged_in_period, 
        MAX(activeTime) AS active_time,                  -- in seconds
        COALESCE(SUM(starvedTime), 0) AS starved_time,   -- in seconds
        0 AS idle_time,                                  -- Need data
        COALESCE(SUM(blockedTime), 0) AS blocked_time,   -- in seconds
        CAST(COALESCE(SUM(picksPerHour), 0) AS INT64) AS picks_per_hour, 
        COALESCE(SUM(linesPerHour), 0) AS lines_per_hour, 
        COALESCE(MAX(donorTotesPerHour), 0) AS donorTotesPerHour,
        COALESCE(MAX(orderTotesPerHour), 0) AS orderTotesPerHour,
        CAST(COALESCE(ROUND((
            MAX(picksPerHour)/(MAX(logged_in_period) - MAX(totalDownTime))) * 15), 0) AS INT64) 
            AS weighted_picks_per_hour, 
        CAST(COALESCE(ROUND((
            MAX(linesPerHour)/(MAX(logged_in_period) - MAX(totalDownTime))) * 15), 0) AS INT64) 
            AS weighted_lines_per_hour,
        CAST(COALESCE(ROUND((
            MAX(donorTotesPerHour)/(MAX(logged_in_period) - MAX(totalDownTime))) * 15), 0) AS INT64) 
            AS weighted_donor_totes_per_hour, 
        CAST(COALESCE(ROUND((
            MAX(orderTotesPerHour)/(MAX(logged_in_period) - MAX(totalDownTime))) * 15), 0) AS INT64) 
            AS weighted_order_totes_per_hour, 
        SAFE_CAST(@end_query_date AS TIMESTAMP) AS record_timestamp

    --- LEFT JOINs to force full workstations listing
    FROM workstationList 
        LEFT JOIN workflowDetails 
            ON workstationList.workstation_uuid = workflowDetails.workstation_uuid 
        LEFT JOIN downTime 
            ON workstationList.workstation_uuid = downTime.workstation_uuid 
        LEFT JOIN throughPutRates 
            ON workstationList.workstation_uuid = throughPutRates.workstation_uuid 
        LEFT JOIN combinedDonorRates 
            ON workstationList.workstation_uuid = combinedDonorRates.workstation_uuid 
        LEFT JOIN combinedOrderRates 
            ON workstationList.workstation_uuid = combinedOrderRates.workstation_uuid 
        LEFT JOIN latestOperators 
            ON workstationList.workstation_uuid = latestOperators.workstation_uuid
        LEFT JOIN weightedSummary 
            ON workstationList.workstation_uuid = weightedSummary.workstation_uuid
    GROUP BY workstation 
    ORDER BY workstation)
    
    SELECT * FROM finalCompilation
);