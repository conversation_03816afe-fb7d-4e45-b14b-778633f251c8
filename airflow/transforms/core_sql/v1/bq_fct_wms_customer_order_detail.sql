/* **Set variables for running outside of DAG in BQ directly** */
/* declare ltarget_tenant string default '${tenant_id}' ; */
/* declare etl_batch_uuid string default '2023-12-12' ; */
/* declare start_query_date timestamp default timestamp ( '2021-09-27 00:00:00' ) ; */
/* declare end_query_date timestamp default timestamp ( '2024-09-27 02:00:00' ) ; */
CREATE TEMP TABLE tmp_staging_fct_wms_detail ( 
    date_added TIMESTAMP,
    date_edited TIMESTAMP,
    record_timestamp TIMESTAMP NOT NULL,
    orderline_id STRING, -- string null
    order_key STRING,
    orderline STRING, -- string null
    qty_allocated INT64,
    qty_ordered INT64 NOT NULL,
    qty_picked INT64,
    qty_shipped INT64,
    ordered_sku STRING, -- string null
    state STRING,
    store_id STRING,
    sts STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING NOT NULL) ;
INSERT INTO tmp_staging_fct_wms_detail ( 
    date_added,
    date_edited,
    record_timestamp,
    orderline_id,
    order_key,
    orderline,
    qty_allocated,
    qty_ordered,
    qty_picked,
    qty_shipped,
    ordered_sku,
    state,
    store_id,
    sts,
    tenant,
    facility,
    source_system,
    etl_batch_id 
  )
WITH
  extracted_order_details AS (
  SELECT
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.ADDDATE')) AS date_added,
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.EDITDATE')) AS date_edited,
    PARSE_TIMESTAMP('%d-%m-%Y %T', JSON_VALUE(data, '$.ADDDATE')) AS record_timestamp,
    SAFE_CAST(JSON_VALUE(DATA, '$.ORDERIDLINE') AS STRING) AS orderline_id,
    JSON_VALUE(DATA, '$.ORDERKEY') AS order_key,
    SAFE_CAST(JSON_VALUE(DATA, '$.ORDERLINE') AS STRING) AS orderline,
    SAFE_CAST(JSON_VALUE(DATA, '$.QTYALLOCATED') AS INT64) AS qty_allocated,
    SAFE_CAST(JSON_VALUE(DATA, '$.QTYORDERED') AS INT64) AS qty_ordered,
    SAFE_CAST(JSON_VALUE(DATA, '$.QTYPICKED') AS INT64) AS qty_picked,
    SAFE_CAST(JSON_VALUE(DATA, '$.QTYSHIPPED') AS INT64) AS qty_shipped,
    JSON_VALUE(DATA, '$.SKU') AS ordered_sku,
    JSON_VALUE(DATA, '$.STATE') AS state,
    JSON_VALUE(DATA, '$.STORERID') AS store_id,
    JSON_VALUE(DATA, '$.STS') AS sts,
    @ltarget_tenant,
    facility,
    source_system,
    @etl_batch_uuid
  FROM
    `${tenant_id}_landing.WMSCustomerOrderDetailFact`
  WHERE
    tenant = @ltarget_tenant
    AND ingestion_date BETWEEN TIMESTAMP(@start_query_date)
    AND TIMESTAMP(@end_query_date))
SELECT
  DISTINCT *
FROM
  extracted_order_details;

-- FINAL FCT Insert
MERGE INTO `${tenant_id}_oa_curated.fct_wms_customer_order_detail` fct USING (
  SELECT * FROM tmp_staging_fct_wms_detail
) src 
    ON fct.date_added = src.date_added
    AND fct.date_edited = src.date_edited
    AND fct.record_timestamp = src.record_timestamp
    AND fct.orderline_id = src.orderline_id
    AND fct.order_key = src.order_key
    AND fct.orderline = src.orderline
    AND fct.qty_allocated = src.qty_allocated
    AND fct.qty_ordered = src.qty_ordered
    AND fct.qty_picked = src.qty_picked
    AND fct.qty_shipped = src.qty_shipped
    AND fct.ordered_sku = src.ordered_sku
    AND fct.state = src.state
    AND fct.store_id = src.store_id
    AND fct.sts = src.sts
    AND fct.tenant = src.tenant
    AND fct.facility = src.facility
    AND fct.source_system = src.source_system
WHEN NOT MATCHED BY TARGET 
THEN INSERT
(    
    date_added,
    date_edited,
    record_timestamp,
    orderline_id,
    order_key,
    orderline,
    qty_allocated,
    qty_ordered,
    qty_picked,
    qty_shipped,
    ordered_sku,
    state,
    store_id,
    sts,
    tenant,
    facility,
    source_system,
    etl_batch_id 
) VALUES (
    src.date_added,
    src.date_edited,
    src.record_timestamp,
    src.orderline_id,
    src.order_key,
    src.orderline,
    src.qty_allocated,
    src.qty_ordered,
    src.qty_picked,
    src.qty_shipped,
    src.ordered_sku,
    src.state,
    src.store_id,
    src.sts,
    src.tenant,
    src.facility,
    src.source_system,
    src.etl_batch_id
);