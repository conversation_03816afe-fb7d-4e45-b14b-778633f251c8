""" import datetime
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryInsertJobOperator
from airflow.utils.dates import days_ago
from airflow.decorators import task
from google.cloud import storage
import os
import shutil

@task(task_id='create_dir')
def create_local_directory(dir_path: str):
    if not os.path.exists(dir_path):
        os.makedirs(dir_path, exist_ok=True)

@task(task_id='clean_config_dir')
def clear_directory_contents(dir_path: str):
    shutil.rmtree(dir_path, ignore_errors=True)

@task
def download_gcs_files(bucket_name: str, src_prefix: str, dst_dir: str):
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    [blob.download_to_filename(filename=os.path.join(dst_dir, os.path.basename(blob.name))) for blob in bucket.list_blobs(prefix=src_prefix) if not blob.name.endswith('/')]

src_bucket = "ict-b-prototype-etlt-bq-queries"
sql_queries_dir = "airflow/gcs/data/poc_transforms"

with DAG("bq_inventory_fct_dag",
         catchup=False,
         schedule_interval=None,
         template_searchpath=[sql_queries_dir],
         max_active_runs=1,
         default_args={"start_date": days_ago(1), "retries": 2}) as dag:

    etl_id = str(datetime.datetime.now())
    etl_start_date = (
        "{{ (prev_data_interval_end_success or data_interval_start).strftime('%Y-%m-%d %H:%M:%S') }}"
    )
    etl_end_date = "{{ data_interval_end.strftime('%Y-%m-%d %H:%M:%S') }}"

    start = EmptyOperator(task_id="start")
    end = EmptyOperator(task_id="end")

    # Initialize the tasks for clearing and creating the directory
    clear_config_dir_task = clear_directory_contents(sql_queries_dir)
    create_config_dir_task = create_local_directory(sql_queries_dir)

    # Download SQL query files from GCS
    download_sql_queries = download_gcs_files(src_bucket, "bq_queries", sql_queries_dir)

    query_file = "bq_inventory_fct.sql"

    select_query_job = BigQueryInsertJobOperator(
        task_id=f"execute_{query_file.replace('/', '_')}",
        configuration={
            "query": {
                "query": f"{{% include '{query_file}' %}}",
                "useLegacySql": False,
                "queryParameters": [
                    {'name': 'ltarget_tenant', 'parameterType': {'type': 'STRING'}, 'parameterValue': {'value': 'ict_development'}},
                    {'name': 'ltarget_facility', 'parameterType': {'type': 'STRING'}, 'parameterValue': {'value': 'ict_development'}},
                    {'name': 'lsource_system', 'parameterType': {'type': 'STRING'}, 'parameterValue': {'value': 'ict_development'}},
                    {'name': 'start_query_date', 'parameterType': {'type': 'TIMESTAMP'}, 'parameterValue': {'value': etl_start_date}},
                    {'name': 'end_query_date', 'parameterType': {'type': 'TIMESTAMP'}, 'parameterValue': {'value': etl_end_date}},
                    {'name': 'etl_batch_uuid', 'parameterType': {'type': 'STRING'}, 'parameterValue': {'value': etl_id}}
                ]
            }
        }
    )

    start >> clear_config_dir_task >> create_config_dir_task >> download_sql_queries >> select_query_job >> end """