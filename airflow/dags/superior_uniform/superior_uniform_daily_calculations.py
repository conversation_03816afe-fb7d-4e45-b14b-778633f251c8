import datetime
import os
from airflow import DAG
from airflow.decorators import task
from airflow.operators.empty import EmptyOperator
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup
from airflow.models import Variable
from utils.airflow_utils import (
    execute_bigquery_insert_with_fileread,
)

tenant_id = "superior_uniform"
sql_queries_dir = f"/home/<USER>/gcs/data/transforms/core_sql/v1"
dag_id = f"{tenant_id}_daily_calculations_dag"

with DAG(
    dag_id,
    catchup=False,
    schedule_interval="0 0 * * *",  # daily at midnight
    max_active_runs=1,
    default_args={
        "start_date": days_ago(1),
        "retries": 2,
        "retry_delay": datetime.timedelta(minutes=1),
    },
) as dag:
    etl_id = str(datetime.datetime.now())
    calc_queries = [
        "bq_calc_daily_inventory.sql",
        "bq_calc_daily_inventory_by_zone.sql",
    ]

    start = EmptyOperator(task_id="start")
    end = EmptyOperator(task_id="end")

    @task(task_id="execute_bigquery_insert_with_fileread")
    def bigquery_execute():
        queryParameters = [
            {
                "name": "ltarget_tenant",
                "parameterType": {"type": "STRING"},
                "parameterValue": {"value": tenant_id},
            },
            {
                "name": "days_to_look_back",
                "parameterType": {"type": "INT64"},
                "parameterValue": {"value": 30},
            },
            {
                "name": "etl_batch_uuid",
                "parameterType": {"type": "STRING"},
                "parameterValue": {"value": etl_id},
            },
        ]

        for query_file in calc_queries:
            execute_bigquery_insert_with_fileread(
                os.path.join(sql_queries_dir, query_file),
                queryParameters,
                os.path.basename(query_file).replace("/", "_"),
                tenant_id,
            )

    with TaskGroup(
        "LOAD_INV_CALC_DATA", tooltip="daily inventory calculations"
    ) as load_calcs:
        bigquery_execute()

    start >> load_calcs >> end
