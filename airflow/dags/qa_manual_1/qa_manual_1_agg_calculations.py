import datetime
import os
from airflow import DAG
from airflow.decorators import task
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from airflow.models import Variable
from airflow.utils.dates import days_ago
from utils.airflow_utils import (
    execute_bigquery_insert_with_fileread,
    set_etl_dates,
)

# Configuration variables
tenant_id = "qa_manual_1"
facility_id = "qa_manual_1"
source_system = "qa_manual_1"
sql_queries_dir = f"/home/<USER>/gcs/data/transforms/core_sql/v1"
tenant_src_folder = f"tenants/{tenant_id}"
dag_id = f"{tenant_id}_agg_calculations_dag"

with DAG(
    dag_id,
    catchup=False,
    schedule_interval="0 14-22 * * 1-5",  # every hour 9am-5pm weekdays UTC
    max_active_runs=1,
    default_args={
        "start_date": days_ago(1),
        "retries": 2,
        "retry_delay": datetime.timedelta(minutes=1),
    },
) as dag:
    etl_id = str(datetime.datetime.now())
    # Be sure to update the lower query_file block as well.
    agg_queries = [
        "bq_agg_operators_online.sql",
        "bq_agg_workstation_activities.sql",
        "bq_agg_workstation_listing.sql",
        "bq_agg_workstation_operators.sql",
        "bq_agg_orders.sql",
    ]

    start = EmptyOperator(task_id="start")
    end = EmptyOperator(task_id="end")

    # Define factory function for BigQuery execution tasks
    def create_bigquery_task(query_file):
        @task(task_id=f"execute_{os.path.splitext(query_file)[0]}")
        def task_execute_bigquery_insert(
            query_path, query_params, query_name, tenant_id
        ):
            return execute_bigquery_insert_with_fileread(
                query_path, query_params, query_name, tenant_id
            )

        return task_execute_bigquery_insert

    def get_timestamps():
        # This task calls the utility function to get the start of the day and current time
        return set_etl_dates("start_of_day", "current_time")

    with TaskGroup("LOAD_AGG_DATA", tooltip="load aggregated data") as load_aggs:
        timestamps = get_timestamps()
        # Extract start_of_day and end_time from the timestamps dictionary
        start_query_date = timestamps["start_of_day"]
        end_query_date = timestamps["current_time"]
        for query_file in [
            "bq_agg_operators_online.sql",
            "bq_agg_workstation_activities.sql",
            "bq_agg_workstation_listing.sql",
            "bq_agg_workstation_operators.sql",
            "bq_agg_orders.sql",
        ]:
            execute_agg_query_task = create_bigquery_task(query_file)
            execute_agg_query_task(
                os.path.join(sql_queries_dir, query_file),
                [
                    {
                        "name": "ltarget_tenant",
                        "parameterType": {"type": "STRING"},
                        "parameterValue": {"value": tenant_id},
                    },
                    {
                        "name": "start_query_date",
                        "parameterType": {"type": "TIMESTAMP"},
                        "parameterValue": {"value": start_query_date},
                    },
                    {
                        "name": "end_query_date",
                        "parameterType": {"type": "TIMESTAMP"},
                        "parameterValue": {"value": end_query_date},
                    },
                    {
                        "name": "etl_batch_uuid",
                        "parameterType": {"type": "STRING"},
                        "parameterValue": {"value": etl_id},
                    },
                ],
                os.path.basename(query_file).replace("/", "_"),
                tenant_id,
            )

    start >> load_aggs >> end
