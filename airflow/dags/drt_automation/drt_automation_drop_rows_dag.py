import datetime
import logging
from airflow import DAG
from airflow.operators.python_operator import Python<PERSON>perator
from airflow.operators.empty import EmptyOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
from utils.airflow_utils import get_list_of_tables, delete_rows_from_table

# Configure logging
logger = logging.getLogger("airflow.task")

# Declare tenant_id
tenant_id = "drt_automation"

# Define the datasets
datasets = [f"{tenant_id}_oa_curated"]

# DAG declaration
dag_id = f"clean_curated_data_{tenant_id}"
with DAG(
    dag_id,
    catchup=False,
    schedule_interval=None,  # Run manually
    max_active_runs=1,
    default_args={
        "start_date": days_ago(1),
    },
) as dag:
    start = EmptyOperator(task_id="start")
    end = EmptyOperator(task_id="end")

    for dataset in datasets:
        # Calling the get_list_of_tables function from airflow_utils module
        tables = get_list_of_tables(dataset)
        for table in tables:
            # Creating a task for each table to delete its rows
            delete_task = PythonOperator(
                task_id=f"delete_rows_{dataset}_{table}",
                python_callable=delete_rows_from_table,
                op_args=[dataset, table],
            )
            start >> delete_task >> end
