import datetime
import os
import shutil
import time
import logging
from google.cloud import storage, bigquery
from typing import List, Dict, Optional, Callable
from airflow.utils.db import Session, provide_session
from airflow.utils.session import NEW_SESSION
from airflow.models.taskinstance import TaskInstance, TaskInstanceState


# Configure logging for the module. This logger will be used across all utility functions.
task_logger = logging.getLogger("airflow.task")

# Define the __all__ variable for wildcard imports
__all__ = [
    "execute_bigquery_insert_with_fileread",
    "get_list_of_tables",
    "delete_rows_from_table",
    "set_etl_dates",
    "get_previous_ti_success",
]


def execute_bigquery_insert_with_fileread(
    query_path: str,
    query_params: List[Dict],
    query_name: str,
    tenant_id: str,
    **context,
):
    """
    Execute a BigQuery insert operation by reading a query from a file.
    Query files are configured in DAGs.

    Args:
        query_path (str): File path of the SQL query.
        query_params (List[Dict]): Parameters for the SQL query.
        query_name (str): Name of the query (used for logging).
        tenant_id (str): Tenant ID (used in query string replacement).
        **context: Additional context for logging.
    """
    task_logger.info(f"Starting execution of query: {query_name}")

    with open(query_path, "r") as file:
        sql_string = file.read().replace("${tenant_id}", tenant_id.replace("-", "_"))

    formatted_query_params = []
    for param in query_params:
        formatted_param = bigquery.ScalarQueryParameter(
            param["name"],
            param["parameterType"]["type"],
            param["parameterValue"]["value"],
        )
        formatted_query_params.append(formatted_param)
        task_logger.info(
            f"Query Parameter: Name - {param['name']}, Type - {param['parameterType']['type']}, Value - {param['parameterValue']['value']}"
        )

    client = bigquery.Client()
    job_config = bigquery.QueryJobConfig(
        query_parameters=formatted_query_params, use_legacy_sql=False
    )
    query_job = client.query(sql_string, job_config=job_config)
    query_job.result()

    _log_bigquery_job_stats(query_job, query_name)
    return query_job.total_bytes_billed


def _check_file_exist_with_retry(
    path_to_file: str, retries: int = 5, sleep_time_sec: int = 10
):
    """
    Check if a file exists at a given path, with a specified number of retries.

    Args:
        path_to_file (str): The file path to check.
        retries (int): Number of retries.
        sleep_time_sec (int): Seconds to wait between retries.
    """
    retry_num = 0
    while retry_num < retries:
        retry_num += 1
        if os.path.exists(path_to_file):
            return True
        else:
            time.sleep(sleep_time_sec)
    return False


def _log_bigquery_job_stats(query_job, query_name):
    """
    Log various statistics of a completed BigQuery job.

    Args:
        query_job (bigquery.job.QueryJob): The BigQuery job to log statistics for.
        query_name (str): Name of the query.
    """
    stats = {
        "Query Name": query_name.upper(),
        "Query Job ID": query_job.job_id,
        "User": query_job.user_email,
        "Query Duration (seconds)": query_job.ended - query_job.started,
        "Total Affected Rows": query_job.num_dml_affected_rows,
        "Total Bytes Processed": "{:,}".format(query_job.total_bytes_processed),
        "Total Bytes Billed": "{:,}".format(query_job.total_bytes_billed),
        "Total Slot Milliseconds": query_job.slot_millis,
        "Errors": query_job.errors,
    }

    for key, value in stats.items():
        task_logger.info(f"{key}: {value}")


def _log_download_task_details(**kwargs):
    """
    Log details for a file download task based on provided keyword arguments.

    Args:
        **kwargs: Key-value pairs representing details to be logged.
    """
    for key, value in kwargs.items():
        task_logger.info(f"{key}: {value}")


def get_list_of_tables(dataset_name):
    """
    Get a list of tables in a BigQuery dataset.

    Args:
        dataset_name (str): The name of the BigQuery dataset.

    Returns:
        list: A list of table names in the dataset.
    """
    client = bigquery.Client()
    query = f"SELECT table_id FROM `{dataset_name}.__TABLES__`"
    query_job = client.query(query)
    return [row.table_id for row in query_job]

def get_list_of_fct_dim_tables(dataset_name):
    """
    Get a list of tables in a BigQuery dataset.

    Args:
        dataset_name (str): The name of the BigQuery dataset.

    Returns:
        list: A list of table names in the dataset.
    """
    client = bigquery.Client()
    query = f"SELECT table_id FROM `{dataset_name}.__TABLES__` where table_id like 'fct%' or table_id like 'dim%'"
    query_job = client.query(query)
    return [row.table_id for row in query_job]

def delete_rows_from_table(dataset, table):
    """
    Delete all rows from a specified BigQuery table and log the count of deleted rows.

    Args:
        dataset (str): The dataset containing the table.
        table (str): The name of the table from which rows will be deleted.
    """
    client = bigquery.Client()
    query = f"DELETE FROM `{dataset}.{table}` WHERE TRUE"
    query_job = client.query(query)
    query_job.result()  # Wait for the query to finish

    # Logging the row count (approximation)
    task_logger.info(
        f"Deleted rows from {dataset}.{table}: {query_job.num_dml_affected_rows}"
    )


# ==============================
# Timestamp Utility Functions
# Here is where we can put our timestamp calculation functions for DAG params.
# When you add a function, add the string to function mapping below
# Use the string as a parameter when you call `set_etl_dates()`
# When you add a new time function below, you do not need to add it to the exported functions at the top of this module.
# ==============================


def get_start_of_day() -> str:
    """Returns the start of the current day."""
    return datetime.datetime.combine(
        datetime.datetime.now().date(), datetime.time.min
    ).strftime("%Y-%m-%d %H:%M:%S")


def get_current_time() -> str:
    """Returns the current time."""
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


# ------------------------------------
# Mapping Parameters to Functions
# ------------------------------------

# Map input parameters to callback functions for easy access and modularity
timestamp_functions: Dict[str, Callable[[], str]] = {
    "start_of_day": get_start_of_day,
    "current_time": get_current_time,
}

# Main Utility Function


def set_etl_dates(*params: str) -> Dict[str, Optional[str]]:
    """
    Calculate and return date/time values based on provided parameters using callback functions.

    Args:
    - params (tuple of str): Parameters specifying which date/time values to calculate.

    Returns:
    - dict: A dictionary where keys are the parameter names and values are the calculated date/time values.
    """

    results: Dict[str, Optional[str]] = {}

    for param in params:
        calculation_function = timestamp_functions.get(param)
        if calculation_function:
            results[param] = calculation_function()
        else:
            task_logger.warning(
                f"Invalid parameter: {param}. No matching calculation found."
            )
            results[param] = None

    # Logging the returned values
    if results:
        for key, value in results.items():
            task_logger.info(f"{key.capitalize()}: {value}")

    return results


@provide_session
def get_previous_ti_by_state(
    task_instance: TaskInstance,
    task_state: TaskInstanceState,
    session: Session = NEW_SESSION,
) -> TaskInstance:
    # retrieve the latest Task Instance model whose TaskInstanceState corresponds to the specified task instance state
    return (
        session.query(TaskInstance)
        .filter_by(state=task_state, task_id=task_instance.task_id)
        .order_by(TaskInstance.start_date.desc())
        .first()
    )


@provide_session
def get_previous_ti_success(
    task_instance: TaskInstance, session: Session = NEW_SESSION
) -> TaskInstance:
    return get_previous_ti_by_state(
        task_instance=task_instance,
        task_state=TaskInstanceState.SUCCESS,
        session=session,
    )
