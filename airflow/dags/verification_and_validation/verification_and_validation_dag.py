import datetime
import os
import logging
from airflow import DAG
from airflow.decorators import task
from airflow.operators.empty import EmptyOperator
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup
from airflow.utils.state import DagRunState
from airflow.models import Variable
from airflow.models.param import Param, ParamsDict

# Configure logging for the module. This logger will be used across all utility functions.
task_logger = logging.getLogger("airflow.task")

# Import utility functions from your utils module
from utils.airflow_utils import (
    execute_bigquery_insert_with_fileread,
    get_previous_ti_success,
)

# Configuration variables
tenant_id = "verification_and_validation"
facility_id = "verification_and_validation"
source_system = "verification_and_validation"
sql_queries_dir = f"/home/<USER>/gcs/data/transforms/core_sql/v1"
tenant_src_folder = f"tenants/{tenant_id}"

dag_id = f"{tenant_id}_main_dag"

with DAG(
    dag_id,
    catchup=False,
    schedule_interval="*/10 14-22 * * 1-5",  # QA every 10 minutes 9am-5pm weekdays UTC
    max_active_runs=1,
    default_args={
        "start_date": days_ago(1),
        "retries": 2,
        "retry_delay": datetime.timedelta(minutes=1),
    },
) as dag:
    etl_id = str(datetime.datetime.now())
    etl_start_date = "{{ (prev_data_interval_end_success or data_interval_start).strftime('%Y-%m-%d %H:%M:%S') }}"
    etl_end_date = "{{ data_interval_end.strftime('%Y-%m-%d %H:%M:%S') }}"

    query_params = [
        {
            "name": "ltarget_tenant",
            "parameterType": {"type": "STRING"},
            "parameterValue": {"value": tenant_id},
        },
        {
            "name": "start_query_date",
            "parameterType": {"type": "TIMESTAMP"},
            "parameterValue": {"value": etl_start_date},
        },
        {
            "name": "end_query_date",
            "parameterType": {"type": "TIMESTAMP"},
            "parameterValue": {"value": etl_end_date},
        },
        {
            "name": "etl_batch_uuid",
            "parameterType": {"type": "STRING"},
            "parameterValue": {"value": etl_id},
        },
    ]

    # Define factory function for BigQuery execution tasks
    def create_bigquery_task(query_file, **kwargs):
        @task(
            task_id=f"execute_{os.path.splitext(query_file)[0]}",
            execution_timeout=datetime.timedelta(seconds=3600),
        )
        def task_execute_bigquery_insert(
            query_path, query_params, query_name, tenant_id, **kwargs
        ):
            manual = False
            dag_run = kwargs.get("dag_run")
            task_instance = kwargs.get("task_instance")

            if dag_run.external_trigger:
                # If it is run manually, use the parameters passed in by the DAG run
                task_logger.info("DAG triggered manually!")
                manual = True
            else:
                task_logger.info("DAG scheduled")
                # Get the start query window based on the last successful run of THIS TASK
                previous_successful_ti = get_previous_ti_success(task_instance)
                task_logger.info("TaskInstance & Previous Successful TaskInstance")
                task_logger.info(task_instance)
                if previous_successful_ti and previous_successful_ti.execution_date:
                    task_logger.info(previous_successful_ti)
                    task_logger.info(previous_successful_ti.execution_date)
                    for item in query_params:
                        if item["name"] == "start_query_date":
                            item["parameterValue"]["value"] = (
                                previous_successful_ti.execution_date.strftime(
                                    "%Y-%m-%d %H:%M:%S"
                                )
                            )
                            break

            return execute_bigquery_insert_with_fileread(
                query_path, query_params, query_name, tenant_id
            )

        return task_execute_bigquery_insert

    # Define DAG structure
    start = EmptyOperator(task_id="start")
    end = EmptyOperator(task_id="end")

    with TaskGroup("LOAD_DIM_QUERY_DATA", tooltip="transform dims") as load_dims:
        for query_file in [
            "bq_dim_item.sql",
            "bq_dim_location.sql",
            "bq_dim_operator.sql",
            "bq_dim_status.sql",
            "bq_dim_subgroup.sql",
            "bq_fct_receiving_advice_based_create_inventory.sql",
        ]:
            execute_query_task = create_bigquery_task(query_file)
            execute_query_task(
                os.path.join(sql_queries_dir, query_file),
                query_params,
                os.path.basename(query_file).replace("/", "_"),
                tenant_id,
            )

    with TaskGroup("LOAD_FCT_DATA", tooltip="transform facts") as load_fcts:
        for query_file in [
            "bq_fct_bin_utilization.sql",
            "bq_fct_counting.sql",
            "bq_fct_dialog_active.sql",
            "bq_fct_facility_order_line.sql",
            "bq_fct_facility_order.sql",
            "bq_fct_fault.sql",
            "bq_fct_handling_unit.sql",
            "bq_fct_induction.sql",
            "bq_fct_inventory.sql",
            "bq_fct_movement.sql",
            "bq_fct_order_line.sql",
            "bq_fct_pack_activity.sql",
            "bq_fct_pack_task_line.sql",
            "bq_fct_pack_task.sql",
            "bq_fct_pick_activity.sql",
            "bq_fct_pick_order.sql",
            "bq_fct_power_chain_telemetry.sql",
            "bq_fct_receiving_advice_summary.sql",
            "bq_fct_sort.sql",
            "bq_fct_transport.sql",
            "bq_fct_wms_customer_order_detail.sql",
            "bq_fct_workstation_workflow.sql",
        ]:

            execute_query_task = create_bigquery_task(query_file)
            execute_query_task(
                os.path.join(sql_queries_dir, query_file),
                query_params,
                os.path.basename(query_file).replace("/", "_"),
                tenant_id,
            )

    with TaskGroup("LOAD_AGG_DATA", tooltip="load aggregated data") as load_aggs:
        agg_query_files = []
        for query_file in agg_query_files:
            execute_agg_query_task = create_bigquery_task(query_file)
            execute_agg_query_task(
                os.path.join(sql_queries_dir, query_file),
                query_params,
                os.path.basename(query_file).replace("/", "_"),
                tenant_id,
            )

    start >> load_dims >> load_fcts >> end
