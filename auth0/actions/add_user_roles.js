/**
 * This action is called post-login and is used to add default roles to a user. "default_roles" is metadata on the organization and
 * is a comma-separated list of role names.
 *
 * This action requires the management API, we cache the token to avoid consuming too many tokens.
 *
 * Info on caching:
 * https://auth0.com/docs/customize/actions/explore-triggers/signup-and-login-triggers/login-trigger/post-login-api-object
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
exports.onExecutePostLogin = async (event, api) => {
  const namespace = "https://ict.dematic.cloud";

  let userRoles = event.authorization?.roles
    ? [...event.authorization.roles]
    : [];

  // Retrieve secrets using both lowercase and uppercase keys
  const domain = event.secrets?.domain ?? event.secrets?.DOMAIN;
  const orgMeta = event.organization?.metadata;
  const defaultRolesString = orgMeta?.default_roles;

  if (
    typeof defaultRolesString === "string" &&
    defaultRolesString.trim() !== ""
  ) {
    const defaultRoleNames = defaultRolesString
      .split(",")
      .map((role) => role.trim())
      .filter(Boolean);

    const newRoleNames = defaultRoleNames.filter(
      (role) => !userRoles.includes(role)
    );

    if (newRoleNames.length > 0) {
      try {
        // Get cached or fresh management token
        const accessToken = await getManagementToken(event, api);

        const { ManagementClient } = require("auth0");
        const management = new ManagementClient({
          domain: domain,
          token: accessToken,
        });

        // Fetch all roles fresh each time (no caching)
        const rolesResponse = await management.roles.getAll();
        const roles = Array.isArray(rolesResponse?.data)
          ? rolesResponse.data
          : [];

        const roleMap = {};
        roles.forEach((role) => {
          roleMap[role.name] = role.id;
        });

        const roleIds = newRoleNames
          .filter((name) => roleMap[name])
          .map((name) => roleMap[name]);

        if (roleIds.length > 0) {
          // Make direct API call to assign organization member roles
          const url = `https://${domain}/api/v2/organizations/${event.organization.id}/members/${event.user.user_id}/roles`;
          console.log(`Assigning organization roles to URL: ${url}`);

          const response = await fetch(url, {
            method: "POST",
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ roles: roleIds }),
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(
              `Failed to assign organization roles: ${response.status} ${response.statusText} - ${errorText}`
            );
          }

          userRoles = [...userRoles, ...newRoleNames];
        }
      } catch (error) {
        console.log("Error assigning default roles:", error);
      }
    }
  }

  if (userRoles.length > 0) {
    api.idToken.setCustomClaim(`${namespace}/roles`, userRoles);
    api.accessToken.setCustomClaim(`${namespace}/roles`, userRoles);
  }
};

/**
 * Get an Auth0 Management API token, cached with TTL.
 */
async function getManagementToken(event, api) {
  const cacheKey = "m2mToken";
  const cached = await api.cache.get(cacheKey);

  const domain = event.secrets?.domain ?? event.secrets?.DOMAIN;
  const clientId = event.secrets?.clientId ?? event.secrets?.CLIENT_ID;
  const clientSecret =
    event.secrets?.clientSecret ?? event.secrets?.CLIENT_SECRET;

  if (cached) return cached;

  const res = await fetch(`https://${domain}/oauth/token`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      client_id: clientId,
      client_secret: clientSecret,
      audience: `https://${domain}/api/v2/`,
      grant_type: "client_credentials",
    }),
  });

  const data = await res.json();

  if (!data.access_token) {
    throw new Error(`Failed to obtain M2M token: ${JSON.stringify(data)}`);
  }

  // Subtract 5 min buffer from our Auth0 Management API token (24 hours)
  const ttl = Math.max(data.expires_in - 300, 60);
  await api.cache.set(cacheKey, data.access_token, { ttl });

  return data.access_token;
}
