const axios = require("axios");

const Auth0TableauServerRoleMap = {
  ct_tableau_viewer: "Viewer",
  ct_tableau_explorer: "ExplorerCanPublish",
  ct_tableau_admin: "ServerAdministrator",
};

/**
 * Handler that will be called during the execution of a PostLogin flow.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
exports.onExecutePostLogin = async (event, api) => {
  const auth0Roles = Object.keys(Auth0TableauServerRoleMap);

  let tableauRole = undefined;
  for (const role of auth0Roles) {
    if (event.authorization && event.authorization.roles.includes(role)) {
      tableauRole = role;
      break;
    }
  }
  if (!tableauRole) {
    console.log(
      `${event.user.user_id} does not have any required Tableau roles.`
    );
    return;
  }

  const tableauSite = event.organization?.metadata?.tableauSite;

  // if the action gets here, the user needs to be able to access Tableau
  // get a Tableau API auth token
  const signInConfig = {
    withCredentials: false,
    headers: {
      "content-type": "application/json",
    },
  };
  const signInBody = {
    credentials: {
      personalAccessTokenName: event.secrets.adminPATName,
      personalAccessTokenSecret: event.secrets.adminPATSecret,
      site: {
        contentUrl: tableauSite,
      },
    },
  };
  const apiAuthURL = `${event.secrets.tableauServerURL}/api/3.6/auth/signin`;

  let signInResponse;
  try {
    const response = await axios.post(apiAuthURL, signInBody, signInConfig);
    if (response.status != 200) {
      throw new Error("Invalid API response code.");
    }
    signInResponse = response.data;
  } catch (err) {
    // logging in Auth0? just return from this action for now
    console.error(err);
    return;
  }

  const siteId = signInResponse.credentials.site.id;
  const apiAuthToken = signInResponse.credentials.token;

  // ensure that a Tableau user exists with this user's Auth0 id
  // if not, attempt to create one
  const getUsersURL = `${event.secrets.tableauServerURL}/api/3.6/sites/${siteId}/users?filter=name:eq:${event.user.user_id}`;
  const apiConfig = {
    headers: {
      "content-type": "application/json",
      "X-Tableau-Auth": apiAuthToken,
    },
  };
  let getUsersResponse;
  try {
    const response = await axios.get(getUsersURL, apiConfig);
    if (response.status != 200) {
      throw new Error("Invalid API response code.");
    }
    getUsersResponse = response.data;
  } catch (err) {
    // logging in Auth0? just return from this action for now
    console.error(err);
    return;
  }

  console.log(getUsersResponse.users);

  if (
    getUsersResponse.users &&
    getUsersResponse.users.user &&
    getUsersResponse.users.user.length > 0
  ) {
    // found the user
    // ensure the role
    const user = getUsersResponse.users.user[0];
    console.log(
      `Found an existing Tableau user, ensuring role... Auth0: ${Auth0TableauServerRoleMap[tableauRole]} Tableau: ${user.siteRole}`
    );

    if (
      Auth0TableauServerRoleMap[tableauRole] &&
      Auth0TableauServerRoleMap[tableauRole] !== user.siteRole
    ) {
      const updateUserURL = `${event.secrets.tableauServerURL}/api/3.6/sites/${siteId}/users/${user.id}`;
      const updateUserBody = {
        user: {
          siteRole: Auth0TableauServerRoleMap[tableauRole],
        },
      };
      try {
        const response = await axios.put(
          updateUserURL,
          updateUserBody,
          apiConfig
        );
        if (response.status != 200) {
          throw new Error("Invalid API response code.");
        }
      } catch (err) {
        // logging in Auth0? continue on specifically here, as we have a Tableau account, we just couln't ensure the role
        console.error(err);
      }
    }
  } else {
    console.log("No existing Tableau user, attempting to create a new one...");

    // no user found, need to attempt to create one
    const createUserURL = `${event.secrets.tableauServerURL}/api/3.6/sites/${siteId}/users`;
    const newUserBody = {
      user: {
        name: event.user.user_id,
        siteRole: Auth0TableauServerRoleMap[tableauRole],
      },
    };
    let createUserResponse;
    try {
      const response = await axios.post(createUserURL, newUserBody, apiConfig);
      if (response.status != 201) {
        throw new Error("Invalid API response code.");
      }
      createUserResponse = response.data;

      // created the user, now update it with the email
      if (createUserResponse.user) {
        const updateUserURL = `${event.secrets.tableauServerURL}/api/3.6/sites/${siteId}/users/${createUserResponse.user.id}`;
        const updateUserBody = {
          user: {
            fullName: event.user.email,
            email: event.user.email,
          },
        };
        try {
          const response = await axios.put(
            updateUserURL,
            updateUserBody,
            apiConfig
          );
          if (response.status != 200) {
            throw new Error("Invalid API response code.");
          }
        } catch (err) {
          // logging in Auth0? continue on specifically here, as we have a Tableau account, we just couln't ensure the role
          console.error(err);
        }
      }
    } catch (err) {
      // logging in Auth0? just return from this action for now
      console.error(err);
      return;
    }
  }
};
