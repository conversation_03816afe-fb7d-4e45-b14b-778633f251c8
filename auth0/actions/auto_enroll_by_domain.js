const { ManagementClient, ApiResponse, GetOrganizations200ResponseOneOf } = require('auth0');

// --- AUTH0 ACTIONS TEMPLATE https://github.com/auth0/opensource-marketplace/blob/main/templates/add-persistence-attribute-POST_USER_REGISTRATION ---
/**
 * Handler that will be called during the execution of a PostUserRegistration flow.. Makes use of a few SECRETS:
 *
 * @param {Event} event - Details about the context and user that has registered.
 * @param {PostUserRegistrationAPI} api - Methods and utilities to help change the behavior after a signup.
 */
exports.onExecutePostUserRegistration = async (event, api) => {
    if (!event.user.email) {
        return;
    }

    const emailDomain = event.user.email.split("@").pop();
    if (!emailDomain) {
        return;
    }

    const management = new ManagementClient({
        domain: event.secrets.domain,
        clientId: event.secrets.clientId,
        clientSecret: event.secrets.clientSecret,
    });

    const apiResponse = await management.organizations.getAll();
    const organizations = apiResponse.data;
    organizations.forEach(async (organization) => {
        const domains = organization.metadata['domains']?.split(',');
        if(domains && domains.includes(emailDomain)) {
            const params = { id : organization.id };
            const data = { members : [event.user.user_id] };

            const res = await management.organizations.addMembers(params, data);
        }
    });
};

