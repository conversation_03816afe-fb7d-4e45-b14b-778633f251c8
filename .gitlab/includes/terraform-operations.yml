---
include:
  - local: "$CI_PROJECT_DIR/.gitlab/includes/base-jobs-templates.yml"

variables:
  # NOTE: TERRAFORM_MODULES is a sequential list, from left to right.  So ensure you put these in order of precedence, if there is any.
  TERRAFORM_MODULES: "VPC Network PubSub BigQuery Auth0 CloudFunction CloudComposer CloudRun"

.exec_terraform_action: &exec_terraform_action
  - |
    export TXT_GREEN='\e[1;92m'
    export TXT_CLEAR='\e[0m'
    for MODULE in $TERRAFORM_MODULES; do
      cd $CI_PROJECT_DIR/infrastructure/$MODULE
      rm -rf .terraform/
      rm -rf .terraform.lock.hcl
      echo
      echo -e "${TXT_GREEN}"
      echo "=================================="
      echo "${MODULE}"
      echo "tofu $TF_ACTION -var-file=../Configs/$TF_CONFIG_FILE $TF_OPTION"
      echo "=================================="
      echo -e "${TXT_CLEAR}"
      tofu init -var-file=../Configs/$TF_CONFIG_FILE
      tofu $TF_ACTION -var-file=../Configs/$TF_CONFIG_FILE $TF_OPTION
    done

# -------------------------------------------------------------------------------------------------
#                                         validate stage jobs
# -------------------------------------------------------------------------------------------------

etl:terraform:check:
  extends: .base_job
  stage: validate
  needs: []
  cache: []
  script:
    - cd infrastructure/
    - tofu --version
    - tofu fmt -recursive -check
  after_script:
    - rm -f ${CI_PROJECT_DIR}/key.json
  rules:
    - if: $IS_ICT_DEV_MERGE_REQUEST_PIPELINE

etl:terraform:plan:
  extends: .auth_base_job
  stage: validate
  variables:
    TF_ACTION: plan
    TF_OPTION: -lock=false
    TF_CONFIG_FILE: ${ICT_ENVIRONMENT}.tfvars
    # need variables to be set here from workflow
    ENVIRONMENT: ${ICT_ENVIRONMENT}
    GCP_PHASE2_PROJECT_ID: ${ICT_DEPLOY_GCP_PROJECT_ID}
    AUTH0_CLIENT_SECRET: ${ICT_DEPLOY_AUTHO_CLIENT_SECRET}
    TF_VAR_tableau_PAT_name: ${ICT_DEPLOY_TABLEAU_PAT_NAME}
    TF_VAR_tableau_PAT_secret: ${ICT_DEPLOY_TABLEAU_PAT_SECRET}
    TF_VAR_auth0_mgmt_client_secret: ${ICT_DEPLOY_AUTH0_MQQT_CLIENT_SECRET}
  needs:
    - etl:docker-build
  script:
    - export AUTH0_CLIENT_SECRET=${AUTH0_CLIENT_SECRET}
    - gcloud config set project ${GCP_PHASE2_PROJECT_ID}
    - *exec_terraform_action
  after_script:
    - rm -f key.json
  timeout: 150 minutes
  rules:
    - if: $IS_ICT_MERGE_REQUEST_PIPELINE

# -------------------------------------------------------------------------------------------------
#                                         deploy stage jobs
# -------------------------------------------------------------------------------------------------

etl:deploy:
  extends: .auth_base_job
  stage: deploy
  allow_failure: false
  variables:
    TF_ACTION: apply
    TF_OPTION: -auto-approve
    # workflow variables.
    ENVIRONMENT: ${ICT_ENVIRONMENT}
    TF_CONFIG_FILE: ${ICT_ENVIRONMENT}.tfvars
    AUTH0_CLIENT_SECRET: ${ICT_DEPLOY_AUTHO_CLIENT_SECRET}
    TF_VAR_tableau_PAT_name: ${ICT_DEPLOY_TABLEAU_PAT_NAME}
    TF_VAR_tableau_PAT_secret: ${ICT_DEPLOY_TABLEAU_PAT_SECRET}
    TF_VAR_auth0_mgmt_client_secret: ${ICT_DEPLOY_AUTH0_MQQT_CLIENT_SECRET}
  # workflow variable.
  environment: ${ICT_ENVIRONMENT}
  needs:
    - etl:docker-build
  resource_group: ${ENVIRONMENT}
  script:
    - echo "Preparing to deploy ${ICT_ENVIRONMENT}"
    - npm install
    - *exec_terraform_action
  rules:
    - if: $IS_ICT_DEPLOY_PIPELINE
      when: always
