---
variables:
  DIND_BUILD_IMAGE: us-docker.pkg.dev/ict-o-registry/ict-o-registry/ops-dind:25-dind

# -------------------------------------------------------------------------------------------------
#                                         deploy stage jobs
# -------------------------------------------------------------------------------------------------

etl:tableau-sites:deploy:
  stage: deploy
  allow_failure: false
  needs: []
  tags:
    - gke-runners-dematic
  script:
    - echo "Installing jq"
    - apt install -y jq
    - echo "Ensuring Tableau sites for tenants that need them"
    - bash ./infrastructure/ensure_tableau_sites.sh ${TF_CONFIG_FILE}
  variables:
    TF_CONFIG_FILE: $ICT_ENVIRONMENT.tfvars
    ENVIRONMENT: $ICT_ENVIRONMENT
    TABLEAU_PAT_NAME: $ICT_DEPLOY_TABLEAU_PAT_NAME
    TABLEAU_PAT_SECRET: $ICT_DEPLOY_TABLEAU_PAT_SECRET
  rules:
    - if: $IS_ICT_DEPLOY_PIPELINE
      when: always
