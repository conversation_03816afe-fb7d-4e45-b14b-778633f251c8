---
# NOTES:
# This requires three variables to be present to utilize
# SRCCLR_API_TOKEN
# VERACODE_API_ID
# VERACODE_API_SECRET

include:
    - local: .gitlab/includes/sast-base.gitlab-ci.yml

.if-dev-mr-pipeline: &if-dev-mr-pipeline
    if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'

.run-veracode-pipeline-scan: &run-veracode-pipeline-scan
    - echo "running veracodepipeline scan..."
    - zip -qr repo.zip ./
    - java -jar /opt/veracode/pipeline-scan.jar
        --veracode_api_id "${VERACODE_API_ID}"
        --veracode_api_key "${VERACODE_API_SECRET}"
        --file "repo.zip"
        --app_id "${CI_PROJECT_NAME}"
        --json_output_file="pipeline_results.json"
        --fail_on_severity="Very High, High"
        --timeout "${CI_TIMEOUT}"
        --project_name "${CI_PROJECT_PATH}"
        --project_url "${CI_REPOSITORY_URL}"
        --project_ref "${CI_COMMIT_REF_NAME}" | tee -a veracode.log
    - cat pipeline_results.json

.run-veracode-api-wrapper: &run-veracode-api-wrapper
    - echo "running API WRAPPER scan"
    - java -jar /opt/veracode/api-wrapper.jar
        -vid "${VERACODE_API_ID}"
        -vkey "${VERACODE_API_SECRET}"
        -action UploadAndScan
        -appname "${CI_PROJECT_NAME}"
        -createprofile false
        -autoscan true
        -version "${CI_MERGE_REQUEST_ID}|${CI_COMMIT_REF_NAME}|${CI_COMMIT_SHORT_SHA}"
        -deleteincompletescan 1
        -filepath ./repo.zip | tee -a veracode.log
    - echo "Scan has been sent along to Veracode and should be in progress..."
    
.run-sca-scan: &run-sca-scan
    - echo "Performing SCA Scan"
    - curl -sSL https://download.sourceclear.com/ci.sh | bash -s scan . --loud --update-advisor --json scaResults.json --allow-dirty 2>&1 | tee sca_output.txt

.run-veracode-dependency-analysis: &run-veracode-dependency-analysis
    - git clone -b "main" --depth 50 https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com/veracode-gitlab-manual/veracode-helpers.git
    - npm install axios mathjs
    - node ./veracode-helpers/Veracode-SCA-Results/dependencies.js ${PRIVATE_TOKEN} false ${CI_PROJECT_ID}

veracode-api-wrapper-scan:
    image: veracode/api-wrapper-java:**********
    extends:
        - .scan-base
        - .veracode-zip-prep
    script:
        - *run-veracode-api-wrapper
    after_script:
        - *run-sca-scan
    allow_failure: true
    artifacts:
        paths:
            - veracode.log
            - scaResults.json
        when: always
        name: "veracode-API-WRAPPER-SCAN-RESULTS-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}"
        expire_in: 1 day
    timeout: 45 minutes
    rules:
        - <<: *if-dev-mr-pipeline

veracode-pipeline-scan:
    image: veracode/pipeline-scan:25.1.0
    extends:
        - .scan-base
    script:
        - *run-veracode-pipeline-scan
    after_script:
        - *run-sca-scan
        - *run-veracode-dependency-analysis
    artifacts:
        paths:
        - veracode.log
        - pipeline_results.json
        - sca_output.txt
        when: always
        name: "veracode-PIPELINE-SCAN-RESULTS-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}"
        expire_in: 1 day
    timeout: 45 minutes
    rules:
        - <<: *if-dev-mr-pipeline