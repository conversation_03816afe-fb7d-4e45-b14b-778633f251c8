---
include:
  - local: "$CI_PROJECT_DIR/.gitlab/includes/base-jobs-templates.yml"

# # Create MR-specific tfvars file
# .create-mr-specific-tfvars: &create-mr-specific-tfvars
#   - cd ${CI_PROJECT_DIR}/infrastructure/${TERRAFORM_MODULE}
#   - TF_CONFIG_FILE=$(../Scripts/update-tfvars.sh ../Configs/${TF_CONFIG_FILE} | grep CREATED_TFVARS_FILE | cut -d'=' -f2)
#   - TF_CONFIG_FILE=$(basename ${TF_CONFIG_FILE})
#   - echo "Created tfvars file - ${TF_CONFIG_FILE}"

# # -------------------------------------------------------------------------------------------------
# #                                         validate stage jobs
# # -------------------------------------------------------------------------------------------------

# etl:liquibase:test:
#   stage: validate
#   services:
#     - name: us-docker.pkg.dev/ict-o-registry/ict-o-registry/ops-dind:25-dind
#       alias: docker
#   allow_failure: true
#   resource_group: liquibase_test-${CI_COMMIT_REF_SLUG}
#   variables:
#     ENVIRONMENT: test
#     PROJECT_ID: ict-b-prototype-etlt
#     TF_CONFIG_FILE: test.tfvars
#     TF_SERVICE_ACCOUNT: "<EMAIL>"
#     TERRAFORM_MODULE: BigQuery
#     PREFIX: "test/etl/BigQuery/${CI_COMMIT_REF_SLUG}"
#   before_script:
#     - !reference [".auth_base"]
#     - cd "${CI_PROJECT_DIR}/infrastructure/${TERRAFORM_MODULE}"
#     - *create-mr-specific-tfvars
#   script:
#     - mise run copy_liquibase_libs 
#     - cat "${CI_PROJECT_DIR}/infrastructure/Configs/${TF_CONFIG_FILE}"
#     - echo "Prefix=${PREFIX}"
#     - tofu init -backend-config="prefix=$PREFIX" -var-file=../Configs/$TF_CONFIG_FILE -var="MR_IID=$CI_MERGE_REQUEST_IID"
#     - tofu apply -var-file=../Configs/$TF_CONFIG_FILE -var="MR_IID=$CI_MERGE_REQUEST_IID" -auto-approve
#     - cd $CI_PROJECT_DIR
#     - echo "Running Liquibase Tests"
#     - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE version $CI_MERGE_REQUEST_IID
#     - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE status $CI_MERGE_REQUEST_IID
#     - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE validate $CI_MERGE_REQUEST_IID
#     - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE update $CI_MERGE_REQUEST_IID
#   after_script:
#     - !reference [".auth_base"]
#     - echo "Liquibase Tests Complete"
#     - cd $CI_PROJECT_DIR/infrastructure/$TERRAFORM_MODULE
#     - *create-mr-specific-tfvars
#     - tofu init -backend-config="prefix=$PREFIX" -var-file=../Configs/$TF_CONFIG_FILE -var="MR_IID=$CI_MERGE_REQUEST_IID"
#     - tofu destroy -var-file=../Configs/$TF_CONFIG_FILE -var="MR_IID=$CI_MERGE_REQUEST_IID" -auto-approve
#     - |
#       if [ $? -eq 0 ]; then
#         gsutil rm -a gs://ict-p-statefiles/${PREFIX}/default.tfstate
#       else
#         echo "tofu destroy failed, skipping gsutil rm"
#       fi
#   rules:
#     - if: ( $IS_ICT_DEV_MERGE_REQUEST_PIPELINE )
#       changes:
#         paths:
#           - liquibase/**/*
#       when: manual

etl:liquibase:validate:
  allow_failure: false
  extends: .auth_base_job
  stage: validate
  needs: ["etl:terraform:plan"]
  variables:
    PROJECT_ID: ${ICT_DEPLOY_GCP_PROJECT_ID}
    ENVIRONMENT: ${ICT_ENVIRONMENT}
    TF_CONFIG_FILE: ${ICT_ENVIRONMENT}.tfvars
  script:
    # This is worth noting! The FIRST project you touch in bq becomes the default.
    - mise run copy_liquibase_libs 
    - bq ls --max_results 1000 --project_id ${PROJECT_ID}
    - echo "Preparing to migrate-diff ${CI_ENVIRONMENT_NAME}"
    - echo "Running Liquibase Tests"
    - cd $CI_PROJECT_DIR
    - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE version
    - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE status
    - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE validate
  artifacts:
    paths:
      - ${CI_JOB_NAME}_${CI_PIPELINE_ID}.log
    expire_in: 1 week
  rules:
    - if: $IS_ICT_MERGE_REQUEST_PIPELINE

etl:liquibase:clear-checksums:
  extends: .auth_base_job
  stage: validate
  allow_failure: true
  variables:
    PROJECT_ID: ${ICT_DEPLOY_GCP_PROJECT_ID}
    ENVIRONMENT: ${ICT_ENVIRONMENT}
    TF_CONFIG_FILE: ${ICT_ENVIRONMENT}.tfvars
  script:
    # This is worth noting! The FIRST project you touch in bq becomes the default.
    - mise run copy_liquibase_libs 
    - bq ls --max_results 1000 --project_id ${PROJECT_ID}
    - echo "Preparing to migrate-diff ${CI_ENVIRONMENT_NAME}"
    - cd $CI_PROJECT_DIR
    - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE clear-checksums
  artifacts:
    paths:
      - ${CI_JOB_NAME}_${CI_PIPELINE_ID}.log
    expire_in: 1 week
  rules:
    - if: $IS_ICT_MERGE_REQUEST_PIPELINE
      when: manual

# -------------------------------------------------------------------------------------------------
#                                         deploy stage jobs
# -------------------------------------------------------------------------------------------------
etl:bigquery:update:
  extends: .auth_base_job
  stage: deploy
  allow_failure: false
  needs: ['etl:deploy']
  variables:
    PROJECT_ID: ${ICT_DEPLOY_GCP_PROJECT_ID}
    ENVIRONMENT: ${ICT_ENVIRONMENT}
    TF_CONFIG_FILE: ${ICT_ENVIRONMENT}.tfvars
  script:
    # This is worth noting! The FIRST project you touch in bq becomes the default.
    - mise run copy_liquibase_libs 
    - bq ls --max_results 1000 --project_id ${PROJECT_ID}
    - echo "Preparing to UPDATE BigQuery in ${CI_ENVIRONMENT_NAME}"
    - echo "Running Liquibase Tests"
    - cd $CI_PROJECT_DIR
    - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE version
    - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE status
    - ./liquibase/scripts/tenant_liquibase_action.sh $TF_CONFIG_FILE update
  artifacts:
    paths:
      - ${CI_JOB_NAME}_${CI_PIPELINE_ID}.log
    expire_in: 1 week
  rules:
    - if: $IS_ICT_DEPLOY_PIPELINE
      when: always
