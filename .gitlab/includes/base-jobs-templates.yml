---

.gcloud_login: &gcloud_login
  - export PATH="$PATH:/root/google-cloud-sdk/bin"
  - echo "CI_MERGE_REQUEST_TARGET_BRANCH_NAME = $CI_MERGE_REQUEST_TARGET_BRANCH_NAME "
  - echo "CI_COMMIT_BRANCH = $CI_COMMIT_BRANCH "
  - |
    if [ "$CI_COMMIT_BRANCH" == "stage" ]; then
        echo ${GCLOUD_TF_SERVICE_ACCOUNT_STAGE} > ${CI_PROJECT_DIR}/key.json
    elif [ "$CI_MERGE_REQUEST_TARGET_BRANCH_NAME" == "stage" ]; then
        echo ${GCLOUD_TF_SERVICE_ACCOUNT_STAGE} > ${CI_PROJECT_DIR}/key.json
    elif [ "$CI_COMMIT_BRANCH" == "main" ]; then
        echo ${GCLOUD_TF_SERVICE_ACCOUNT_PROD} > ${CI_PROJECT_DIR}/key.json
    elif [ "$CI_MERGE_REQUEST_TARGET_BRANCH_NAME" == "main" ]; then
        echo ${GCLOUD_TF_SERVICE_ACCOUNT_PROD} > ${CI_PROJECT_DIR}/key.json
    else
        echo ${GCLOUD_TF_SERVICE_ACCOUNT_DEV} > ${CI_PROJECT_DIR}/key.json
    fi
  - gcloud auth activate-service-account --key-file=${CI_PROJECT_DIR}/key.json
  - gcloud auth configure-docker us-docker.pkg.dev --quiet
  - gcloud auth list
  - cd ${CI_PROJECT_DIR}
  - export GOOGLE_APPLICATION_CREDENTIALS=${CI_PROJECT_DIR}/key.json
  - echo "gCloud Login Complete!"

.mise-en-place: &mise-en-place
  - echo -e "section_start:`date +%s`:mise-init[collapsed=true]\r\e[0Kmise initialization"
  - mise install --yes --verbose
  - echo -e "section_end:`date +%s`:mise-init\r\e[0K"

.auth_base:
  - *gcloud_login
  - *mise-en-place

.base_job:
  before_script:
    - *mise-en-place

.auth_base_job:
  before_script:
    - *gcloud_login
    - *mise-en-place

