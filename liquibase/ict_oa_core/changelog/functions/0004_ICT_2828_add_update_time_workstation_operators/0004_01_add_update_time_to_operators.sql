--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0004-01_01 endDelimiter:/
CREATE OR REPLACE PROCEDURE `${tenant_id}_oa_curated.expandOverTime`(runtime TIMESTAMP)

BEGIN

DECLARE x INT64 DEFAULT 1;
DECLARE z INT64 DEFAULT 0;
DECLAR<PERSON> logged_in_duration INT64;
DECLARE logInTime TIMESTAMP;
DECLARE logOutTime TIMESTAMP;
DECLARE operator_uuid STRING;
DECLARE workstation_uuid STRING;
DECLARE endingHour TIMESTAMP;

--- SELECT records from transitional table (eval_workstation_history) 
--- where log in duration times are NULL (extend beyond hour interval)
CREATE TEMP TABLE adjustedLoggingRecords AS SELECT
  CASE 
    WHEN record_timestamp = LAG(record_timestamp) OVER (PARTITION BY operator_uuid, workstation_uuid ORDER BY record_timestamp)
    THEN GREATEST(record_timestamp, TIMESTAMP_SUB(TIMESTAMP_TRUNC(next_record_timestamp, HOUR), INTERVAL 1 HOUR))
    ELSE record_timestamp
  END as record_timestamp, 
logged_in_duration, event_hour, event, next_event, next_record_timestamp, operator_uuid, workstation_uuid, 
ROW_NUMBER() OVER(ORDER BY record_timestamp) as rownum 
FROM `${tenant_id}_oa_curated.eval_workstation_history` 
ORDER BY record_timestamp;

CREATE TEMP TABLE loggingRecords AS 
SELECT * FROM adjustedLoggingRecords 
WHERE logged_in_duration IS NULL 
ORDER BY record_timestamp;

--- COUNT records for iteration purposes
SET z = (SELECT COUNT(*) FROM loggingRecords);

    WHILE x <= z DO
    
    SET logInTime = (SELECT record_timestamp FROM loggingRecords WHERE rownum = x);
    SET logOutTime = (SELECT next_record_timestamp FROM loggingRecords WHERE rownum = x);
    SET operator_uuid = (SELECT operator_uuid FROM loggingRecords WHERE rownum = x);
    SET workstation_uuid = (SELECT workstation_uuid FROM loggingRecords WHERE rownum = x);
    --- Move the hour of consideration ahead as marker to calculate logged in time from.
    SET endingHour =  TIMESTAMP_ADD(TIMESTAMP_TRUNC(logInTime, HOUR), INTERVAL 1 HOUR);

    --- WHILE adjusted 'login' hour does not match the log out hour...
    WHILE TIMESTAMP_TRUNC(logInTime, HOUR) != TIMESTAMP_TRUNC(logOutTime, HOUR) DO
      INSERT INTO `${tenant_id}_oa_curated.eval_workstation_history` (
          logged_in_duration,
          event_hour,
          record_timestamp,
          next_record_timestamp,
          operator_uuid,
          workstation_uuid
      )
      VALUES(
          TIMESTAMP_DIFF(endingHour, logInTime, MINUTE), -- logged_in_duration,
          TIMESTAMP_TRUNC(logInTime, HOUR), -- event_hour,
          logInTime,-- record_timestamp,
          endingHour, -- next_record_timestamp,
          operator_uuid, -- operator_uuid,
          workstation_uuid -- workstation_uuid
          );

        --- After initial log in time calculation, move log in time to the beginning of the next hour.
        SET logInTime = endingHour;
        --- Move endingHour to the next hour.
        SET endingHour =  TIMESTAMP_ADD(endingHour, INTERVAL 1 HOUR);
      --- If this is the last logged in hour, calculate logged in time as from 'logInTime' (beginning of hour) 
      --- to actual log out time. Insert into transitional table
      IF logInTime = TIMESTAMP_TRUNC(logOutTime, HOUR)
        THEN 
          INSERT INTO `${tenant_id}_oa_curated.eval_workstation_history` (
            logged_in_duration,
            event_hour,
            record_timestamp,
            next_record_timestamp,
            operator_uuid,
            workstation_uuid
          )
          VALUES(
            TIMESTAMP_DIFF(logOutTime, logInTime, MINUTE), -- logged_in_duration,
            TIMESTAMP_TRUNC(logInTime, HOUR), -- event_hour,
            logInTime,-- record_timestamp,
            logOutTime, -- next_record_timestamp,
            operator_uuid, -- operator_uuid,
            workstation_uuid -- workstation_uuid        
          );
      END IF;
    --- Once all the time gaps have been filled, move on to the next record.
    END WHILE;
    SET x=x+1;
  END WHILE;

  --- Merge evaluated records into aggregage table, updating as needed.
  MERGE INTO `${tenant_id}_oa_curated.agg_workstation_operators` agg USING (
    SELECT 
      logged_in_duration,
      event_hour,
      record_timestamp,
      next_record_timestamp,
      operator_uuid,
      workstation_uuid
    FROM `${tenant_id}_oa_curated.eval_workstation_history` 
    WHERE logged_in_duration > 0
    ) src ON agg.event_hour = src.event_hour
      AND agg.starting_timestamp = src.record_timestamp
      AND agg.operator_uuid = src.operator_uuid
      AND agg.workstation_uuid = src.workstation_uuid
    WHEN MATCHED
      AND (
        agg.logged_in_duration != src.logged_in_duration
        OR agg.ending_timestamp != src.next_record_timestamp
    ) THEN
   UPDATE SET
    agg.logged_in_duration = src.logged_in_duration,
    agg.ending_timestamp = src.next_record_timestamp,
    agg.update_time = runtime
   WHEN NOT MATCHED BY TARGET 
 THEN
      INSERT (
      logged_in_duration,
      event_hour,
      starting_timestamp,
      ending_timestamp,
      operator_uuid,
      workstation_uuid,
      update_time
    ) VALUES (
      src.logged_in_duration,
      src.event_hour,
      src.record_timestamp,
      src.next_record_timestamp,
      src.operator_uuid,
      src.workstation_uuid,
      runtime
  );

  --- Clear out the transitional table of all records;
  DELETE `${tenant_id}_oa_curated.eval_workstation_history` 
  WHERE true;

END
/

-- rollback DROP PROCEDURE ${tenant_id}_oa_curated.expandOverTime;
