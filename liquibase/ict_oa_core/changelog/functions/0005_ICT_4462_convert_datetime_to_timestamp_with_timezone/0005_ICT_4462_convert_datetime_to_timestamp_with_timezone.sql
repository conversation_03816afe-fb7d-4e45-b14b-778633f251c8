--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0005-01_01
/**
* Accepts a date time in string format and a timezone that the datetime string represents, then 
* returns the UTC timestamp type
**/
CREATE OR REPLACE FUNCTION `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(date_time_string STRING, timezone STRING)
RETURNS TIMESTAMP
AS (
  (SELECT
    CASE
      WHEN REGEXP_CONTAINS(date_time_string, r'Z$') = true 
        -- if there is a Z at the end, it's in UTC.  Ignore the timezone param
        THEN SAFE_CAST(SUBSTR(date_time_string ,0, 26) AS TIMESTAMP)
      WHEN REGEXP_CONTAINS(date_time_string, r'.[0-9]*[+|-][0-9]{2}:?[0-9]{0,2}$') = true 
        -- if there is a timezone offset included, just convert it using that
        THEN SAFE_CAST(date_time_string AS TIMESTAMP)
      WHEN timezone IS NOT NULL 
         -- if a timezone is passed, convert using the timezone param
        THEN TIMESTAMP(DATETIME(date_time_string), timezone)
      ELSE SAFE_CAST(date_time_string AS TIMESTAMP)
    END
  )
);
-- rollback DROP FUNCTION ${tenant_id}_oa_curated.ConvertDatetimeStringToTimestamp;
