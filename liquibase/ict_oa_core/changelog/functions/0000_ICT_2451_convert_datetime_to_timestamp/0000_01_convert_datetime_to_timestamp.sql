--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0000-01_01
CREATE FUNCTION `${tenant_id}_oa_curated`.ConvertDatetimeStringToTimestamp(date_time_string STRING)
RETURNS TIMESTAMP
AS (
  (SELECT
    CASE
      WHEN REGEXP_CONTAINS(date_time_string, r'Z$') IS NOT NULL 
        THEN SAFE_CAST(SUBSTR(date_time_string ,0, 26) AS TIMESTAMP)
      WHEN REGEXP_CONTAINS(date_time_string, r'.[0-9]*[+|-][0-9]{2}:?[0-9]{0,2}$') IS NOT NULL 
        THEN SAFE_CAST(date_time_string AS TIMESTAMP)
      ELSE SAFE_CAST(date_time_string AS TIMESTAMP)
    END
  )
);
-- rollback DROP FUNCTION ${tenant_id}_oa_curated.ConvertDatetimeStringToTimestamp;
