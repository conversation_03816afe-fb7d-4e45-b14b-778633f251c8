--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0031-01_01

CREATE TABLE ${tenant_id}_oa_curated.agg_workstation_operators 
  ( 
  logged_in_duration INT64,
  event_hour TIMESTAMP,
  event STRING,
  next_event STRINg,
  record_timestamp TIMESTAMP,
  next_record_timestamp TIMESTAMP,
  operator_uuid STRING,
  workstation_uuid STRING 
  )
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.agg_workstation_operators;

--changeset ${tenant_id}_oa_curated:0031-01_02
CREATE TABLE ${tenant_id}_oa_curated.eval_workstation_history
(
  logged_in_duration INT64,
  event_hour TIMESTAMP,
  event STRING,
  next_event STRINg,
  record_timestamp TIMESTAMP,
  next_record_timestamp TIMESTAMP,
  operator_uuid STRING,
  workstation_uuid STRING 
  )
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.eval_workstation_history;
