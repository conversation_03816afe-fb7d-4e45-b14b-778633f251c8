--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:ICT-2349-01_01
CREATE TABLE ${tenant_id}_oa_curated.dim_subgroup
(
  subgroup_uuid STRING NOT NULL,
  etl_batch_id STRING NOT NULL,
  subgroup_code STRING NOT NULL,
  subgroup_id STRING NOT NULL,
  group_code STRING NOT NULL,
  subgroup_type STRING,
  configuration_status STRING,
  y INT64,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL
)
CLUSTER BY facility
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_subgroup;
