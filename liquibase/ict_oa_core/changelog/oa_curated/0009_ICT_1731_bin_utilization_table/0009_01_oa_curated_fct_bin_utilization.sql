--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0009-01_01
CREATE TABLE ${tenant_id}_oa_curated.fct_bin_utilization
(
    aisle INT64,
    area STRING,
    empty_locations INT64,
    empty_openings INT64,
    faulted_locations INT64,
    percent_empty_openings FLOAT64,
    percent_empty_storage_locs FLOAT64,
    record_timestamp TIMESTAMP,
    total_locations INT64,
    total_openings INT64,
    unavailable_locations INT64,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING
)
CLUSTER BY facility, area;

-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_bin_utilization;
