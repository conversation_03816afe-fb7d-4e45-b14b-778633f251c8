--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:0021-01_01
CREATE TABLE ${tenant_id}_oa_curated.hst_item
(
    etl_batch_id STRING NOT NULL,
    item_uuid STRING NOT NULL,
    source_system STRING NOT NULL,
    record_timestamp TIMESTAMP NOT NULL,
    days_on_hand FLOAT64 DEFAULT 0 NOT NULL,
    distribution_status STRING NOT NULL,
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY);
-- rollback DROP TABLE ${tenant_id}_oa_curated.hst_item;

--changeset ${tenant_id}_oa_curated:0021-01_02
CREATE TABLE ${tenant_id}_oa_curated.agg_hst_inventory
(
    etl_batch_id STRING NOT NULL,
    source_system STRING NOT NULL,
    record_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    over_inventory_count INT64 DEFAULT 0 NOT NULL,
    at_inventory_count INT64 DEFAULT 0 NOT NULL,
    under_inventory_count INT64 DEFAULT 0 NOT NULL,
    no_inventory_count INT64 DEFAULT 0 NOT NULL,
    total_inventory_count INT64 DEFAULT 0 NOT NULL,
    over_inventory_percent  FLOAT64 DEFAULT 0 NOT NULL,
    at_inventory_precent FLOAT64 DEFAULT 0 NOT NULL,
    under_inventory_percent FLOAT64 DEFAULT 0 NOT NULL,
    no_inventory_percent FLOAT64 DEFAULT 0 NOT NULL,
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY);
-- rollback DROP TABLE ${tenant_id}_oa_curated.agg_hst_inventory;