--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0000-02_01
CREATE TABLE ${tenant_id}_oa_curated.dim_pick_order_event_map (
    order_type STRING,
    source_state STRING,
    analytics_conformed_state STRING,
    ending_event STRING
)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_pick_order_event_map;

--changeset ${tenant_id}_oa_curated:0000-02_02
INSERT INTO ${tenant_id}_oa_curated.dim_pick_order_event_map (order_type, source_state, analytics_conformed_state, ending_event)
VALUES
('CustomPW','CREATED','Order_Created','PackLine'),
('CustomPW','RELEASED','Order_Released','PackLine'),
('CustomPW','ACTIVE','Order_Activated','PackLine'),
('CustomPW','BIND','Order_Assigned','PackLine'),
('CustomPW','PUT_COMPLETE','Order_Put_Lines_Complete','PackLine'),
('CustomPW','PUT_COMPLETE','Order_Lines_Complete','PackLine'),
('CustomPW','PACK_ASSIGNED','Order_Pack_Assigned','PackLine'),
('CustomPW','CANCELLED','Order_Cancelled','Order_Cancelled'),
('Standard','CREATED','Order_Created','Order_Complete'),
('Standard','RELEASED','Order_Released','Order_Complete'),
('Standard','ACTIVE','Order_Activated','Order_Complete'),
('Standard','COMPLETE','Order_Lines_Complete','Order_Complete'),
('Standard','COMPLETE','Order_Complete','Order_Complete'),
('Standard','CANCELLED','Order_Cancelled','Order_Cancelled'),
('StandardPW','CREATED','Order_Created','Order_Complete'),
('StandardPW','RELEASED','Order_Released','Order_Complete'),
('StandardPW','ACTIVE','Order_Activated','Order_Complete'),
('StandardPW','BIND','Order_Assigned','Order_Complete'),
('StandardPW','PUT_COMPLETE','Order_Put_Lines_Complete','Order_Complete'),
('StandardPW','PUT_COMPLETE','Order_Lines_Complete','Order_Complete'),
('StandardPW','PACK_ASSIGNED','Order_Pack_Assigned','Order_Complete'),
('StandardPW','PUTWALL_PACK_COMPLETE','Order_Put_Wall_Pack_Complete','Order_Complete'),
('StandardPW','PUTWALL_PACK_COMPLETE','Order_Complete','Order_Complete'),
('StandardPW','CANCELLED','Order_Cancelled','Order_Cancelled'),
('Replenishment','CREATED','Order_Created','Order_Complete'),
('Replenishment','RELEASED','Order_Released','Order_Complete'),
('Replenishment','ACTIVE','Order_Activated','Order_Complete'),
('Replenishment','PICK_COMPLETE','Order_Lines_Complete','Order_Complete'),
('Replenishment','PICKED','Order_Lines_Complete','Order_Complete'),
('Replenishment','COMPLETE','Order_Complete','Order_Complete'),
('Replenishment','CANCELLED','Order_Cancelled','Order_Cancelled'),
('Shipping','CREATED','Order_Created','Order_Complete'),
('Shipping','RELEASED','Order_Released','Order_Complete'),
('Shipping','ACTIVE','Order_Activated','Order_Complete'),
('Shipping','BIND','Order_Assigned','Order_Complete'),
('Shipping','PUT_COMPLETE','Order_Put_Lines_Complete','Order_Complete'),
('Shipping','PUT_COMPLETE','Order_Pack_Assigned','Order_Complete'),
('Shipping','PUTWALL_PACK_COMPLETE','Order_Put_Wall_Pack_Complete','Order_Complete'),
('Shipping','PICK_COMPLETE','Order_Lines_Complete','Order_Complete'),
('Shipping','PICKED','Order_Lines_Complete','Order_Complete'),
('Shipping','PACK_COMPLETE','Order_Pack_Complete','Order_Complete'),
('Shipping','PACKED','Order_Pack_Complete','Order_Complete'),
('Shipping','COMPLETE','Order_Complete','Order_Complete'),
('Shipping','CANCELLED','Order_Cancelled','Order_Cancelled'),
('Putaway','CREATED','Order_Created','Order_Complete'),
('Putaway','RELEASED','Order_Released','Order_Complete'),
('Putaway','ACTIVE','Order_Activated','Order_Complete'),
('Putaway','COMPLETE','Order_Lines_Complete','Order_Complete'),
('Putaway','COMPLETE','Order_Complete','Order_Complete'),
('Putaway','CANCELLED','Order_Cancelled','Order_Cancelled');
