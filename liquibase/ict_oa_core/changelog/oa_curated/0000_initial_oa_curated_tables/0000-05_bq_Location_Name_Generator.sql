--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0000-05 endDelimiter:/
CREATE OR REPLACE FUNCTION ${tenant_id}_oa_curated.Location_Name_Generator
(
	Location_Code  STRING
)
RETURNS STRING
AS
(
	CASE 
    WHEN STRPOS(COALESCE(Location_Code,''), '.')>0
          AND REVERSE(LEFT(REVERSE(Location_Code), STRPOS(REVERSE(Location_Code), '.') - 1)) != '0'
          THEN REPLACE(RIGHT(Location_Code,STRPOS(REVERSE(Location_Code), '.')),'.','')
    ELSE Location_Code
  END
)
/
