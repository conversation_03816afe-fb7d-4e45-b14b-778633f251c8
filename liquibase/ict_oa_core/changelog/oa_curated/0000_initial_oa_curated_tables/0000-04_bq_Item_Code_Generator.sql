--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0000-04 endDelimiter:/
CREATE OR REPLACE FUNCTION ${tenant_id}_oa_curated.Item_Code_Generator
(
    Item_SKU STRING,
    Item_Product_Code STRING,
    UOM  STRING,
    Client STRING
)
RETURNS STRING
AS
(
    CASE
        WHEN COALESCE(Item_SKU,'') = '' OR UPPER(Item_SKU) LIKE '%EMPTY_SKU%' THEN ''
        ELSE CONCAT(
			      UPPER(ARRAY_REVERSE(SPLIT(SUBSTR(Item_SKU, STRPOS(Item_SKU, '#')+1, LENGTH(Item_SKU)), '.'))[OFFSET(0)]), '-',
            CASE WHEN COALESCE(Item_Product_Code, '') = '' THEN '0' ELSE UPPER(Item_Product_Code) END, '-',
            CASE WHEN COALESCE(UOM, '') = '' THEN '0' ELSE UPPER(UOM) END, '-',
			      CASE WHEN COALESCE(Client, '') = '' THEN
              CASE WHEN ARRAY_LENGTH(SPLIT(Item_SKU, '#')) > 1 THEN COALESCE(UPPER(SPLIT(Item_SKU, '#')[OFFSET(0)]), '0') ELSE '0' END
            ELSE Client END 
        )
    END
)
/
