--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0000-03 endDelimiter:/
CREATE FUNCTION ${tenant_id}_oa_curated.DispositionCalculatorFunction(arr ARRAY<STRING>)
RETURNS INT64
LANGUAGE js
AS r"""
function DispositionCalculatorFunction(arr) {
    if (arr === null || arr === undefined || arr.length === 0) { 
        return 0;
    }

    let exponent = parseInt(arr[0]) || -1;
    let power = Math.floor(2 ** exponent);
    let subquery = DispositionCalculatorFunction(arr.slice(1))
    return power + subquery;
}

let result = DispositionCalculatorFunction(arr);
"""
/
