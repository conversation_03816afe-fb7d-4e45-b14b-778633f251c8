--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0000-01_01
CREATE TABLE ${tenant_id}_oa_curated.dim_item
(
  item_uuid STRING NOT NULL,
  item_category_code STRING NOT NULL, -- denormalized dim.item_category
  item_code STRING NOT NULL,
  item_name STRING,
  item_sku STRING,
  item_product_code STRING,
  uom STRING,
  client STRING,
  total_weight FLOAT64,
  total_volume FLOAT64,
  length FLOAT64,
  width FLOAT64,
  height FLOAT64,
  palletizing_mode STRING,
  max_units_per_pick INT64,
  item_shelf_life_in_days INT64,
  preferred_container_type STRING,
  velocity_classification STRING,
  hazard_classification STRING,
  supplier_code STRING,
  base_uom STRING,
  default_pick_uom INT64,
  preferred_fulfillment_handling STRING,
  amcap_qualification_ind STRING,
  toppling_rate INT64,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  active_rec_ind INT64,
  etl_batch_id STRING,
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_item
ADD PRIMARY KEY(item_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_item;

--changeset ${tenant_id}_oa_curated:0000-01_02
CREATE TABLE ${tenant_id}_oa_curated.dim_subsystem
(
  subsystem_uuid STRING NOT NULL,
  subsystem_code STRING NOT NULL,
  subsystem_name STRING,
  subsystem_category STRING,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_subsystem
ADD PRIMARY KEY(subsystem_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_subsystem;

--changeset ${tenant_id}_oa_curated:0000-01_03
CREATE TABLE ${tenant_id}_oa_curated.dim_device
(
  device_uuid STRING NOT NULL,
  device_code STRING NOT NULL,
  subsystem_uuid STRING,
  device_name STRING,
  device_weight_factor INT64,
  device_functional_type STRING,
  device_hardware_type STRING,
  device_manufacturer STRING,
  device_engineered_rate INT64,
  device_area STRING,
  dc_tenant_name STRING,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_device
ADD PRIMARY KEY(device_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_device;

--changeset ${tenant_id}_oa_curated:0000-01_04
CREATE TABLE ${tenant_id}_oa_curated.dim_container_type
(
  container_type_uuid STRING NOT NULL,
  container_type_code STRING NOT NULL,
  container_type_name STRING,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_container_type
ADD PRIMARY KEY(container_type_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_container_type;

--changeset ${tenant_id}_oa_curated:0000-01_05
CREATE TABLE ${tenant_id}_oa_curated.dim_reason
(
  reason_uuid STRING NOT NULL,
  reason_code STRING NOT NULL,
  reason_name STRING,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_reason
ADD PRIMARY KEY(reason_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_reason;

--changeset ${tenant_id}_oa_curated:0000-01_06
CREATE TABLE ${tenant_id}_oa_curated.dim_workstation
(
  workstation_uuid STRING NOT NULL,
  workstation_code STRING NOT NULL,
  workstation_name STRING,
  workstation_type STRING,
  terminal_code STRING,
  operation_mode STRING,
  workstation_area STRING,
  workstation_group STRING,
  workstation_subgroup STRING,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_workstation
ADD PRIMARY KEY(workstation_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_workstation;

--changeset ${tenant_id}_oa_curated:0000-01_08
CREATE TABLE ${tenant_id}_oa_curated.dim_operator
(
  operator_uuid STRING NOT NULL,
  operator_code STRING NOT NULL,
  operator_name STRING,
  operator_full_name STRING,
  operator_description STRING,
  start_date TIMESTAMP,
  end_date TIMESTAMP,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;

ALTER TABLE ${tenant_id}_oa_curated.dim_operator
ADD PRIMARY KEY(operator_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_operator;

--changeset ${tenant_id}_oa_curated:0000-01_09
CREATE TABLE ${tenant_id}_oa_curated.dim_zone
(
  zone_uuid STRING NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  zone_code STRING NOT NULL,
  logical_destination_uuid STRING NOT NULL,
  zone_name STRING,
  zone_type STRING,
  qty_picked_target INT64,
  line_picked_target INT64,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_zone
ADD PRIMARY KEY(zone_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_zone;

--changeset ${tenant_id}_oa_curated:0000-01_10
CREATE TABLE ${tenant_id}_oa_curated.dim_pick_order
(
  pick_order_uuid STRING NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING,
  customer_order_uuid STRING NOT NULL,
  pick_order_code STRING NOT NULL,
  pick_order_name STRING,
  put_wall_flg INT64,
  pick_order_type STRING,
  pick_order_category STRING,
  pick_order_fulfillment_handling_type STRING,
  pick_order_value_added_service STRING,
  pick_order_priority INT64,
  pick_order_packing_requirement STRING,
  pick_order_latest_staging_date_time TIMESTAMP,
  pick_order_order_channel STRING,
  pick_order_line_count_expected INT64,
  pick_order_total_qty_expected INT64,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_pick_order
ADD PRIMARY KEY(pick_order_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_pick_order;

--changeset ${tenant_id}_oa_curated:0000-01_11
CREATE TABLE ${tenant_id}_oa_curated.dim_work_area
(
  work_area_uuid STRING NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  work_area_code STRING NOT NULL,
  work_area_name STRING,
  work_area_type STRING,
  work_area_description STRING,
  work_area_operation_mode STRING,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_work_area
ADD PRIMARY KEY(work_area_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_work_area;

--changeset ${tenant_id}_oa_curated:0000-01_12
CREATE TABLE ${tenant_id}_oa_curated.dim_module
(
  module_uuid STRING NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  work_area_uuid STRING NOT NULL,
  module_code STRING NOT NULL,
  module_name STRING,
  module_type STRING,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_module
ADD PRIMARY KEY(module_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_module;

--changeset ${tenant_id}_oa_curated:0000-01_07
CREATE TABLE ${tenant_id}_oa_curated.dim_location
(
  location_uuid STRING NOT NULL,
  -- put_wall_cubby_id INT64, -- to be pulled out
  -- module_code STRING NOT NULL,    -- denormalized of DIM_Module
  -- work_area_code STRING NOT NULL,      -- denormalized of DIM_Module -> DIM_Work_Area
  -- level_id INT64,  --will be covered by module/work_area  TBD
  -- aisle_id INT64,  --will be covered by module/work_area --> module_code
  -- workstation_id INT64, -- to be pulled out, TODO need to attach to the fact
  location_type_code STRING, -- denormalized DIM_location_type 
  location_code STRING NOT NULL,
  location_name STRING,
  relative_x_axis STRING,
  relative_y_axis STRING,
  relative_z_axis STRING,
  side_position STRING,
  slot_position STRING,
  location_group STRING,
  bay STRING,
  weight_limit INT64,
  location_size STRING,
  hazardous_material_allowed INT64,
  bay_width INT64,
  effective_begin_date TIMESTAMP,
  effective_end_date TIMESTAMP,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_location
ADD PRIMARY KEY(location_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_location;

--changeset ${tenant_id}_oa_curated:0000-01_13
CREATE TABLE ${tenant_id}_oa_curated.dim_wave 
( 
    wave_uuid STRING NOT NULL,
    wave_code STRING NOT NULL,
    wave_name STRING,
    wave_status STRING,
    wave_priority STRING,
    wave_created_date_time TIMESTAMP,
    wave_released_date_time TIMESTAMP,
    wave_active_date_time TIMESTAMP,
    wave_closed_date_time TIMESTAMP,
    wave_item_count INT64,
    tenant STRING,
    facility STRING,
    source_system STRING,
    active_rec_ind INT64,
    etl_batch_id STRING,
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_wave
ADD PRIMARY KEY(wave_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_wave;

--changeset ${tenant_id}_oa_curated:0000-01_15
CREATE TABLE ${tenant_id}_oa_curated.fct_movement
(
  record_timestamp TIMESTAMP NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING  NOT NULL,
  source_location_uuid STRING NOT NULL,
  source_work_area_uuid STRING NOT NULL,
  source_module_uuid STRING NOT NULL,
  destination_location_uuid STRING NOT NULL,
  destination_work_area_uuid STRING NOT NULL,
  destination_module_uuid STRING NOT NULL,
  status_uuid STRING NOT NULL,
  device_uuid STRING NOT NULL,
  physical_device_uuid STRING NOT NULL, 
  item_uuid STRING NOT NULL, 
  transport_reason_uuid STRING NOT NULL,
  container_type_uuid STRING NOT NULL,
  workstation_uuid STRING NOT NULL, 
  movement_type_code STRING, -- demormalized DIM_Movement_Type
  move_start_date_time TIMESTAMP NOT NULL,
  load_unit_code STRING NOT NULL,
  transport_request_id STRING NOT NULL,
  group_transport_request_id STRING NOT NULL,
  move_duration_seconds INT64,
  item_count INT64,
  x_distance_traveled INT64,
  aisles_traveled_count INT64,
  load_unit_content_code STRING,
  move_arrive_date_time TIMESTAMP,
  move_leave_date_time TIMESTAMP,
  etl_batch_id STRING
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_movement;

--changeset ${tenant_id}_oa_curated:0000-01_16
CREATE TABLE ${tenant_id}_oa_curated.fct_inventory
(
  record_timestamp TIMESTAMP NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING  NOT NULL,
  item_uuid STRING NOT NULL,
  location_uuid STRING NOT NULL,
  work_area_uuid STRING NOT NULL,
  module_uuid STRING NOT NULL,
  load_unit_code STRING NOT NULL,
  inventory_unit_code STRING NOT NULL,
  inventory_batch_code STRING NOT NULL,
  base_load_unit_code STRING,
  receiving_processing_code STRING,
  inventory_qty INT64,
  inventory_unassigned_qty INT64,
  etl_batch_id STRING,
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_inventory;

--changeset ${tenant_id}_oa_curated:0000-01_17
CREATE TABLE ${tenant_id}_oa_curated.fct_pick_activity 
(
    record_timestamp TIMESTAMP NOT NULL,
    operator_uuid STRING NOT NULL,
    module_uuid STRING NOT NULL,
    work_area_uuid STRING NOT NULL,
    zone_uuid STRING NOT NULL,
    workstation_uuid STRING NOT NULL,
    technology_uuid STRING NOT NULL,
    work_type_uuid STRING NOT NULL,
    container_type_uuid STRING NOT NULL,
    module_code STRING NOT NULL,
    load_unit_code STRING NOT NULL,
    event STRING NOT NULL,
    sku_code STRING NOT NULL,
    cart_code STRING NOT NULL,   
    container_instance_code STRING,
    cart_instance_code STRING,
    induct_type STRING,
    cluster_train_code STRING,
    induction_zone_code STRING,
    load_unit_usage_type_code STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_pick_activity;

--changeset ${tenant_id}_oa_curated:0000-01_18
CREATE TABLE ${tenant_id}_oa_curated.fct_pick_order 
(
    record_timestamp TIMESTAMP NOT NULL,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    pick_order_uuid STRING NOT NULL,
    wave_uuid STRING NOT NULL,
    location_uuid STRING NOT NULL,
    work_area_uuid STRING NOT NULL,
    module_uuid STRING NOT NULL,    
    operator_uuid STRING NOT NULL,
    zone_uuid STRING NOT NULL,
    reason_uuid STRING NOT NULL,
    pick_order_event STRING,
    unique_event_identifier STRING,
    event_detail STRING,
    etl_batch_id STRING 
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_pick_order;

--changeset ${tenant_id}_oa_curated:0000-01_19
CREATE TABLE ${tenant_id}_oa_curated.dim_status
(
  status_uuid STRING NOT NULL,
  status_code STRING NOT NULL,
  subsystem_uuid STRING NOT NULL,
  status_category_code STRING NOT NULL, -- denormalized DIM_Status_Category
  status_locale_code STRING NOT NULL, --> denormalized dim_status_locale
  status_locale_name STRING, --> denormalized 
  status_type STRING,
  dc_tenant_name STRING,
  status_relevancy_ind INT64,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_status
ADD PRIMARY KEY(status_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_status;

--changeset ${tenant_id}_oa_curated:0000-01_20
CREATE TABLE ${tenant_id}_oa_curated.dim_work_type
(
  work_type_uuid STRING NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  work_type_code STRING NOT NULL,
  work_type_desc STRING,
  work_type_style STRING,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_work_type
ADD PRIMARY KEY(work_type_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_work_type;

--changeset ${tenant_id}_oa_curated:0000-01_21
CREATE TABLE ${tenant_id}_oa_curated.dim_customer_order
(
  customer_order_uuid STRING NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  customer_order_code STRING,
  customer_order_name STRING,
  customer_order_type STRING,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;

ALTER TABLE ${tenant_id}_oa_curated.dim_customer_order
ADD PRIMARY KEY(customer_order_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_customer_order;

--changeset ${tenant_id}_oa_curated:0000-01_22
CREATE TABLE ${tenant_id}_oa_curated.dim_technology
(
  technology_uuid STRING NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  technology_code STRING NOT NULL,
  technology_name STRING,
  technology_vendor STRING,
  active_rec_ind INT64,
  etl_batch_id STRING
) 
;
ALTER TABLE ${tenant_id}_oa_curated.dim_technology
ADD PRIMARY KEY(technology_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_technology;

--changeset ${tenant_id}_oa_curated:0000-01_23
CREATE TABLE ${tenant_id}_oa_curated.fct_order_line
(
  record_timestamp TIMESTAMP NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  location_uuid STRING NOT NULL, 		--dim_location
  work_area_uuid STRING NOT NULL,
  module_uuid STRING NOT NULL, 
  operator_uuid STRING NOT NULL,		--dim_operator
  zone_uuid STRING NOT NULL,			--dm_zone
  pick_order_uuid STRING NOT NULL,	  --dim_pick_order
  item_uuid STRING NOT NULL,		  --dim_item
  cart_instance_code STRING NOT NULL, -- flatten
  cart_instance_name STRING NOT NULL, -- flatten
  technology_uuid STRING NOT NULL,		--dim_tech
  container_train_code STRING NOT NULL, -- flatten
  pick_to_container_type_uuid STRING NOT NULL, -- dim_container_type
  line_item STRING NOT NULL,
  work_type_uuid STRING NOT NULL,		--dim_work_type
  work_request_code STRING NOT NULL,
  container_type_uuid STRING NOT NULL, -- dim_container_type
  process_type_code STRING NOT NULL, -- flatten
  reason_uuid STRING NOT NULL,			--dim_reason
  pick_order_line_complete_ind INT64,
  container_physical_code STRING,
  pick_to_container_physical_code STRING,
  container_instance_code STRING,
  pick_batch_code STRING,
  duration_seconds FLOAT64,
  picked_qty FLOAT64,
  skipped_qty FLOAT64,
  shorted_qty FLOAT64,
  new_container_qty FLOAT64,
  requested_qty FLOAT64,
  etl_batch_id STRING
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_order_line;

--changeset ${tenant_id}_oa_curated:0000-01_24
CREATE  TABLE ${tenant_id}_oa_curated.fct_fault
(
  record_timestamp TIMESTAMP NOT NULL,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  fault_start_date_time TIMESTAMP NOT NULL,
  device_uuid STRING NOT NULL,
  physical_device_uuid STRING NOT NULL,
  location_uuid STRING  NOT NULL,
  work_area_uuid STRING NOT NULL,
  module_uuid STRING NOT NULL,
  status_uuid STRING  NOT NULL,
  item_uuid STRING NOT NULL,
  operator_uuid STRING NOT NULL,
  fault_tag_reason_uuid STRING NOT NULL,
  container_physical_code STRING NOT NULL,
  plc_code STRING NOT NULL,
  fault_duration_seconds INT64,
  fault_repair_duration_seconds INT64,
  fault_acknowledgement_date_time TIMESTAMP,
  etl_batch_id STRING
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_fault;

--changeset ${tenant_id}_oa_curated:0000-01_25
CREATE  TABLE ${tenant_id}_oa_curated.fct_pick_order_complete (
    record_timestamp TIMESTAMP NOT NULL,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    pick_order_uuid STRING NOT NULL,
    hour_quarter_uuid STRING NOT NULL,
    operator_uuid STRING NOT NULL,
    location_uuid STRING NOT NULL,
    wave_uuid STRING NOT NULL,
    zone_uuid STRING NOT NULL,
    order_released_date_time TIMESTAMP,
    order_assigned_date_time TIMESTAMP,
    order_assigned_location_uuid STRING,
    order_put_start_date_time TIMESTAMP,
    order_pack_start_date_time TIMESTAMP,
    order_line_count INT64,
    order_total_qty INT64,
    overflow_location_count INT64,
    order_put_lines_complete_date_time TIMESTAMP,
    order_put_wall_pack_complete_date_time TIMESTAMP,
    order_put_wall_pack_operator_uuid STRING,
    order_put_wall_pack_location_uuid STRING,
    order_put_wall_pack_zone_uuid STRING,
    order_lines_complete_date_time TIMESTAMP,
    order_pack_complete_date_time TIMESTAMP,
    order_complete_date_time TIMESTAMP,
    pick_order_complete_ind INT64,
    order_cancelled_ind INT64,
    order_cancelled_date_time TIMESTAMP,
    etl_batch_id STRING,
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_pick_order_complete;

--changeset ${tenant_id}_oa_curated:0000-01_26
CREATE TABLE ${tenant_id}_oa_curated.dim_logical_destination
(
    logical_destination_uuid STRING NOT NULL,
    scanner_uuid         STRING NOT NULL,
    logical_destination_code STRING NOT NULL,
    logical_destination_name STRING,
    logical_destination_type STRING,
    dc_tenant_name     STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    active_rec_ind INT64,
    etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_logical_destination
ADD PRIMARY KEY(logical_destination_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_logical_destination;

--changeset ${tenant_id}_oa_curated:0000-01_27
CREATE TABLE ${tenant_id}_oa_curated.dim_physical_destination
(
    physical_destination_uuid STRING NOT NULL,
    scanner_uuid         STRING NOT NULL,
    physical_destination_code STRING NOT NULL,
    physical_destination_name STRING,
    physical_destination_type STRING,
    dc_tenant_name     STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    active_rec_ind INT64,
    etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_physical_destination
ADD PRIMARY KEY(physical_destination_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_physical_destination;

--changeset ${tenant_id}_oa_curated:0000-01_28
CREATE TABLE ${tenant_id}_oa_curated.dim_scanner
(
    scanner_uuid       STRING NOT NULL,
    scanner_code       STRING NOT NULL,
    device_uuid        STRING NOT NULL,
    scanner_name       STRING,
    engineered_rate    INT64,
    scanner_functional_type STRING,
    scanner_manufacturer STRING,
    scanner_hardware_type STRING,
    dc_tenant_name     STRING ,
    active_rec_ind INT64,
    tenant             STRING NOT NULL,
    facility           STRING NOT NULL,
    source_system      STRING NOT NULL,
    etl_batch_id STRING
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_scanner
ADD PRIMARY KEY(scanner_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_scanner;

--changeset ${tenant_id}_oa_curated:0000-01_29
CREATE TABLE ${tenant_id}_oa_curated.fct_sort
(
  record_timestamp TIMESTAMP NOT NULL,
  scanned_code STRING NOT NULL,
  intended_logical_destination_uuid STRING NOT NULL,
  actual_logical_destination_uuid STRING NOT NULL,
  intended_physical_destination_uuid STRING NOT NULL,
  actual_physical_destination_uuid STRING NOT NULL,
  dispatch_status_uuid STRING NOT NULL,
  disposition_status_uuid STRING NOT NULL,
  wave_uuid STRING NOT NULL,
  tracking_code STRING NOT NULL,
  operator_uuid STRING NOT NULL,
  reason_uuid STRING NOT NULL,
  container_type_uuid STRING NOT NULL,
  disposition_reason_uuid STRING NOT NULL,
  dispatch_request_timestamp TIMESTAMP,
  dispatch_assignment_timestamp TIMESTAMP,
  disposition_bit_status INT64,
  induction_lane_code STRING,
  terminal_code STRING,
  dispatch_success_count INT64,
  dispatch_error_count INT64,
  dispatch_total_count INT64,
  disposition_success_count INT64,
  disposition_error_count INT64,
  disposition_total_count INT64,
  recirc_destination_ind INT64,
  reject_destination_ind INT64,
  recirculation_count INT64,
  qc_ind INT64,
  unit_sorter_carrier_code STRING,
  tenant           STRING NOT NULL,
  facility         STRING NOT NULL,
  source_system    STRING NOT NULL,
  etl_batch_id STRING,
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_sort;