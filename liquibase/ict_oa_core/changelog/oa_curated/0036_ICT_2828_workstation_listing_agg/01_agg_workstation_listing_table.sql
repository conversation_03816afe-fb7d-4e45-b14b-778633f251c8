--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:0035-01_01
CREATE TABLE ${tenant_id}_oa_curated.agg_workstation_listing (
        workstation STRING,
        status STRING, 
        work_mode STRING, 
        workflow_status STRING,
        operator_id STRING, 
        logged_in_period INT64,
        active_time INT64,                  
        starved_time INT64,   
        idle_time INT64,                                  
        blocked_time INT64,   
        picks_per_hour INT64, 
        lines_per_hour INT64, 
        donor_totes_per_hour INT64, 
        order_totes_per_hour INT64, 
        weighted_picks_per_hour INT64, 
        weighted_lines_per_hour INT64,
        weighted_donor_totes_per_hour INT64,
        weighted_order_totes_per_hour INT64,
        record_timestamp TIMESTAMP
      )
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY);
--rollback DROP TABLE ${tenant_id}_oa_curated.agg_workstation_listing;