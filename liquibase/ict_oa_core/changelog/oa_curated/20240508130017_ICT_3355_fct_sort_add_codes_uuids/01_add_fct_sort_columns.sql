--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:ICT-3355-01_01
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN subsystem_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN subsystem_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_04
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN status_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN status_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_05
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN status_category_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN status_category_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_06
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN operator_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN operator_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_07
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN reason_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN reason_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_08
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN wave_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN wave_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_09
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN container_type_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN container_type_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_10
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN scanner_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN scanner_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_11
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN logical_destination_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN logical_destination_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_12
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN physical_destination_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN physical_destination_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_13
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN intended_logical_destination_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN intended_logical_destination_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_14
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN actual_logical_destination_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN actual_logical_destination_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_15
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN intended_physical_destination_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN intended_physical_destination_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_16
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN actual_physical_destination_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN actual_physical_destination_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_17
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN dispatch_status_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN dispatch_status_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_18
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN disposition_status_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN disposition_status_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_20
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN sort_device_code STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN sort_device_code;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_21
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN subsystem_category STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN subsystem_category;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_22
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN actual_logical_destination_type STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN actual_logical_destination_type;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_23
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN actual_physical_destination_type STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN actual_physical_destination_type;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_25
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN actual_physical_destination_name STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN actual_physical_destination_name;

--changeset ${tenant_id}_oa_curated:ICT-3355-01_26
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    ADD COLUMN intended_physical_destination_name STRING;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` DROP COLUMN intended_physical_destination_name;
