--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0038-01_01
CREATE TABLE ${tenant_id}_oa_curated.fct_handling_unit
(
    record_timestamp TIMESTAMP NOT NULL,
    display_timestamp TIMESTAMP,
    event_type STRING,
    handling_id STRING,
    handling_unit_type STRING,
    workstation_uuid STRING,
    tenant STRING,
    facility STRING,
    source_system STRING,
    etl_batch_id STRING
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY);
--rollback DROP TABLE ${tenant_id}_oa_curated.fct_handling_unit; 