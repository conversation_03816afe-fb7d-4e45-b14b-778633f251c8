--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:0034-01_01
CREATE TABLE ${tenant_id}_oa_curated.fct_counting
(
  record_timestamp TIMESTAMP NOT NULL,
  item_uuid STRING NOT NULL,
  item_code STRING NOT NULL,
  item_category_code STRING NOT NULL,
  expected_quantity INT64,
  handling_unit_b_id STRING,
  invalid BOOL,
  inventory_audit_execution_type STRING,
  owner_b_id STRING,
  workstation_b_id STRING,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  etl_batch_id STRING NOT NULL,
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY);
--rollback DROP TABLE ${tenant_id}_oa_curated.fct_counting; 