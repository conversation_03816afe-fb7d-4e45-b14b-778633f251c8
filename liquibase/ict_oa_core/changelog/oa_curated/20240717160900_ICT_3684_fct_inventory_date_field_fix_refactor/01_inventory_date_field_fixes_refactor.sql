--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:ICT-3684-03_01
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN IF EXISTS last_activity_date;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN IF NOT EXISTS last_activity_date TIMESTAMP;

--changeset ${tenant_id}_oa_curated:ICT-3684-03_02
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN IF NOT EXISTS last_activity_date TIMESTAMP;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN IF EXISTS last_activity_date;

--changeset ${tenant_id}_oa_curated:ICT-3684-03_03
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN IF EXISTS last_activity_date_drop;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN IF NOT EXISTS last_activity_date_drop TIMESTAMP;


--changeset ${tenant_id}_oa_curated:ICT-3684-03_04
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN IF EXISTS last_cc_inspection_date;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN IF NOT EXISTS last_cc_inspection_date TIMESTAMP;

--changeset ${tenant_id}_oa_curated:ICT-3684-03_05
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN IF NOT EXISTS last_cc_inspection_date TIMESTAMP;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN IF EXISTS last_cc_inspection_date;

--changeset ${tenant_id}_oa_curated:ICT-3684-03_06
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN IF EXISTS last_cc_inspection_date_drop;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN IF NOT EXISTS last_cc_inspection_date_drop TIMESTAMP

