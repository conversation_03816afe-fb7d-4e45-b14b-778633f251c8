--liquibase formatted sql
--changeset ${tenant_id}_curated:0012-01_01
CREATE TABLE ${tenant_id}_oa_curated.fct_power_chain_telemetry
(
    fact_type STRING NOT NULL,
    file STRING NOT NULL,
    telemetry_type STRING NOT NULL,
    tel_incident_id STRING NOT NULL,
    tel_type_processing_ms FLOAT64,
    tel_type_size_bytes INT64,
    tel_type_size_lines INT64,
    record_timestamp TIMESTAMP NOT NULL,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING NOT NULL
)
PARTITION BY DATE(record_timestamp)
CLUSTER BY facility;

-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_power_chain_telemetry;