--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0006-01_01
ALTER TABLE ${tenant_id}_oa_curated.fct_induction
ADD COLUMN tenant STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_induction DROP COLUMN tenant;

--changeset ${tenant_id}_oa_curated:0006-01_02
ALTER TABLE ${tenant_id}_oa_curated.fct_induction
ADD COLUMN facility STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_induction DROP COLUMN facility;

--changeset ${tenant_id}_oa_curated:0006-01_03
ALTER TABLE ${tenant_id}_oa_curated.fct_induction
DROP COLUMN IF EXISTS record_timestamp;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_induction ADD COLUMN record_timestamp DATETIME;

--changeset ${tenant_id}_oa_curated:0006-01_04
ALTER TABLE ${tenant_id}_oa_curated.fct_induction
ADD COLUMN record_timestamp TIMESTAMP;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_induction DROP COLUMN record_timestamp;


--changeset ${tenant_id}_oa_curated:0006-01_05
ALTER TABLE ${tenant_id}_oa_curated.dim_receiving_advice
ADD COLUMN tenant STRING,
ADD COLUMN facility STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.dim_receiving_advice DROP COLUMN tenant, DROP COLUMN facility;

--changeset ${tenant_id}_oa_curated:0006-01_06
ALTER TABLE ${tenant_id}_oa_curated.fct_receiving_advice_line
ADD COLUMN tenant STRING,
ADD COLUMN facility STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_receiving_advice_line DROP COLUMN tenant, DROP COLUMN facility;

--changeset ${tenant_id}_oa_curated:0006-01_07
ALTER TABLE ${tenant_id}_oa_curated.fct_receiving_advice_summary
ADD COLUMN tenant STRING,
ADD COLUMN facility STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_receiving_advice_summary DROP COLUMN tenant, DROP COLUMN facility;

--changeset ${tenant_id}_oa_curated:0006-01_08
ALTER TABLE ${tenant_id}_oa_curated.fct_transport
ADD COLUMN tenant STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_transport DROP COLUMN tenant, DROP COLUMN facility;

--changeset ${tenant_id}_oa_curated:0006-01_09
ALTER TABLE ${tenant_id}_oa_curated.dim_facility_order
ADD COLUMN tenant STRING,
ADD COLUMN facility STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.dim_facility_order DROP COLUMN tenant, DROP COLUMN facility;

--changeset ${tenant_id}_oa_curated:0006-01_10
ALTER TABLE ${tenant_id}_oa_curated.fct_facility_order
ADD COLUMN tenant STRING,
ADD COLUMN facility STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_facility_order DROP COLUMN tenant, DROP COLUMN facility;

--changeset ${tenant_id}_oa_curated:0006-01_11
ALTER TABLE ${tenant_id}_oa_curated.fct_facility_order_line
ADD COLUMN tenant STRING,
ADD COLUMN facility STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_facility_order_line DROP COLUMN tenant, DROP COLUMN facility;

