--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0005-01_01
CREATE TABLE ${tenant_id}_oa_curated.dim_receiving_advice
(
  receiving_advice_uuid STRING NOT NULL,
  receiving_advice_code STRING,
  receiving_advice_owner STRING,
  receiving_advice_type STRING,
  supplier STRING,
  source_system STRING,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_receiving_advice;

--changeset ${tenant_id}_oa_curated:0005-01_02
CREATE TABLE ${tenant_id}_oa_curated.fct_induction
(
  record_timestamp DATETIME NOT NULL,
  location_uuid STRING NOT NULL,
  work_area_uuid STRING NOT NULL,
  module_uuid STRING NOT NULL,
  container_type_uuid STRING NOT NULL,
  load_unit_code STRING NOT NULL,
  height FLOAT64,
  weight FLOAT64,
  contour_details STRING,
  source_system STRING,
  etl_batch_id STRING
)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_induction;

--changeset ${tenant_id}_oa_curated:0005-01_03
CREATE TABLE ${tenant_id}_oa_curated.fct_receiving_advice_line
(
  record_timestamp DATETIME NOT NULL,
  receiving_advice_uuid STRING NOT NULL,
  receiving_advice_line_code STRING NOT NULL,
  item_uuid STRING NOT NULL,
  handling_unit_code STRING,
  container_type_uuid STRING NOT NULL,
  receiving_advice_qty INT64,
  originator STRING,
  packaging_level_code STRING,
  source_system STRING,
  etl_batch_id STRING
)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_receiving_advice_line;

--changeset ${tenant_id}_oa_curated:0005-01_04
CREATE TABLE ${tenant_id}_oa_curated.fct_receiving_advice_summary
(
  record_timestamp DATETIME NOT NULL,
  receiving_advice_uuid STRING NOT NULL,
  receiving_advice_complete_date_time DATETIME,
  receiving_advice_completed_pallet_count INT64,
  receiving_advice_expected_line_count INT64,
  receiving_advice_over_delivered_line_count INT64,
  receiving_advice_completed_line_count INT64,
  receiving_advice_under_delivered_line_count INT64,
  receiving_advice_over_delivered_pallet_count INT64,
  receiving_advice_exactly_delivered_line_count INT64,
  receiving_advice_under_delivered_pallet_count INT64,
  receiving_advice_exactly_delivered_pallet_count INT64,
  source_system STRING,
  etl_batch_id STRING
)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_receiving_advice_summary;

--changeset ${tenant_id}_oa_curated:0005-01_05
CREATE TABLE ${tenant_id}_oa_curated.fct_transport
(
  record_timestamp DATETIME NOT NULL,
  source_location_uuid STRING NOT NULL,
  source_work_area_uuid STRING NOT NULL,
  source_module_uuid STRING NOT NULL,
  destination_location_uuid STRING NOT NULL,
  destination_work_area_uuid STRING NOT NULL,
  destination_module_uuid STRING NOT NULL,
  item_uuid STRING NOT NULL,
  container_type_uuid STRING NOT NULL,
  movement_type_uuid STRING NOT NULL,
  transport_request_code STRING NOT NULL,
  handling_unit_code STRING NOT NULL,
  transport_request_type STRING,
  facility STRING,
  source_system STRING,
  etl_batch_id STRING
)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_transport;

--changeset ${tenant_id}_oa_curated:0005-01_06
CREATE TABLE ${tenant_id}_oa_curated.dim_facility_order
(
  facility_order_uuid STRING NOT NULL,
  facility_order_code STRING,
  facility_order_name STRING,
  facility_order_owner STRING,
  facility_order_type STRING,
  facility_order_processing_type STRING,
  facility_order_category STRING,
  facility_order_priority INT64,
  facility_order_urgency INT64,
  facility_order_packing_requirement STRING,
  facility_order_vas_requirememnt STRING,
  route STRING,
  facility_order_destination_store STRING,
  facility_order_customer_receiving_method STRING,
  initiating_customer_order_reference_id STRING,
  requested_line_count INT64,
  requested_qty INT64,
  requested_sku_count INT64,
  facility_order_deadline_date_time DATETIME,
  facility_order_earliest_staging_date_time DATETIME,
  facility_order_staging_deadline_date_time DATETIME,
  facility_order_earliest_start_date_time DATETIME,
  facility_order_start_deadline_date_time DATETIME,
  facility_order_picking_deadline_date_time DATETIME,
  facility_order_loading_start_deadline_date_time DATETIME,
  facility_order_ship_deadline_date_time DATETIME,
  facility_order_delivery_deadline_date_time DATETIME,
  source_system STRING,
  active_rec_ind INT64,
  etl_batch_id STRING
)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_facility_order;

--changeset ${tenant_id}_oa_curated:0005-01_07
CREATE TABLE ${tenant_id}_oa_curated.fct_facility_order
(
  record_timestamp DATETIME NOT NULL,
  facility_order_uuid STRING NOT NULL,
  subsystem_uuid STRING NOT NULL,
  facility_order_event STRING NOT NULL,
  reason_uuid STRING NOT NULL,
  wave_uuid STRING NOT NULL,
  event_detail STRING,
  source_system STRING,
  etl_batch_id STRING
)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_facility_order;

--changeset ${tenant_id}_oa_curated:0005-01_08
CREATE TABLE ${tenant_id}_oa_curated.fct_facility_order_line
(
  record_timestamp DATETIME NOT NULL,
  facility_order_uuid STRING NOT NULL,
  facility_order_line_code STRING NOT NULL,
  requested_item_uuid STRING NOT NULL,
  item_uuid STRING NOT NULL,
  facility_order_line_event STRING NOT NULL,
  reason_uuid STRING NOT NULL,
  batch_number STRING NOT NULL,
  completed_qty INT64 NOT NULL,
  requested_qty INT64,
  source_system STRING,
  etl_batch_id STRING
)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_facility_order_line;
