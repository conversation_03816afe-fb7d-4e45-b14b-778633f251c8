--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0030-01_01
CREATE TABLE ${tenant_id}_oa_curated.agg_workstation_activities
(
    starvation_time INT64,
    blocking_time INT64,
    event STRING,
    next_event STRING,
    load_unit_code STRING,
    next_load_unit_code STRING,
    load_unit_type STRING,
    next_load_unit_type STRING,
    record_timestamp TIMESTAMP,
    next_record_timestamp TIMESTAMP,
    workstation_uuid STRING,
    next_workstation_uuid STRING,
    operator_uuid STRING,
    facility STRING,
    source_system STRING,
    tenant STRING,
    etl_batch_id STRING
)
CLUSTER BY facility
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.agg_workstation_activities;