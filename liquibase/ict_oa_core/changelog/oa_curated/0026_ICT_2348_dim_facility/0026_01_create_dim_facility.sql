--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0026-01_01
CREATE TABLE ${tenant_id}_oa_curated.dim_facility
(
  facility_uuid STRING NOT NULL,
  etl_batch_id STRING NOT NULL,
  record_timestamp TIMESTAMP NOT NULL,
  facility_code STRING NOT NULL,
  facility_name STRING,
  primary_key STRING NOT NULL,
  description STRING,
  facility STRING NOT NULL,
  tenant STRING NOT NULL,
  source_system STRING NOT NULL
)
CLUSTER BY facility
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_facility;

