--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:ICT-4076-01_01
CREATE TABLE ${tenant_id}_oa_curated.hst_item_by_zone
(
    etl_batch_id STRING NOT NULL,
    item_uuid STRING NOT NULL,
    record_timestamp TIMESTAMP NOT NULL,
    work_area_code STRING,
    inventory_qty INT64 DEFAULT 0 NOT NULL,
    avg_picked_qty FLOAT64 DEFAULT 0,
    avg_picked_orders FLOAT64 DEFAULT 0
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY);
--rollback DROP TABLE ${tenant_id}_oa_curated.hst_item_by_zone;