--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:ICT-2680-01_01
ALTER TABLE ${tenant_id}_oa_curated.hst_item
    ADD COLUMN avg_picked_qty FLOAT64;
ALTER TABLE ${tenant_id}_oa_curated.hst_item 
    ALTER COLUMN avg_picked_qty SET DEFAULT 0;
UPDATE ${tenant_id}_oa_curated.hst_item SET avg_picked_qty = 0 WHERE TRUE;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.hst_item DROP COLUMN avg_picked_qty;

--changeset ${tenant_id}_oa_curated:ICT-2680-01_02
ALTER TABLE ${tenant_id}_oa_curated.hst_item
    ADD COLUMN avg_picked_orders FLOAT64;
ALTER TABLE ${tenant_id}_oa_curated.hst_item 
    ALTER COLUMN avg_picked_orders SET DEFAULT 0;
UPDATE ${tenant_id}_oa_curated.hst_item SET avg_picked_orders = 0 WHERE TRUE;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.hst_item DROP COLUMN avg_picked_orders;