--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:0017-01_01
ALTER TABLE ${tenant_id}_oa_curated.dim_receiving_advice
ADD COLUMN IF NOT EXISTS start_time TIMESTAMP;
-- rollback: ALTER TABLE ${tenant_id}_oa_curated.dim_receiving_advice DROP COLUMN start_time;

--changeset ${tenant_id}_oa_curated:0017-01_02
ALTER TABLE ${tenant_id}_oa_curated.dim_receiving_advice
ADD COLUMN IF NOT EXISTS create_time TIMESTAMP;
-- rollback: ALTER TABLE ${tenant_id}_oa_curated.dim_receiving_advice DROP COLUMN create_time;

--changeset ${tenant_id}_oa_curated:0017-01_03
ALTER TABLE ${tenant_id}_oa_curated.dim_receiving_advice
ADD COLUMN IF NOT EXISTS finish_time TIMESTAMP;
-- rollback: ALTER TABLE ${tenant_id}_oa_curated.dim_receiving_advice DROP COLUMN finish_time;
