--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:0019-01_01
CREATE TABLE ${tenant_id}_oa_curated.fct_pack_task
(
    record_timestamp TIMESTAMP NOT NULL,
    pack_task_uuid STRING NOT NULL,
    pack_task_code STRING NOT NULL,
    facility_order_uuid STRING NOT NULL,
    facility_order_code STRING NOT NULL,
    workstation_uuid STRING,
    workstation_code STRING,
    event STRING NOT NULL,
    service_qualifier STRING,
    shipping_label_barcode STRING,
    destination_uuid STRING,
    destination_code STRING,
    preferred_handling_unit_type_uuid STRING,
    preferred_handling_unit_type_code STRING,
    packing_document_type STRING,
    vas_requirement STRING,
    vas_instructions STRING,
    priority INT64,
    vas_sequence STRING,
    order_channel STRING,
    packing_documents STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING,
)
PARTITION BY DATE(record_timestamp)
CLUSTER BY facility;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_pack_task;

--changeset ${tenant_id}_oa_curated:0019-01_02
CREATE TABLE ${tenant_id}_oa_curated.fct_pack_task_line
(
    record_timestamp TIMESTAMP NOT NULL,
    pack_task_line_uuid STRING NOT NULL,
    pack_task_line_code STRING NOT NULL,
    pack_task_uuid STRING NOT NULL,
    pack_task_code STRING NOT NULL,
    facility_order_uuid STRING,
    facility_order_code STRING,
    facility_order_line_uuid STRING,
    facility_order_line_code STRING,
    source_handling_unit_uuid STRING,
    source_handling_unit_code STRING,
    destination_handling_unit_uuid STRING,
    destination_handling_unit_code STRING,
    workstation_uuid STRING,
    workstation_code STRING,
    source_handling_unit_type STRING,
    destination_handling_unit_type STRING,
    destination_preferred_handling_unit_type STRING,
    technology STRING,
    area_uuid STRING,
    area_code STRING,
    group_uuid STRING,
    group_code STRING,
    sku_uuid STRING NOT NULL,
    sku_code STRING NOT NULL,
    u_o_m STRING NOT NULL,
    product_code STRING NOT NULL,
    quantity_target INT64 NOT NULL,
    quantity_packed INT64,
    user_uuid STRING,
    user_code STRING,
    event STRING NOT NULL,
    confirmation_code STRING,
    vas_instructions_text STRING,
    vas_language STRING,
    vas_sequence STRING,
    expiration_date TIMESTAMP,
    batch_id STRING,
    reason STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING,
)
PARTITION BY DATE(record_timestamp)
CLUSTER BY facility;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_pack_task_line;



--changeset ${tenant_id}_oa_curated:0019-01_03
CREATE TABLE ${tenant_id}_oa_curated.fct_pack_activity
(
    record_timestamp TIMESTAMP NOT NULL,
    user_uuid STRING,
    user_code STRING,
    workstation_uuid STRING NOT NULL,
    workstation_code STRING NOT NULL,
    event STRING NOT NULL,
    event_status STRING NOT NULL,
    technology STRING,
    handling_unit_uuid STRING,
    handling_unit_code STRING,
    handling_unit_type STRING,
    workflow_uuid STRING,
    workflow_code STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING,
)
PARTITION BY DATE(record_timestamp)
CLUSTER BY facility;
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_pack_activity


--changeset ${tenant_id}_oa_curated:0019-01_04
CREATE TABLE ${tenant_id}_oa_curated.dim_pack_task
(
    pack_task_uuid STRING NOT NULL,
    pack_task_code STRING NOT NULL,
    facility_order_uuid STRING NOT NULL,
    facility_order_code STRING NOT NULL,
    workstation_uuid STRING,
    workstation_code STRING,
    service_qualifier STRING,
    destination_uuid STRING,
    destination_code STRING,
    preferred_handling_unit_type_uuid STRING,
    preferred_handling_unit_type_code STRING,
    packing_document_type STRING,
    vas_requirement STRING,
    vas_instructions STRING,
    priority INT64,
    vas_sequence STRING,
    order_channel STRING,
    packing_documents STRING,
    last_updated_timestamp TIMESTAMP,
    task_status STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING,
)
;
ALTER TABLE ${tenant_id}_oa_curated.dim_pack_task
ADD PRIMARY KEY(pack_task_uuid) NOT ENFORCED;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_pack_task;