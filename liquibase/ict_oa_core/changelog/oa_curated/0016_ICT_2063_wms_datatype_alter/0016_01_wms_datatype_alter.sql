--liquibase formatted sql
--changeset ${tenant_id}_curated:0016-01_01
DROP TABLE ${tenant_id}_oa_curated.fct_wms_customer_order;
--changeset ${tenant_id}_curated:0016-01_02
CREATE TABLE ${tenant_id}_oa_curated.fct_wms_customer_order
(
    date_added TIMESTAMP,
    carrier_id STRING,
    delivery_date TIMESTAMP,
    date_edited TIMESTAMP,
    effective_date TIMESTAMP,
    record_timestamp TIMESTAMP NOT NULL,
    external_id STRING,
    order_date TIMESTAMP,
    order_id STRING,
    order_type STRING,
    priority INT64,
    ship_date TIMESTAMP,
    ship_no_later_date TIMESTAMP,
    short_flag STRING,
    state STRING,
    sts STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING NOT NULL
)
PARTITION BY DATE(record_timestamp)
CLUSTER BY facility;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_wms_customer_order;

--changeset ${tenant_id}_curated:0016-01_03
DROP TABLE ${tenant_id}_oa_curated.fct_wms_customer_order_detail;
--changeset ${tenant_id}_curated:0016-01_04
CREATE TABLE ${tenant_id}_oa_curated.fct_wms_customer_order_detail
(
    date_added TIMESTAMP,
    date_edited TIMESTAMP,
    record_timestamp TIMESTAMP NOT NULL,
    orderline_id STRING,
    order_key STRING,
    orderline STRING, 
    qty_allocated INT64,
    qty_ordered INT64,
    qty_picked INT64,
    qty_shipped INT64,
    ordered_sku STRING,
    state STRING,
    store_id STRING,
    sts STRING,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING NOT NULL
)
PARTITION BY DATE(record_timestamp)
CLUSTER BY facility;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_wms_customer_order_detail;
