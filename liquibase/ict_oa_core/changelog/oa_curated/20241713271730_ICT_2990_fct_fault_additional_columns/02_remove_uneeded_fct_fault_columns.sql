--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:ICT-3355-02_01
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    DROP COLUMN status_code;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` ADD COLUMN status_code STRING;

--changeset ${tenant_id}_oa_curated:ICT-3355-02_02
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    DROP COLUMN logical_destination_code;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` ADD COLUMN logical_destination_code STRING;

--changeset ${tenant_id}_oa_curated:ICT-3355-02_03
ALTER TABLE `${tenant_id}_oa_curated.fct_sort`
    DROP COLUMN physical_destination_code;
-- rollback ALTER TABLE `${tenant_id}_oa_curated.fct_sort` ADD COLUMN physical_destination_code STRING;
