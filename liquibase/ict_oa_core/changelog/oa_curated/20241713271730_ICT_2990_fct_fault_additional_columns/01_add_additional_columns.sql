--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:ICT-2990-01_01
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN condition_name STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN condition_name;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_02
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN event_category STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN event_category;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_03
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN event_type STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN event_type;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_04
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN quality STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN quality;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_05
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN node STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN node;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_06
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN subsystem_uuid STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN subsystem_uuid;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_07
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN device_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN device_code;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_08
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN fault_tag_reason_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN fault_tag_reason_code;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_09
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN item_category_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN item_category_code;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_10
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN item_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN item_code;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_11
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN location_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN location_code;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_12
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN module_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN module_code;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_13
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN operator_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN operator_code;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_14
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN physical_device_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN physical_device_code;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_15
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN subsystem_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN subsystem_code;

--changeset ${tenant_id}_oa_curated:ICT-2990-01_16
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN status_category_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN status_category_code;
