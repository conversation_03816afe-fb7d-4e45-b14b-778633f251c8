--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:0023-01_01
ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online
DROP COLUMN IF EXISTS online_operator_uuids;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online ADD COLUMN online_operator_uuids STRING;

--changeset ${tenant_id}_oa_curated:0023-01_02
ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online
DROP COLUMN IF EXISTS interval_start;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online ADD COLUMN interval_start TIMESTAMP;

--changeset ${tenant_id}_oa_curated:0023-01_03
ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online
DROP COLUMN IF EXISTS interval_end;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online ADD COLUMN interval_end TIMESTAMP;

--changeset ${tenant_id}_oa_curated:0023-01_04
ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online
ADD COLUMN record_timestamp TIMESTAMP;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online DROP COLUMN record_timestamp;

--changeset ${tenant_id}_oa_curated:0023-01_05
ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online
ADD COLUMN area STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online DROP COLUMN area;

--changeset ${tenant_id}_oa_curated:0023-01_06
ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online
ADD COLUMN facility STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online DROP COLUMN facility;

--changeset ${tenant_id}_oa_curated:0023-01_07
ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online
ADD COLUMN source_system STRING;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_operators_online DROP COLUMN source_system;
