--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0025-01_01
CREATE TABLE ${tenant_id}_oa_curated.fct_workstation_workflow (
  workstation_uuid	STRING NOT NULL,
  etl_batch_id STRING NOT NULL,
  record_timestamp TIMESTAMP NOT NULL,
  workstation_code STRING NOT NULL,
  tenant_name STRING,
  mod_user STRING,
  workflow_status STRING,
  workstation_status STRING,
  workflow STRING,
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(record_timestamp, DAY)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_workstation_workflow;
