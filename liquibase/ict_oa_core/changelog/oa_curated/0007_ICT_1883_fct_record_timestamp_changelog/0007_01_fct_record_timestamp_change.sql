--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0007-01_01
ALTER TABLE ${tenant_id}_oa_curated.fct_receiving_advice_line
DROP COLUMN IF EXISTS record_timestamp;

ALTER TABLE ${tenant_id}_oa_curated.fct_receiving_advice_line
ADD COLUMN record_timestamp TIMESTAMP;

-- rollback DROP COLUMN IF EXISTS record_timestamp;

--changeset ${tenant_id}_oa_curated:0007-01_02
ALTER TABLE ${tenant_id}_oa_curated.fct_receiving_advice_summary
DROP COLUMN IF EXISTS record_timestamp;

ALTER TABLE ${tenant_id}_oa_curated.fct_receiving_advice_summary
ADD COLUMN record_timestamp TIMESTAMP;

-- rollback DROP COLUMN IF EXISTS record_timestamp;

--changeset ${tenant_id}_oa_curated:0007-01_03
ALTER TABLE ${tenant_id}_oa_curated.fct_transport
DROP COLUMN IF EXISTS record_timestamp;

ALTER TABLE ${tenant_id}_oa_curated.fct_transport
ADD COLUMN record_timestamp TIMESTAMP;

-- rollback DROP COLUMN IF EXISTS record_timestamp;

--changeset ${tenant_id}_oa_curated:0007-01_04
ALTER TABLE ${tenant_id}_oa_curated.dim_facility_order
DROP COLUMN IF EXISTS record_timestamp;

ALTER TABLE ${tenant_id}_oa_curated.dim_facility_order
ADD COLUMN record_timestamp TIMESTAMP;

-- rollback DROP COLUMN IF EXISTS record_timestamp;

--changeset ${tenant_id}_oa_curated:0007-01_05
ALTER TABLE ${tenant_id}_oa_curated.fct_facility_order
DROP COLUMN IF EXISTS record_timestamp;

ALTER TABLE ${tenant_id}_oa_curated.fct_facility_order
ADD COLUMN record_timestamp TIMESTAMP;

-- rollback DROP COLUMN IF EXISTS record_timestamp;

--changeset ${tenant_id}_oa_curated:0007-01_06
ALTER TABLE ${tenant_id}_oa_curated.fct_facility_order_line
DROP COLUMN IF EXISTS record_timestamp;

ALTER TABLE ${tenant_id}_oa_curated.fct_facility_order_line
ADD COLUMN record_timestamp TIMESTAMP;

-- rollback DROP COLUMN IF EXISTS record_timestamp;
