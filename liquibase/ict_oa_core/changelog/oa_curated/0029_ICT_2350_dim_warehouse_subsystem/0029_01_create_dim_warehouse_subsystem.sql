--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0029-01_01
CREATE TABLE ${tenant_id}_oa_curated.dim_warehouse_subsystem
(
  warehouse_subsystem_uuid STRING NOT NULL,
  warehouse_subsystem_code STRING NOT NULL,
  facility_code STRING NOT NULL,
  etl_batch_id STRING NOT NULL,
  facility STRING NOT NULL,
  tenant STRING NOT NULL,
  source_system STRING NOT NULL,
  bid STRING,
  type STRING
)
CLUSTER BY facility
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.dim_warehouse_subsystem;