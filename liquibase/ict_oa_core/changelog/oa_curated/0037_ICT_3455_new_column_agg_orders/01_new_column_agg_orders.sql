--liquibase formatted sql
--changeset ${tenant_id}_oa_curated.agg_orders:0037-01_01
DROP TABLE ${tenant_id}_oa_curated.agg_orders;
-- rollback DROP TABLE ${tenant_id}_oa_curated.agg_orders;

--changeset ${tenant_id}_oa_curated.agg_orders:0037-01_02
CREATE TABLE ${tenant_id}_oa_curated.agg_orders
(
    record_timestamp TIMESTAMP NOT NULL,
    tenant STRING NOT NULL,	
    facility STRING	NOT NULL,
    source_system STRING NOT NULL,	
    total_orders INTEGER NOT NULL,
    completed_orders INTEGER NOT NULL,	
    incompleted_orders INTEGER NOT NULL,	
    percent_completed FLOAT64	NOT NULL,
    etl_batch_id STRING	NOT NULL,
)
-- rollback DROP TABLE ${tenant_id}_oa_curated.agg_orders;