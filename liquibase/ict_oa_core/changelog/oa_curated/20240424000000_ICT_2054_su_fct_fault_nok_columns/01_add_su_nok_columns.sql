--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:ICT-2054-01_01
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN classification STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN classification;

--changeset ${tenant_id}_oa_curated:ICT-2054-01_02
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN deviceStatus STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN deviceStatus;

--changeset ${tenant_id}_oa_curated:ICT-2054-01_03
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN deviceType STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN deviceType;

--changeset ${tenant_id}_oa_curated:ICT-2054-01_04
ALTER TABLE ${tenant_id}_oa_curated.fct_fault ADD COLUMN statusId STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_fault DROP COLUMN statusId;

