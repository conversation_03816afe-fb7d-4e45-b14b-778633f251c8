--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:00011-01_01
CREATE TABLE ${tenant_id}_oa_curated.fct_heartbeat
(
    dc_hostname STRING,
    dc_username STRING,
    drive0_name STRING,
    drive0_space_gb FLOAT64,
    drive1_name STRING,
    drive1_space_gb FLOAT64,
    error_backlog_files INT64,
    heartbeat_type STRING,
    insights_backlog_files INT64,
    insights_tenant STRING,
    os_free_mem_gb FLOAT64,
    os_max_mem_gb FLOAT64,
    os_name STRING,
    os_uptime_days FLOAT64,
    os_version STRING,
    power_chain_version STRING,
    ps_version STRING,
    record_timestamp TIMESTAMP,
    sprocket_backlog_files INT64,
    sprocket_customer_id STRING,
    tenant STRING,
    facility STRING,
    source_system STRING,
    etl_batch_id STRING
)
PARTITION BY DATE(record_timestamp)
CLUSTER BY facility;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_heartbeat;
