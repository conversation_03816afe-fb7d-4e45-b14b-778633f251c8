--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:0020-01_01
ALTER TABLE ${tenant_id}_oa_curated.fct_pack_activity
DROP COLUMN IF EXISTS handling_unit_uuid;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_pack_activity ADD COLUMN handling_unit_uuid STRING;

--changeset ${tenant_id}_oa_curated:0020-01_02
ALTER TABLE ${tenant_id}_oa_curated.fct_pack_activity
DROP COLUMN IF EXISTS workflow_uuid;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_pack_activity ADD COLUMN workflow_uuid STRING;


--changeset ${tenant_id}_oa_curated:0020-01_03
ALTER TABLE ${tenant_id}_oa_curated.fct_pack_task_line
DROP COLUMN IF EXISTS source_handling_unit_uuid;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_pack_task_line ADD COLUMN source_handling_unit_uuid STRING;

--changeset ${tenant_id}_oa_curated:0020-01_04
ALTER TABLE ${tenant_id}_oa_curated.fct_pack_task_line
DROP COLUMN IF EXISTS destination_handling_unit_uuid;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_pack_task_line ADD COLUMN destination_handling_unit_uuid STRING;