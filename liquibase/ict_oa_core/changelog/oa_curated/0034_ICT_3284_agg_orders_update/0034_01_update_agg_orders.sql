--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0034_02-01
ALTER TABLE ${tenant_id}_oa_curated.agg_orders
    DROP COLUMN IF EXISTS record_time_by_day;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_orders ADD COLUMN IF NOT EXISTS record_time_by_day TIMESTAMP;

--changeset ${tenant_id}_oa_curated:0034_02-02
ALTER TABLE ${tenant_id}_oa_curated.agg_orders
    DROP COLUMN IF EXISTS pick_order_event;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_orders ADD COLUMN IF NOT EXISTS pick_order_event STRING;

--changeset ${tenant_id}_oa_curated:0034_02-03
ALTER TABLE ${tenant_id}_oa_curated.agg_orders
    DROP COLUMN IF EXISTS pick_order_totals;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_orders ADD COLUMN IF NOT EXISTS pick_order_totals STRING;

--changeset ${tenant_id}_oa_curated:0034_02-04
ALTER TABLE ${tenant_id}_oa_curated.agg_orders
    ADD COLUMN IF NOT EXISTS total_orders INT64;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_orders DROP COLUMN IF EXISTS total_orders;

--changeset ${tenant_id}_oa_curated:0034_02-05
ALTER TABLE ${tenant_id}_oa_curated.agg_orders
    ADD COLUMN IF NOT EXISTS completed_orders INT64;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_orders DROP COLUMN IF EXISTS completed_orders;

--changeset ${tenant_id}_oa_curated:0034_02-06
ALTER TABLE ${tenant_id}_oa_curated.agg_orders
    ADD COLUMN IF NOT EXISTS incomplete_orders INT64;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_orders DROP COLUMN IF EXISTS incomplete_orders;
