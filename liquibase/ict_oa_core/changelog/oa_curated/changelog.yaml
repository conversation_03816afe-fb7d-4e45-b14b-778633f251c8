databaseChangeLog:
  - include:
      file: changelog/oa_curated/0000_initial_oa_curated_tables/changelog.yaml
  - include:
      file: changelog/oa_curated/0001_ICT_1638_agg_tables/changelog.yaml
  - include:
      file: changelog/oa_curated/0002_ICT_1638_dim_module_change/changelog.yaml
  - include:
      file: changelog/oa_curated/0003_ICT_1638_dim_zone_change/changelog.yaml
  - include:
      file: changelog/oa_curated/0004_ICT_1746_debug_stage_environment/changelog.yaml
  - include:
      file: changelog/oa_curated/0005_ICT_1757_dematic_solutions_onboarding/changelog.yaml
  - include:
      file: changelog/oa_curated/0006_ICT_1725_induction_fact_changes/changelog.yaml
  - include:
      file: changelog/oa_curated/0007_ICT_1883_fct_record_timestamp_changelog/changelog.yaml
  - include:
      file: changelog/oa_curated/0008_ICT_1700_fct_order_line_workstation/changelog.yaml
  - include:
      file: changelog/oa_curated/0009_ICT_1731_bin_utilization_table/changelog.yaml
  - include:
      file: changelog/oa_curated/0010_ICT_1722_receiving_advice_code/changelog.yaml
  - include:
      file: changelog/oa_curated/0011_ICT_2060_heartbeat_curated_tables/changelog.yaml
  - include:
      file: changelog/oa_curated/0012_ICT_2045_power_chain_telemetry_curated/changelog.yaml
  - include:
      file: changelog/oa_curated/0013_ICT_2063_wms_customer_order_info_curated/changelog.yaml
  - include:
      file: changelog/oa_curated/0014_ICT_1730_dialog_active_curated/changelog.yaml
  - include:
      file: changelog/oa_curated/0015_ICT_2051_add_area_column/changelog.yaml
  - include:
      file: changelog/oa_curated/0016_ICT_2063_wms_datatype_alter/changelog.yaml
  - include:
      file: changelog/oa_curated/0017_ICT_2256_dim_receiving_additions/changelog.yaml
  - include:
      file: changelog/oa_curated/0018_ICT_2314_dialog_active_remove_excess/changelog.yaml
  - include:
      file: changelog/oa_curated/0019_ICT_2226_create_curated_pack_facts/changelog.yaml
  - include:
      file: changelog/oa_curated/0020_ICT_2226_alter_pack_facts/changelog.yaml
  - include:
      file: changelog/oa_curated/0021_ICT_2395_inventory_dist_stat_history/changelog.yaml
  - include:
      file: changelog/oa_curated/0022_ICT_2262_inventory_history_column_typo/changelog.yaml
  - include:
      file: changelog/oa_curated/0023_ICT_2663_agg_operator_changes/changelog.yaml
  - include:
      file: changelog/oa_curated/0024_ICT_2351_fct_customer_order/changelog.yaml
  - include:
      file: changelog/oa_curated/0025_ICT_2353_fct_workstation_workflow/changelog.yaml
  - include:
      file: changelog/oa_curated/0026_ICT_2348_dim_facility/changelog.yaml
  - include:
      file: changelog/oa_curated/202425032328_ICT_2952_multiplicity_column/changelog.yaml
  - include:
      file: changelog/oa_curated/ICT_2680_inv_avgdailyorders_avgdailyquantity/changelog.yaml
  - include:
      file: changelog/oa_curated/0029_ICT_2350_dim_warehouse_subsystem/changelog.yaml
  - include:
      file: changelog/oa_curated/0027_ICT_2891_orders_outstanding_etl/changelog.yaml
  - include:
      file: changelog/oa_curated/0030_ICT_3005_agg_workstation_activities/changelog.yaml
  - include:
      file: changelog/oa_curated/0031_ICT_2227_fact_event_column/changelog.yaml
  - include:
      file: changelog/oa_curated/ICT_2349_dim_subgroup/changelog.yaml
  - include:
      file: changelog/oa_curated/0032_ICT_3131_agg_workstation_operators/changelog.yaml
  - include:
      file: changelog/oa_curated/ICT_3007_dupe_errors/changelog.yaml
  - include:
      file: changelog/oa_curated/0033_ICT_3131_update_column_names/changelog.yaml  
  - include:
      file: changelog/oa_curated/20241713271730_ICT_2990_fct_fault_additional_columns/changelog.yaml
  - include:
      file: changelog/oa_curated/20242204000000_ICT_3178_fct_inventory_wms_columns/changelog.yaml    
  - include:
      file: changelog/oa_curated/0034_ICT_3323_receiving_advice_line_additional_column_changes/changelog.yaml
  - include:
      file: changelog/oa_curated/20240508130017_ICT_3355_fct_sort_add_codes_uuids/changelog.yaml
  - include:
      file: changelog/oa_curated/0035_ICT_3267_counting_facts/changelog.yaml
  - include:
      file: changelog/oa_curated/20240424000000_ICT_2054_su_fct_fault_nok_columns/changelog.yaml
  - include:
      file: changelog/oa_curated/0034_ICT_3284_agg_orders_update/changelog.yaml
  - include:
      file: changelog/oa_curated/0036_ICT_2828_workstation_listing_agg/changelog.yaml
  - include:
      file: changelog/oa_curated/0037_ICT_3455_new_column_agg_orders/changelog.yaml
  - include:
      file: changelog/oa_curated/0038_ICT_3561_handling_unit/changelog.yaml
  - include:
      file: changelog/oa_curated/20241007000000_ICT_3684_fct_inventory_last_activity_date/changelog.yaml
  - include: 
      file: changelog/oa_curated/20240717160900_ICT_3684_fct_inventory_date_field_fix_refactor/changelog.yaml
  - include: 
      file: changelog/oa_curated/202408011511_ICT_4076_agg_hst_item_by_zone/changelog.yaml
  - include:
      file: changelog/oa_curated/ICT_3928_new_fault_code_column/changelog.yaml

