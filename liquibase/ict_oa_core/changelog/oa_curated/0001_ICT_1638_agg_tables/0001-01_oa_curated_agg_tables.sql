--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0001-01_01
CREATE TABLE ${tenant_id}_oa_curated.agg_operators_online (
    interval_start TIMESTAMP NOT NULL,
    interval_end TIMESTAMP NOT NULL,
    online_operators INT64 NOT NULL,
    online_operator_uuids STRING NOT NULL,
    tenant           STRING NOT NULL,
    etl_batch_id STRING NOT NULL
);
-- rollback DROP TABLE ${tenant_id}_oa_curated.agg_operators_online;

--changeset ${tenant_id}_oa_curated:0000-01_02
CREATE TABLE ${tenant_id}_oa_curated.agg_inventory_activities
(
  record_timestamp TIMESTAMP NOT NULL,
  item_uuid STRING,
  item_sku STRING,
  location_uuid STRING,
  location_name STRING,
  process_type_code STRING,
  tenant STRING,
  etl_batch_id STRING NOT NULL
)
;
-- rollback DROP TABLE ${tenant_id}_oa_curated.agg_inventory_activities;