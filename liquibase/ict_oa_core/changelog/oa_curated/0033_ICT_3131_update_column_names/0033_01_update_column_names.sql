--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0033-01_01
ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators 
DROP COLUMN IF EXISTS event;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators ADD COLUMN event STRING;

--changeset ${tenant_id}_oa_curated:0033-01_02
ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators 
DROP COLUMN IF EXISTS next_event;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators ADD COLUMN next_event STRING;

--changeset ${tenant_id}_oa_curated:0033-01_03
ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators 
DROP COLUMN IF EXISTS record_timestamp;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators ADD COLUMN record_timestamp STRING;

--changeset ${tenant_id}_oa_curated:0033-01_04
ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators 
DROP COLUMN IF EXISTS next_record_timestamp;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators ADD COLUMN next_record_timestamp STRING;


--changeset ${tenant_id}_oa_curated:0033-01_05
ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators 
ADD COLUMN starting_timestamp TIMESTAMP;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators DROP COLUMN starting_timestamp;

--changeset ${tenant_id}_oa_curated:0033-01_06
ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators 
ADD COLUMN ending_timestamp TIMESTAMP;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.agg_workstation_operators DROP COLUMN ending_timestamp;

