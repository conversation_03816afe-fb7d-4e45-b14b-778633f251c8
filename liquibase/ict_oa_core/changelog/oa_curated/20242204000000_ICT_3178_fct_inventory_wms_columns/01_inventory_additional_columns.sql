--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:ICT-3178-01_01
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN item_category_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN item_category_code;

--changeset ${tenant_id}_oa_curated:ICT-3178-01_02
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN item_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN item_code;

--changeset ${tenant_id}_oa_curated:ICT-3178-01_03
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN item_name STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN item_name;

--changeset ${tenant_id}_oa_curated:ICT-3178-01_04
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN item_sku STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN item_sku;

--changeset ${tenant_id}_oa_curated:ICT-3178-01_05
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN item_product_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN item_product_code;

--changeset ${tenant_id}_oa_curated:ICT-3178-01_06
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN min_cont_in_dms STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN min_cont_in_dms;

--changeset ${tenant_id}_oa_curated:ICT-3178-01_07
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN max_cont_in_dms STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN max_cont_in_dms;

--changeset ${tenant_id}_oa_curated:ICT-3178-01_08
ALTER TABLE ${tenant_id}_oa_curated.fct_inventory ADD COLUMN condition_code STRING;
--rollback ALTER TABLE ${tenant_id}_oa_curated.fct_inventory DROP COLUMN condition_code;
