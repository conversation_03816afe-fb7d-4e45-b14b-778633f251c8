--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:0018-01_01
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS user_subject;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN user_subject STRING;

--changeset ${tenant_id}_oa_curated:0018-01_02
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS activated_at;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN activated_at TIMESTAMP;

--changeset ${tenant_id}_oa_curated:0018-01_03
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS closed;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN closed BOOLEAN;

--changeset ${tenant_id}_oa_curated:0018-01_04
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS create_date;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN create_date TIMESTAMP;

--changeset ${tenant_id}_oa_curated:0018-01_05
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS create_user_type;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN create_user_type STRING;

--changeset ${tenant_id}_oa_curated:0018-01_06
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS create_user_value;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN create_user_value STRING;

--changeset ${tenant_id}_oa_curated:0018-01_07
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS deactivated_at;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN deactivated_at TIMESTAMP;

--changeset ${tenant_id}_oa_curated:0018-01_08
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS dialog_id;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN dialog_id STRING;

--changeset ${tenant_id}_oa_curated:0018-01_09
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS end_time;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN end_time TIMESTAMP;

--changeset ${tenant_id}_oa_curated:0018-01_10
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS frame_type;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN frame_type STRING;

--changeset ${tenant_id}_oa_curated:0018-01_11
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS interaction_count;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN interaction_count INT64;

--changeset ${tenant_id}_oa_curated:0018-01_12
ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active
DROP COLUMN IF EXISTS tenant_name;
-- rollback ALTER TABLE ${tenant_id}_oa_curated.fct_dialog_active ADD COLUMN tenant_name STRING;
