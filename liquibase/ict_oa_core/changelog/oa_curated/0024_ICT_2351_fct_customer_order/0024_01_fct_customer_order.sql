--liquibase formatted sql

--changeset ${tenant_id}_oa_curated:0024-01_01
CREATE TABLE ${tenant_id}_oa_curated.fct_customer_order
(
  customer_order_uuid STRING NOT NULL,
  etl_batch_id STRING NOT NULL,
  customer_order_code STRING NOT NULL,
  tenant STRING NOT NULL, 
  facility STRING NOT NULL, 
  source_system STRING NOT NULL,
  customer_order_type STRING,
  record_timestamp TIMESTAMP NOT NULL,
  expected_order_line_count INTEGER,
  actual_order_line_count INTEGER,
  actual_container_count INTEGER,
  undelivered_line_count INTEGER,
  over_delivered_line_count INTEGER,
  under_delivered_line_count INTEGER,
  order_complete_date_time TIMESTAMP
)
PARTITION BY DATE(record_timestamp)
CLUSTER BY facility, customer_order_type, customer_order_code;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_customer_order;