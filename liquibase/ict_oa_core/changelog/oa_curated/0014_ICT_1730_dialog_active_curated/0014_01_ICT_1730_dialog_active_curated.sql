--liquibase formatted sql
--changeset ${tenant_id}_curated:0014-01_01
CREATE TABLE ${tenant_id}_oa_curated.fct_dialog_active
(
    dialog_name STRING NOT NULL,
    dialog_type STRING NOT NULL,
    event_date TIM<PERSON><PERSON>MP NOT NULL,
    user_subject STRING NOT NULL,
    activated_at TIMESTAMP,
    closed B<PERSON><PERSON>EAN,
    create_date TIM<PERSON><PERSON>MP,
    create_user_type STRING,
    create_user_value STRING,
    deactivated_at TIMESTAMP,
    dialog_id STRING,
    end_time TIMESTAMP,
    frame_type STRING,
    interaction_count INT64,
    session_id STRING,
    tenant_name STRING,
    terminal_id STRING,
    user_id STRING,
    operator_uuid STRING,
    record_timestamp TIMESTAMP NOT NULL,
    tenant STRING NOT NULL,
    facility STRING NOT NULL,
    source_system STRING NOT NULL,
    etl_batch_id STRING NOT NULL
)
PARTITION BY DATE(record_timestamp)
CLUSTER BY facility;
-- rollback DROP TABLE ${tenant_id}_oa_curated.fct_dialog_active;
