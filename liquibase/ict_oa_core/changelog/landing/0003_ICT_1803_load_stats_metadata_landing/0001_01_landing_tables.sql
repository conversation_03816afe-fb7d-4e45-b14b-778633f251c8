--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:0003-01_01
ALTER TABLE ${tenant_id}_landing.AdviceCreatedFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.AdviceCreatedFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_02
ALTER TABLE ${tenant_id}_landing.AdviceFinishedFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.AdviceFinishedFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_03
ALTER TABLE ${tenant_id}_landing.AdviceLineBasedCreateInventoryFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.AdviceLineBasedCreateInventoryFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_04
ALTER TABLE ${tenant_id}_landing.AreaDimension
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.AreaDimension DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_05
ALTER TABLE ${tenant_id}_landing.BinUtilization
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.BinUtilization DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_06
ALTER TABLE ${tenant_id}_landing.ConnectionMovementFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.ConnectionMovementFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_07
ALTER TABLE ${tenant_id}_landing.ContainerMovementFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.ContainerMovementFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_08
ALTER TABLE ${tenant_id}_landing.FaultTextDimension
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.FaultTextDimension DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_09
ALTER TABLE ${tenant_id}_landing.FacilityOrderDimension
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.FacilityOrderDimension DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_10
ALTER TABLE ${tenant_id}_landing.FacilityOrderFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.FacilityOrderFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_11
ALTER TABLE ${tenant_id}_landing.FacilityOrderLineFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.FacilityOrderLineFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_12
ALTER TABLE ${tenant_id}_landing.GroupDimension
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.GroupDimension DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_13
ALTER TABLE `${tenant_id}_landing.HeartBeat-End`
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE `${tenant_id}_landing.HeartBeat-End` DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_14
ALTER TABLE `${tenant_id}_landing.HeartBeat-Start`
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE `${tenant_id}_landing.HeartBeat-Start` DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_15
ALTER TABLE ${tenant_id}_landing.InventoryFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.InventoryFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_16
ALTER TABLE ${tenant_id}_landing.IPointMovementFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.IPointMovementFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_17
ALTER TABLE ${tenant_id}_landing.LocationDimension
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.LocationDimension DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_18
ALTER TABLE ${tenant_id}_landing.MultishuttleMovementFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.MultishuttleMovementFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_19
ALTER TABLE ${tenant_id}_landing.NokIntervalFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.NokIntervalFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_20
ALTER TABLE ${tenant_id}_landing.PickActivityFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.PickActivityFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_21
ALTER TABLE ${tenant_id}_landing.PickFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.PickFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_22
ALTER TABLE ${tenant_id}_landing.PickOrderFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.PickOrderFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_23
ALTER TABLE ${tenant_id}_landing.PickOrderPlanningFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.PickOrderPlanningFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_24
ALTER TABLE ${tenant_id}_landing.SampleFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.SampleFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_25
ALTER TABLE ${tenant_id}_landing.SCADAFaultFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.SCADAFaultFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_26
ALTER TABLE ${tenant_id}_landing.SKUDimension
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.SKUDimension DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_27
ALTER TABLE ${tenant_id}_landing.SKUProductCodeDimension
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.SKUProductCodeDimension DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_28
ALTER TABLE ${tenant_id}_landing.SKUQuantityUnitDimension
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.SKUQuantityUnitDimension DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_29
ALTER TABLE ${tenant_id}_landing.SorterDispositionFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.SorterDispositionFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_30
ALTER TABLE ${tenant_id}_landing.SortFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.SortFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_31
ALTER TABLE ${tenant_id}_landing.StackercraneMovementFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.StackercraneMovementFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_32
ALTER TABLE ${tenant_id}_landing.StatusDimension
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.StatusDimension DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_33
ALTER TABLE ${tenant_id}_landing.TransportTaskConfirmedFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.TransportTaskConfirmedFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_34
ALTER TABLE ${tenant_id}_landing.UnknownFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.UnknownFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_35
ALTER TABLE ${tenant_id}_landing.UserDimension
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.UserDimension DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_36
ALTER TABLE ${tenant_id}_landing.VehicleMovementFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.VehicleMovementFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_37
ALTER TABLE ${tenant_id}_landing.viz_EventLogFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.viz_EventLogFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;

--changeset ${tenant_id}_oa_curated:0003-01_38
ALTER TABLE ${tenant_id}_landing.WorkstationModeFact
ADD COLUMN IF NOT EXISTS pubsub_message_id STRING,
ADD COLUMN IF NOT EXISTS message_hash STRING,
ADD COLUMN IF NOT EXISTS row_hash STRING,
ADD COLUMN IF NOT EXISTS x_cloud_trace_context STRING,
ADD COLUMN IF NOT EXISTS source_ip STRING,
ADD COLUMN IF NOT EXISTS attributes JSON,
ADD COLUMN IF NOT EXISTS metadata JSON;
-- rollback ALTER TABLE ${tenant_id}_landing.WorkstationModeFact DROP COLUMN IF NOT EXISTS pubsub_message_id, DROP COLUMN IF NOT EXISTS message_hash, DROP COLUMN IF NOT EXISTS row_hash, DROP COLUMN IF NOT EXISTS x_cloud_trace_context, DROP COLUMN IF NOT EXISTS source_ip, DROP COLUMN IF NOT EXISTS attributes, DROP COLUMN IF NOT EXISTS metadata;