--liquibase formatted sql
--changeset ${tenant_id}_landing:0005-01_01
DROP TABLE IF EXISTS ${tenant_id}_landing.TransportTaskConfirmedFact;

CREATE TABLE ${tenant_id}_landing.TransportTaskConfirmationFact (
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL,
  pubsub_message_id STRING,
  message_hash STRING,
  row_hash STRING,
  x_cloud_trace_context STRING,
  source_ip STRING,
  attributes JSON,
  metadata JSON
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant, facility, source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.TransportTaskConfirmationFact;