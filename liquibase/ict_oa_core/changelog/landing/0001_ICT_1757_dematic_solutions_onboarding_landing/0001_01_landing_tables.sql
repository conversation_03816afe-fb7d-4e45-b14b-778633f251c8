--liquibase formatted sql
--changeset ${tenant_id}_landing:0001-01_01
CREATE TABLE ${tenant_id}_landing.IPointMovementFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant, facility, source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.IPointMovementFact;


--changeset ${tenant_id}_landing:0001-01_02
CREATE TABLE ${tenant_id}_landing.FacilityOrderFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant, facility, source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.FacilityOrderFact;

--changeset ${tenant_id}_landing:0001-01_03
CREATE TABLE ${tenant_id}_landing.FacilityOrderLineFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant, facility, source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.FacilityOrderLineFact;

--changeset ${tenant_id}_landing:0001-01_04
CREATE TABLE ${tenant_id}_landing.FacilityOrderDimension
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant, facility, source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.FacilityOrderDimension;
