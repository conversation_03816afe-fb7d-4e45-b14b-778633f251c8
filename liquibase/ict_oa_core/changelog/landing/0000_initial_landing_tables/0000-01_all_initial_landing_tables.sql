--liquibase formatted sql
--changeset ${tenant_id}_landing:0000-01_01
CREATE TABLE ${tenant_id}_landing.AreaDimension
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.AreaDimension;

--changeset ${tenant_id}_landing:0000-01_02
CREATE TABLE ${tenant_id}_landing.ConnectionMovementFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.ConnectionMovementFact;

--changeset ${tenant_id}_landing:0000-01_03
CREATE TABLE ${tenant_id}_landing.ContainerMovementFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.ContainerMovementFact;

--changeset ${tenant_id}_landing:0000-01_04
CREATE TABLE ${tenant_id}_landing.FaultTextDimension
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.FaultTextDimension;

--changeset ${tenant_id}_landing:0000-01_05
CREATE TABLE ${tenant_id}_landing.GroupDimension
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.GroupDimension;

--changeset ${tenant_id}_landing:0000-01_06
CREATE TABLE `${tenant_id}_landing.HeartBeat-End`
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE `${tenant_id}_landing.HeartBeat-End`;

--changeset ${tenant_id}_landing:0000-01_07
CREATE TABLE `${tenant_id}_landing.HeartBeat-Start`
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE `${tenant_id}_landing.HeartBeat-Start`;

--changeset ${tenant_id}_landing:0000-01_08
CREATE TABLE ${tenant_id}_landing.InventoryFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.InventoryFact;

--changeset ${tenant_id}_landing:0000-01_09
CREATE TABLE ${tenant_id}_landing.LocationDimension
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.LocationDimension;

--changeset ${tenant_id}_landing:0000-01_10
CREATE TABLE ${tenant_id}_landing.MultishuttleMovementFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.MultishuttleMovementFact;

--changeset ${tenant_id}_landing:0000-01_11
CREATE TABLE ${tenant_id}_landing.NokIntervalFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.NokIntervalFact;

--changeset ${tenant_id}_landing:0000-01_12
CREATE TABLE ${tenant_id}_landing.PickActivityFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.PickActivityFact;

--changeset ${tenant_id}_landing:0000-01_13
CREATE TABLE ${tenant_id}_landing.PickFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.PickFact;

--changeset ${tenant_id}_landing:0000-01_14
CREATE TABLE ${tenant_id}_landing.PickOrderFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.PickOrderFact;

--changeset ${tenant_id}_landing:0000-01_15
CREATE TABLE ${tenant_id}_landing.PickOrderPlanningFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.PickOrderPlanningFact;

--changeset ${tenant_id}_landing:0000-01_16
CREATE TABLE ${tenant_id}_landing.SampleFact 
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.SampleFact;

--changeset ${tenant_id}_landing:0000-01_17
CREATE TABLE ${tenant_id}_landing.SCADAFaultFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.SCADAFaultFact;

--changeset ${tenant_id}_landing:0000-01_18
CREATE TABLE ${tenant_id}_landing.SKUDimension
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.SKUDimension;

--changeset ${tenant_id}_landing:0000-01_19
CREATE TABLE ${tenant_id}_landing.SKUProductCodeDimension
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.SKUProductCodeDimension;

--changeset ${tenant_id}_landing:0000-01_20
CREATE TABLE ${tenant_id}_landing.SKUQuantityUnitDimension
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
-- rollback DROP TABLE ${tenant_id}_landing.SKUQuantityUnitDimension;

--changeset ${tenant_id}_landing:0000-01_21
CREATE TABLE ${tenant_id}_landing.SorterDispositionFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.SorterDispositionFact;

--changeset ${tenant_id}_landing:0000-01_22
CREATE TABLE ${tenant_id}_landing.SortFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.SortFact;

--changeset ${tenant_id}_landing:0000-01_23
CREATE TABLE ${tenant_id}_landing.StackercraneMovementFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.StackercraneMovementFact;
--changeset ${tenant_id}_landing:0000-01_24
CREATE TABLE ${tenant_id}_landing.StatusDimension
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.StatusDimension;

--changeset ${tenant_id}_landing:0000-01_25
CREATE TABLE ${tenant_id}_landing.UnknownFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  fact_type STRING,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.UnknownFact;

--changeset ${tenant_id}_landing:0000-01_26
CREATE TABLE ${tenant_id}_landing.UserDimension
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.UserDimension;

--changeset ${tenant_id}_landing:0000-01_27
CREATE TABLE ${tenant_id}_landing.VehicleMovementFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.VehicleMovementFact;

--changeset ${tenant_id}_landing:0000-01_28
CREATE TABLE ${tenant_id}_landing.viz_EventLogFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.viz_EventLogFact;

--changeset ${tenant_id}_landing:0000-01_29
CREATE TABLE ${tenant_id}_landing.WorkstationModeFact 
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.WorkstationModeFact;