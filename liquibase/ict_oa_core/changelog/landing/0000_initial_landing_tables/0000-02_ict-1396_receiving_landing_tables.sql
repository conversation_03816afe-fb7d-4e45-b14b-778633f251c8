--liquibase formatted sql
--changeset ${tenant_id}_landing:0000-02_01
CREATE TABLE ${tenant_id}_landing.AdviceCreatedFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.AdviceCreatedFact;

--changeset ${tenant_id}_landing:0000-02_02
CREATE TABLE ${tenant_id}_landing.AdviceFinishedFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.AdviceFinishedFact;

--changeset ${tenant_id}_landing:0000-02_03
CREATE TABLE ${tenant_id}_landing.AdviceLineBasedCreateInventoryFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.AdviceLineBasedCreateInventoryFact;

--changeset ${tenant_id}_landing:0000-02_04
CREATE TABLE ${tenant_id}_landing.BinUtilization
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.BinUtilization;

--changeset ${tenant_id}_landing:0000-02_05
CREATE TABLE ${tenant_id}_landing.TransportTaskConfirmedFact
(
  tenant STRING NOT NULL,
  facility STRING NOT NULL,
  source_system STRING NOT NULL,
  data JSON  NOT NULL,
  ingestion_date TIMESTAMP NOT NULL
)
PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
CLUSTER BY tenant,facility,source_system
;
-- rollback DROP TABLE ${tenant_id}_landing.TransportTaskConfirmedFact;