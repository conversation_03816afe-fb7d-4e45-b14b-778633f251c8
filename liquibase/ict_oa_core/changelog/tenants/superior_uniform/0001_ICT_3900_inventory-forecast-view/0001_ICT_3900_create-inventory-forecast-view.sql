--liquibase formatted sql
--changeset ${tenant_id}_oa_curated:ICT-3900-0001-01_01

CREATE OR REPLACE VIEW `superior_uniform_oa_curated.forecasting_data_view` AS SELECT * FROM 


(
--- Get the data from the last time the inference was run
WITH lastRun AS (
  SELECT 
    DATE(TIMESTAMP_TRUNC(PARSE_TIMESTAMP('%Y-%m-%dT%H:%M:%S%Ez', inference_datetime), DAY)) AS run_day,
    sku, 
    t_plus_1_quantity_demand AS demand_tomorrow,
    CASE 
      WHEN t_plus_1_quantity_demand IS NOT NULL 
        AND t_plus_1_quantity_demand > 0 
        AND known_quantity_demand IS NOT NULL 
      THEN (known_quantity_demand/t_plus_1_quantity_demand) 
      ELSE NULL
    END AS known_demand, 
    t_plus_2_quantity_demand AS two_day_demand, 
  FROM `${edp_project_id}.superior_uniform.silver_sku_features_inference` 



  WHERE TIMESTAMP_TRUNC(__raw_message_ingestion_time, DAY) = (
    SELECT MAX(TIMESTAMP_TRUNC(__raw_message_ingestion_time, DAY)) 
    FROM `${edp_project_id}.superior_uniform.silver_sku_features_inference`



) ), 
--- Only display the inference data for the day the inference was run, else hide it.
--- SU is the only tenant using this feature at the moment - possibly move to a config in the future.
forecastingData AS (
  SELECT
    sku,
    CASE WHEN CAST(run_day AS DATE) = (CURRENT_DATE('America/Chicago')) THEN COALESCE(demand_tomorrow, 0) ELSE NULL END AS demand_tomorrow,
    CASE WHEN CAST(run_day AS DATE) = (CURRENT_DATE('America/Chicago')) THEN COALESCE(known_demand, 0) ELSE NULL END AS known_demand,
    CASE WHEN CAST(run_day AS DATE) = (CURRENT_DATE('America/Chicago')) THEN COALESCE(two_day_demand,0) ELSE NULL END AS two_day_demand,
    CASE WHEN CAST(run_day AS DATE) = (CURRENT_DATE('America/Chicago')) THEN true ELSE false END AS show_data
  FROM lastRun
  ORDER BY sku
),
--- Get the last recorded sku data for each container
skuData AS (
  SELECT 
    gwi.sku,
    sku_style, 
    sku_size,
    location_id, 
    container_id, 
    quantity, 
    zone, 
    ROW_NUMBER() OVER (PARTITION BY container_id ORDER BY last_activity_date_local DESC) AS row_num 
FROM `${edp_project_id}.superior_uniform.gold_wms_inventory` gwi 


  JOIN forecastingData 
  ON gwi.sku = forecastingData.sku WHERE quantity > 0
),
--- Sum the quantity by grouped zones - configs to pass in zone areas (reserve vs forward) handled in API call
availableQuantityData AS (
  SELECT 
    sku,
    sku_style, 
    sku_size,
    COALESCE(SUM(quantity),0) AS zone_quantity,
    zone, 
  FROM skuData 
  WHERE row_num = 1
  GROUP BY sku, sku_style, sku_size, zone
),


allOrders AS (
  SELECT 
    order_code,
    state_code,
    sts_code, 
    ship_date_timestamp_utc,
    short_flag,
    __raw_message_ingestion_time,
    ROW_NUMBER() OVER (PARTITION BY order_code ORDER BY __raw_message_ingestion_time DESC) AS row_num
  FROM `${edp_project_id}.superior_uniform.silver_wms_customer_order`


-- Most orders should be within a few days but there are occasional stragglers hence the 4 week time window
  
  WHERE TIMESTAMP_TRUNC(__raw_message_ingestion_time, DAY) > TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 28 DAY)
),
-- From the most recent activity on the order, only pickup orders that are allocated (3) and open (0)
orders AS (
  SELECT * 
  FROM allOrders 
  WHERE row_num = 1 AND state_code = '3' AND sts_code = '0' AND ship_date_timestamp_utc IS NULL AND short_flag = '0' 
  ORDER BY __raw_message_ingestion_time
),
-- Using the smaller orders subset, only pickup most recent order lines activity
orderDetails AS (
  SELECT DISTINCT 
    order_id_line, 
    order_key, 
    order_line, 
    qty_allocated, 
    sku, 
    storer_id, 
    state, 
    sts,
    ROW_NUMBER() OVER (PARTITION BY order_key, order_line ORDER BY od.__raw_message_ingestion_time DESC) AS detail_row_num
  FROM `${edp_project_id}.superior_uniform.silver_wms_customer_order_detail` od



  JOIN orders o ON od.order_key = o.order_code
  -- Most orders should be within a few days but there are occasional stragglers hence the 4 week time window
  WHERE TIMESTAMP_TRUNC(od.__raw_message_ingestion_time, DAY) > TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 28 DAY) 
  AND od.qty_ordered > 0
),
-- And add 'em if the status is open (0)
outstandingDemand AS (
  SELECT 
    COALESCE(SUM(qty_allocated), 0) AS outstanding_demand, 
    sku, 
    storer_id 
  FROM orderDetails
  WHERE detail_row_num = 1
  AND sts = '0'
  GROUP BY sku, storer_id
),
-- Pending actions are sent in time chunks, only concerned here with the most recent chunk
pendingReplenishment AS (
  SELECT 
    style_code, 
    style_size_code, 
    -- This value should sum the quantity of outstanding replenishment tasks for an item 
    CASE WHEN task_type_code = 'PendReplenish' THEN COALESCE(SUM(total_quantity_amount),0) END AS pending_replenishment, 
    CASE WHEN task_type_code = 'PendPicks' THEN COALESCE(SUM(total_quantity_amount),0) END AS pending_picks 
  FROM `${edp_project_id}.superior_uniform.gold_wms_order_replenishment` 



  WHERE query_date_time_local = (
    SELECT MAX(query_date_time_local) AS latestSnapshot 
    FROM `${edp_project_id}.superior_uniform.gold_wms_order_replenishment`) 



GROUP BY style_code, style_size_code, task_type_code), 
-- Because pending actions are sent in time chunks, repeats send happen throughout the day until it's completed. For avg 30 day history,
-- get the distinct records included task_id (added 10/15/24) to identify unique repleneshment requests
repleneshmentHistory AS (
  SELECT DISTINCT 
    TIMESTAMP_TRUNC(query_date_time_local, DAY) AS day, 
    style_code, 
    style_size_code, 
    total_quantity_amount, 
    task_id 
  FROM `${edp_project_id}.superior_uniform.gold_wms_order_replenishment` 



  WHERE DATETIME_TRUNC(query_date_time_local, DAY) > DATETIME_SUB(DATETIME_TRUNC(CURRENT_DATETIME('America/Chicago'), DAY), INTERVAL 30 DAY) 
  AND task_type_code = 'PendReplenish' 
  AND total_quantity_amount IS NOT NULL),
  
-- Divide total repleneshments by amount of days that repleneshment requests occured,
avgRepleneshment AS (
  SELECT 
    style_code, 
    style_size_code, 
    SUM(total_quantity_amount)/(SELECT COUNT(DISTINCT day) FROM repleneshmentHistory) AS avg_repleneshment_quantity 
  FROM repleneshmentHistory 
  GROUP BY style_code, style_size_code
),
-- Using picks completed, determine avg daily demand per sku 
-- over the amount of days where a pick was completed within the last 30 days,
productDemandHistory AS (
  SELECT SUM(picked_qty) AS daily_picked_qty, 
  DATETIME_TRUNC(event_date_time_local, DAY) AS day,
  item_sku
FROM `${edp_project_id}.superior_uniform.gold_pick` 
  WHERE DATETIME_TRUNC(event_date_time_local, DAY) > DATETIME_SUB(DATETIME_TRUNC(CURRENT_DATETIME('America/Chicago'), DAY), INTERVAL 30 DAY)
  AND work_type_code IN ('PICK', 'PICKSNBX', 'SNBX')
  GROUP BY item_sku, day
),
avgDemand AS (
  SELECT 
    item_sku,
    SUM(daily_picked_qty)/
      (SELECT COUNT(DISTINCT(day)) 
      FROM productDemandHistory) AS avg_demand_quantity 
  FROM productDemandHistory 
  GROUP BY item_sku)
-- Insert into the view so the API request can handle the remaining logic with the area configs.
SELECT 
    forecastingData.sku, 
    COALESCE(zone_quantity, 0) AS zone_quantity, 
    zone,
    COALESCE(pending_replenishment, 0) AS pending_repleneshment, 
    COALESCE(pending_picks, 0) AS pending_picks, 
    (COALESCE(outstanding_demand,0) + COALESCE(pending_picks,0)) AS outstanding_demand,
    COALESCE(avg_repleneshment_quantity, 0) AS average_repleneshment,
    COALESCE(avg_demand_quantity, 0) AS average_demand,
    forecastingData.demand_tomorrow,
    forecastingData.known_demand,
    forecastingData.two_day_demand,
    show_data,
FROM forecastingData 
  LEFT JOIN availableQuantityData 
  ON forecastingData.sku = availableQuantityData.sku 
  LEFT JOIN outstandingDemand 
  ON outstandingDemand.sku = availableQuantityData.sku_style 
    AND outstandingDemand.storer_id = availableQuantityData.sku_size 
  LEFT JOIN pendingReplenishment 
  ON pendingReplenishment.style_code = availableQuantityData.sku_style 
    AND pendingReplenishment.style_size_code = availableQuantityData.sku_size
  LEFT JOIN avgRepleneshment
  ON avgRepleneshment.style_code = availableQuantityData.sku_style 
    AND avgRepleneshment.style_size_code = availableQuantityData.sku_size
  LEFT JOIN avgDemand 
  ON avgDemand.item_sku = forecastingData.sku )

-- rollback DROP VIEW superior_uniform_oa_curated.forecasting_data_view;