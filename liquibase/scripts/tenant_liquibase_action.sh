#!/bin/bash
set -e

# Common functions
update_default_dataset() {
    local properties_file="./liquibase/ict_oa_core/test.liquibase.properties"
    local new_dataset="test_${MR_IID}_liquibase_changelogs"
    sed -i "s/DefaultDataset=[^;]*/DefaultDataset=$new_dataset/" "$properties_file"
    echo "Updated DefaultDataset to $new_dataset in $properties_file"
}

show_help() {
    cat <<EOF
Usage: $0 <tf_config_file> <action> [mr_iid]

This script is used to run liquibase actions for a tenant

Arguments:
  <tf_config_file>: Terraform config file
  <action>: Action to run [update/status/version/validate/clear-checksums]
  [mr_iid]: Optional Merge Request IID (if provided, updates test.liquibase.properties)

Actions:
  update: Run liquibase update for a tenant
  status: Get liquibase status for a tenant
  version: Get liquibase version
  clear-checksums: Clear liquibase checksums for a tenant

Examples:
  $0 dev.tfvars update
  $0 dev.tfvars status
  $0 stage.tfvars status
  $0 stage.tfvars clear-checksums
  $0 test.tfvars version
EOF
}

# Main script
TF_CONFIG_FILE=$1
ACTION=$2
MR_IID=$3

# Validate input
[[ "$1" == "-h" || "$1" == "--help" || "$1" == "help" || "$#" -lt 2 || "$#" -gt 3 ]] && {
    show_help
    exit 1
}
[[ -z "$ACTION" ]] && {
    echo "ACTION is not set"
    exit 1
}
[[ -z "$TF_CONFIG_FILE" ]] && {
    echo "TF_CONFIG_FILE is not set"
    exit 1
}

# Construct full path to TF_CONFIG_FILE
TF_CONFIG_FILE="./infrastructure/Configs/$TF_CONFIG_FILE"
[[ ! -f "$TF_CONFIG_FILE" ]] && {
    echo "Error: Config file not found: $TF_CONFIG_FILE"
    exit 1
}

# Extract tenants
TENANTS=($(grep "^[[:space:]]*dataset" "$TF_CONFIG_FILE" | awk '{print $3}' ORS=' ' | tr -d '"'))
edp_project_id=($(grep "^[[:space:]]*edp_project_id" "$TF_CONFIG_FILE" | awk '{print $3}' ORS=' ' | tr -d '"'))

if [[ -z "$TENANTS" ]]; then
    cat <<EOF
No tenants found in $TF_CONFIG_FILE
-----------------------------------
Please add tenants in $TF_CONFIG_FILE in the tenants block.
Example: tenants = [
  {
    name = "tenant1"
  },
  {
    name = "tenant2"
  }
]
-----------------------------------
EOF
    exit 1
fi

# Display configuration
cat <<EOF
-----------------------------------
Liquibase Action: $ACTION
TF Config File: $TF_CONFIG_FILE
Tenants: ${TENANTS[@]}
Environment: ${ENVIRONMENT}
EDP Project: ${edp_project_id}
EOF

[[ -n "$MR_IID" ]] && {
    echo "MR IID: $MR_IID"
    update_default_dataset
}
echo "-----------------------------------"

# Liquibase action functions
clear-checksums() {
    echo "Clearing checksums"
    liquibase --defaults-file="./liquibase/ict_oa_core/${ENVIRONMENT}.liquibase.properties" \
        --changeLogFile="./liquibase/ict_oa_core/changelog/changelog-master.yaml" \
        --logLevel=INFO \
        --search-path="./liquibase/,./,./liquibase/ict_oa_core/,./liquibase/ict_oa_core/changelog/,./liquibase/ict_oa_core/changelog/" \
        clear-checksums
}

status() {
    for tenant_id in "${TENANTS[@]}"; do
        echo "Checking ${tenant_id}"
        liquibase --defaults-file="./liquibase/ict_oa_core/${ENVIRONMENT}.liquibase.properties" \
            --changeLogFile="./liquibase/ict_oa_core/changelog/changelog-master.yaml" \
            --logLevel=INFO \
            --search-path="./liquibase/,./,./liquibase/ict_oa_core/,./liquibase/ict_oa_core/changelog/,./liquibase/ict_oa_core/changelog/" \
            status --verbose \
            -Dtenant_id=${tenant_id} \
            -Dedp_project_id=${edp_project_id}
    done
}

validate() {
    for tenant_id in "${TENANTS[@]}"; do
        echo "Checking ${tenant_id}"
        liquibase --defaults-file="./liquibase/ict_oa_core/${ENVIRONMENT}.liquibase.properties" \
            --changeLogFile="./liquibase/ict_oa_core/changelog/changelog-master.yaml" \
            --logLevel=INFO \
            --search-path="./liquibase/,./,./liquibase/ict_oa_core/,./liquibase/ict_oa_core/changelog/,./liquibase/ict_oa_core/changelog/" \
            validate \
            -Dtenant_id=${tenant_id} \
            -Dedp_project_id=${edp_project_id}
    done
}

version() {
    echo "Checking version"
    liquibase --version --search-path="./liquibase/,./,./liquibase/ict_oa_core/,./liquibase/ict_oa_core/changelog/,./liquibase/ict_oa_core/changelog/"
}

update() {
    for tenant_id in "${TENANTS[@]}"; do
        echo "Updating ${tenant_id}"
        liquibase --defaults-file="./liquibase/ict_oa_core/${ENVIRONMENT}.liquibase.properties" \
            --changeLogFile="./liquibase/ict_oa_core/changelog/changelog-master.yaml" \
            --logLevel=INFO \
            --search-path="./liquibase/,./,./liquibase/ict_oa_core/,./liquibase/ict_oa_core/changelog/,./liquibase/ict_oa_core/changelog/" \
            update \
            -Dtenant_id=${tenant_id} \
            -Dedp_project_id=${edp_project_id}
    done
}

# Execute action
case $ACTION in
update | status | version | validate | clear-checksums) $ACTION ;;
*)
    echo "Invalid action: $ACTION"
    show_help
    exit 1
    ;;
esac
