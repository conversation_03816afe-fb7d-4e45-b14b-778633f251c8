--liquibase formatted sql
--changeset ict:0001-22

CREATE TABLE api.fct_multishuttle_movement (
    transportRequestId STRING,
    transportReason STRUCT<
                value STRING,
                type STRING
                >,
    subsystemId STRUCT<
                value STRING,
                type STRING
                >,
    skuId STRING,
    shiftPrimaryKey STRING,
    shiftPlan STRING,
    productRangeId STRING,
    primaryKeyAsLong INT,
    primaryKey STRING,
    nokIntervalFactUuIds ARRAY<STRING>,
    shuttleId STRING,
    nokCounter INT,
    modDate TIMESTAMP,
    modUser STRUCT<
                value STRING,
                type STRING
                >,
    leaveTime TIMESTAMP,
    loadUnitId STRUCT<
                value STRING,
                type STRING
                >,
    liftId STRING,
    movementType STRUCT<
                value STRING,
                type STRING
                >,
    sourceLocationId STRING,
    eventTime TIMESTAMP,
    level STRING, 
    cacheKey STRING,
    endTime TIMESTAMP,
    destinationLocationId STRING,
    eventCode STRUCT<
                value STRING,
                type STRING
                >,
    duration INT,
    hardwareIdentifier STRING,
    createUser STRUCT<
                value STRING,
                type STRING
                >,
    deltax INT,
    deltaAisle INT,
    startTime TIMESTAMP,
    clientId STRING,
    createDate TIMESTAMP,
    tenantName STRING,
    aisle STRING,
    arriveTime TIMESTAMP,
    causingRetrievals STRING,
    appSystemId STRING
)

-- rollback DROP TABLE fct_multishuttle_movement;