--liquibase formatted sql
--changeset ict:0001-29

CREATE VIEW api.demo01_internal_tf_areas_status AS
SELECT 
    Area.id, Area.name, AreaMapLink.mapId,
    AreaStatus.status, AreaStatus.alert, AreaStatus.active_operators, AreaStatus.low_rec_operators, AreaStatus.high_rec_operators, AreaStatus.max_operators, AreaStatus.pick_rate, AreaStatus.queued_orders
  FROM
    api.demo01_internal_area_map_link as AreaMapLink
  INNER JOIN
    api.dim_area as Area on AreaMapLink.areaId = Area.id
  INNER JOIN
    api.demo01_internal_area_status as AreaStatus on Area.id = AreaStatus.areaId

-- rollback DROP VIEW demo01_internal_tf_areas_status;