--liquibase formatted sql
--changeset ict:0001-37

CREATE VIEW api.demo01_area_assigned_orders AS

SELECT 
    pickOrder.pickOrderPk as pickOrderPk,
    pickOrder.event as event,
    pickOrder.eventTime as eventTime,
    CAST(pickFact.quantityPicked AS INT64) as quantityPicked,
    CAST(pickFact.quantityTarget AS INT64) as quantityTarget,
    CASE 
        WHEN MOD(CAST(pickOrder.pickOrderPk AS INT64), 4) = 0 THEN 'testArea1'
        WHEN MOD(CAST(pickOrder.pickOrderPk AS INT64), 4) = 1 THEN 'testArea2'
        WHEN MOD(CAST(pickOrder.pickOrderPk AS INT64), 4) = 2 THEN 'testArea3'
        ELSE 'testArea4'
    END AS areaId,
    CASE 
        WHEN COUNT(DISTINCT pickFact.pickPk) = 0 THEN 1
        ELSE COUNT(DISTINCT pickFact.pickPk) 
    END AS linesPerOrder
FROM 
    api.fct_pick_order AS pickOrder
INNER JOIN 
    api.fct_pick AS pickFact
ON 
    pickOrder.pickOrderPk = pickFact.pickOrderPk
GROUP BY 
    pickOrder.pickOrderPk, pickOrder.event, pickOrder.eventTime,pickOrder.eventTime,pickFact.quantityPicked, pickFact.quantityTarget

-- rollback DROP VIEW demo01_area_assigned_orders;