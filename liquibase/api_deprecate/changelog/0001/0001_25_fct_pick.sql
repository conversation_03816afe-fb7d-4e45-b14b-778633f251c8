--liquibase formatted sql
--changeset ict:0001-25

CREATE TABLE api.fct_pick (
    zonePk STRING,
    tenantName STRING,
    skuPk INT,
    workflowPk STRING,
    quantityRemaining STRING,
    primaryKey INT,
    quantityPicked INT,
    pickOrderPk STRING,
    pickBatchPk STRING,
    workstationStrategy STRING,
    orderPk STRING,
    operatorPickTime INT,
    pickerId INT,
    modUser STRUCT<
                value STRING,
                type STRING
                >,
    loadUnitId STRING,
    technology STRING,
    locationPk STRING,
    confirmationReason STRING,
    hostLineId STRING,
    createUser STRUCT<
                value STRING,
                type STRING
                >,
    processType STRING,
    cartPk STRING,
    createDate TIMESTAMP,
    eventTime TIMESTAMP,
    containerPk STRING,
    modDate TIMESTAMP,
    quantityUnitPk STRING,
    loadUnitType STRING,
    sourceLoadUnitType STRING,
    client STRING,
    inductionZonePk STRING,
    workstationId STRING,
    confirmationCode STRING,
    primaryKeyAsLong INT,
    cartId STRING,
    sourceLoadUnit STRING,
    quantityTarget INT,
    pickPk INT,
    terminalId STRING,
    appSystemId STRING
);

-- rollback DROP TABLE fct_pick;