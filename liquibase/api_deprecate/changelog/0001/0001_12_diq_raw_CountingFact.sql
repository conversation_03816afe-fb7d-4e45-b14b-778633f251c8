--liquibase formatted sql
--changeset ict:0001-12

CREATE TABLE diq_raw_anonymized.CountingFact (
    userPk INT,
    workstationId STRING,
    primaryKeyAsLong INT,
    primaryKey STRING,
    modUser STRUCT<
                value INT,
                type STRING
                >,
    loadUnitId INT,
    createUser STRUCT<
                value INT,
                type STRING
                >,
    expectedQuantity INT,
    tenantName STRING,
    createDate TIMESTAMP,
    skuId STRING,
    countedQuantity INT,
    eventTime TIMESTAMP,
    invalid BOOLEAN,
    countingErrorCode STRING,
    bbd DATE,
    batch STRING,
    modDate TIMESTAMP,
    countingTimeStamp TIMESTAMP,
    stocktakingExecutionType STRING,
    clientId STRING,
    appSystemId STRING
);

-- rollback DROP TABLE diq_raw_anonymized.CountingFact;