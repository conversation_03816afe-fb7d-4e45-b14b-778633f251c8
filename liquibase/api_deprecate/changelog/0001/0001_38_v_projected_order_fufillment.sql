--liquibase formatted sql
--changeset ict:0001-38

CREATE VIEW api.projected_order_fulfillment AS
WITH total_pick_target AS (
      SELECT
        skuPk,
        CAST(skuPk AS STRING) AS sku_str,
        SUM(quantityTarget) AS total_pick_target,
        eventTime
      FROM
        api.fct_pick
      GROUP BY
        skuPk, sku_str, eventTime
    ),
ranked_inventory AS (
    SELECT
      skuPk,
      locationId,
      eventTime,
      quantityFree,
      REGEXP_EXTRACT(skuPk, r'\d+$') AS sku_num,
      ROW_NUMBER() OVER (PARTITION BY locationId ORDER BY eventTime DESC) AS rn
    FROM
      api.fct_inventory
  ),
latest_inventory AS (
    SELECT
      skuPk,
      sku_num,
      SUM(quantityFree) AS total_quantity_free
    FROM
      ranked_inventory
    WHERE
      rn = 1
    GROUP BY
      skuPk, sku_num
  ),
fulfillment_status AS (
      SELECT
        total_pick_target.total_pick_target,
        latest_inventory.total_quantity_free,
        eventTime,
        CASE
          WHEN total_pick_target.total_pick_target <= latest_inventory.total_quantity_free THEN 1
          ELSE 0
        END AS fullfilment_chance
      FROM
        total_pick_target
      LEFT JOIN
        latest_inventory AS latest_inventory
      ON
        total_pick_target.sku_str = latest_inventory.sku_num
    )
    SELECT
      *
    FROM
      fulfillment_status

-- rollback DROP VIEW projected_order_fulfillment;