--liquibase formatted sql
--changeset ict:0001-03

CREATE TABLE api.fct_event_log(
    Message STRING,
    ConditionActive BOOLEAN,
    Acked BOOLEAN,
    ConditionName STRING,
    EventTime TIMESTAMP,
    _TimeUTC TIMESTAMP,
    ActiveTime TIMESTAMP,
    EventCategory INT,
    XY_CMD INT,
    LZ_CMD INT,
    XY_POS INT,
    AckComment BOOLEAN,
    TUID1 STRING,
    FLT_DESC STRING,
    Severity INT,
    Node STRING,
    FLT_CODE STRING,
    ActorID STRING,
    OBJ_NAME STRING,
    EventType STRING,
    LZ_POS INT,
    Area STRING,
    TUID2 STRING,
    CV BOOLEAN,
    Tag STRING,
    ms INT,
    Quality STRING,
    Info STRING,
    ChangeMask INT
)

-- rollback DROP TABLE fct_event_log;