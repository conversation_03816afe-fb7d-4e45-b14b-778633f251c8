--liquibase formatted sql
--changeset ict:0001-23

CREATE VIEW api.demo01_loadUnitClassification AS
WITH LoadUnitClassification AS (
    SELECT DISTINCT 
        l.loadUnitId,
        l.skuPk,
        l.locationId,
        l.timestamp,
        l.quantity,
        -- Check if the loadUnitId from InventoryFact exists in MultishuttleMovementFact.
        -- If it does, classify it as 'Automated', otherwise 'Manual'.
        CASE 
            WHEN m.loadUnitId.value IS NOT NULL THEN "Automated"
            ELSE "Manual"
        END AS type
    FROM api.fct_inventory l
    -- Left join ensures we keep all records from InventoryFact, 
    -- even if they don't have a match in MultishuttleMovementFact.
    LEFT JOIN api.fct_multishuttle_movement m 
    ON CAST(l.loadUnitId AS STRING) = m.loadUnitId.value
)

-- Select the necessary fields from the CTE to be included in the view.
SELECT skuPk, locationId, timestamp, quantity, type
FROM LoadUnitClassification

-- rollback DROP VIEW demo01_loadUnitClassification;