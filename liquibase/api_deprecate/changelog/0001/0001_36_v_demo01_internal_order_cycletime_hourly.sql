--liquibase formatted sql
--changeset ict:0001-36

CREATE VIEW api.demo01_internal_order_cycletime_hourly AS

WITH
  startTimes AS (
    SELECT
      pickOrderPk,
      TIMESTAMP(eventTime) AS startTime
    FROM api.fct_pick_order
    WHERE event = "ACTIVE"
  ),
  endTimes AS (
    SELECT
      pickOrderPk,
      TIMESTAMP(eventTime) AS endTime
    FROM api.fct_pick_order
    WHERE event = "COMPLETE"
  ),
  startEndTimes AS (
    SELECT
      DISTINCT(st.pickOrderPk),
      endTime,
      TIMESTAMP_DIFF(endTime, startTime, SECOND) AS cycletime_seconds
    FROM startTimes st
    JOIN endTimes et
    ON st.pickOrderPk = et.pickOrderPk
  )
SELECT
  TIMESTAMP_TRUNC(endTime, HOUR) AS event_hour,
  ROUND(AVG(cycletime_seconds)) AS average_cycletime_seconds,
FROM startEndTimes
GROUP BY event_hour

-- rollback DROP VIEW demo01_internal_order_cycletime_hourly;