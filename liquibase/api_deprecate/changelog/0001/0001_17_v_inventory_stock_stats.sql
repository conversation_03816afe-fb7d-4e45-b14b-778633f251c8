--liquibase formatted sql
--changeset ict:0001-17

CREATE VIEW api.inventory_stock_stats AS (
with ExpectedQuantity as ( -- create a CTE with SKUs and their expected quantites, ordering them by the Fact's timestamp descending
    SELECT
      Counting.skuId,
      Counting.countedQuantity,
      Counting.expectedQuantity,
      Counting.countingTimeStamp
    FROM api.fct_counting Counting
    ORDER BY Counting.countingTimeStamp DESC
  ),
  LatestQuantities as ( -- join the two above SKU CTEs, only selecting the latest rows based on their timestamps
    SELECT 
      ExpQuant.skuId,
      ARRAY_AGG(
        ExpQuant.countedQuantity ORDER BY ExpQuant.countingTimeStamp DESC
      )[SAFE_OFFSET(0)] as quantity,
      ARRAY_AGG(
        ExpQuant.expectedQuantity ORDER BY ExpQuant.countingTimeStamp DESC
      )[SAFE_OFFSET(0)] as expectedQuantity
    FROM ExpectedQuantity ExpQuant
    GROUP BY ExpQuant.skuId
  )
  SELECT
  ( -- get the count of latest quantity rows with quantites above the expected value as OverStock
    SELECT COUNT(*) FROM LatestQuantities LQ
    WHERE LQ.quantity > LQ.expectedQuantity
  ) as overStockCount,
  ( -- get the count of latest quantity rows with quantites equal to the expected value as AtStock
    SELECT COUNT(*) FROM LatestQuantities LQ
    WHERE LQ.quantity = LQ.expectedQuantity
  ) as atStockCount,
  ( -- get the count of latest quantity rows with quantites below the expected value but still above 0 as UnderStock
    SELECT COUNT(*) FROM LatestQuantities LQ
    WHERE LQ.quantity < LQ.expectedQuantity AND LQ.quantity > 0
  ) as underStockCount,
  ( -- get the count of latest quantity rows with quantites at or below (error data) 0 as OutOfStock
    SELECT COUNT(*) FROM LatestQuantities LQ
    WHERE LQ.quantity <= 0
  ) as outOfStockCount,
  ( -- get the total count of the quantity rows to be able to calculate percentages
    SELECT COUNT(*) FROM LatestQuantities LQ
  ) as totalCount
);

-- rollback DROP VIEW inventory_stock_stats;