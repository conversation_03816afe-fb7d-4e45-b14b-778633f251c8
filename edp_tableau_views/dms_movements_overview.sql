SELECT
  TIMESTAMP_TRUNC(TIMESTAMP(movement_start_date_time_local), HOUR) as Date_Hour,
  TIMESTAMP(movement_start_date_time_local) as Record_Timestamp,
  IFNULL(movement_type_code, 'No Code Provided') as Movement_Type_Code,
  CASE
    WHEN REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]') AND SUBSTRING(source_location_code, 11, 2) = "SH" 
      THEN "Shuttle"
    WHEN REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]') AND SUBSTRING(source_location_code, 11, 2) = "LO"
      THEN "Lift"
    WHEN REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]') AND SUBSTRING(source_location_code, 11, 2) = "DS"
      THEN "Drop Station"
    WHEN REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]') AND SUBSTRING(source_location_code, 11, 2) = "PS"
      THEN "Pick Station"
    WHEN REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]') AND SUBSTRING(source_location_code, 11, 2) = "RI"
      THEN "Rack Inbound"
    WHEN REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]') AND SUBSTRING(source_location_code, 11, 2) = "RO"
      THEN "Rack Outbound"
    WHEN REGEXP_CONTAINS(source_location_code, r'MS[0-9][0-9]')
      THEN "Storage"
    ELSE "Other"
  END as Source_Location_Type,
  CASE
    WHEN REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
      THEN SUBSTRING(source_location_code, 5, 2)
    WHEN REGEXP_CONTAINS(source_location_code, r'MS[0-9][0-9]')
      THEN SUBSTRING(source_location_code, 3, 2)
    ELSE "N/A"
  END as Source_Location_Aisle,
  -- level_code column is all NULL, so we get the level from the source_location_code instead
  CASE
    WHEN REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
      THEN SUBSTRING(source_location_code, 9, 2)
    ELSE "N/A"
  END as Source_Location_Level,
  CASE
    WHEN 
      REGEXP_CONTAINS(destination_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
      AND SUBSTRING(destination_location_code, 11, 2) = "SH" 
        THEN "Shuttle"
    WHEN 
      REGEXP_CONTAINS(destination_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
      AND SUBSTRING(destination_location_code, 11, 2) = "LO"
        THEN "Lift"
    WHEN
      REGEXP_CONTAINS(destination_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
      AND SUBSTRING(destination_location_code, 11, 2) = "DS"
        THEN "Drop Station"
    WHEN
      REGEXP_CONTAINS(destination_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
      AND SUBSTRING(destination_location_code, 11, 2) = "PS"
        THEN "Pick Station"
    WHEN
      REGEXP_CONTAINS(destination_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
      AND SUBSTRING(destination_location_code, 11, 2) = "RI"
        THEN "Rack Inbound"
    WHEN
      REGEXP_CONTAINS(destination_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
      AND SUBSTRING(destination_location_code, 11, 2) = "RO"
        THEN "Rack Outbound"
    WHEN
      REGEXP_CONTAINS(destination_location_code, r'MS[0-9][0-9]')
        THEN "Storage"
    ELSE "N/A"
  END as Dest_Location_Type,
  CASE
    WHEN REGEXP_CONTAINS(destination_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
      THEN SUBSTRING(destination_location_code, 5, 2)
    WHEN REGEXP_CONTAINS(destination_location_code, r'MS[0-9][0-9]')
      THEN SUBSTRING(destination_location_code, 3, 2)
    ELSE "N/A"
  END as Dest_Location_Aisle,
  CASE
    WHEN REGEXP_CONTAINS(destination_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
      THEN SUBSTRING(destination_location_code, 9, 2)
    ELSE "N/A"
  END as Dest_Location_Level,
  handling_unit_code as Load_Unit_Code,
  item_sku as Item_SKU,
  1 as Movements
FROM
  `${edp_project_id}.${tenant_id}.gold_multishuttle_movements`
WHERE movement_status_code = "OK"