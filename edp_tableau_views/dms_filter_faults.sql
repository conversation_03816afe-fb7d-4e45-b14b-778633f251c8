WITH nok_faults AS (
  SELECT
    TIMESTAMP_TRUNC(TIMESTAMP(fault_start_date_time_local), HOUR) as Hour_Start_Time,
    TIMESTAMP_TRUNC(TIMESTAMP(fault_end_date_time_local), HOUR) as Hour_End_Time,
    fault_start_timestamp_utc as Record_Timestamp,
    fault_start_timestamp_utc as Fault_Start_Date_Time,
    SUBSTRING(equipment_code, 5, 2) as Aisle,
    SUBSTRING(equipment_code, 9, 2) as Level,
    equipment_code as Device_Code,
    CASE
      WHEN SUBSTRING(equipment_code, 11, 2) = "SH" THEN "Shuttle"
      WHEN SUBSTRING(equipment_code, 11, 2) = "LO" THEN "Lift"
      WHEN SUBSTRING(equipment_code, 11, 2) = "DS" THEN "Drop Station"
      WHEN SUBSTRING(equipment_code, 11, 2) = "PS" THEN "Pick Station"
      WHEN SUBSTRING(equipment_code, 11, 2) = "RI" THEN "Rack Inbound"
      WHEN SUBSTRING(equipment_code, 11, 2) = "RO" THEN "Rack Outbound"
      ELSE "N/A"
    END as Device_Type,
    IFNULL(fault_duration_milliseconds, 0)/1000 as Fault_Duration_Seconds,   
    CAST(status_code as STRING) as Status_Code,
    status_category_code as Status_Category_Code,
    CAST(NULL as STRING) as Status_Name, -- not sure where to get this
    item_name as Item_Name,
    sku_code as Item_SKU,
    physical_equipment_code as Container_Physical_Code,
    status_category_code as Fault_Description,
    source_system as Source_System,
    1 as Faults
  FROM
    `${edp_project_id}.${tenant_id}.gold_nok_interval`
  WHERE
    REGEXP_CONTAINS(equipment_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
  ),
  viz_faults AS (
    SELECT
      TIMESTAMP_TRUNC(TIMESTAMP(event_time_date_time_local), HOUR) as Hour_Start_Time,
      TIMESTAMP_TRUNC(TIMESTAMP_ADD(TIMESTAMP(event_time_date_time_local), INTERVAL milliseconds MILLISECOND), HOUR) as Hour_End_Time,
      event_time_timestamp_utc as Record_Timestamp,
      event_time_timestamp_utc as Fault_Start_Date_Time,
      SUBSTRING(object_name, 5, 2) as Aisle,
      SUBSTRING(object_name, 9, 2) as Level,
      object_name as Device_Code,
      CASE
        WHEN SUBSTRING(object_name, 11, 2) = "SH" THEN "Shuttle"
        WHEN SUBSTRING(object_name, 11, 2) = "LO" THEN "Lift"
        WHEN SUBSTRING(object_name, 11, 2) = "DS" THEN "Drop Station"
        WHEN SUBSTRING(object_name, 11, 2) = "PS" THEN "Pick Station"
        WHEN SUBSTRING(object_name, 11, 2) = "RI" THEN "Rack Inbound"
        WHEN SUBSTRING(object_name, 11, 2) = "RO" THEN "Rack Outbound"
        ELSE "N/A"
      END as Device_Type,
      IFNULL(milliseconds, 0)/1000 as Fault_Duration_Seconds,
      fault_code as Status_Code,
      CAST(event_category as STRING) as Status_Category_Code,
      fault_description as Status_Name,
      CAST(NULL as STRING) as Item_Name, -- not sure where to get this
      CAST(NULL as STRING) as Item_SKU, -- not sure where to get this
      CAST(NULL as STRING) as Container_Physical_Code, -- not sure where to get this
      fault_description AS Fault_Description,
      source_system as Source_System,
      1 as Faults,
    FROM
      `${edp_project_id}.${tenant_id}.gold_viz_event_log`
    WHERE
      REGEXP_CONTAINS(object_name, r'MSAI[0-9][0-9]..[0-9][0-9]')
  ),
  base_faults AS (
    SELECT
      Hour_Start_Time,
      Hour_End_Time,
      Record_Timestamp,
      Fault_Start_Date_Time,
      Aisle,
      Level,
      Device_Code,
      Device_Type,
      Fault_Duration_Seconds,
      Status_Code,
      Status_Category_Code,
      Status_Name,
      Item_Name,
      Item_SKU,
      Container_Physical_Code,
      Fault_Description,
      Source_System,
      Faults,
      CASE 
        WHEN Fault_Description LIKE '%GATE%' THEN 1 
        WHEN Fault_Description LIKE 'Maintenance Lvl Safety Barrier Tripped__' THEN 1
        WHEN Fault_Description = 'Maintenance Level Conveyor Contactor Flt' THEN 1
        WHEN Fault_Description = 'Maintenance Level Shuttle Contactor Flt'  THEN 1
        WHEN Fault_Description = 'Maintenance Level Front Emergency Stop'   THEN 1
        WHEN Fault_Description = 'Maintenance Level Rear Emergency Stop'    THEN 1
        WHEN Fault_Description = 'Maintenance Level Safety Barrier Tripped' THEN 1
        WHEN Fault_Description = 'Maintenance Level Shuttle Breaker Fault'  THEN 1
        ELSE 0 
      END as Gate,
    FROM
      (
        SELECT * FROM nok_faults
        UNION ALL
        SELECT * FROM viz_faults
      )
  ),
  remove_unneeded_codes AS (
    -- Remove unneeded faults and warnings, filters out codes in array and all codes in the 800s
    SELECT * FROM base_faults
    WHERE NOT (Status_Code IN UNNEST(["0", "5", "109", "110", "111", "112", "113", "114", "184", "137", "170", "171", "172", "173", "175", "176", "180", "191", "192", "662", "694"]) 
        OR REGEXP_CONTAINS(Status_Code, r'8[0-9][0-9]') OR Status_Code IS NULL)
  ),
  remove_short_faults AS (
    -- Remove any faults less than 15 seconds
    SELECT * FROM remove_unneeded_codes
    WHERE Fault_Duration_Seconds > 15
  ),
  remove_non_ms_faults AS (
    -- Remove faults from devices that follow Multishuttle naming, but are not MS
    SELECT * FROM remove_short_faults
    WHERE Device_Type != "N/A"
  ),
  remove_self_correcting AS (
    -- Remove any faults that can self correct (and duration <= 60 seconds)
    SELECT * FROM remove_non_ms_faults
    WHERE NOT (UPPER(Status_Name) = 'OCCUPIED WITHOUT A DESTINATION' AND Fault_Duration_Seconds <= 60)
  ),
  remove_gate_auxiliary_faults AS (
    -- Remove any fault on the same aisle/level as gate fault within 60 seconds after it ends and last less than 60 seconds
    SELECT 
      tbl1.Hour_Start_Time,
      tbl1.Hour_End_Time,
      tbl1.Record_Timestamp,
      tbl1.Fault_Start_Date_Time,
      tbl1.Aisle,
      tbl1.Level,
      tbl1.Device_Code,
      tbl1.Device_Type,
      tbl1.Fault_Duration_Seconds,
      tbl1.Status_Code,
      tbl1.Status_Category_Code,
      tbl1.Status_Name,
      tbl1.Item_Name,
      tbl1.Item_SKU,
      tbl1.Container_Physical_Code,
      tbl1.Fault_Description,
      tbl1.Source_System,
      tbl1.Faults 
    FROM remove_self_correcting tbl1
    -- Join is finding those that are to be removed
    LEFT JOIN remove_self_correcting tbl2
    ON
      LEFT(tbl2.Device_Code, 2) = LEFT(tbl1.Device_Code, 2)
      AND tbl2.Aisle = tbl1.Aisle
      AND tbl2.Source_System = tbl1.Source_System
      AND tbl2.Device_Code <> tbl1.Device_Code
      -- Ensure the "start" fault is a GATE fault
      AND tbl2.Gate = 1
      -- Started within 60 seconds of the gate fault
      AND DATE_DIFF(tbl1.Fault_Start_Date_Time, tbl2.Record_Timestamp, SECOND) <= 60
      -- Started AFTER the gate fault ended.
      AND DATE_DIFF(tbl1.Fault_Start_Date_Time, tbl2.Record_Timestamp, SECOND) >= 0
      -- Lasted <= 60 seconds
      AND tbl2.Fault_Duration_Seconds <= 60
    -- After everything is joined only keep those that didn't "join"
    WHERE tbl2.Gate IS NULL
  ),
  faults_with_row_ids AS (
    -- add row ids so that we can remove overlapping faults
    SELECT
      ROW_NUMBER() OVER (PARTITION BY Source_System ORDER BY Fault_Start_Date_Time ASC) AS RN_ID,
      Hour_Start_Time,
      Hour_End_Time,
      Record_Timestamp,
      Fault_Start_Date_Time,
      Aisle,
      Level,
      Device_Code,
      Device_Type,
      Fault_Duration_Seconds,
      Status_Code,
      Status_Category_Code,
      Status_Name,
      Item_Name,
      Item_SKU,
      Container_Physical_Code,
      Fault_Description,
      Source_System,
      Faults 
    FROM remove_gate_auxiliary_faults
  ),
  remove_aisle_level_matching AS (
    -- Remove Aisle/Level Overlappings
    -- Removes ANY occurance that started before another ended
    -- Removes ANY complete overlap occurances
    SELECT
      tbl1.Hour_Start_Time,
      tbl1.Hour_End_Time,
      tbl1.Record_Timestamp,
      tbl1.Fault_Start_Date_Time,
      tbl1.Aisle,
      tbl1.Level,
      tbl1.Device_Code,
      tbl1.Device_Type,
      tbl1.Fault_Duration_Seconds,
      tbl1.Status_Code,
      tbl1.Status_Category_Code,
      tbl1.Status_Name,
      tbl1.Item_Name,
      tbl1.Item_SKU,
      tbl1.Container_Physical_Code,
      tbl1.Fault_Description,
      tbl1.Source_System,
      tbl1.Faults 
    FROM faults_with_row_ids tbl1
    LEFT OUTER JOIN faults_with_row_ids tbl2 
    -- Same System, Same Aisle, Level, SourceSystem, but different row number ID
    ON  
      LEFT(tbl2.Device_Code, 2) = LEFT(tbl1.Device_Code, 2)
      AND tbl2.Aisle = tbl1.Aisle
      AND tbl2.Level = tbl1.Level
      AND tbl2.Source_System = tbl1.Source_System
      AND (
        -- End Overlap (tbl2 Started Before tbl1 Started, tbl2 Ended After tbl1 Started, and tbl2 Ended Before tbl1)
        (tbl2.Fault_Start_Date_Time < tbl1.Fault_Start_Date_Time AND tbl2.Record_Timestamp > tbl1.Fault_Start_Date_Time AND tbl2.Record_Timestamp <= tbl1.Record_Timestamp)
        OR
        -- Complete Overlap (tbl2 Started Before and tbl2 Ended After)
        (tbl2.Fault_Start_Date_Time <= tbl1.Fault_Start_Date_Time AND tbl2.Record_Timestamp >= tbl1.Record_Timestamp)
      )
      AND NOT (tbl1.RN_ID = tbl2.RN_ID)
    -- Remove any that met the conditions above
    WHERE tbl2.RN_ID IS NULL
  )

SELECT * FROM remove_aisle_level_matching