-- gold table locations = tableau openings
-- gold table location positions = tableau locations

SELECT
  IFNULL(aisle_code, "N/A") as Aisle_Code,
  CONCAT(COALESCE(area_code, ''), COALESCE(aisle_code, '')) AS `Area and Aisle`,
  area_code as Area_Code,
  etl_create_timestamp as `ETL Update Timestamp`,
  __raw_message_ingestion_time as MS_FM_DateStart,
  etl_create_timestamp as MS_FM_DateEnd,
  event_date_time_local as `Time of Day`,
  location_utilization_percentage as `Average % Utilized`,
  empty_location_position_count as `Empty Locations`,
  empty_location_count as `Empty Openings`,
  batch_uid as ETL_Batch_ID,
  faulted_location_position_count as `Faulted Locations`,
  location_utilization_percentage as `Max % Utilized`,
  location_utilization_percentage as `Minimum % Utilized`,
  1 as `Number of Records`,
  SAFE_CAST(empty_locations_percentage as FLOAT64) as `Percent Empty Openings`,
  SAFE_CAST(empty_location_positions_percentage as FLOAT64) as `Percent Empty Storage Locs`,
  location_utilization_percentage as `Percent Utilized Storage Locs`,
  total_location_position_count as `Total Locations`,
  total_location_count as `Total Openings`,
  unavailable_location_position_count as `Unavailable Locations`
FROM
  `edp-d-us-east2-etl.superior_uniform.gold_storage_utilization`