WITH base_movements AS (
  SELECT
    TIMESTAMP_TRUNC(TIMESTAMP(movement_start_date_time_local), HOUR) as Hour_Start_Time,
    TIMESTAMP_TRUNC(TIMESTAMP_ADD(TIMESTAMP(movement_end_date_time_local), INTERVAL 30 MINUTE), HOUR) as Hour_End_Time,
    -- if equipment_code is '' then we want to use shuttle code instead, if that is also '' then use N/A
    CASE
      WHEN equipment_code != '' AND REGEXP_CONTAINS(equipment_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        THEN SUBSTRING(equipment_code, 5, 2)
      WHEN equipment_code != '' AND REGEXP_CONTAINS(equipment_code, r'MS[0-9][0-9]')
        THEN SUBSTRING(equipment_code, 3, 2)
      WHEN shuttle_code != '' AND REGEXP_CONTAINS(shuttle_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        THEN SUBSTRING(shuttle_code, 5, 2)
      WHEN shuttle_code != '' AND REGEXP_CONTAINS(shuttle_code, r'MS[0-9][0-9]')
        THEN SUBSTRING(shuttle_code, 3, 2)
      ELSE "N/A"
    END as Aisle,
    CASE
      WHEN equipment_code != '' AND REGEXP_CONTAINS(equipment_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        THEN SUBSTRING(equipment_code, 9, 2)
      WHEN shuttle_code != '' AND REGEXP_CONTAINS(shuttle_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        THEN SUBSTRING(shuttle_code, 9, 2)
      ELSE "N/A"
    END as Level,
    INITCAP(equipment_type) as Device_Type,
    COALESCE(NULLIF(equipment_code, ''), NULLIF(shuttle_code, ''), '') as Device_Code,
    movement_status_code as Status_Code,
    IFNULL(movement_duration_seconds, 0) as Movement_Duration_Seconds,
    1 as Movements,
    movement_type_code as Movement_Type_Code,
    source_system as Source_System
  FROM
    `${edp_project_id}.${tenant_id}.gold_multishuttle_movements`
  WHERE
    movement_status_code = "OK"
)

SELECT
  Hour_Start_Time,
  Hour_End_Time,
  Aisle,
  Level,
  Device_Type,
  Device_Code,
  Status_Code,
  Movements,
  Movement_Duration_Seconds,
  0 as Fault_Duration_Seconds,
  0 as Faults,
  Movement_Type_Code,
  Source_System
FROM base_movements
UNION ALL
SELECT
  Hour_Start_Time,
  Hour_End_Time,
  Aisle,
  Level,
  Device_Type,
  Device_Code,
  CASE
    WHEN Fault_Description IS NULL THEN Status_Code
    ELSE CONCAT(Status_Code, ': ', Fault_Description)
  END as Status_Code,
  0 as Movements,
  0 as Movement_Duration_Seconds,
  Fault_Duration_Seconds,
  Faults,
  CAST(NULL as STRING) as Movement_Type_Code,
  Source_System
FROM `${tableau_project_id}.tableau_views.dms_filter_faults`
