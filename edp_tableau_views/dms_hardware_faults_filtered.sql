-- dependent on dms_movements_faults_filtered

SELECT
  Hour_Start_Time as DateHour,
  NULL as HardwareID,
  Device_Code as Device_Code,
  IFNULL(Movement_Type_Code, "N/A") as Movement_Type,
  Status_Code as Status_Code,
  SUM(Movements) as Movements,
  SUM(Faults) as Faults,
  Source_System as Source_System
FROM `${tableau_project_id}.tableau_views.dms_movements_faults_filtered`
WHERE
  Status_Code NOT LIKE '%514%' -- Bin Empty
  AND Device_Type = 'Shuttle'
GROUP BY
  DateHour,
  HardwareID,
  Device_Code,
  Movement_Type,
  Status_Code,
  Source_System;