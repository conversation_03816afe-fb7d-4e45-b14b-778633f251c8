**ict-etl-insights-pubsub-to-bigquery**

This a simple cloud function that is subscribed to a pub/sub topic, takes the message, and streaming loads to a BigQuery table. Based on the message fact_type it looks up the appropriate tables in the table_map.py, for almost all it is a 1:1 naming between fact_type and table name. It then creates a streaming insert object with the tenant, facility, source system, data and ingestion date having each data array element as a row for the insert.If the fact type is not in the table map or there is no fact type in the message it will insert the data into the UnknownFact table for further investigationIt has a REST endpoint that receives pubsub messages from a PUSH subscription, subscribed from the Insights -> Control Tower Topic from-insightsThis should be deployed as a Gen-1 CloudFunction, however we could try a Gen-2, increase the CPU and Memory and allow multiple connections, perhaps 100, this would need to be evaluated for optimization.

Prototype deployment
https://console.cloud.google.com/functions/details/us-central1/cf-from-insights-pubsub-to-bigquery?env=gen1&project=ict-b-prototype-etlt

Prototype sandbox subscription
projects/ict-b-prototype-etlt/subscriptions/test-insights-pubsub-to-bigquery
https://console.cloud.google.com/cloudpubsub/subscription/detail/test-insights-pubsub-to-bigquery?project=ict-b-prototype-etlt

Topic in ETL project to create the subscription
projects/ict-d-etl/topics/from-insights