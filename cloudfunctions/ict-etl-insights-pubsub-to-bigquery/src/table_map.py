tables = {
    "advicecreatedfact": {
        "dataset": "_landing",
        "table": "AdviceCreatedFact",
        "rows": [],
    },
    "advicefinishedfact": {
        "dataset": "_landing",
        "table": "AdviceFinishedFact",
        "rows": [],
    },
    "advicelinebasedcreateinventoryfact": {
        "dataset": "_landing",
        "table": "AdviceLineBasedCreateInventoryFact",
        "rows": [],
    },
    "areadimension": {
        "dataset": "_landing",
        "table": "AreaDimension",
        "rows": [],
    },
    "binutilization": {
        "dataset": "_landing",
        "table": "BinUtilization",
        "rows": [],
    },
    "connectionmovementfact": {
        "dataset": "_landing",
        "table": "ConnectionMovementFact",
        "rows": [],
    },
    "containermovementfact": {
        "dataset": "_landing",
        "table": "ContainerMovementFact",
        "rows": [],
    },
    "countingfact": {
        "dataset": "_landing",
        "table": "CountingFact",
        "rows": [],
    },
    "dialogactivefact": {
        "dataset": "_landing",
        "table": "DialogActiveFact",
        "rows": [],
    },
    "facilitydimension": {
        "dataset": "_landing",
        "table": "FacilityDimension",
        "rows": [],
    },
    "facilityorderdimension": {
        "dataset": "_landing",
        "table": "FacilityOrderDimension",
        "rows": [],
    },
    "facilityorderfact": {
        "dataset": "_landing",
        "table": "FacilityOrderFact",
        "rows": [],
    },
    "facilityorderlinefact": {
        "dataset": "_landing",
        "table": "FacilityOrderLineFact",
        "rows": [],
    },
    "faulteventfact": {
        "dataset": "_landing",
        "table": "FaultEventFact",
        "rows": [],
    },
    "faulttextdimension": {
        "dataset": "_landing",
        "table": "FaultTextDimension",
        "rows": [],
    },
    "groupdimension": {
        "dataset": "_landing",
        "table": "GroupDimension",
        "rows": [],
    },
    "handlingunitfact": {
        "dataset": "_landing",
        "table": "HandlingUnitFact",
        "rows": [],
    },
    "heartbeat-end": {
        "dataset": "_landing",
        "table": "HeartBeat-End",
        "rows": [],
    },
    "heartbeat-start": {
        "dataset": "_landing",
        "table": "HeartBeat-Start",
        "rows": [],
    },
    "inventoryfact": {
        "dataset": "_landing",
        "table": "InventoryFact",
        "rows": [],
    },
    "ipointmovementfact": {
        "dataset": "_landing",
        "table": "IPointMovementFact",
        "rows": [],
    },
    "locationdimension": {
        "dataset": "_landing",
        "table": "LocationDimension",
        "rows": [],
    },
    "multishuttlemovementfact": {
        "dataset": "_landing",
        "table": "MultishuttleMovementFact",
        "rows": [],
    },
    "nokintervalfact": {
        "dataset": "_landing",
        "table": "NokIntervalFact",
        "rows": [],
    },
    "ordercreatedfact": {
        "dataset": "_landing",
        "table": "OrderCreatedFact",
        "rows": [],
    },
    "packactivityfact": {
        "dataset": "_landing",
        "table": "PackActivityFact",
        "rows": [],
    },
    "packtaskfact": {
        "dataset": "_landing",
        "table": "PackTaskFact",
        "rows": [],
    },
    "packtasklinefact": {
        "dataset": "_landing",
        "table": "PackTaskLineFact",
        "rows": [],
    },
    "pickactivityfact": {
        "dataset": "_landing",
        "table": "PickActivityFact",
        "rows": [],
    },
    "pickfact": {
        "dataset": "_landing",
        "table": "PickFact",
        "rows": [],
    },
    "pickfactcp": {
        "dataset": "_landing",
        "table": "PickFact",
        "rows": [],
    },
    "pickorderfact": {
        "dataset": "_landing",
        "table": "PickOrderFact",
        "rows": [],
    },
    "pickorderlinefact": {
        "dataset": "_landing",
        "table": "PickOrderLineFact",
        "rows": [],
    },
    "pickorderplanningfact": {
        "dataset": "_landing",
        "table": "PickOrderPlanningFact",
        "rows": [],
    },
    "powerchaintelemetry": {
        "dataset": "_landing",
        "table": "PowerChainTelemetry",
        "rows": [],
    },
    "samplefact": {
        "dataset": "_landing",
        "table": "SampleFact",
        "rows": [],
    },
    "scadafaultfact": {
        "dataset": "_landing",
        "table": "SCADAFaultFact",
        "rows": [],
    },
    "skudimension": {
        "dataset": "_landing",
        "table": "SKUDimension",
        "rows": [],
    },
    "skuproductcodedimension": {
        "dataset": "_landing",
        "table": "SKUProductCodeDimension",
        "rows": [],
    },
    "skuquantityunitdimension": {
        "dataset": "_landing",
        "table": "SKUQuantityUnitDimension",
        "rows": [],
    },
    "sortfact": {
        "dataset": "_landing",
        "table": "SortFact",
        "rows": [],
    },
    "sorterdispositionfact": {
        "dataset": "_landing",
        "table": "SorterDispositionFact",
        "rows": [],
    },
    "stackercranemovementfact": {
        "dataset": "_landing",
        "table": "StackercraneMovementFact",
        "rows": [],
    },
    "statusdimension": {
        "dataset": "_landing",
        "table": "StatusDimension",
        "rows": [],
    },
    "subgroupdimension": {
        "dataset": "_landing",
        "table": "SubgroupDimension",
        "rows": [],
    },
    "transporttaskconfirmationfact": {
        "dataset": "_landing",
        "table": "TransportTaskConfirmationFact",
        "rows": [],
    },
    "unknownfact": {
        "dataset": "_landing",
        "table": "UnknownFact",
        "rows": [],
    },
    "userdimension": {
        "dataset": "_landing",
        "table": "UserDimension",
        "rows": [],
    },
    "vehiclemovementfact": {
        "dataset": "_landing",
        "table": "VehicleMovementFact",
        "rows": [],
    },
    "wms_customerorderdetailfact": {
        "dataset": "_landing",
        "table": "WMSCustomerOrderDetailFact",
        "rows": [],
    },
    "wms_customerorderfact": {
        "dataset": "_landing",
        "table": "WMSCustomerOrderFact",
        "rows": [],
    },
    "wmsinventoryfact": {
        "dataset": "_landing",
        "table": "WMSInventoryFact",
        "rows": [],
    },    
    "wms": {
        "dataset": "_landing",
        "table": "WMSCustomerOrderDetailFact",
        "rows": [],
    },
    "warehousesubsystemdimension": {
        "dataset": "_landing",
        "table": "WarehouseSubsystemDimension",
        "rows": [],
    },
    "workstationmodefact": {
        "dataset": "_landing",
        "table": "WorkstationModeFact",
        "rows": [],
    },
    "workstationworkflowentryfact": {
        "dataset": "_landing",
        "table": "WorkstationworkFlowEntryFact",
        "rows": [],
    },
    "viz": {
        "dataset": "_landing",
        "table": "viz_EventLogFact",
        "rows": [],
    },
    "viz_eventlogfact": {
        "dataset": "_landing",
        "table": "viz_EventLogFact",
        "rows": [],
    },
    "vizeventlogfact": {
        "dataset": "_landing",
        "table": "viz_EventLogFact",
        "rows": [],
    },
}
