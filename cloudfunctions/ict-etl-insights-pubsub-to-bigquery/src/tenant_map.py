tenants = {
    "ict_development": {
        "dataset_base": "ict_development",
        "landing_dataset": "ict_development_landing",
        "curated_dataset": "ict_development_oa_curated",
        "replace_time": False,
    },
    "ace_hardware": {
        "dataset_base": "ace_hardware",
        "landing_dataset": "ace_hardware_landing",
        "curated_dataset": "ace_hardware_oa_curated",
        "replace_time": False,
    },
    "dematic": {
        "dataset_base": "dematic",
        "landing_dataset": "dematic_landing",
        "curated_dataset": "dematic_oa_curated",
        "replace_time": False,
    },
    "superior_uniform": {
        "dataset_base": "superior_uniform",
        "landing_dataset": "superior_uniform_landing",
        "curated_dataset": "superior_uniform_oa_curated",
        "replace_time": False,
    },
    "qa_manual": {
        "dataset_base": "qa_manual",
        "landing_dataset": "qa_manual_landing",
        "curated_dataset": "qa_manual_oa_curated",
        "replace_time": False,
    },
    "qa_automation": {
        "dataset_base": "qa_automation",
        "landing_dataset": "qa_automation_landing",
        "curated_dataset": "qa_automation_oa_curated",
        "replace_time": False,
    },
    "dematic_software": {
        "dataset_base": "dematic_software",
        "landing_dataset": "dematic_software_landing",
        "curated_dataset": "dematic_software_oa_curated",
        "replace_time": False,
    },
    "drt_automation": {
        "dataset_base": "drt_automation",
        "landing_dataset": "drt_automation_landing",
        "curated_dataset": "drt_automation_oa_curated",
        "replace_time": False,
    },
    "verification_and_validation": {
        "dataset_base": "verification_and_validation",
        "landing_dataset": "verification_and_validation_landing",
        "curated_dataset": "verification_and_validation_oa_curated",
        "replace_time": False,
    },
    "qa_manual_1": {
        "dataset_base": "qa_manual_1",
        "landing_dataset": "qa_manual_1_landing",
        "curated_dataset": "qa_manual_1_oa_curated",
        "replace_time": False,
    },
    "integration_test": {
        "dataset_base": "integration_test",
        "landing_dataset": "integration_test_landing",
        "curated_dataset": "integration_test_oa_curated",
        "replace_time": False,
    },
    "tti": {
        "dataset_base": "tti",
        "landing_dataset": "tti_landing",
        "curated_dataset": "tti_oa_curated",
        "replace_time": False,
    },
}
