import base64, json, sys, os, copy
import functions_framework
from google.cloud import pubsub_v1


PROJECT_ID = os.environ.get("PROJECT_ID", "ict-d-etl")
TOPIC_NAME = os.environ.get("TOPIC_NAME", "dev-adi-to-bq")

# Initialize Publisher and Subscriber clients
publisher = pubsub_v1.PublisherClient()

try:
    topic_path = publisher.topic_path(PROJECT_ID, TOPIC_NAME)
    print(f"Publish topic_path={topic_path}")
except Exception as e:
    print(f"Error Error={e} topic_path={topic_path}")


@functions_framework.http
def handler(request):

    request_json = request.get_json(silent=True)
    request_args = request.args
    
    print(request_json)
    print(**request_args)
    
    if request_json and 'message' in request_json:
        message = get_emqx_payload(request_json)
        if message is None:
            return 'No EMQX Payload', 422
        attributes = request_json["message"].get('attributes', {})
        result = publish_adi_pubsub(message, attributes)
        if result is None:
            return 'Error Publishing', 422
        return 'Success'
    else:
        print('No Data')
        return 'No Data', 422


def get_emqx_payload(request_json):

    # The Pub/Sub message is passed as the CloudEvent's data payload.
    base64data = base64.b64decode(request_json["message"]["data"]).decode()
    # If expected data not found, exit
    if not base64data:
        print('No data found in message!')
        return 'No data found in message!'
        
    print(f"Processing messageId {request_json['message']['messageId']}")

    try:
        # Parse the event data
        message_data = json.loads(base64data)
        print(message_data)
    except json.JSONDecodeError as err:
        print('Error parsing JSON:', err)
        return None
    
    if 'payload' in message_data:
        return message_data['payload']
    else:
        print('No EMQX Payload')
        return None


def publish_adi_pubsub(message, attributes):

    print("Message", message)
    print("Attributes", attributes)

    try:
        future = publisher.publish(topic_path, message.encode("utf-8"), **attributes)
        result = future.result()
        print(result)
        return result
    except Exception as e:
        print(f'Error={e}, Failed to publish message to project={PROJECT_ID}, path={topic_path} ')
        return None