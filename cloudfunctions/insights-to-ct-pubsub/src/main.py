import base64
import os
import functions_framework
from google.cloud import pubsub_v1


PUBLISHER_PROJECT_ID = os.environ.get("PUBLISHER_PROJECT_ID")
# No schema, and no message retention, with google managed encryption key
PUBLISHER_TOPIC_NAME = os.environ.get("PUBLISHER_TOPIC_NAME")

if PUBLISHER_PROJECT_ID is None:
    raise Exception("Environment variable PUBLISHER_PROJECT_ID is not set.")

if PUBLISHER_TOPIC_NAME is None:
    raise Exception("Environment variable PUBLISHER_TOPIC_NAME is not set.")

LOG_RAW_DATA = os.environ.get("LOG_RAW_DATA", "false").lower() == "true"

OVERRIDE_TENANT = os.environ.get("OVERRIDE_TENANT")

print(
    f"Frowarding Messages to: PUBLISHER_PROJECT_ID={PUBLISHER_PROJECT_ID}, PUBLISHER_TOPIC_NAME={PUBLISHER_TOPIC_NAME}"
)

if LOG_RAW_DATA:
    print(f"LOG_RAW_DATA=True")
else:
    print(f"LOG_RAW_DATA=False")

if OVERRIDE_TENANT is not None:
    print(f"OVERRIDE_TENANT={OVERRIDE_TENANT}")
else:
    print(f"OVERRIDE_TENANT=None")

# Initialize Publisher and Subscriber clients
publisher = pubsub_v1.PublisherClient()

try:
    topic_path = publisher.topic_path(PUBLISHER_PROJECT_ID, PUBLISHER_TOPIC_NAME)
    print(f"Forwarding Messages to: Publish topic_path={topic_path}")
except Exception as e:
    print(f"Error Error={e} topic_path={topic_path}")
    raise e


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def hello_pubsub(cloud_event):
    try:
        data = base64.b64decode(cloud_event.data["message"]["data"])
        attributes = cloud_event.data["message"]["attributes"]

        if OVERRIDE_TENANT is not None:
            attributes["tenant"] = OVERRIDE_TENANT

        future = publisher.publish(topic_path, data=data, **attributes)

        if LOG_RAW_DATA:
		    # Print out the data from Pub/Sub, to prove that it worked
            print(f" attributes={attributes}, data = {data}")

        result = future.result()
    except Exception as e:
        print(
            f"Failed to forward message to project={PUBLISHER_PROJECT_ID}, path={topic_path}. Error=e "
        )
