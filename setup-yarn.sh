#!/bin/bash 

echo "This helper script executes a basic bootstrap of the monorepo using lerna."

set -u
set -o pipefail

NPM_LOCAL_ARTIFACTORY_URL=artifactory.dematic.com/artifactory/api/npm/npm-virtual


echo "Set the following variables in your local environment"

echo " - DEV_BLD_ARTIFACTORY_AUTHTOKEN"
echo " - DEV_BLD_ARTIFACTORY_PWD"
echo " - DEV_BLD_ARTIFACTORY_PWD_ENCODED"
echo " - DEV_BLD_USERNAME"

if [ x"${DEV_BLD_USERNAME}" == "x" ]; then
    echo "Error, missing at least one environment variable needed."
    exit 0
fi

#npm install ./packages/foundation-utils
npm install -g yarn@1.22.19 lerna@6.4.1

# The below should be added to package.json as dependencies, so that yarn-install from root will install them. 
npm i -g serverless@1.57.0
npm i -g lint-staged jest apollo-server-testing eslint serverless-azure-functions graphql@0.13.2
# abort-controller duplexify
yarn --version

yarn config set strict-ssl=false
yarn config set always-auth=true
yarn config set @nui:registry=https://$NPM_LOCAL_ARTIFACTORY_URL
yarn config set @sls:registry=https://$NPM_LOCAL_ARTIFACTORY_URL
yarn config set @dso:registry=https://$NPM_LOCAL_ARTIFACTORY_URL
yarn config set @arrify:registry=https://$NPM_LOCAL_ARTIFACTORY_URL
yarn config set @google-cloud/paginator:registry=https://$NPM_LOCAL_ARTIFACTORY_URL
yarn config set https://$NPM_LOCAL_ARTIFACTORY_URL/:_password=$DEV_BLD_ARTIFACTORY_PWD_ENCODED
yarn config set https//$NPM_LOCAL_ARTIFACTORY_URL/:username=$DEV_BLD_USERNAME


yarn config get registry

while true; do
    read -p "Do you wish to clena up first? [ runs lerna clean ] " yn
    case $yn in
        [Yy]* ) lerna clean; break;;
        [Nn]* ) echo "Continuing with the bootstrap ... " ; break;;
        * ) echo "Please answer yes or no.";;
    esac
done


lerna link
lerna bootstrap