Details for the DB views and automation to support the ICT Daily Performance Report Page

The report is intended to be a general ad-hoc report screen with the ability to export to CSV. However, the implementation is completely Superior Uniform specific. The client has a similar report that is created manually using some Insights data, this is intended to automate that.

The source of the data is from a primary report source view using views built to support the new Insights Tableau CT migration. The source data is from EDP gold tables.


Requirement Ticket: https://jira.dematic.net/browse/ICT-3612


The views are currently deployed to the Tableau prod environment, ict-p-tableau. 
It is intended that all of these views to be deployed to the ICT API projects.

For stable access and view materialization for performance the view is being queried and loaded into a static BQ table:
ict-d-api.superior_uniform_oa_curated.superior_workstation_daily_performance

The API endpoint queries this table
/workstation/daily-performance/list

https://gitlab.com/dematic/controltower/control-tower/-/blob/dev/services/api/apps/api/ict-workstation-api/src/stores/workstation-store.ts?ref_type=heads

getWorkstationDailyPerformanceList




Automation

Currently the generation of the superior_workstation_daily_performance table is using a BigQuery Scheduled Query manually created in each environment. This is due to the need of the query to be able to access the views in the tableau project and the data in the EDP projects.
Note: all ICT environments are just loading from the tableau prod and edp projects.

Scheduled Query link:

superior_workstation_daily_performance
https://console.cloud.google.com/bigquery/scheduled-queries/locations/us/configs/67d8f536-0000-266b-864f-2405887a7a60/details?inv=1&invt=AbtFTA&project=ict-d-api

Query:
SELECT * FROM `ict-p-tableau.superior_uniform_tableau_views.eudora_ar_report_workstation_daily_performance_view` order by day DESC, workstation_code

Destination Table: superior_workstation_daily_performance

Write preference: WRITE_TRUNCATE

Schedule (UTC): every 1 hours




Views

superior_uniform_tableau_views.eudora_ar_report_workstation_daily_performance_view - Primary view generating all report data

superior_uniform_tableau_views.eudora_ar_dms_ml_storage_retrieval - Counts for DMS and AutoStore movements

superior_uniform_tableau_views.eudora_ar_workstation_totes - Count of Order and Donor totes

superior_uniform_tableau_views.eudora_ar_oper_ws_fulfillment - Existing Tableau view for the majority of the operator data

