CREATE OR REPLACE VIEW `superior_uniform_tableau_views.eudora_ar_dms_ml_storage_retrieval`
AS 
  -- eudora_ar_dms_ml_storage_retrieval ------------------------------------------------------------
  -- Simple view to count the multishuttle and autostore vehical movements.
  -- Storage and Retrieval, grouped by Day
WITH
  multishuttle_movements AS (
  SELECT distinct
    datetime(TIMESTAMP_TRUNC(Date_Hour, DAY)) AS day,
    COUNTIF(LOWER(movement_type_code)='retrieval') AS retrieval,
    COUNTIF(LOWER(movement_type_code)='storage') AS storage,
    COUNTIF(LOWER(movement_type_code)='shuffle') AS shuffle
  FROM
    `superior_uniform_tableau_views.eudora_ar_dms_movements_overview`
  GROUP BY
    datetime(TIMESTAMP_TRUNC(Date_Hour, DAY))
  order by day  
    ),
  miniload_movements AS (
  SELECT distinct
    TIMESTAMP_TRUNC(Record_Timestamp, DAY) AS day,
    COUNTIF(LOWER(movement_type_code)='retrieval') AS retrieval,
    COUNTIF(LOWER(movement_type_code)='storage') AS storage,
    COUNTIF(LOWER(movement_type_code)='shuffle') AS shuffle
  FROM
    `superior_uniform_tableau_views.eudora_ar_ml_movements_overview`
  GROUP BY
    TIMESTAMP_TRUNC(Record_Timestamp, DAY)
  order by day  
   )
SELECT
  ms.day,
  ms.retrieval AS multishuttle_retrieval,
  ms.storage AS multishuttle_storage,
  ms.shuffle AS multishuttle_shuffle,
  ml.retrieval AS miniload_retrieval,
  ml.storage AS miniload_storage,
  ml.shuffle AS miniload_shuffle
FROM
  multishuttle_movements ms
LEFT JOIN
  miniload_movements ml
ON
  ms.day = ml.day;
