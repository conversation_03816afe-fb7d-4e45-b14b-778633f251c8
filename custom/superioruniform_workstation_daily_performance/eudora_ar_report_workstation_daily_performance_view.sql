CREATE OR REPLACE VIEW
  `superior_uniform_tableau_views.eudora_ar_report_workstation_daily_performance_view` AS
WITH
  operator_idle AS (
  SELECT
    DATETIME_TRUNC(o.Hour_Quarter_Start_Time, DAY) AS day,
    o.Hour_Quarter_Start_Time,
    o.workstation_code,
    o.Operator_Code,
    SUM(logged_in_time_sec) AS logged_in_time_seconds,
    SUM(starved_time_sec) AS starved_seconds,
    SUM(Presented_Time_Sec) AS presented_time_sec,
    SUM(no_action_time_sec) AS no_action_time_sec,
    SUM(picked_lines) AS all_picked_lines,
    SUM(CASE
        WHEN work_type IN ("PICKALTR", "PICK", "PICKSNBX", "SNBX") THEN Pick_Orders_Worked
        ELSE 0
    END
      ) AS orders,
    SUM(CASE
        WHEN work_type IN ("PICKALTR", "PICK", "PICKSNBX", "SNBX") THEN Operator_Operations 
        ELSE 0
    END
      ) AS picked_lines,
    SUM(CASE
        WHEN work_type IN ("PICKALTR", "PICK", "PICKSNBX", "SNBX") THEN picked_qty
        ELSE 0
    END
      ) AS picked_quantity,
    SUM(CASE
        WHEN work_type IN ("PICKALTR", "PICK", "PICKSNBX", "SNBX") THEN presentations
        ELSE 0
    END
      ) AS presentations,
    SUM(CASE
        WHEN work_type IN ("PICKALTR", "PICK", "PICKSNBX", "SNBX") THEN outbound_presentations
        ELSE 0
    END
      ) AS outbound_presentations,
    SUM(CASE
        WHEN EXTRACT(TIME FROM Hour_Quarter_Start_Time) BETWEEN TIME '07:00:00' AND TIME '14:59:59' THEN logged_in_time_sec
        ELSE 0
    END
      ) AS seconds_1st_shift,
    SUM(CASE
        WHEN EXTRACT(TIME FROM Hour_Quarter_Start_Time) BETWEEN TIME '07:00:00' AND TIME '14:59:59' THEN starved_time_sec
        ELSE 0
    END
      ) AS starved_1st_shift,      
    SUM(CASE
        WHEN EXTRACT(TIME FROM Hour_Quarter_Start_Time) BETWEEN TIME '07:00:00' AND TIME '14:59:59'
          AND work_type IN ("PICKALTR", "PICK", "PICKSNBX", "SNBX") THEN Operator_Operations 
        ELSE 0
    END
      ) AS lines_1st_shift,
    SUM(CASE
        WHEN EXTRACT(TIME FROM Hour_Quarter_Start_Time) BETWEEN TIME '15:00:00' AND TIME '22:59:59' THEN logged_in_time_sec
        ELSE 0
    END
      ) AS seconds_2st_shift,
    SUM(CASE
        WHEN EXTRACT(TIME FROM Hour_Quarter_Start_Time) BETWEEN TIME '15:00:00' AND TIME '22:59:59' THEN starved_time_sec
        ELSE 0
    END
      ) AS starved_2st_shift,      
    SUM(CASE
        WHEN EXTRACT(TIME FROM Hour_Quarter_Start_Time) BETWEEN TIME '15:00:00' AND TIME '22:59:59'
          AND work_type IN ("PICKALTR", "PICK", "PICKSNBX", "SNBX") THEN Operator_Operations 
        ELSE 0
    END
      ) AS lines_2st_shift
  FROM
    `superior_uniform_tableau_views.eudora_ar_oper_ws_fulfillment` o

  GROUP BY
    Hour_Quarter_Start_Time,
    workstation_code,
    Operator_Code
  ORDER BY
    Hour_Quarter_Start_Time,
    workstation_code,
    Operator_Code )
, operator_day as (
SELECT
  o.day,
  o.workstation_code,
  NULLIF(SUM(logged_in_time_seconds), 0) AS logged_in_time_seconds,
  NULLIF(SUM(logged_in_time_seconds)/60, 0) AS total_logged_in_minutes,
  NULLIF(SUM(starved_seconds), 0) AS starved_seconds,
  NULLIF(SUM(starved_seconds)/60, 0) AS starved_time_in_minutes,
  NULLIF(SUM(Presented_Time_Sec), 0) AS all_presented_time_seconds,
  NULLIF(SUM(Presented_Time_Sec)/60, 0) AS all_presented_time_minutes,
  NULLIF(SUM(no_action_time_sec), 0) AS no_action_time_seconds,
  NULLIF(SUM(no_action_time_sec)/60, 0) AS no_action_time_in_minutes,
  NULLIF(SUM(CASE
        WHEN picked_lines=0 THEN presented_time_sec
        ELSE 0
    END
      ), 0) AS presented_nopick_time_seconds,
  NULLIF(SUM(CASE
        WHEN picked_lines=0 THEN presented_time_sec
        ELSE 0
    END
      )/60, 0) AS presented_nopick_time_in_minutes,
  NULLIF((SUM(CASE
          WHEN picked_lines=0 THEN presented_time_sec
          ELSE 0
      END
        ) + SUM(no_action_time_sec)) /60, 0) AS idle_time_in_minutes,
  NULLIF(SUM(all_picked_lines), 0) AS all_picked_lines,
  NULLIF(SUM(picked_lines), 0) AS lines,
  NULLIF(SUM(picked_quantity), 0) AS pick_line_qty,
  NULLIF(case when ifnull(sum(picked_lines), 0) != 0 then sum(picked_quantity)/sum(picked_lines) else 0 end, 0) as pick_line_avg,  
  NULLIF(SUM(presentations), 0) AS presentations,
  NULLIF(SUM(outbound_presentations), 0) AS outbound_presentations,  
  NULLIF(SUM(seconds_1st_shift)-SUM(starved_1st_shift), 0) AS seconds_1st_shift,
  NULLIF((SUM(seconds_1st_shift)-SUM(starved_1st_shift))/60/60, 0) AS hours_1st_shift, -- logged in time - starved
  NULLIF(SUM(lines_1st_shift), 0) AS lines_1st_shift,
  NULLIF(SUM(seconds_2st_shift)-SUM(starved_1st_shift), 0) AS seconds_2nd_shift,
  NULLIF((SUM(seconds_2st_shift)-SUM(starved_1st_shift))/60/60, 0) AS hours_2nd_shift, -- logged in time - starved
  NULLIF(SUM(lines_2st_shift), 0) AS lines_2nd_shift
FROM
  operator_idle o
GROUP BY
  o.day,
  o.workstation_code
ORDER BY
  o.day,
  o.workstation_code
)
SELECT 
  oo.day,
  oo.workstation_code,
  logged_in_time_seconds,
  total_logged_in_minutes,
  starved_seconds,
  starved_time_in_minutes,
  all_presented_time_seconds,
  all_presented_time_minutes,
  no_action_time_seconds,
  no_action_time_in_minutes,
  presented_nopick_time_seconds,
  presented_nopick_time_in_minutes,
  idle_time_in_minutes,
  all_picked_lines,
  lines,
  pick_line_qty,
  pick_line_avg,  
  presentations,
  outbound_presentations,  
  t.donor_totes AS donor_containers,
  t.order_totes AS gtp_containers,
  seconds_1st_shift,
  hours_1st_shift,
  lines_1st_shift,
  seconds_2nd_shift,
  hours_2nd_shift,
  lines_2nd_shift,
  sr.multishuttle_retrieval AS retrieval_from_dms,
  sr.multishuttle_storage AS storage_to_dms,
  sr.miniload_retrieval AS retrieval_from_asrs,
  sr.miniload_storage AS storage_to_asrs
FROM
  operator_day oo
left join `superior_uniform_tableau_views.eudora_ar_dms_ml_storage_retrieval` sr
  on sr.day = oo.day
left join `superior_uniform_tableau_views.eudora_ar_workstation_totes` t
  on t.day = oo.day
  and t.workstation_code = oo.workstation_code
ORDER BY
  oo.day,
  oo.workstation_code







/*
select 
  day,
  TRIM(CAST(SUM(total_logged_in_minutes) / 60 AS STRING FORMAT '9999.9')) AS total_logged_in_hours,
  NULLIF(SUM(presentations), 0) AS presentations,
  NULLIF(SUM(outbound_presentations), 0) AS outbound_presentations,  
  NULLIF(SUM(donor_containers), 0) AS donor_containers,
  NULLIF(SUM(gtp_containers), 0) AS gtp_containers,
 from 
`superior_uniform_tableau_views.eudora_ar_report_workstation_daily_performance_view`
where 
day between  '2025-03-03' and '2025-03-09'
group by day
order by day --Hour_Quarter_Start_Time
*/

/*
select 
  day,
  workstation_code,
  total_logged_in_minutes,
presentations,
  donor_containers,
  gtp_containers,
 from 
`superior_uniform_tableau_views.eudora_ar_report_workstation_daily_performance_view`
where 
day between  '2025-03-03' and '2025-03-03'

order by day --Hour_Quarter_Start_Time
*/




