create or replace view `superior_uniform_tableau_views.eudora_ar_workstation_totes` as
  -- View to count the number of donor totes and order totes in 15 minute windows and then aggrated by day
WITH
  cte_totes AS (
  SELECT 
    event_date_time_local,
    DATETIME(`ict-p-tableau.superior_uniform_tableau_views.eudora_ar_round_15minutes_timestamp`(TIMESTAMP(event_date_time_local))) AS Window15,
    workstation_code,
    operator_code,
    source_handling_unit_code AS donor_tote,
    source_handling_unit_type,
    destination_handling_unit_code AS order_tote,
    destination_handling_unit_type,
    work_type_code,
    reason_code
  FROM
    `edp-p-us-east1-etl.superior_uniform.gold_pick`
  WHERE
    event_timestamp_utc BETWEEN TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 31 DAY)
    AND TIMESTAMP_ADD(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 1425 MINUTE)
    AND work_type_code IN ('PICKALTR', 'PICK', 'PICKSNBX', 'SNBX')
    AND NULLIF(operator_code,'') IS NOT NULL 
    )
, cte_count_15 AS (
  SELECT
    TIMESTAMP_TRUNC(Window15, DAY) AS day,
    Window15,
    workstation_code,
    work_type_code,
    COUNT(distinct donor_tote) AS donor_totes,
    COUNT(distinct order_tote) AS order_totes
  FROM
    cte_totes
  GROUP BY
    Window15
    , workstation_code 
    , work_type_code
)
select 
  day, 
  workstation_code,
  sum(donor_totes) donor_totes,
  sum(order_totes) order_totes
  from cte_count_15
  GROUP BY day, workstation_code 
  order by day, workstation_code 


  /*
  select 
day, 
 -- workstation_code,
  sum(donor_totes) donor_totes,
  sum(order_totes) order_totes
  from `superior_uniform_tableau_views.eudora_ar_workstation_totes`
  WHERE day BETWEEN '2025-03-03' AND '2025-03-09'
  GROUP BY day --,workstation_code
  order by day --,workstation_code
  */


