# SonarQube server
# ------------------------------
sonar.host.url=https://sonarqube.dematic.com

# Project settings.
# ------------------------------
sonar.projectKey=ICT-control-tower-etl
sonar.projectName=ControlTower ETL
# sonar.projectVersion=1.0
sonar.projectDescription="A part of Control Tower..."

# Scan settings.
# ------------------------------
sonar.sources=.

# Fail CI pipeline if Sonar fails.
# ------------------------------
sonar.qualitygate.wait=false

sonar.python.version=3
