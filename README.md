# Control Tower ETL

## Adding a new Tenant

To add a new tenant, follow these steps:

1. Add tenant name to list of tenants in terraform for liquibase actions within the required environments. Example:

   ```bash
   tenants = [
        {
            name         = "ict_development"
            display_name = "ICT Development"
            metadata = {
                dataset = "ict_development"
            }
        },
        {
           ...
        },
   ]
   ```

   - [Dev Config](/infrastructure/Configs/dev.tfvars)
   - [Stage Config](/infrastructure/Configs/stage.tfvars)
   - [Prod Config](/infrastructure/Configs/prod.tfvars)

2. Create new airflow DAGs folder for tenant

   - Copy [airflow/dags/dematic](/airflow/dags/dematic/) folder and name it airflow/dags/${new_tenant_name}
   - Update the names of the files to replace dematic with ${new_tenant_name}
   - Update all usages of `dematic` with `${new_tenant_name}` in all the copied files

3. Add ${new_tenant_name} object to the tenant map - [cloudfunctions/ict-etl-insights-pubsub-to-bigquery/src/tenant_map.py](/cloudfunctions/ict-etl-insights-pubsub-to-bigquery/src/tenant_map.py)

# How to start and run local Liquidbase

1. locate docker file: liquibase/docker/Dockerfile
2. ensure gcloud CLI is installed then run cmd `gcloud auth application-default login`
3. update url inside local.liquibase.properties so that ProjectId=ict-b-[sandbox name] (ex: ...bigquery/v2:443;ProjectId=ict-b-farleyz)
4. Navigate to your sandbox inside google console and add the below dataset names
   test
   test_landing
   test_oa_curated
   \*NOTE: these names don't need to be used but if you use something different ensure the property DefaultDataset inside url is updated.

5. Run docker cmd: `docker build . -t localliquibase`
6. Run docker cmd: `docker run -v $(pwd)/control-tower-etl/liquibase/ict_oa_core:/liquibase/changelog -v "$HOME/.config/gcloud/application_default_credentials.json":/gcp/creds.json:ro -e GOOGLE_APPLICATION_CREDENTIALS=/gcp/creds.json liquidbase --defaults-file=./changelog/liquibase.properties --changeLogFile=./changelog/changelog-master.yaml --search-path=/liquibase/changelog status -Dtenant_id=test`
7. Run docker cmd: `docker run -v $(pwd)/control-tower-etl/liquibase/ict_oa_core:/liquibase/changelog -v "$HOME/.config/gcloud/application_default_credentials.json":/gcp/creds.json:ro -e GOOGLE_APPLICATION_CREDENTIALS=/gcp/creds.json liquidbase --defaults-file=./changelog/liquibase.properties --changeLogFile=./changelog/changelog-master.yaml --search-path=/liquibase/changelog update -Dtenant_id=test`
8. Ensure all landing and oa_curated tables appear in google console

# Liquibase changelog folder format

`<timestamp>_<ticket>_<description>`
