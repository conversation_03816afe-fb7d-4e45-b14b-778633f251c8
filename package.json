{"name": "control-tower-etl", "version": "0.0.1", "description": "Monorepo for control tower etl", "private": true, "scripts": {"bootstrap": "lerna bootstrap", "bootstrap:prod": "lerna bootstrap -- --production", "setup": "yarn install && lerna link && lerna bootstrap", "setup:prod": "yarn install && yarn run bootstrap:prod", "lint": "lerna run lint"}, "repository": "**********************:controltower/control-tower-etl.git", "author": "", "license": "ISC", "devDependencies": {"lerna": "^6.4.1", "npm-run-all": "^4.1.5", "sonar-scanner": "^3.1.0", "yaml": "^1.10.0", "yargs": "^16.2.0"}, "dependencies": {}}