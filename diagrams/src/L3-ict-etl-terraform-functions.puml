@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

LAYOUT_TOP_DOWN()

title L3 Control Tower ETL Terraform Functions

System_Boundary(terraform, "git://control-tower-etl", "gitlab") {


  Container_Ext(cloudRun, "CloudRun", "") {
            Container(enableCloudRunP2Services, "enableGCPServices")
            Container(runTestETL, "TestETL")
            Container(gcpScheduledJobs, "Scheduled Jobs API")
  }
  Container_Ext(cloudStorage, "CloudStorage", "") { 
            Container(enableCloudStorageServices, "enableGCPServices")
            Container(mockAPIData, "Mock Cloud Storage Bucket")

  }
  Container_Ext(configs, "Configs", "") {
            Container(tfVarsFiles, "Environment Config Files")

   }
  Container_Ext(network, "Network", "") {
            Container(enableNetworkP2Services, "enableGCPServices")
            Container(cloudNAT, "Cloud NAT", "Required for outbound connections from all CloudRuns")
            Container(subnetVPCServiceConnect, "VPC for Service Connect")
            Container(allocatedRangePSC, "Allocated Range /20 for Private Service Connections")
            Container(vpcAccessConnector, "VPC Serverless Connector for all Cloud Runs")

   }

  Container_Ext(vpc, "VPC", "") { 
            Container(enableVPCServices, "enableGCPServices")
            Container(networkAPI, "Creates ETL network and temp subnets as needed")

  }

}

Rel_D(vpc, network, "Step 0")

Rel_U(network, cloudRunP2, "Step1")



' CloudRunP2
Rel(runLB, runCA, "https")
Rel(runLB, oauth2Client, "Auth")
Rel(runLB, runMockDataAPI, "mock")
Rel(runLB, runEquipmentAPI, "equipment")
Rel(runLB, runInventoryAPI, "inventory")
Rel(runLB, runOperatorsAPI, "operators")
Rel(runLB, runOrdersAPI, "orderes")

' Apigee
Rel(createApigeeOrg, createApigeeEnv, "Apigee ENV")
Rel_D(createApigeeEnv, createApigeeIns, "Ext GCP Apigee")
Rel_D(createApigeeEnv, createApigeeMIG, "ENV Association Bridge")
Rel(createApigeeLB, createApigeeMIG, "Managed Instange Group Rules")
Rel(createApigeeMIG, createApigeeIns, "Bridge Established")


center footer Generated for Dematic.com Intelligent Control Tower 

@enduml