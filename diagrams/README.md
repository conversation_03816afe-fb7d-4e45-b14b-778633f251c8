# ETL Diagrams

```mermaid
%% Cloud Composer Architecture
graph TD
    subgraph Google Cloud Services
        BigQuery[BigQuery]
    end
    subgraph Terraform Resources
        ComposerEnv[Composer Environment]
        ComposerSA[Composer Service Account]
        IAMBindings[IAM Bindings]
        AirflowDAGs[Airflow DAGs Deployment]
        AirflowTransforms[Airflow Transforms Deployment]
        ComposerSubnet[Cloud Composer Subnetwork]
        SourceBucket[Source Bucket]
    end
    ComposerSA --> ComposerEnv
    ComposerSA --> IAMBindings
    ComposerEnv --> AirflowDAGs
    ComposerEnv --> AirflowTransforms
    ComposerSubnet --> ComposerEnv
    SourceBucket --> AirflowTransforms
    IAMBindings --> BigQuery

%% Insights PubSub to BigQuery ETL Architecture
    subgraph Cloud Functions
        InsightsETL[Insights PubSub to BigQuery]
    end
    subgraph Service Accounts
        CFSA[Cloud Function Service Account]
    end
    subgraph Storage
        ETLZip[ETL Source Code Zip]
        CFStorage[CF Storage Bucket]
    end
    subgraph IAM
        FunctionInvoker[Function Invoker]
        CloudRunInvoker[Cloud Run Invoker]
    end
    InsightsETL --> ETLZip
    InsightsETL --> CFStorage
    InsightsETL --> CFSA
    CFSA --> FunctionInvoker
    CFSA --> CloudRunInvoker

%% BigQuery Datasets Architecture
    subgraph BigQuery Landing
        LandingDS[Landing Dataset]
    end
    subgraph BigQuery OA Curated
        OACuratedDS[OA Curated Dataset]
    end
    subgraph Service Accounts
        MockDataGen[Mock Data Generator]
        Operators[Operators Simple]
        Orders[Orders Simple]
        Inventory[Inventory Simple]
        EquipmentGen[Equipment Generator]
    end
    MockDataGen --> LandingDS
    MockDataGen --> OACuratedDS
    Operators --> LandingDS
    Operators --> OACuratedDS
    Orders --> LandingDS
    Orders --> OACuratedDS
    Inventory --> LandingDS
    Inventory --> OACuratedDS
    EquipmentGen --> LandingDS
    EquipmentGen --> OACuratedDS

%% BigQuery Dataset Configuration
    subgraph BigQuery Datasets
        Dataset[Dataset]
    end
    subgraph Service Accounts
        MockDataGen[Mock Data Gen]
        Operators[Operators Simple]
        Orders[Orders Simple]
        Inventory[Inventory Simple]
        EquipmentGen[Equipment Gen]
    end
    subgraph Project Owners
        DataOwner[Data Owner]
    end
    MockDataGen --> Dataset
    Operators --> Dataset
    Orders --> Dataset
    Inventory --> Dataset
    EquipmentGen --> Dataset
    DataOwner --> Dataset

%% GCP Network Infrastructure
    subgraph Network Services
        CloudRouter[Cloud Router]
        NATGateway[NAT Gateway]
    end
    subgraph Private Services
        PrivateRange[Private Range]
        VPCPeering[VPC Peering]
    end
    subgraph Serverless
        VPCConnector[VPC Access Connector]
    end
    CloudRouter --> NATGateway
    PrivateRange --> VPCPeering
    VPCPeering --> CloudRouter

%% Terraform Managed Google Cloud VPC
    subgraph VPC
        PublicSubnet01[Public Subnet 01]
        PrivateSubnet02[Private Subnet 02 PRIV]
        VPCConnector[VPC Serverless Connector 03]
    end
    InternetGateway[Internet Gateway]
    Firewall[Firewall]
    InternetGateway --> PublicSubnet01
    InternetGateway --> PrivateSubnet02
    InternetGateway --> VPCConnector
    Firewall --> PublicSubnet01
    Firewall --> PrivateSubnet02
    Firewall --> VPCConnector

%% Google Cloud PubSub to BigQuery Integration
    subgraph PubSub Subscriptions
        InsightsSub[Insights PubSub to BigQuery Subscription]
        ADISub[ADI PubSub to BigQuery Subscription]
    end
    subgraph Cloud Functions
        PubSubToBQ[PubSub to BigQuery]
        ADIFormatter[ADI Formatter to PubSub]
    end
    subgraph BigQuery
        InsightsDS[Insights Dataset]
        ADIDS[ADI Dataset]
    end
    subgraph Service Accounts
        CFSA[Cloud Function Service Account]
        ADICFSA[Cloud Function ADI Formatter Service Account]
    end
    InsightsSub --> PubSubToBQ
    ADISub --> ADIFormatter
    PubSubToBQ --> InsightsDS

%% Cloud Architecture for ADI Formatter to PubSub
    subgraph Cloud Functions
        ADIFormatter[ADI Formatter to PubSub]
    end
    subgraph Storage
        SourceCodeZIP[Source Code ZIP]
        CFSource[Cloud Function Source]
    end
    subgraph Service Accounts
        CFSA[Cloud Function Service Account]
    end
    subgraph PubSub
        PubSubTopic[PubSub Topic]
    end
    subgraph IAM
        FunctionInvoker[Function Invoker]
        CloudRunInvoker[Cloud Run Invoker]
    end
    SourceCodeZIP --> ADIFormatter
    CFSource --> ADIFormatter
    CFSA --> ADIFormatter
    ADIFormatter --> PubSubTopic
    FunctionInvoker --> ADIFormatter
    CloudRunInvoker --> ADIFormatter
```

<br>

> Exported using [Eraser.io](https://app.eraser.io)
![ETL Diagram](./etl-diagram-export.svg)
