import base64
import functions_framework
from google.cloud import pubsub_v1


PUBLISHER_PROJECT_ID = 'ict-d-etl'
# No schema, and no message retention, with google managed encryption key
PUBLISHER_TOPIC_NAME = 'from-insights'

# Initialize Publisher and Subscriber clients
publisher = pubsub_v1.PublisherClient()

try:
    topic_path = publisher.topic_path(PUBLISHER_PROJECT_ID, PUBLISHER_TOPIC_NAME)
    print(f"Publish topic_path={topic_path}")
except Exception as e:
    print(f"Error Error={e} topic_path={topic_path}")

# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def hello_pubsub(cloud_event):
    # Print out the data from Pub/Sub, to prove that it worked
    #print(base64.b64decode(cloud_event.data["message"]["data"]))
    try:
        data = base64.b64decode(cloud_event.data["message"]["data"])
        future = publisher.publish(topic_path, data)
        result = future.result()
    except Exception as e:
        print(f'Error=e, Failed to forward message from topic=insights-migration to project={PUBLISHER_PROJECT_ID}, path={topic_path} ')