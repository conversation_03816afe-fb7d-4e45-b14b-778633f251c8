---
stages:
  - build
  - validate
  - scan
  - deploy
  - docs
  - pages

include:
  - local: "/.gitlab/includes/terraform-operations.yml"
  # - local: "/.gitlab/includes/liquibase-operations.yml"
  - local: "/.gitlab/includes/tableau-operations.yml"
  - local: "/.gitlab/includes/base-jobs-templates.yml"
  - project: "dematic/controltower/ict-gitlab-templates"
    file: "Scanners/sonarqube-template.yml"
  - local: "/.gitlab/includes/veracode.gitlab-ci.yml"
  - local: "/.gitlab/includes/sast-base.gitlab-ci.yml"

variables:
  GITLAB_RUNNER: us-docker.pkg.dev/ict-o-registry/ict-o-registry/gitlab-runner:latest
  ARTIFACTORY_AUTH_TOKEN: $DEV_BLD_ARTIFACTORY_TOKEN
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  NODE_VERSION: "18.12"
  NPM_CONFIG_CACHE: "$CI_PROJECT_DIR/.npm"
  TF_VERSION: "1.7.4"
  TF_ROOT: ${CI_PROJECT_DIR}
  TF_STATE_NAME: default
  ICT_BRANCH_DEV: "dev"
  ICT_BRANCH_STAGE: "stage"
  ICT_BRANCH_PROD: "main"

default:
  image: $GITLAB_RUNNER
  tags:
    - gke-runners-dematic
  retry:
    max: 2
    when: runner_system_failure
    exit_codes: 137

# ---------------------------------------------------------------------------------------------
# IMPORTANT NOTE: The first rule condition that is met is what will be evaluated for the
# pipeline to run.  Once the condition is met, then we can use the variables to trigger what
# jobs are going to be executed in the pipeline.
#
# ict ui pipeline workflow
#
# ---------------------------------------------------------------------------------------------
workflow:
  rules:
    # ---------------------------------------------------------------------------------------------
    #  Control when a MR pipeline targeting the dev branch.
    # ---------------------------------------------------------------------------------------------
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $ICT_BRANCH_DEV && $CI_MERGE_REQUEST_STATE != "merged"
      when: always
      variables:
        IS_ICT_MERGE_REQUEST_PIPELINE: true
        IS_ICT_DEV_MERGE_REQUEST_PIPELINE: true
        ICT_ENVIRONMENT: dev
        ICT_DEPLOY_GCP_PROJECT_ID: "ict-d-etl"
        ICT_DEPLOY_AUTHO_CLIENT_SECRET: ${AUTH0_DEV_CLIENT_SECRET}
        ICT_DEPLOY_TABLEAU_PAT_NAME: ${TABLEAU_DEV_PAT_NAME}
        ICT_DEPLOY_TABLEAU_PAT_SECRET: ${TABLEAU_DEV_PAT_SECRET}
        ICT_DEPLOY_AUTH0_MQQT_CLIENT_SECRET: ${AUTH0_DEV_MGMT_CLIENT_SECRET}
    # ---------------------------------------------------------------------------------------------
    #  Control when dev branch pipelines run.
    # ---------------------------------------------------------------------------------------------
    - if: $CI_COMMIT_BRANCH == $ICT_BRANCH_DEV
      when: always
      variables:
        IS_ICT_DEPLOY_PIPELINE: true
        IS_ICT_DEPLOY_DEV_PIPELINE: true
        ICT_ENVIRONMENT: dev
        ICT_DEPLOY_GCP_PROJECT_ID: "ict-d-etl"
        ICT_DEPLOY_AUTHO_CLIENT_SECRET: ${AUTH0_DEV_CLIENT_SECRET}
        ICT_DEPLOY_TABLEAU_PAT_NAME: ${TABLEAU_DEV_PAT_NAME}
        ICT_DEPLOY_TABLEAU_PAT_SECRET: ${TABLEAU_DEV_PAT_SECRET}
        ICT_DEPLOY_AUTH0_MQQT_CLIENT_SECRET: ${AUTH0_DEV_MGMT_CLIENT_SECRET}
    # ---------------------------------------------------------------------------------------------
    #  Control when a MR pipeline targeting the stage branch.
    # ---------------------------------------------------------------------------------------------
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $ICT_BRANCH_STAGE && $CI_MERGE_REQUEST_STATE != "merged"
      when: always
      variables:
        IS_ICT_MERGE_REQUEST_PIPELINE: true
        ICT_ENVIRONMENT: stage
        ICT_DEPLOY_GCP_PROJECT_ID: "ict-s-us-east1-etl"
        ICT_DEPLOY_AUTHO_CLIENT_SECRET: ${AUTH0_STAGE_CLIENT_SECRET}
        ICT_DEPLOY_TABLEAU_PAT_NAME: ${TABLEAU_STAGE_PAT_NAME}
        ICT_DEPLOY_TABLEAU_PAT_SECRET: ${TABLEAU_STAGE_PAT_SECRET}
        ICT_DEPLOY_AUTH0_MQQT_CLIENT_SECRET: ${AUTH0_STAGE_MGMT_CLIENT_SECRET}
    # ---------------------------------------------------------------------------------------------
    #  Control when stage branch pipelines run.
    # ---------------------------------------------------------------------------------------------
    - if: $CI_COMMIT_BRANCH == $ICT_BRANCH_STAGE
      when: always
      variables:
        IS_ICT_DEPLOY_PIPELINE: true
        IS_ICT_DEPLOY_STAGE_PIPELINE: true
        ICT_ENVIRONMENT: stage
        ICT_DEPLOY_GCP_PROJECT_ID: "ict-s-us-east1-etl"
        ICT_DEPLOY_AUTHO_CLIENT_SECRET: ${AUTH0_STAGE_CLIENT_SECRET}
        ICT_DEPLOY_TABLEAU_PAT_NAME: ${TABLEAU_STAGE_PAT_NAME}
        ICT_DEPLOY_TABLEAU_PAT_SECRET: ${TABLEAU_STAGE_PAT_SECRET}
        ICT_DEPLOY_AUTH0_MQQT_CLIENT_SECRET: ${AUTH0_STAGE_MGMT_CLIENT_SECRET}
    # ---------------------------------------------------------------------------------------------
    #  Control when a MR pipeline targeting the main/prod branch.
    # ---------------------------------------------------------------------------------------------
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $ICT_BRANCH_PROD && $CI_MERGE_REQUEST_STATE != "merged"
      when: always
      variables:
        IS_ICT_MERGE_REQUEST_PIPELINE: true
        ICT_ENVIRONMENT: prod
        ICT_DEPLOY_GCP_PROJECT_ID: "ict-p-us-east1-etl"
        ICT_DEPLOY_AUTHO_CLIENT_SECRET: ${AUTH0_PROD_CLIENT_SECRET}
        ICT_DEPLOY_TABLEAU_PAT_NAME: ${TABLEAU_PROD_PAT_NAME}
        ICT_DEPLOY_TABLEAU_PAT_SECRET: ${TABLEAU_PROD_PAT_SECRET}
        ICT_DEPLOY_AUTH0_MQQT_CLIENT_SECRET: ${AUTH0_PROD_MGMT_CLIENT_SECRET}
    #  Control when main production branch pipelines run.
    # ---------------------------------------------------------------------------------------------
    - if: $CI_COMMIT_BRANCH == $ICT_BRANCH_PROD
      when: always
      variables:
        IS_ICT_DEPLOY_PIPELINE: true
        IS_ICT_DEPLOY_PROD_PIPELINE: true
        ICT_ENVIRONMENT: prod
        ICT_DEPLOY_GCP_PROJECT_ID: "ict-p-us-east1-etl"
        ICT_DEPLOY_AUTHO_CLIENT_SECRET: ${AUTH0_PROD_CLIENT_SECRET}
        ICT_DEPLOY_TABLEAU_PAT_NAME: ${TABLEAU_PROD_PAT_NAME}
        ICT_DEPLOY_TABLEAU_PAT_SECRET: ${TABLEAU_PROD_PAT_SECRET}
        ICT_DEPLOY_AUTH0_MQQT_CLIENT_SECRET: ${AUTH0_PROD_MGMT_CLIENT_SECRET}
    # ---------------------------------------------------------------------------------------------
    #  Stop any commit pipeline when there is an open merge request.
    # ---------------------------------------------------------------------------------------------
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    # ---------------------------------------------------------------------------------------------
    #  If none of the conditions were met, do not run a pipeline
    # ---------------------------------------------------------------------------------------------
    - when: never

# -------------------------------------------------------------------------------------------------
#                                         .pre stage jobs
# -------------------------------------------------------------------------------------------------

etl:workflow:
  stage: .pre
  script:
    # gitlab pipeline variables...
    - echo " ---- gitlab pipeline variables ----"
    - echo $CI_PIPELINE_SOURCE
    - echo $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    - echo $CI_COMMIT_BRANCH
    - echo $CI_COMMIT_DESCRIPTION
    - echo $CI_ENVIRONMENT_ACTION
    - echo $CI_MERGE_REQUEST_EVENT_TYPE
    - echo $CI_MERGE_REQUEST_APPROVED
    - echo $CI_MERGE_REQUEST_SOURCE_BRANCH_PROTECTED
    - echo $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - echo " ----- workflow configuration -----"
    # merge request pipeline...
    - echo $ICT_ENVIRONMENT
    - echo $IS_ICT_MERGE_REQUEST_PIPELINE
    - echo $IS_ICT_DEV_MERGE_REQUEST_PIPELINE
    - echo $IS_ICT_DEPLOY_PIPELINE
    - echo $IS_ICT_DEPLOY_DEV_PIPELINE
    - echo $IS_ICT_DEPLOY_STAGE_PIPELINE
    - echo $IS_ICT_DEPLOY_PROD_PIPELINE
    - echo $ICT_DEPLOY_GCP_PROJECT_ID
    - echo $ICT_DEPLOY_TABLEAU_PAT_NAME
    - echo "checking required secretes"
    - |
      if [ -z "$ICT_DEPLOY_AUTHO_CLIENT_SECRET" ]; then
      echo "false"
      else
      echo "true"
      fi
    - |
      if [ -z "$ICT_DEPLOY_TABLEAU_PAT_SECRET" ]; then
      echo "false"
      else
      echo "true"
      fi
    - |
      if [ -z "$ICT_DEPLOY_AUTH0_MQQT_CLIENT_SECRET" ]; then
      echo "false"
      else
      echo "true"
      fi
  tags:
    - gke-runners-dematic

# -------------------------------------------------------------------------------------------------
#                                         build stage jobs
# -------------------------------------------------------------------------------------------------

etl:docker-build:
  extends: .auth_base_job
  stage: build
  services:
    - name: docker:27.3.1-dind
  script:
    - cd ${CI_PROJECT_DIR}/infrastructure/
    - ./build-common-docker-images.sh
  rules:
    - if: $IS_ICT_MERGE_REQUEST_PIPELINE
    - if: $IS_ICT_DEPLOY_PIPELINE

# -------------------------------------------------------------------------------------------------
#                                         scan stage jobs
# -------------------------------------------------------------------------------------------------

etl:sonar-cube:
  needs: []
  extends:
    - .sonar-qube_base_job
  stage: scan
  rules:
    - if: $IS_ICT_DEV_MERGE_REQUEST_PIPELINE

# -------------------------------------------------------------------------------------------------
#                                         metric processor docs stage jobs
# -------------------------------------------------------------------------------------------------

docs:
  stage: docs
  image: python:3.11
  before_script:
    - cd ./cloudrun/ict-metric-processor
    - python -m pip install --upgrade pip
    - pip install poetry
    - poetry install --with docs
  script:
    - poetry run mkdocs build -d public
  artifacts:
    paths:
      - cloudrun/ict-metric-processor/public
    expire_in: 30 days
  rules:
    - if: $CI_COMMIT_BRANCH == $ICT_BRANCH_DEV # Trigger for the dev branch

pages:
  stage: pages
  script:
    - mv ./cloudrun/ict-metric-processor/public ./public
    - echo "Deploying to GitLab Pages"
  artifacts:
    paths:
      - public
    expire_in: 30 days
  rules:
    - if: $CI_COMMIT_BRANCH == $ICT_BRANCH_DEV # Trigger for the dev branch
