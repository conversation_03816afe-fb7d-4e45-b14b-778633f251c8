import argparse
import os
import importlib.util
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import pandas as pd
from datetime import datetime, timedelta, timezone
import json

# Configurable defaults
DEFAULT_BASE_CONFIG_DIR = "./cloudrun/ict-metric-processor/src/config"
DEFAULT_OUTPUT_DIR = "./results"
PROJECT_ID = "edp-d-us-east2-etl"
DEFAULT_FACILITY = "superioruniform_eudoraar"
DEFAULT_TENANT = "superior_uniform"
DEFAULT_FACT_TYPE = None
DEFAULT_METRIC = None
DEFAULT_AREA = None


def load_metrics_from_python_file(config_path):
    spec = importlib.util.spec_from_file_location("config_module", config_path)
    config_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(config_module)

    if not hasattr(config_module, "metrics"):
        raise ValueError(
            f"Configuration file {config_path} is missing a 'metrics' object."
        )

    return config_module.metrics


def generate_query_for_metric(
    project_id, tenant, facility, fact_type, metric_name, metric_config
):
    # Include the project ID in the table name
    table_name = f"`{project_id}.{tenant}.silver_{fact_type}`"
    match_conditions = metric_config.get("match_conditions", {})

    # Collect columns to be selected from match_conditions and add tenant_id, __raw_message_ingestion_time
    select_columns = (
        ", ".join(match_conditions.keys()) + ", tenant_id, __raw_message_ingestion_time"
    )

    # Convert the match conditions to WHERE conditions using REGEXP_CONTAINS
    where_clauses = []
    for column, regex in match_conditions.items():
        where_clauses.append(f"REGEXP_CONTAINS(CAST({column} AS STRING), r'{regex}')")

    # Add condition to filter by the last 15 minutes using the __raw_message_ingestion_time column
    fifteen_minutes_ago = (datetime.utcnow() - timedelta(minutes=15)).isoformat()
    where_clauses.append(f"__raw_message_ingestion_time >= '{fifteen_minutes_ago}'")

    # Combine all conditions into a single WHERE clause
    where_clause = " AND ".join(where_clauses)

    # Generate the final SQL query with only the selected columns and a LIMIT
    query = (
        f"SELECT {select_columns} FROM {table_name} WHERE {where_clause} LIMIT 1000;"
    )

    return query


def get_fact_types_from_directory(facility_dir):
    # Exclude __init__.py and other irrelevant files
    fact_types = [
        os.path.splitext(file)[0]
        for file in os.listdir(facility_dir)
        if file.endswith(".py") and file != "__init__.py"
    ]

    if not fact_types:
        raise ValueError(
            f"No fact types found in {facility_dir}. Please check the directory structure."
        )

    return fact_types


def execute_queries_and_save_to_ndjson(
    queries, client, output_dir, tenant, facility, run_id
):
    # Create a folder for NDJSON files inside the run_id folder
    run_folder = os.path.join(output_dir, tenant, facility, run_id)
    json_folder = os.path.join(run_folder)

    os.makedirs(json_folder, exist_ok=True)

    for fact_type, metric, query in queries:
        try:
            print(f"Executing query for fact_type={fact_type}, metric={metric}")
            query_job = client.query(query)
            df = query_job.to_dataframe()

            # Convert any timestamp columns to strings (ISO 8601 format)
            for column in df.select_dtypes(
                include=["datetime64[ns]", "datetime64[ns, UTC]", "object"]
            ):
                if "time" in column.lower():
                    df[column] = df[column].astype(str)

            # Prepare NDJSON file path
            ndjson_output_file = os.path.join(json_folder, f"{fact_type}_{metric}.json")

            # Write the DataFrame to newline-delimited JSON
            with open(ndjson_output_file, "w") as json_file:
                for record in df.to_dict(orient="records"):
                    json_file.write(
                        json.dumps(record) + "\n"
                    )  # Write each record on a new line

            print(f"Results for fact_type {fact_type} saved to {ndjson_output_file}")

        except NotFound:
            print(f"Table not found for fact_type={fact_type}. Skipping.")
        except Exception as e:
            print(
                f"Error executing query for fact_type={fact_type}, metric={metric}: {str(e)}"
            )


def generate_queries(
    base_config_dir,
    project_id,
    tenant,
    facility,
    fact_type=None,
    metric=None,
    areas=None,
):
    facility_dir = os.path.join(base_config_dir, tenant, facility)
    fact_types = (
        [fact_type] if fact_type else get_fact_types_from_directory(facility_dir)
    )

    all_queries = []
    for fact_type in fact_types:
        config_path = os.path.join(facility_dir, f"{fact_type}.py")
        metrics = load_metrics_from_python_file(config_path)

        for metric_name, metric_config in metrics.items():
            # If areas are specified, filter metrics by areas (the first part of the metric name)
            if areas and not any(metric_name.startswith(area) for area in areas):
                continue

            # If a specific metric is provided, match only that metric
            if metric and metric_name != metric:
                continue

            query = generate_query_for_metric(
                project_id, tenant, facility, fact_type, metric_name, metric_config
            )
            all_queries.append((fact_type, metric_name, query))

    if not all_queries:
        raise ValueError("No valid queries generated. Please check your configuration.")

    return all_queries


if __name__ == "__main__":
    print(f"Current working directory: {os.getcwd()}")
    # Set up argument parsing with command-line arguments
    parser = argparse.ArgumentParser(
        description="Generate and execute BigQuery SQL queries based on a config file."
    )
    parser.add_argument(
        "--base_config_dir",
        type=str,
        default=DEFAULT_BASE_CONFIG_DIR,
        help=f"Base config directory (default: {DEFAULT_BASE_CONFIG_DIR})",
    )
    parser.add_argument(
        "--tenant",
        type=str,
        default=DEFAULT_TENANT,
        help=f"Tenant (BigQuery dataset) (default: {DEFAULT_TENANT})",
    )
    parser.add_argument(
        "--facility",
        type=str,
        default=DEFAULT_FACILITY,
        help=f"Facility name (default: {DEFAULT_FACILITY})",
    )
    parser.add_argument(
        "--fact_type",
        type=str,
        default=DEFAULT_FACT_TYPE,
        help="Fact type (optional, defaults to all in the facility)",
    )
    parser.add_argument(
        "--metric",
        type=str,
        default=DEFAULT_METRIC,
        help="Metric name (optional, defaults to all)",
    )
    parser.add_argument(
        "--areas",
        type=str,
        default=DEFAULT_AREA,
        help="Comma-separated list of area names to filter metrics by (optional, e.g., 'multishuttle,workstations')",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default=DEFAULT_OUTPUT_DIR,
        help=f"Directory to save the JSON files (default: {DEFAULT_OUTPUT_DIR})",
    )

    args = parser.parse_args()

    # Notify if using defaults
    if args.tenant == DEFAULT_TENANT:
        print(f"No tenant provided. Defaulting to tenant '{DEFAULT_TENANT}'.")
    if args.facility == DEFAULT_FACILITY:
        print(f"No facility provided. Defaulting to facility '{DEFAULT_FACILITY}'.")

    # Parse the comma-separated areas list into a Python list
    areas = args.areas.split(",") if args.areas else None

    run_id = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    client = bigquery.Client(project=PROJECT_ID)

    try:
        # Areas are optional, and if not provided, all areas will be included.
        queries = generate_queries(
            args.base_config_dir,
            PROJECT_ID,
            args.tenant,
            args.facility,
            args.fact_type,
            args.metric,
            areas,
        )
        execute_queries_and_save_to_ndjson(
            queries, client, args.output_dir, args.tenant, args.facility, run_id
        )
    except Exception as e:
        print(f"Error: {str(e)}")
