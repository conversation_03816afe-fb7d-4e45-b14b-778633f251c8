--declare tab string default 'WMSInventoryFact';
-- delete dedupe tables if need to rebuild
/*
begin
  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id like 'dedupe%')
  do
    execute immediate format("""drop table if exists `superior_uniform_landing.%s`;""", tab.table_id);
  end for;
end;
*/
/* Option 1 Rename and swap - needs to have streaming buffer clear*/
-- rename original
/*
begin
  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id='WMSInventoryFact')
  do
    execute immediate format("""alter table `superior_uniform_landing.%s` rename to `original_%s`;""", tab.table_id, tab.table_id);
  end for;
end;

-- rename deduplicated table to proper naming
begin
  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id='WMSInventoryFact')
  do
    execute immediate format("""alter table `superior_uniform_landing.dedupe_%s` rename to `%s`;""", tab.table_id, tab.table_id);
  end for;
end;
*/

/*
Option 2
CLONE, TRUNCATE and INSERT
*/
-- CLONE landing table to original_*
begin
  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id not like 'dedupe%' and table_id not like 'original%' and table_id not like 'UnknownFact')
  do
    execute immediate format("""CREATE TABLE `superior_uniform_landing.original_%s` CLONE `superior_uniform_landing.%s`;""", tab.table_id, tab.table_id);
  end for;
end;
-- TRUNCATE original landing table
begin
  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id not like 'dedupe%' and table_id not like 'original%' and table_id not like 'UnknownFact')
  do
    execute immediate format("""TRUNCATE TABLE `superior_uniform_landing.%s`;""", tab.table_id);
  end for;
end;
-- COPY from dedupe table
begin
  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id not like 'dedupe%' and table_id not like 'original%' and table_id not like 'UnknownFact')
  do
    execute immediate format("""INSERT INTO `superior_uniform_landing.%s` SELECT * FROM `superior_uniform_landing.dedupe_%s` order by ingestion_date desc;""", tab.table_id, tab.table_id);
  end for;
end;

/* ======================== AGGRAGATION TABLE CLEAN ==================================== */
-- TRUNCATE agg landing table
begin
  for tab in (select table_id from superior_uniform_oa_curated.__TABLES__ where table_id like 'agg%' or table_id like 'hst%')
  do
    execute immediate format("""TRUNCATE TABLE `superior_uniform_oa_curated.%s`;""", tab.table_id);
  end for;
end;


/* ======================== CLEAN-UP ==================================== */
/*
-- delete dedupe tables
begin
  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id like '%dedupe%')
  do
    execute immediate format("""drop table if exists `superior_uniform_landing.%s`;""", tab.table_id);
  end for;
end;
*/
/*
-- delete original tables
begin
--  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id like '%original%')
  do
--    execute immediate format("""drop table if exists `superior_uniform_landing.%s`;""", tab.table_id);
  end for;
end;
*/

/* =========================== REVERT ==================================== */
/*
-- TRUNCATE original landing table
begin
  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id not like 'dedupe%' and table_id not like 'original%' and table_id not like 'UnknownFact')
  do
    execute immediate format("""TRUNCATE TABLE `superior_uniform_landing.%s`;""", tab.table_id);
  end for;
end;
-- COPY from ORIGINAL table
begin
  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id not like 'dedupe%' and table_id not like 'original%' and table_id not like 'UnknownFact')
  do
    execute immediate format("""INSERT INTO `superior_uniform_landing.%s` SELECT * FROM `superior_uniform_landing.original_%s` order by ingestion_date desc;""", tab.table_id, tab.table_id);
  end for;
end;
*/


