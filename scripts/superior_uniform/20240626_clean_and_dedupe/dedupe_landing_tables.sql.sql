/*
Step 0: Update pub/sub subscription, rename CF endpoint to something nonexistant, e.g. cf-from-insights-pubsub-to-bigquery2
Step 1: Loop through the landing tables and creating dedupe_* tables
        dedupe based on contents of json data
        remove any ict_development entries
        update facility, source system to 'superioruniform_eudoraar', 'superioruniform_eudoraar_diq'
Step 2: Rename all existing tables to orig_*
Step 3: Rename all dedupe tables, removing dedupe_ to now be the new landing tables
Step 4: Update pub/sub subscription, rename CF endpoint to correct URL: cf-from-insights-pubsub-to-bigquery
Step 5: After acceptance: delete orig_* tables
*/
declare sql string;
begin

  for tab in (select table_id from superior_uniform_landing.__TABLES__ where table_id not like 'UnknownFact' and  table_id not like 'dedupe%' and table_id not like 'original%' order by table_id)
  do
    set sql = FORMAT(""" 
    create table `superior_uniform_landing.dedupe_%s`
    (
      tenant STRING NOT NULL,
      facility STRING NOT NULL,
      source_system STRING NOT NULL,
      data JSON NOT NULL,
      ingestion_date TIMESTAMP NOT NULL,
      pubsub_message_id STRING,
      message_hash STRING,
      row_hash STRING,
      x_cloud_trace_context STRING,
      source_ip STRING,
      attributes JSON,
      metadata JSON
    )
    PARTITION BY TIMESTAMP_TRUNC(ingestion_date, DAY)
    CLUSTER BY tenant, facility, source_system
    AS
    with dedupe as (
      SELECT 
      'superior_uniform' as tenant,
      'superioruniform_eudoraar' as facility,
      'superioruniform_eudoraar_diq' as source_system,
      to_json_string(data) as str_data,
      max(ingestion_date) as ingestion_date,
      max(pubsub_message_id) as pubsub_message_id,
      max(message_hash) as message_hash,
      max(row_hash) as row_hash,
      max(x_cloud_trace_context) as x_cloud_trace_context,
      max(source_ip) as source_ip,
      max(to_json_string(attributes)) as str_attributes,
      max(to_json_string(metadata)) as str_metadata
      FROM `superior_uniform_landing.%s`
      where source_system <> 'ict_development' 
      group by to_json_string(data)
      )
    select 
    tenant,
    facility,
    source_system,
    parse_json(str_data) as data,
    ingestion_date,
    pubsub_message_id,
    message_hash,
    row_hash,
    x_cloud_trace_context,
    source_ip,
    parse_json(str_attributes) as attributes,
    parse_json(str_metadata) as metadata
    from dedupe;
    """, tab.table_id, tab.table_id);

    execute immediate sql;
  end for;
end;
