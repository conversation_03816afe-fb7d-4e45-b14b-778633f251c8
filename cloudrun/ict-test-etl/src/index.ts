import {Storage} from '@google-cloud/storage';
import express from 'express';
import {createClient} from 'redis';
import isPortReachable from 'is-port-reachable';

const bucketName = process.env.BUCKETNAME
  ? process.env.BUCKETNAME
  : 'mockapidata';

// Initialize storage client
const storage = new Storage();

const app = express();

const REDISHOST = process.env.REDISHOST ? process.env.REDISHOST : 'localhost';
const REDISPORT = (process.env.REDISPORT as unknown as number)
  ? (process.env.REDISPORT as unknown as number)
  : 6379;
const REDISAUTHSTRING = process.env.REDISAUTHSTRING
  ? process.env.REDISAUTHSTRING
  : '';

const urlPath = `redis://default:${REDISAUTHSTRING}@${REDISHOST}:${REDISPORT}`;

isPortReachable(REDISPORT, {host: REDISHOST})
  .then(isReachable =>
    console.log(
      'Is redis reachable : ' +
        isReachable +
        'Port = ' +
        REDISPORT +
        ', Host = ' +
        REDISHOST
    )
  )
  .catch(err =>
    console.error(err + 'No! Port: ' + REDISPORT + 'Host: ' + REDISHOST)
  );

const redisClient = createClient({
  url: urlPath,
});
// eslint-disable-next-line no-console
redisClient.on('error', err => console.log('Redis Client Error', err));

app.get('/*', async (req: express.Request, res: express.Response) => {
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET');
  try {
    // Acquire info about request
    const caller = req.header('x-forwarded-for') || req.socket.remoteAddress;
    const caching = req.query.cache === 'false' ? false : true;
    let requestPath = req.path.slice(1, req.path.length);
    if (requestPath.charAt(requestPath.length - 1) !== '/') {
      requestPath = requestPath + '/';
    }
    console.debug('Request path is ' + requestPath);

    // Default file name
    let fileName = 'test.json';
    const fileListing: string[] = [];

    //Acquire file listing from bucket and add file name to array.
    const options = {
      prefix: requestPath,
    };
    console.debug('I am going to attempt to connect to: ' + bucketName);
    const [files] = await storage.bucket(bucketName).getFiles(options);
    files.forEach(file => {
      const storageName = file.name.replace(requestPath, '');
      // Filter out directories within the requested directoy - they're not files
      if (storageName.length > 0 && storageName.indexOf('/') === -1) {
        fileListing.push(storageName);
      }
    });

    console.debug('Files are ' + fileListing);

    // If there's no ipAddress or no more than one file available, serve the default file and escape early
    if (!caching || !caller || fileListing.length <= 1) {
      if (requestPath.length > 1) {
        fileName = requestPath + fileName;
      }
    } else {
      await redisClient.connect();

      const serverTime = await redisClient.time();
      // 2022-02-25T12:57:40.000Z { microseconds: 351346 }
      console.log(serverTime);

      if (redisClient.isReady) {
        console.debug('Connected to redis.');
      } else {
        console.debug('Redis not connected.');
      }

      // Check redis for entry for caller with request path;
      const findCallerWithPath = await redisClient.hExists(caller, requestPath);

      // If there's no request history, add entry for request
      if (!findCallerWithPath) {
        await redisClient.hSet(caller, requestPath, fileListing[0]);
      } else {
        const lastFileName = await redisClient.hGet(caller, requestPath);
        console.debug('Last file served for caller was ' + lastFileName);

        if (lastFileName) {
          let index = fileListing.indexOf(lastFileName);
          if (index === fileListing.length - 1) {
            index = 0;
          } else {
            index += 1;
          }
          fileName = fileListing[index];
        }
      }

      console.debug('Returned file ' + fileName);
      // Update redis file served listing and return
      await redisClient.hSet(caller, requestPath, fileName);
      await redisClient.quit();
      fileName = requestPath + fileName;
    }

    const contents = await storage.bucket(bucketName).file(fileName).download();
    res.status(200).send(contents.toString());
  } catch (error: unknown) {
    if (redisClient.isOpen) {
      await redisClient.quit();
    }
    console.error(error);
    res
      .status(500)
      .send({success: false, error: 'Error retrieving mocked data'});
  }
});

// this is hardcoded to 8080 as it is matching up with the Dockerfile
const port = 8080;
const server = app.listen(port, () =>
  console.log(`Mock Data API listening on local port ${port}.`)
);

export default app;
export {server};
