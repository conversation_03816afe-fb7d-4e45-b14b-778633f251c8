{"name": "mock-data-api", "version": "1.0.0", "description": "A quick and dirty cloudrun function that returns a json file from a storage bucket based on the url path", "author": "Dematic", "license": "ISC", "main": "build/index.js", "engines": {"node": ">=18.0.0"}, "scripts": {"test": "mocha ./src/test/**/*.spec.ts", "test:watch": "mocha --watch", "start": "nodemon", "lint": "npx eslint-config-prettier src/index.ts && gts lint --fix", "clean": "gts clean", "compile": "tsc", "gcp-build": "yarn build", "build": "tsc", "fix": "gts fix", "prepare": "yarn compile", "pretest": "yarn compile", "posttest": "yarn lint", "debug": "nodemon --inspect=0.0.0.0", "watch": "concurrently \"npm run compile -- --watch\" \"nodemon --inspect=0.0.0.0 --watch ./src/\""}, "dependencies": {"@google-cloud/storage": "^6.11.0", "@types/chai": "^4.3.5", "@types/express": "^4.17.17", "chai": "^4.3.7", "express": "^4.18.2", "is-port-reachable": "^4.0.0", "nodemon": "^2.0.22", "redis": "^4.6.5", "sinon": "^15.0.4", "ts-node": "^10.9.1"}, "devDependencies": {"@babel/preset-typescript": "^7.21.5", "@types/mocha": "^10.0.1", "@types/node": "^14.11.2", "@types/sinon": "^10.0.14", "concurrently": "^8.0.1", "eslint-config-prettier": "^8.8.0", "gts": "^3.1.1", "mocha": "^10.2.0", "typescript": "^5.0.4"}}