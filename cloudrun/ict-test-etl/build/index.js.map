{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,mDAA8C;AAC9C,sDAA8B;AAC9B,iCAAmC;AAEnC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU;IACvC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU;IACxB,CAAC,CAAC,aAAa,CAAC;AAElB,4BAA4B;AAC5B,MAAM,OAAO,GAAG,IAAI,iBAAO,EAAE,CAAC;AAE9B,MAAM,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;AAEtB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC;AAC9E,MAAM,SAAS,GAAI,OAAO,CAAC,GAAG,CAAC,SAA+B;IAC5D,CAAC,CAAE,OAAO,CAAC,GAAG,CAAC,SAA+B;IAC9C,CAAC,CAAC,IAAI,CAAC;AAET,MAAM,OAAO,GAAG,WAAW,SAAS,IAAI,SAAS,EAAE,CAAC;AAEpD,MAAM,WAAW,GAAG,IAAA,oBAAY,EAAC;IAC/B,GAAG,EAAE,OAAO;CACb,CAAC,CAAC;AACH,sCAAsC;AACtC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC,CAAC;AAEvE,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAO,GAAoB,EAAE,GAAqB,EAAE,EAAE;IAClE,GAAG,CAAC,GAAG,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;IAC5C,GAAG,CAAC,GAAG,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;IAC/C,IAAI;QACF,6BAA6B;QAC7B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC;QACzE,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3D,IAAI,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrD,IAAI,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YACtD,WAAW,GAAG,WAAW,GAAG,GAAG,CAAC;SACjC;QACD,OAAO,CAAC,KAAK,CAAC,kBAAkB,GAAG,WAAW,CAAC,CAAC;QAEhD,oBAAoB;QACpB,IAAI,QAAQ,GAAG,WAAW,CAAC;QAC3B,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,8DAA8D;QAC9D,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,WAAW;SACpB,CAAC;QACF,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACnE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YACvD,2EAA2E;YAC3E,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC7D,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAC/B;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,WAAW,CAAC,CAAC;QAE1C,sGAAsG;QACtG,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE;YAClD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;aACnC;SACF;aAAM;YACL,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;YAE5B,IAAI,WAAW,CAAC,OAAO,EAAE;gBACvB,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;aACtC;iBAAM;gBACL,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;aACvC;YAED,sDAAsD;YACtD,MAAM,kBAAkB,GAAG,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAE1E,uDAAuD;YACvD,IAAI,CAAC,kBAAkB,EAAE;gBACvB,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7D;iBAAM;gBACL,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACjE,OAAO,CAAC,KAAK,CAAC,kCAAkC,GAAG,YAAY,CAAC,CAAC;gBAEjE,IAAI,YAAY,EAAE;oBAChB,IAAI,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;oBAC9C,IAAI,KAAK,KAAK,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;wBACpC,KAAK,GAAG,CAAC,CAAC;qBACX;yBAAM;wBACL,KAAK,IAAI,CAAC,CAAC;qBACZ;oBACD,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;iBAC/B;aACF;YAED,OAAO,CAAC,KAAK,CAAC,gBAAgB,GAAG,QAAQ,CAAC,CAAC;YAC3C,8CAA8C;YAC9C,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;YACtD,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;YACzB,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;SACnC;QAED,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5E,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;KAC3C;IAAC,OAAO,KAAc,EAAE;QACvB,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;SAC1B;QACD,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,GAAG;aACA,MAAM,CAAC,GAAG,CAAC;aACX,IAAI,CAAC,EAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,8BAA8B,EAAC,CAAC,CAAC;KAClE;AACH,CAAC,CAAA,CAAC,CAAC;AAEH,qEAAqE;AACrE,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,CACnC,OAAO,CAAC,GAAG,CAAC,yCAAyC,IAAI,GAAG,CAAC,CAC9D,CAAC;AAGM,wBAAM;AADd,kBAAe,GAAG,CAAC"}