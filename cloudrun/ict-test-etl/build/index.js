"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.server = void 0;
const storage_1 = require("@google-cloud/storage");
const express_1 = __importDefault(require("express"));
const redis_1 = require("redis");
const bucketName = process.env.BUCKETNAME
    ? process.env.BUCKETNAME
    : 'mockapidata';
// Initialize storage client
const storage = new storage_1.Storage();
const app = (0, express_1.default)();
const REDISHOST = process.env.REDISHOST ? process.env.REDISHOST : 'localhost';
const REDISPORT = process.env.REDISPORT
    ? process.env.REDISPORT
    : 6379;
const urlPath = `redis://${REDISHOST}:${REDISPORT}`;
const redisClient = (0, redis_1.createClient)({
    url: urlPath,
});
// eslint-disable-next-line no-console
redisClient.on('error', err => console.log('Redis Client Error', err));
app.get('/*', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET');
    try {
        // Acquire info about request
        const caller = req.header('x-forwarded-for') || req.socket.remoteAddress;
        const caching = req.query.cache === 'false' ? false : true;
        let requestPath = req.path.slice(1, req.path.length);
        if (requestPath.charAt(requestPath.length - 1) !== '/') {
            requestPath = requestPath + '/';
        }
        console.debug('Request path is ' + requestPath);
        // Default file name
        let fileName = 'test.json';
        const fileListing = [];
        //Acquire file listing from bucket and add file name to array.
        const options = {
            prefix: requestPath,
        };
        const [files] = yield storage.bucket(bucketName).getFiles(options);
        files.forEach(file => {
            const storageName = file.name.replace(requestPath, '');
            // Filter out directories within the requested directoy - they're not files
            if (storageName.length > 0 && storageName.indexOf('/') === -1) {
                fileListing.push(storageName);
            }
        });
        console.debug('Files are ' + fileListing);
        // If there's no ipAddress or no more than one file available, serve the default file and escape early
        if (!caching || !caller || fileListing.length <= 1) {
            if (requestPath.length > 1) {
                fileName = requestPath + fileName;
            }
        }
        else {
            yield redisClient.connect();
            if (redisClient.isReady) {
                console.debug('Connected to redis.');
            }
            else {
                console.debug('Redis not connected.');
            }
            // Check redis for entry for caller with request path;
            const findCallerWithPath = yield redisClient.hExists(caller, requestPath);
            // If there's no request history, add entry for request
            if (!findCallerWithPath) {
                yield redisClient.hSet(caller, requestPath, fileListing[0]);
            }
            else {
                const lastFileName = yield redisClient.hGet(caller, requestPath);
                console.debug('Last file served for caller was ' + lastFileName);
                if (lastFileName) {
                    let index = fileListing.indexOf(lastFileName);
                    if (index === fileListing.length - 1) {
                        index = 0;
                    }
                    else {
                        index += 1;
                    }
                    fileName = fileListing[index];
                }
            }
            console.debug('Returned file ' + fileName);
            // Update redis file served listing and return
            yield redisClient.hSet(caller, requestPath, fileName);
            yield redisClient.quit();
            fileName = requestPath + fileName;
        }
        const contents = yield storage.bucket(bucketName).file(fileName).download();
        res.status(200).send(contents.toString());
    }
    catch (error) {
        if (redisClient.isOpen) {
            yield redisClient.quit();
        }
        console.error(error);
        res
            .status(500)
            .send({ success: false, error: 'Error retrieving mocked data' });
    }
}));
// this is hardcoded to 8080 as it is matching up with the Dockerfile
const port = 8080;
const server = app.listen(port, () => console.log(`Mock Data API listening on local port ${port}.`));
exports.server = server;
exports.default = app;
//# sourceMappingURL=index.js.map