# this Dockerfile does expect to be run in the root folder of the repo as it needs to copy the foundation files that it depends on
FROM node:18-bullseye

# Zscaler Certificate. Required for Pulse Secure and Zscaler connections to artifactory
# This double-installs it to ensure full coverage    <<< LOCAL DEVELOPMENT STUB >>>
COPY /certs/ZscalerRootCertificate-Win-2048-SHA256.crt /usr/local/share/ca-certificates/ZscalerRootCertificate-Win-2048-SHA256.crt

# Update packages and install curl and ca-certificates in one RUN command to reduce layers
RUN cat /usr/local/share/ca-certificates/ZscalerRootCertificate-Win-2048-SHA256.crt >> /etc/ssl/certs/ca-certificates.crt && \
    apt-get update && \
    apt-get install -y curl ca-certificates && \
    update-ca-certificates && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /usr/app

COPY ./ ./cloudrun/ict-etl-test/

WORKDIR /usr/app/cloudrun/ict-etl-test

EXPOSE 8080

RUN chmod +x ./entry.sh
CMD ["./entry.sh"]