#!/bin/bash

# Source .env file if it exists
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
fi

# Convert ENV variable to lowercase if it exists
if [[ $ENV ]]; then
  ENV=$(echo "$ENV" | tr '[:upper:]' '[:lower:]')
fi

# Set default port if not provided
PORT=${PORT:-8080}

# If ENV is not set or is set to "dev", run the application in debugging mode
if [[ -z "$ENV" || "$ENV" = "dev" ]]; then
    echo "debugging..."
    gunicorn --bind 0.0.0.0:$PORT  --timeout 120 "src.main:app"
else 
    # For any other environment, run the application normally
    gunicorn --bind 0.0.0.0:$PORT "src.main:app"
fi
