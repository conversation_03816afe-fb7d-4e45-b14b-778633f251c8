import json
import os
import time
import logging
import redis
import structlog

logging = structlog.get_logger()


class MetricService:
    """A processor for scanning metrics stored in Redis by Tenant/Facility and performing the more complex calculations needed by some metrics."""

    # Shared class-level dictionary to hold configurations
    config_cache = {}
    _instance = None
    redis_client = None

    def __new__(cls, *args, **kwargs):
        logging.debug("Metric Service: New instance created.")
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            host = os.getenv("REDIS_HOST", "localhost")
            port = int(os.getenv("REDIS_PORT", "6379"))
            db = int(os.getenv("REDIS_DB", "0"))
            auth_string = os.getenv("REDIS_AUTH_STRING", "")
            cls._instance.redis_client = redis.StrictRedis(
                host=host, port=port, db=db, password=auth_string
            )
        return cls._instance

    def __init__(self):
        """
        Initializes the MetricCalculator.
        """
        if not hasattr(self, "_initialized"):
            logging.info("Initializing MetricService.")
            self._initialized = True

    def get_metric_values(self, metrics, tenant_id):
        """
        Processes calculations for the list of metrics and returns the processed list.
        """
        current_time = time.time()
        # Execute all Redis commands asynchronously
        pipeline = [
            self.build_redis_command(metric, current_time) for metric in metrics
        ]
        results = [
            result.decode("utf-8") if isinstance(result, bytes) else result
            for result in pipeline
        ]
        metric_results = self.process_results(metrics, results)
        last_processed_time = self.redis_client.get(f"{tenant_id}:last_processed_time")
        logging.debug(
            f"Last processed time retrieved for tenant {tenant_id}: {last_processed_time}"
        )
        return {"last_processed_time": last_processed_time, "metrics": metric_results}

    def build_redis_command(self, metric_name: str, current_time: float):
        """
        Builds the appropriate Redis command based on the metric type.
        """
        # Handle current occupied time calculation for destination locations
        if "occupied_time" in metric_name:
            # Get the latest entry from the sorted set (highest score = most recent)
            latest_entries = self.redis_client.zrange(
                metric_name, -1, -1, withscores=True
            )

            if latest_entries:
                # Get the latest timestamp (score) and entry data
                latest_entry, latest_timestamp = latest_entries[0]

                # Calculate current occupied time in minutes since the latest event
                current_occupied_time = (current_time - latest_timestamp) / 60
                logging.debug("Current occupied time: %s", current_occupied_time)
                return current_occupied_time
            else:
                # No entries found, location is not occupied
                return 0

        if "_set" in metric_name:
            set_minutes = self._get_minutes_from_metric_id(metric_name)
            start_time = int(self._get_x_minutes_ago(set_minutes))
            end_time = int(current_time)

            if metric_name.endswith("hourly_rate") or metric_name.endswith("count"):
                logging.debug(
                    "Building zcount redis command for metric: ZCOUNT %s %s %s",
                    metric_name,
                    start_time,
                    end_time,
                )
                return self.redis_client.zcount(metric_name, start_time, end_time)
            # Currently we only have one use case for this method, so we'll hardcode it for now.
            # In the future, we could make this more flexible in the config perhaps by providing the numerator and denominator metric names.
            elif "destination_position_ratio" in metric_name:
                logging.debug(
                    "Building zcount redis commands for dynamic ratio metric: %s %s %s",
                    metric_name,
                    start_time,
                    end_time,
                )
                parts = metric_name.split(":")

                node_name = parts[2]
                # Make sure node_name is a workstation code like M11-GTP-01 and not a induction zone code like M11-GTP-01B1 for stations and for stationlocations.
                node_parts = node_name.split("-")
                if len(node_parts[2]) > 2:
                    node_name = (
                        node_parts[0] + "-" + node_parts[1] + "-" + node_parts[2][:2]
                    )

                denominator_metric_name = ":".join(
                    [
                        parts[0],  # tenant
                        parts[1],  # facility
                        node_name,  # node_name
                        "destination_position_ratio_denominator",  # metric type
                        parts[4],  # time window
                        parts[5],  # aggregation
                    ]
                )
                logging.debug("Denominator metric name: %s", denominator_metric_name)

                # Get the numerator and denominator counts from Redis.
                numerator = self.redis_client.zcount(metric_name, start_time, end_time)
                logging.debug("Numerator count: %s", numerator)
                denominator = self.redis_client.zcount(
                    denominator_metric_name, start_time, end_time
                )
                logging.debug("Denominator count: %s", denominator)

                return {"numerator": numerator, "denominator": denominator}
            elif (
                metric_name.endswith("ratio")
                and "destination_position_ratio" not in metric_name
            ):
                logging.debug(
                    "Building zcount redis commands for ratio metric: %s %s %s",
                    metric_name,
                    start_time,
                    end_time,
                )
                # Parse the piece of the metric_name that identifies this metric,
                # use that to find the corresponding numerator and denominator
                # stored in Redis to calculate the ratio from.
                metric = metric_name.split(":")[3]
                numerator_metric_name = metric_name.replace(
                    metric, f"{metric}_numerator"
                )
                denominator_metric_name = metric_name.replace(
                    metric, f"{metric}_denominator"
                )
                return {
                    "numerator": self.redis_client.zcount(
                        numerator_metric_name, start_time, end_time
                    ),
                    "denominator": self.redis_client.zcount(
                        denominator_metric_name, start_time, end_time
                    ),
                }
            else:
                logging.debug(
                    "Building zrangebyscore redis command for metric: %s", metric_name
                )
                return self.redis_client.zrangebyscore(
                    metric_name, start_time, end_time
                )

        return self.redis_client.get(metric_name)

    def process_results(self, metrics: list, results: list) -> list:
        """
        Processes the results from Redis and builds the final metric response.
        """
        logging.debug("Processing results...")
        processed_metrics = []
        for i, metric_name in enumerate(metrics):
            cur_result = results[i]
            if metric_name.endswith("hourly_rate"):
                logging.debug(
                    ' - Processing hourly rate metric: "%s" with "%s"...',
                    metric_name,
                    cur_result,
                )
                value = cur_result * (
                    60 / self._get_minutes_from_metric_id(metric_name)
                )
                processed_metrics.append(
                    self.build_response(metric_name, value, "hourly")
                )

            elif metric_name.endswith("count"):
                logging.debug(
                    ' - Processing count metric: "%s" with "%s"...',
                    metric_name,
                    cur_result,
                )
                processed_metrics.append(self.build_response(metric_name, cur_result))

            elif metric_name.endswith("sum"):
                logging.debug(
                    ' - Processing sum metric: "%s" with "%s"...',
                    metric_name,
                    cur_result,
                )
                # Special handling for occupied time metrics that return a number
                if "occupied_time" in metric_name:
                    processed_metrics.append(
                        self.build_response(metric_name, cur_result)
                    )
                else:
                    value = self._sum_numbers_after_underscore(cur_result)
                    processed_metrics.append(self.build_response(metric_name, value))

            elif metric_name.endswith("sum_item_values"):
                logging.debug(
                    ' - Processing sum_item_values metric: "%s" with "%s"...',
                    metric_name,
                    cur_result,
                )
                value = self._sum_item_values(cur_result)
                processed_metrics.append(self.build_response(metric_name, value))

            elif metric_name.endswith("avg"):
                logging.debug(
                    ' - Processing average metric: "%s" with "%s"...',
                    metric_name,
                    cur_result,
                )
                value = self._average_numbers_after_underscore(cur_result)
                processed_metrics.append(self.build_response(metric_name, value))

            elif metric_name.endswith("ratio"):
                logging.debug(
                    ' - Processing ratio metric: "%s" with "%s"...',
                    metric_name,
                    cur_result,
                )
                value = self._calculate_ratio(
                    float(cur_result.get("numerator", 0)),
                    float(cur_result.get("denominator", 0)),
                )
                processed_metrics.append(
                    self.build_response(metric_name, value, "ratio")
                )

            else:
                logging.debug(
                    ' - Getting simple metric: "%s" with "%s"...',
                    metric_name,
                    cur_result,
                )
                processed_metrics.append(self.build_response(metric_name, cur_result))

        return processed_metrics

    def build_response(
        self, metric_name: str, value: float, metric_type: str = "normal"
    ) -> dict:
        """
        Builds the final response object for a metric.
        """
        return {
            "name": metric_name,
            "value": value,
            "type": metric_type,
        }

    def _average_numbers_after_underscore(self, arr: list) -> float:
        if len(arr) == 0:
            return 0  # Handle case where array is empty
        total_sum = self._sum_numbers_after_underscore(arr)
        return total_sum / len(arr)

    def _calculate_ratio(self, numerator: float, denominator: float) -> float:
        ratio = 0
        if denominator > 0:
            ratio = numerator / denominator
        return ratio

    def _get_minutes_from_metric_id(self, metric_id: str) -> int:
        split = metric_id.split(":")
        time_part = split[-2]

        if "_set" not in time_part:
            logging.warning("Metric ID does not contain _set")
            return 15  # Default fallback

        return int(time_part.split("m")[0])

    def _get_x_minutes_ago(self, minutes: int) -> float:
        return time.time() - minutes * 60

    def _sum_item_values(self, arr: list) -> int:
        """Given an array of stringified JSON objects, this method returns the sum of the `value` field across all JSON objects in array.

        Args:
            arr (list): An array of strings which can each be converted to JSON objects that represent an event captured in Redis.

        Returns:
            int: The sum of all of the JSON object's `value` fields in the array `arr`.
        """
        arr = [item.decode() if isinstance(item, bytes) else item for item in arr]
        sum_of_values = 0
        for item_json_str in arr:
            item = json.loads(item_json_str)
            value = int(item.get("value", "0"))
            sum_of_values = sum_of_values + value
        return sum_of_values

    def _sum_numbers_after_underscore(self, arr: list) -> int:
        """Given an array of strings, this parses the string to get the stored values and returns the sum of all values.

        Args:
            arr (list): An array of strings that are formatted as "<identifier>_<value>".

        Returns:
            int: The sum of all values that are encoded and stored in the `arr` array.
        """
        arr = [item.decode() if isinstance(item, bytes) else item for item in arr]
        return sum([float(item.split("_")[1]) for item in arr])
