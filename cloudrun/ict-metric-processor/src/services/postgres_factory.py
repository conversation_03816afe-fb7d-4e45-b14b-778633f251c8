"""
This module contains the PostgresFactory class for managing PostgreSQL service instances.

The PostgresFactory class provides methods to create and manage PostgresService instances
for different tenants, handling connection pooling and secret management.
"""

import os
import json
import structlog
from pathlib import Path
from dotenv import load_dotenv
from google.cloud import secretmanager
from .postgres_service import PostgresService

# Load environment variables from the correct .env file
env_path = (
    Path(__file__).parents[2] / ".env"
)  # Go up two directories to find .env in cloudrun/ict-metric-processor/
print(f"Loading .env from: {env_path}")
load_dotenv(dotenv_path=env_path)

# Debug logging of environment variables
print("\nEnvironment Variables:")
print(f"LOCALDEV: {os.getenv('LOCALDEV')}")
print(f"POSTGRES_HOST: {os.getenv('POSTGRES_HOST')}")
print(f"POSTGRES_USER: {os.getenv('POSTGRES_USER')}")
print(f"POSTGRES_PORT: {os.getenv('POSTGRES_PORT')}")
print(
    f"POSTGRES_PASSWORD: {'*' * len(os.getenv('POSTGRES_PASSWORD', ''))}"
)  # Mask password

logging = structlog.get_logger()


class PostgresFactory:
    """
    A factory class to create and manage PostgresService instances for different tenants.
    """

    _instances = {}

    @classmethod
    def get_instance(cls, tenant: str) -> PostgresService:
        """
        Gets or creates a PostgresService instance for the specified tenant.

        Args:
            tenant (str): The tenant identifier.

        Returns:
            PostgresService: A PostgresService instance for the tenant.
        """
        if tenant not in cls._instances:
            secret_data = cls._fetch_secret()
            if secret_data:
                logging.debug("Using secret data", secret_data=secret_data)
                instance = PostgresService(secret_data, tenant)
                if instance.is_connected():
                    cls._instances[tenant] = instance
        return cls._instances.get(tenant)

    @classmethod
    def _fetch_secret(cls) -> dict:
        """
        Fetches PostgreSQL connection secrets from Google Secret Manager in control tower API project.
        For local development, uses environment variables with POSTGRES_ prefix.

        Returns:
            dict: A dictionary containing PostgreSQL connection details in either format:
                - Local format from .env:
                    - 'POSTGRES_HOST': The host for the PostgreSQL database.
                    - 'POSTGRES_USER': The username for PostgreSQL authentication.
                    - 'POSTGRES_PASSWORD': The password for PostgreSQL authentication.
                    - 'POSTGRES_PORT': The port for the PostgreSQL database.
                - Secret Manager format:
                    - 'host': The host for the PostgreSQL database.
                    - 'username': The username for PostgreSQL authentication.
                    - 'password': The password for PostgreSQL authentication.
                    - 'port': The port for the PostgreSQL database.
        """
        if os.getenv("LOCALDEV", "false").lower() == "true":
            # Use environment variables for local development
            secret_data = {
                "POSTGRES_HOST": os.getenv("POSTGRES_HOST"),
                "POSTGRES_USER": os.getenv("POSTGRES_USER"),
                "POSTGRES_PASSWORD": os.getenv("POSTGRES_PASSWORD"),
                "POSTGRES_PORT": os.getenv("POSTGRES_PORT"),
            }
            return secret_data

        try:
            secret_client = secretmanager.SecretManagerServiceClient()
            # Get the project ID and secret name from environment variables defined in infrastructure/CloudRun/metric-processor.tf
            source_project_id = os.getenv("POSTGRES_SOURCE_PROJECT_ID")
            secret_name = os.getenv("POSTGRES_SOURCE_SECRET_NAME")

            if not source_project_id or not secret_name:
                raise ValueError(
                    "POSTGRES_SOURCE_PROJECT_ID and POSTGRES_SOURCE_SECRET_NAME environment variables must be set"
                )

            # Access the secret from control tower API project
            secret_path = (
                f"projects/{source_project_id}/secrets/{secret_name}/versions/latest"
            )
            secret_response = secret_client.access_secret_version(name=secret_path)
            secret_payload = secret_response.payload.data.decode("UTF-8")
            return json.loads(secret_payload)
        except json.JSONDecodeError:
            logging.exception(f"Failed to decode JSON from secret {secret_name}")
        except Exception:
            logging.exception("An unexpected error occurred while fetching secret")
        return None

    @classmethod
    def close_instance(cls, tenant: str):
        """
        Closes and removes a PostgresService instance for the specified tenant.

        Args:
            tenant (str): The tenant identifier.
        """
        if tenant in cls._instances:
            cls._instances[tenant].close()
            del cls._instances[tenant]

    @classmethod
    def clear_cache(cls):
        """
        Closes and removes all PostgresService instances.
        """
        for tenant in list(cls._instances.keys()):
            cls.close_instance(tenant)
