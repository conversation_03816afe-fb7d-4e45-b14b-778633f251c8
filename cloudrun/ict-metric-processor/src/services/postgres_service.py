"""
This module contains the PostgresService class for interacting with PostgreSQL databases.

The PostgresService class provides methods to connect to and interact with tenant-specific
PostgreSQL databases. Each tenant has its own database, and this service manages those
connections.
"""

import asyncio
import os
from contextlib import contextmanager
from typing import Optional, Dict, Any, List

from psycopg2 import pool
from psycopg2.extras import RealDictCursor

import structlog

logger = structlog.get_logger()


class PostgresService:
    """
    A service class to interact with PostgreSQL databases.

    Attributes:
        connection_pool (psycopg2.pool.SimpleConnectionPool): A connection pool for the database.
        tenant (str): The tenant identifier for this database connection.
    """

    def __init__(self, secret_data: Dict[str, str], tenant: str):
        """
        Initializes the PostgresService with connection details.

        Args:
            secret_data (dict): A dictionary containing PostgreSQL connection details.
            tenant (str): The tenant identifier from the message attributes.
        """
        self.tenant = tenant
        self.connection_pool = None
        self._initialize_pool(secret_data)

    def _initialize_pool(self, secret_data: Dict[str, str]):
        """
        Initializes the connection pool with the provided secret data.

        Args:
            secret_data (dict): A dictionary containing PostgreSQL connection details.
        """
        try:
            is_local = os.getenv("LOCALDEV", "false").lower() == "true"
            host = secret_data["POSTGRES_HOST" if is_local else "host"]
            user = secret_data["POSTGRES_USER" if is_local else "username"]
            password = secret_data["POSTGRES_PASSWORD" if is_local else "password"]
            port = secret_data["POSTGRES_PORT" if is_local else "port"]

            # Log connection parameters (masking password)
            logger.debug(
                "Initializing PostgreSQL connection pool",
                host=host,
                user=user,
                port=port,
                is_local=is_local,
                tenant=self.tenant,
            )

            # Create connection parameters
            conn_params = {
                "host": host,
                "user": user,
                "password": password,
                "port": port,
                "dbname": self.tenant,  # Use tenant_id from message as database name
                "connect_timeout": 10,  # Add timeout
            }

            # Log the actual connection attempt
            logger.debug(
                "Attempting to create connection pool",
                conn_params={**conn_params, "password": "***"},
                tenant=self.tenant,
            )

            # Increase pool size and add connection timeout
            self.connection_pool = pool.SimpleConnectionPool(
                5,  # Minimum connections (increased from 1)
                20,  # Maximum connections (increased from 10)
                **conn_params,
            )

            # Test the connection
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT 1")
                    logger.info(
                        "Successfully connected to PostgreSQL", tenant=self.tenant
                    )

        except Exception as e:
            logger.exception(
                "An unexpected error occurred while connecting to PostgreSQL",
                error=str(e),
                error_type=type(e).__name__,
                tenant=self.tenant,
            )
            raise

    @contextmanager
    def get_connection(self):
        """
        Gets a connection from the pool and ensures it's returned.

        Yields:
            psycopg2.extensions.connection: A database connection from the pool.
        """
        if not self.connection_pool:
            raise ConnectionError("Connection pool not initialized")

        conn = None
        try:
            conn = self.connection_pool.getconn()
            logger.debug("Got connection from pool", tenant=self.tenant)
            yield conn
        except Exception as e:
            logger.error(
                "Error using connection",
                error=str(e),
                error_type=type(e).__name__,
                tenant=self.tenant,
            )
            raise
        finally:
            if conn:
                self.connection_pool.putconn(conn)
                logger.debug("Returned connection to pool", tenant=self.tenant)

    def is_connected(self) -> bool:
        """
        Checks if the PostgreSQL connection pool is initialized.

        Returns:
            bool: True if connected, False otherwise.
        """
        return self.connection_pool is not None

    def close(self):
        """
        Closes all connections in the pool.
        """
        if self.connection_pool:
            self.connection_pool.closeall()
            self.connection_pool = None
            logger.info("Closed all connections in pool", tenant=self.tenant)

    async def execute_query(self, query: str, params: Optional[dict] = None) -> Any:
        """
        Executes a query asynchronously and returns the results.

        Args:
            query (str): The SQL query to execute.
            params (dict, optional): Parameters for the query.

        Returns:
            Any: The query results as a list of dictionaries.
        """
        # Run the synchronous database operation in a thread pool
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, lambda: self._execute_query_sync(query, params)
        )

    def _execute_query_sync(self, query: str, params: Optional[dict] = None) -> Any:
        """
        Synchronous version of execute_query for internal use.
        """
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Format query for logging by removing extra whitespace
                formatted_query = " ".join(
                    line.strip() for line in query.split("\n") if line.strip()
                )
                logger.debug(
                    "Executing query",
                    query=formatted_query,
                    params=params,
                    tenant=self.tenant,
                )
                try:
                    cur.execute(query, params)
                    if cur.description:  # If the query returns results
                        results = cur.fetchall()
                        logger.debug(
                            "Query executed successfully",
                            row_count=len(results),
                            tenant=self.tenant,
                        )
                        return results
                    return None
                except Exception as e:
                    logger.error(
                        "Error executing query",
                        exc_info=True,
                        query=formatted_query,
                        params=params,
                        tenant=self.tenant,
                        error=str(e),
                    )
                    raise

    async def get_metric_configs(
        self, tenant: str, facility_id: str, fact_type: str
    ) -> List[Dict]:
        """
        Get metric configurations for a specific fact type, combining default and custom configs.

        Args:
            tenant: The tenant ID
            facility_id: The facility ID
            fact_type: The fact type

        Returns:
            List of metric configurations
        """
        try:
            logger.debug(
                "Getting metric configs from postgres",
                tenant=tenant,
                facility_id=facility_id,
                fact_type=fact_type,
            )

            query = f"""
                WITH default_configs AS (
                    SELECT 
                        to_jsonb(d.*) as config_data,
                        d.metric_config_name,
                        d.enabled,
                        False as is_custom,
                    FROM process_flow.default_metric_configurations d
                    WHERE fact_type = %(fact_type)s
                      AND (
                        d.enabled IS NULL
                        OR NOT (
                            d.enabled ? '{facility_id}'
                            AND d.enabled->'{facility_id}' = 'false'
                        )
                      )
                ),
                custom_configs AS (
                    -- Custom configs that override a default
                    SELECT 
                        to_jsonb(c.*) as config_data,
                        c.metric_config_name,
                        True as is_custom,
                    FROM process_flow.custom_metric_configurations c
                    JOIN default_configs d ON c.metric_config_name = d.metric_config_name
                    WHERE c.facility_id = %(facility_id)s
                      AND c.fact_type = %(fact_type)s
                      AND c.enabled = TRUE
                      AND d.enabled ? '{facility_id}'
                      AND d.enabled->'{facility_id}' = 'true'

                    UNION ALL

                    -- Custom configs with no default
                    SELECT 
                        to_jsonb(c.*) as config_data,
                        c.metric_config_name,
                        True as is_custom,
                    FROM process_flow.custom_metric_configurations c
                    LEFT JOIN default_configs d ON c.metric_config_name = d.metric_config_name
                    WHERE c.facility_id = %(facility_id)s
                      AND c.fact_type = %(fact_type)s
                      AND d.metric_config_name IS NULL
                      AND c.enabled = TRUE
                ),
                final_configs AS (
                    SELECT config_data, is_custom FROM default_configs
                    UNION ALL
                    SELECT config_data, is_custom FROM custom_configs
                )
                SELECT config_data, is_custom FROM final_configs;
            """
            # Parameters: use a dictionary for named parameters
            params = {"fact_type": fact_type, "facility_id": facility_id}

            # Execute query asynchronously
            results = await self.execute_query(query, params)
            # Convert RealDictRow to regular dict
            # As a list we can loop through each row
            return [dict(row) for row in results] if results else []

        except Exception as e:
            logger.error(
                "Error getting metric configs",
                exc_info=True,
                tenant=tenant,
                facility_id=facility_id,
                fact_type=fact_type,
                error=str(e),
            )
            raise

    async def update_config_active_status(
        self, is_custom: bool, metric_config_name: str, facility: str, is_active: bool
    ) -> None:
        """
        Updates the active status of a metric config in the database.
        """
        try:
            logger.info(
                "Updating config active status in database",
                is_custom=is_custom,
                metric_config_name=metric_config_name,
                facility=facility,
                is_active=is_active,
            )
            if is_custom:
                # Update the custom config active status in the database
                query = """
                    UPDATE process_flow.custom_metric_configurations
                    SET active = %(is_active)s
                    WHERE metric_config_name = %(metric_config_name)s
                    AND facility_id = %(facility)s
                """
                params = {
                    "is_active": is_active,
                    "metric_config_name": metric_config_name,
                    "facility": facility,
                }
            else:
                # Update the default config active status in the database
                # For default configs, we need to update the hstore 'active' field
                query = """
                    UPDATE process_flow.default_metric_configurations
                    SET active = COALESCE(active, '{}'::hstore) || %(facility)s => %(is_active)s
                    WHERE metric_config_name = %(metric_config_name)s
                """
                params = {
                    "facility": facility,
                    "is_active": str(is_active).lower(),
                    "metric_config_name": metric_config_name,
                }

            # Execute the query asynchronously
            await self.execute_query(query, params)

            logger.info(
                "Config active status updated in database",
                is_custom=is_custom,
                metric_config_name=metric_config_name,
                facility=facility,
                is_active=is_active,
            )
        except Exception as e:
            logger.error(
                "Error updating config active status in database",
                exc_info=True,
                is_custom=is_custom,
                metric_config_name=metric_config_name,
                facility=facility,
                is_active=is_active,
                error=str(e),
            )
            raise
