"""
Integration tests for PostgreSQL services focusing on connection and SELECT queries.
"""

import unittest
import os
from pathlib import Path
from dotenv import load_dotenv
import psycopg2
from src.services.postgres_service import PostgresService
from src.services.postgres_factory import PostgresFactory

# Load environment variables first
env_path = Path(__file__).parents[3] / ".env"
load_dotenv(dotenv_path=env_path)
print(f"Loading .env from: {env_path}")


print("\nPostgreSQL Environment Variables:")
print(f"POSTGRES_USER: {os.getenv('POSTGRES_USER')}")
print(f"POSTGRES_HOST: {os.getenv('POSTGRES_HOST')}")
print(f"POSTGRES_PORT: {os.getenv('POSTGRES_PORT')}")
print(
    f"POSTGRES_PASSWORD: {'*' * len(os.getenv('POSTGRES_PASSWORD', ''))}"
)  # Mask password
print(f"LOCALDEV: {os.getenv('LOCALDEV')}\n")

# Then check if we're in local development mode
if os.getenv("LOCALDEV", "false").lower() == "true":
    print("Running in local development mode")


# TODO: We should figure out how to run these in a pipeline when we move this over to ict-api project
class TestPostgresIntegration(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """Set up test database and tables before running tests."""
        cls.test_tenant = "ict_development"

        # Get database config from Secret Manager via PostgresFactory
        cls.service = PostgresFactory.get_instance(cls.test_tenant)
        if not cls.service:
            raise Exception("Failed to get PostgresService instance from factory")

        # Create test tables
        with cls.service.get_connection() as conn:
            with conn.cursor() as cur:
                # Create test table
                cur.execute(
                    """
                    CREATE TABLE IF NOT EXISTS test_table (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(100),
                        value INTEGER
                    )
                """
                )
                # Clean up any existing test data
                cur.execute("DELETE FROM test_table")
                # Create test data
                cur.execute(
                    """
                    INSERT INTO test_table (name, value) VALUES
                    ('test1', 100),
                    ('test2', 200),
                    ('test3', 300)
                """
                )

    def setUp(self):
        """Set up test fixtures before each test."""
        self.service = PostgresFactory.get_instance(self.test_tenant)

    def tearDown(self):
        """Clean up after each test."""
        PostgresFactory.close_instance(self.test_tenant)

    @classmethod
    def tearDownClass(cls):
        """Clean up after all tests."""
        PostgresFactory.clear_cache()

    def test_connection(self):
        """Test that we can connect to the database."""
        self.assertTrue(self.service.is_connected())

    def test_execute_query_select(self):
        """Test executing a SELECT query."""
        results = self.service.execute_query("SELECT * FROM test_table ORDER BY id")
        self.assertEqual(len(results), 3)
        self.assertEqual(results[0][1], "test1")  # name column
        self.assertEqual(results[0][2], 100)  # value column

    def test_factory_get_instance(self):
        """Test PostgresFactory with real connection."""
        # Get instance
        instance = PostgresFactory.get_instance(self.test_tenant)
        self.assertIsNotNone(instance)
        self.assertTrue(instance.is_connected())

        # Test query execution
        results = instance.execute_query("SELECT COUNT(*) FROM test_table")
        self.assertEqual(results[0][0], 3)  # Should have 3 records

        # Clean up
        PostgresFactory.close_instance(self.test_tenant)


if __name__ == "__main__":
    unittest.main()
