from tools.generate_timestamps import update_timestamp

recent_timestamp = update_timestamp()

TEST_SUITE = {
    "test_suite": "fault_denominator_tests",
    "test_suite_description": "Tests for fault denominators used to calculate movements_per_fault and arrivals_per_fault",
    "tests": [
        # Test Multishuttle Movements per Fault Denominator
        {
            "test_description": "multishuttle movements per fault denominator metric",
            "test_input": [
                {
                    "area": "DMS123",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:multishuttle:movements_per_fault_denominator:60m_set:ratio",
                    "success": True,
                    "config_name": "multishuttle_movements_per_fault_denominator",
                }
            ],
        },
        # Test Miniload Movements per Fault Denominator (ML Area Match)
        {
            "test_description": "miniload movements per fault denominator metric (ML area)",
            "test_input": [
                {
                    "area": "ML789",
                    "equipment_code": "ML001",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:movements_per_fault_denominator:60m_set:ratio",
                    "success": True,
                    "config_name": "miniload_movements_per_fault_denominator",
                }
            ],
        },
        # Test Miniload Movements per Fault Denominator (Miniload Area Match)
        {
            "test_description": "miniload movements per fault denominator metric (Miniload area)",
            "test_input": [
                {
                    "area": "Miniload123",
                    "equipment_code": "ML002",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:movements_per_fault_denominator:60m_set:ratio",
                    "success": True,
                    "config_name": "miniload_movements_per_fault_denominator",
                }
            ],
        },
    ],
}
