from tools.generate_timestamps import update_timestamp

recent_timestamp = update_timestamp()

TEST_SUITE = {
    "test_suite": "multishuttle_movementtests",
    "test_suite_description": "tests for multishuttle_movement metrics",
    "tests": [
        # Test distinct aisles count
        {
            "test_description": "distinct aisle count metric",
            "test_input": [
                {
                    "aisle_code": "01",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:multishuttle:num_aisles:60m_set:count",
                    "success": True,
                    "config_name": "multishuttle_num_distinct_aisles",
                }
            ],
        },
        # Test distinct shuttles count
        {
            "test_description": "distinct shuttle count metric",
            "test_input": [
                {
                    "shuttle_code": "MSAI01LV01SH01",
                    "aisle_code": "01",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:multishuttle:num_shuttles:60m_set:count",
                    "success": True,
                    "config_name": "multishuttle_num_distinct_shuttles",
                }
            ],
        },
        # Test total shuttle movements
        {
            "test_description": "total shuttle movements metric",
            "test_input": [
                {
                    "aisle_code": "01",
                    "shuttle_code": "MSAI01LV01SH01",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI01LV01SH01:total_shuttle_movements:60m_set:count",
                    "success": True,
                    "config_name": "shuttle_total_shuttle_movements",
                }
            ],
        },
        # Test retrieval rate
        {
            "test_description": "retrieval rate metric",
            "test_input": [
                {
                    "aisle_code": "01",
                    "movement_type_code": "Retrieval",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:multishuttle:retrieval_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "multishuttle_movement_retrieval_rate",
                },
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:multishuttle:total_retrieval_movements:60m_set:count",
                    "success": True,
                    "config_name": "multishuttle_movement_total_retrieval_movements",
                },
            ],
        },
        # Test aisle_based retrieval rate
        {
            "test_description": "aisle retrieval rate metric",
            "test_input": [
                {
                    "aisle_code": "01",
                    "movement_type_code": "Retrieval",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI01:retrieval_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "aisle_retrieval_rate",
                }
            ],
        },
        # Test total aisle storage movements
        {
            "test_description": "aisle total storage movements metric",
            "test_input": [
                {
                    "aisle_code": "02",
                    "movement_type_code": "Storage",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI02:total_storage_movements:60m_set:count",
                    "success": True,
                    "config_name": "aisle_total_storage_movements",
                }
            ],
        },
        # Test inventory totes shuttle storage movements
        {
            "test_description": "inventory totes shuttle storage movements metric",
            "test_input": [
                {
                    "aisle_code": "01",
                    "shuttle_code": "MSAI01LV01SH01",
                    "movement_type_code": "Storage",
                    "sku_code": "SKU123",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI01LV01SH01:inventory_totes_shuttle_storage_movements:60m_set:count",
                    "success": True,
                    "config_name": "shuttle_inventory_totes_storage_movements",
                }
            ],
        },
        # Test empty totes shuttle storage movements
        {
            "test_description": "empty totes shuttle storage movements metric",
            "test_input": [
                {
                    "aisle_code": "01",
                    "shuttle_code": "MSAI01LV01SH01",
                    "movement_type_code": "Storage",
                    "sku_code": "",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI01LV01SH01:empty_totes_shuttle_storage_movements:60m_set:count",
                    "success": True,
                    "config_name": "shuttle_empty_totes_storage_movements",
                }
            ],
        },
        # Test bypass movement rate
        {
            "test_description": "bypass movement rate metric",
            "test_input": [
                {
                    "movement_type_code": "ByPass",
                    "aisle_code": "01",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:multishuttle:bypass_movement_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "multishuttle_bypass_movement_rate",
                }
            ],
        },
        # Test shuttle retrieval movements for inventory totes
        {
            "test_description": "inventory totes shuttle retrieval movements metric",
            "test_input": [
                {
                    "aisle_code": "01",
                    "shuttle_code": "MSAI01LV01SH01",
                    "movement_type_code": "Retrieval",
                    "sku_code": "SKU456",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI01LV01SH01:inventory_totes_shuttle_retrieval_movements:60m_set:count",
                    "success": True,
                    "config_name": "shuttle_inventory_totes_retrieval_movements",
                }
            ],
        },
        # Aisle-Based Total Retrieval Movements
        {
            "test_description": "aisle total retrieval movements metric",
            "test_input": [
                {
                    "aisle_code": "02",
                    "movement_type_code": "Retrieval",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI02:total_retrieval_movements:60m_set:count",
                    "success": True,
                    "config_name": "aisle_movement_total_retrieval_movements",
                }
            ],
        },
        # Total Shuttle Bypass Movements
        {
            "test_description": "total shuttle movements metric",
            "test_input": [
                {
                    "aisle_code": "02",
                    "shuttle_code": "MSAI02LV01SH02",
                    "movement_type_code": "ByPass",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI02LV01SH02:total_shuttle_movements:60m_set:count",
                    "success": True,
                    "config_name": "shuttle_total_shuttle_movements",
                }
            ],
        },
        # Shuttle-Specific Retrieval Rate
        {
            "test_description": "shuttle retrieval rate metric",
            "test_input": [
                {
                    "aisle_code": "01",
                    "shuttle_code": "MSAI01LV02SH02",
                    "movement_type_code": "Retrieval",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI01LV02SH02:total_shuttle_retrieval_movements:60m_set:count",
                    "success": True,
                    "config_name": "shuttle_total_retrieval_movements",
                }
            ],
        },
        # Shuttle-Based Shuffle Movements
        {
            "test_description": "shuttle total shuffle movements metric",
            "test_input": [
                {
                    "aisle_code": "02",
                    "shuttle_code": "MSAI02LV02SH03",
                    "movement_type_code": "Shuffle",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI02LV02SH03:total_shuttle_shuffle_movements:60m_set:count",
                    "success": True,
                    "config_name": "shuttle_total_shuffle_movements",
                }
            ],
        },
        # Empty Totes Shuttle Retrieval Movements
        {
            "test_description": "empty totes shuttle retrieval movements metric",
            "test_input": [
                {
                    "aisle_code": "02",
                    "shuttle_code": "MSAI02LV01SH05",
                    "movement_type_code": "Retrieval",
                    "sku_code": "",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI02LV01SH05:empty_totes_shuttle_retrieval_movements:60m_set:count",
                    "success": True,
                    "config_name": "shuttle_empty_totes_retrieval_movements",
                }
            ],
        },
        # Storage Movement Rate
        {
            "test_description": "storage movement rate metric",
            "test_input": [
                {
                    "aisle_code": "01",
                    "movement_type_code": "Storage",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:multishuttle:storage_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "multishuttle_storage_movement_rate",
                }
            ],
        },
        # Intra-Aisle Transfer Movements
        {
            "test_description": "intra-aisle transfer movement metric",
            "test_input": [
                {
                    "aisle_code": "01",
                    "shuttle_code": "MSAI01LV01SH01",
                    "movement_type_code": "IAT",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI01:total_iat_movements:60m_set:count",
                    "success": True,
                    "config_name": "aisle_total_iat_movements",
                }
            ],
        },
    ],
}
