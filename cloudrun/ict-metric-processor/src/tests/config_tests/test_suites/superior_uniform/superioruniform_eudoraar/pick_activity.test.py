from tools.generate_timestamps import update_timestamp

recent_timestamp = update_timestamp()

TEST_SUITE = {
    "test_suite": "workstations_tests",
    "test_suite_description": "Tests for workstations metrics",
    "tests": [
        # Test source container arrivals total count
        {
            "test_description": "source container arrival count metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M01GTPA1D1",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:source_container_arrival_count:60m_set:count",
                    "success": True,
                    "config_name": "workstations_source_container_arrival_count",
                }
            ],
        },
        # Test source container arrival rate
        {
            "test_description": "source container arrival rate metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M02GTPB2D1",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:source_container_arrival_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "workstations_source_container_arrival_rate",
                }
            ],
        },
        # Test destination container arrivals total count
        {
            "test_description": "destination container arrival count metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M03GTPA1B3",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:destination_container_arrival_count:60m_set:count",
                    "success": True,
                    "config_name": "workstations_destination_container_arrival_count",
                }
            ],
        },
        # Test destination container arrival rate
        {
            "test_description": "destination container arrival rate metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M04GTPA1F5",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:destination_container_arrival_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "workstations_destination_container_arrival_rate",
                }
            ],
        },
        # Test total arrivals per fault numerator
        {
            "test_description": "arrivals per fault numerator metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M05GTPA1F1",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:arrivals_per_fault_numerator:60m_set:ratio",
                    "success": True,
                    "config_name": "workstations_arrivals_per_fault_numerator",
                }
            ],
        },
        # Test active operators increment
        {
            "test_description": "active operators increment metric",
            "test_input": [
                {
                    "workstation_code": "M11-GTP-05",
                    "event_code": "Logon",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:active_operators:60m_set:count",
                    "success": True,
                    "config_name": "workstations_active_operators_increment",
                }
            ],
        },
        # Test active operators decrement
        {
            "test_description": "active operators decrement metric",
            "test_input": [
                {
                    "workstation_code": "GTP456",
                    "event_code": "Logoff",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:active_operators:60m_set:count",
                    "success": True,
                    "config_name": "workstations_active_operators_decrement",
                }
            ],
        },
        # Test occupied source locations increment
        {
            "test_description": "occupied source locations increment metric",
            "test_input": [
                {
                    "workstation_code": "GTP789",
                    "event_code": "Arrival",
                    "induction_zone_code": "M06GTPA1D1",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:occupied_source_locations:60m_set:count",
                    "success": True,
                    "config_name": "workstations_occupied_source_locations_increment",
                }
            ],
        },
        # Test occupied source locations decrement
        {
            "test_description": "occupied source locations decrement metric",
            "test_input": [
                {
                    "workstation_code": "GTP101",
                    "event_code": "Pick",
                    "induction_zone_code": "M07GTPA1D1",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:occupied_source_locations:60m_set:count",
                    "success": True,
                    "config_name": "workstations_occupied_source_locations_decrement",
                }
            ],
        },
        # Test occupied destination locations increment
        {
            "test_description": "occupied destination locations increment metric",
            "test_input": [
                {
                    "workstation_code": "GTP111",
                    "event_code": "Arrival",
                    "induction_zone_code": "M08GTPA1B2",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:occupied_destination_locations:60m_set:count",
                    "success": True,
                    "config_name": "workstations_occupied_destination_locations_increment",
                }
            ],
        },
        # Test occupied destination locations decrement
        {
            "test_description": "occupied destination locations decrement metric",
            "test_input": [
                {
                    "workstation_code": "GTP121",
                    "event_code": "Ship",
                    "induction_zone_code": "M09GTPA1F6",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:occupied_destination_locations:60m_set:count",
                    "success": True,
                    "config_name": "workstations_occupied_destination_locations_decrement",
                }
            ],
        },
        ####################################
        ## Workstation metric config tests
        ####################################
        {
            "test_description": "Workstation source container arrival rate metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-01D1",
                    "workstation_code": "M11-GTP-01",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-01:source_container_arrival_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "workstation_source_container_arrival_rate",
                }
            ],
        },
        {
            "test_description": "Workstation source container arrival count metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-02D1",
                    "workstation_code": "M11-GTP-02",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-02:source_container_arrivals:60m_set:count",
                    "success": True,
                    "config_name": "workstation_source_container_arrival_count",
                }
            ],
        },
        {
            "test_description": "Workstation occupied source locations increment count",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-03D1",
                    "workstation_code": "M11-GTP-03",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-03:occupied_source_locations:60m_set:count",
                    "success": True,
                    "config_name": "workstation_occupied_source_locations_increment",
                }
            ],
        },
        {
            "test_description": "Workstation occupied source locations decrement count",
            "test_input": [
                {
                    "event_code": "Foo",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-03D1",
                    "workstation_code": "M11-GTP-03",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-03:occupied_source_locations:60m_set:count",
                    "success": True,
                    "config_name": "workstation_occupied_source_locations_decrement",
                }
            ],
        },
        {
            "test_description": "Workstation occupied destination locations increment count",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-03F1",
                    "workstation_code": "M11-GTP-03",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-03:occupied_destination_locations:60m_set:count",
                    "success": True,
                    "config_name": "workstation_occupied_destination_locations_increment",
                }
            ],
        },
        {
            "test_description": "Workstation occupied destination locations decrement count",
            "test_input": [
                {
                    "event_code": "Foo",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-03F1",
                    "workstation_code": "M11-GTP-03",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-03:occupied_destination_locations:60m_set:count",
                    "success": True,
                    "config_name": "workstation_occupied_destination_locations_decrement",
                }
            ],
        },
        {
            "test_description": "Workstation active operators increment count",
            "test_input": [
                {
                    "event_code": "Logon",
                    "event_timestamp_utc": recent_timestamp,
                    "workstation_code": "M11-GTP-04",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-04:active_operators:60m_set:count",
                    "success": True,
                    "config_name": "workstation_active_operators_increment",
                }
            ],
        },
        {
            "test_description": "Workstation active operators decrement count",
            "test_input": [
                {
                    "event_code": "Logoff",
                    "event_timestamp_utc": recent_timestamp,
                    "workstation_code": "M11-GTP-04",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-04:active_operators:60m_set:count",
                    "success": True,
                    "config_name": "workstation_active_operators_decrement",
                }
            ],
        },
        {
            "test_description": "Workstation destination container arrival count",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-01B1",
                    "workstation_code": "M11-GTP-01",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-01:destination_container_arrival_count:60m_set:count",
                    "success": True,
                    "config_name": "workstation_destination_container_arrival_count",
                }
            ],
        },
        {
            "test_description": "Workstation destination container arrival rate",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-02B1",
                    "workstation_code": "M11-GTP-02",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-02:destination_container_arrival_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "workstation_destination_container_arrival_rate",
                }
            ],
        },
        # Workstation - Logged In Time
        {
            "test_description": "Workstation workstation logged in time start",
            "test_input": [
                {
                    "event_code": "Logon",
                    "event_timestamp_utc": recent_timestamp,
                    "workstation_code": "M11-GTP-01",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-01:logged_in_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_logged_in_time_start",
                }
            ],
        },
        {
            "test_description": "Workstation workstation logged in time stop",
            "test_input": [
                {
                    "event_code": "Logoff",
                    "event_timestamp_utc": recent_timestamp,
                    "workstation_code": "M11-GTP-01",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-01:logged_in_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_logged_in_time_stop",
                }
            ],
        },
        # Workstation - Occupied Time
        {
            "test_description": "Workstation workstation occupied time start",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "event_timestamp_utc": recent_timestamp,
                    "workstation_code": "M11-GTP-02",
                    "induction_zone_code": "M11-GTP-02D1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-02:occupied_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_occupied_time_start",
                }
            ],
        },
        {
            "test_description": "Workstation workstation occupied time stop",
            "test_input": [
                {
                    "event_code": "Departure",
                    "event_timestamp_utc": recent_timestamp,
                    "workstation_code": "M11-GTP-02",
                    "induction_zone_code": "M11-GTP-02D1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-02:occupied_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_occupied_time_stop",
                }
            ],
        },
        # Workstation - Active Time
        {
            "test_description": "Workstation workstation active time start",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-01D1",
                    "operator_code": "123",
                    "workstation_code": "M11-GTP-01",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-01:active_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_active_time_start",
                }
            ],
        },
        {
            "test_description": "Workstation workstation active time stop",
            "test_input": [
                {
                    "event_code": "Release",
                    "event_timestamp_utc": recent_timestamp,
                    "workstation_code": "M11-GTP-01",
                    "induction_zone_code": "M11-GTP-01D1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-01:active_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_active_time_stop",
                }
            ],
        },
        # Workstation - Idle Time
        {
            "test_description": "Workstation workstation idle time start",
            "test_input": [
                {
                    "event_code": "Departure",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-02D1",
                    "operator_code": "123",
                    "workstation_code": "M11-GTP-02",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-02:idle_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_idle_time_start",
                }
            ],
        },
        {
            "test_description": "Workstation workstation idle time stop",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "event_timestamp_utc": recent_timestamp,
                    "workstation_code": "M11-GTP-02",
                    "induction_zone_code": "M11-GTP-02D1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-02:idle_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_idle_time_stop",
                }
            ],
        },
        # Workstation - Starved Time
        {
            "test_description": "Workstation workstation starved time start",
            "test_input": [
                {
                    "event_timestamp_utc": recent_timestamp,
                    "handling_unit_code": "None",
                    "operator_code": "123",
                    "workstation_code": "M11-GTP-03",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-03:starved_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_starved_time_start",
                }
            ],
        },
        {
            "test_description": "Workstation workstation starved time stop",
            "test_input": [
                {
                    "event_code": "Logoff",
                    "event_timestamp_utc": recent_timestamp,
                    "workstation_code": "M11-GTP-03",
                    "induction_zone_code": "M11-GTP-03D1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-03:starved_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_starved_time_stop",
                }
            ],
        },
        # Workstation - Blocked Time
        {
            "test_description": "Workstation workstation blocked time start",
            "test_input": [
                {
                    "event_code": "Release",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-03D1",
                    "operator_code": "123",
                    "workstation_code": "M11-GTP-03",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-03:blocked_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_blocked_time_start",
                }
            ],
        },
        {
            "test_description": "Workstation workstation blocked time stop",
            "test_input": [
                {
                    "event_code": "Departure",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-03D1",
                    "workstation_code": "M11-GTP-03",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-03:blocked_time:60m_set:sum",
                    "success": True,
                    "config_name": "workstation_blocked_time_stop",
                }
            ],
        },
        ################# Destination Location Tests ##################
        # Test destination position ratio calculation
        {
            "test_description": "Test destination position ratio calculation",
            "test_input": [
                # Numerator event - specific destination position
                {
                    "event_code": "PICK",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-03B1",
                    "zone_code": "M11-GTP-03",
                    "workflow_code": "PICK"
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-03B1:M11-GTP-03B1_destination_position_ratio:60m_set:destination_position_ratio",
                    "success": True,
                    "config_name": "workstation_destination_position_pick_lines_ratio",
                }
            ],
        },
        # NEW TESTS FOR DESTINATION LOCATION METRICS
        # Test edge from workstation source to destination location
        {
            "test_description": "edge from workstation source to destination location",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "success": True,
                    "config_name": "edge_from_workstation_source_location_to_destination_location",
                }
            ],
        },
        # Test destination location occupied
        {
            "test_description": "destination location occupied metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:occupied_location:value:static",
                    "success": True,
                    "config_name": "destination_location_occupied",
                }
            ],
        },
        # Test destination location unoccupied
        {
            "test_description": "destination location unoccupied metric",
            "test_input": [
                {
                    "event_code": "Departure",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:occupied_location:value:static",
                    "success": True,
                    "config_name": "destination_location_unoccupied",
                }
            ],
        },
        # Test destination location arrival rate
        {
            "test_description": "destination location arrival rate metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:destination_container_arrival_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "destination_location_arrival_rate",
                }
            ],
        },
        # Test destination location arrival count
        {
            "test_description": "destination location arrival count metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:destination_container_arrivals:60m_set:count",
                    "success": True,
                    "config_name": "destination_location_arrival_count",
                }
            ],
        },
        # Test destination location active operator
        {
            "test_description": "destination location active operator metric",
            "test_input": [
                {
                    "event_code": "Logon",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:active_operator:value:static",
                    "success": True,
                    "config_name": "destination_location_active_operator",
                }
            ],
        },
        # Test destination location container ID
        {
            "test_description": "destination location container ID metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "handling_unit_code": "CONTAINER123",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:container_id:value:static",
                    "success": True,
                    "config_name": "destination_location_container_id",
                }
            ],
        },
        # Test destination location inactive operator
        {
            "test_description": "destination location inactive operator metric",
            "test_input": [
                {
                    "event_code": "Logoff",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:active_operator:value:static",
                    "success": True,
                    "config_name": "destination_location_inactive_operator",
                }
            ],
        },
        # Test destination location load unit type
        {
            "test_description": "destination location load unit type metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "handling_unit_type": "TOTE",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:load_unit_type:value:static",
                    "success": True,
                    "config_name": "destination_location_load_unit_type",
                }
            ],
        },
        # Test destination location unset load unit type
        {
            "test_description": "destination location unset load unit type metric",
            "test_input": [
                {
                    "event_code": "Departure",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:load_unit_type:value:static",
                    "success": True,
                    "config_name": "destination_location_load_unit_type_remove",
                }
            ],
        },
        # Test destination location occupied time start
        {
            "test_description": "destination location occupied time start metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:occupied_time:60m_set:sum",
                    "success": True,
                    "config_name": "destination_location_occupied_time_start",
                }
            ],
        },
        # Test destination location occupied time stop
        {
            "test_description": "destination location occupied time stop metric",
            "test_input": [
                {
                    "event_code": "Departure",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:occupied_time:60m_set:sum",
                    "success": True,
                    "config_name": "destination_location_occupied_time_stop",
                }
            ],
        },
        # Test destination location active time start
        {
            "test_description": "destination location active time start metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "operator_code": "OP123",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:active_time:60m_set:sum",
                    "success": True,
                    "config_name": "destination_location_active_time_start",
                }
            ],
        },
        # Test destination location active time stop
        {
            "test_description": "destination location active time stop metric",
            "test_input": [
                {
                    "event_code": "Release",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:active_time:60m_set:sum",
                    "success": True,
                    "config_name": "destination_location_active_time_stop",
                }
            ],
        },
        # Test destination location idle time start
        {
            "test_description": "destination location idle time start metric",
            "test_input": [
                {
                    "event_code": "Release",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "operator_code": "OP123",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:idle_time:60m_set:sum",
                    "success": True,
                    "config_name": "destination_location_idle_time_start",
                }
            ],
        },
        # Test destination location idle time stop
        {
            "test_description": "destination location idle time stop metric",
            "test_input": [
                {
                    "event_code": "Arrival",
                    "induction_zone_code": "M01GTPB2",
                    "workstation_code": "M01GTP-02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01GTPB2:idle_time:60m_set:sum",
                    "success": True,
                    "config_name": "destination_location_idle_time_stop",
                }
            ],
        },
        # Test destination location destination position ratio numerator
        {
            "test_description": "destination location destination position ratio numerator metric",
            "test_input": [
                {
                    "induction_zone_code": "M01-GTP-B2",
                    "workstation_code": "M01-GTP-02",
                    "zone_code": "ZONE1",
                    "workflow_code": "PICK",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M01-GTP-B2:destination_position_ratio:60m_set:destination_position_ratio",
                    "success": True,
                    "config_name": "destination_location_destination_position_ratio_numerator",
                }
            ],
        },
    ],
}
