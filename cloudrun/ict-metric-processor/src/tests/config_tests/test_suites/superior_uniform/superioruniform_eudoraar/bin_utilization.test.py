from tools.generate_timestamps import update_timestamp

# Generate a recent timestamp for the test cases
recent_timestamp = update_timestamp()

# Define the test suite
TEST_SUITE = {
    "test_suite": "multishuttle_storage_tests",
    "test_suite_description": "tests for multishuttle and miniload storage metrics",
    "tests": [
        # Test Multishuttle Total Locations Available
        {
            "test_description": "multishuttle total locations available metric",
            "test_input": [
                {
                    "area_code": "MS - Storage",
                    "empty_location_position_count": "50",
                    "aisle_code": "01",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:multishuttle:total_locations_available:60m_set:sum_item_values",
                    "success": True,
                    "config_name": "multishuttle_total_locations_available",
                }
            ],
        },
        # Test Multishuttle Total Locations Occupied
        {
            "test_description": "multishuttle total locations occupied metric",
            "test_input": [
                {
                    "area_code": "MS - Storage",
                    "empty_location_position_count": "30",
                    "total_location_position_count": "100",
                    "aisle_code": "02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:multishuttle:total_locations_occupied:60m_set:sum_item_values",
                    "success": True,
                    "config_name": "multishuttle_total_locations_occupied",
                }
            ],
        },
        {
            "test_description": "miniload aisle available count metric",
            "test_input": [
                {
                    "area_code": "ML - Storage",
                    "empty_location_position_count": "20",
                    "aisle_code": "01",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MLAI01:locations_available:value:static",
                    "success": True,
                    "config_name": "aisle_locations_available_count",
                }
            ],
        },
        # Test Miniload Total Locations Available
        {
            "test_description": "miniload total locations available metric",
            "test_input": [
                {
                    "area_code": "ML - Storage",
                    "empty_location_position_count": "40",
                    "aisle_code": "02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:total_locations_available:60m_set:sum_item_values",
                    "success": True,
                    "config_name": "miniload_total_locations_available",
                }
            ],
        },
        # Test Miniload Total Locations Occupied
        {
            "test_description": "miniload total locations occupied metric",
            "test_input": [
                {
                    "area_code": "ML - Storage",
                    "empty_location_position_count": "20",
                    "total_location_position_count": "80",
                    "aisle_code": "03",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:total_locations_occupied:60m_set:sum_item_values",
                    "success": True,
                    "config_name": "miniload_total_locations_occupied",
                }
            ],
        },
        # Test Multishuttle Aisle Storage Utilization
        {
            "test_description": "multishuttle aisle storage utilization metric",
            "test_input": [
                {
                    "area_code": "MS - Storage",
                    "empty_location_position_count": "10",
                    "total_location_position_count": "90",
                    "aisle_code": "01",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI01:storage_utilization:value:static",
                    "success": True,
                    "config_name": "multishuttle_aisle_storage_utilization",
                }
            ],
        },
        # Test Miniload Aisle Location Distribution - Available
        {
            "test_description": "multishuttle aisle location distribution available",
            "test_input": [
                {
                    "area_code": "MS - Storage",
                    "empty_location_position_count": "25",
                    "aisle_code": "01",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI01:location_distribution_available:value:static",
                    "success": True,
                    "config_name": "multishuttle_aisle_storage_location_distribution_available",
                }
            ],
        },
        {
            "test_description": "multishuttle aisle location distribution occupied",
            "test_input": [
                {
                    "area_code": "MS - Storage",
                    "empty_location_position_count": "25",
                    "total_location_position_count": "100",
                    "aisle_code": "01",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MSAI01:location_distribution_occupied:value:static",
                    "success": True,
                    "config_name": "multishuttle_aisle_storage_location_distribution_occupied",
                }
            ],
        },
        # Test Miniload Aisle Locations Available Count
        {
            "test_description": "miniload aisle locations available count metric",
            "test_input": [
                {
                    "area_code": "ML - Storage",
                    "empty_location_position_count": "25",
                    "aisle_code": "01",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:aisles_available:60m_set:count",
                    "success": True,
                    "config_name": "miniload_aisle_available_count",
                }
            ],
        },
        # Test Miniload Aisle Locations Occupied
        {
            "test_description": "miniload aisle locations occupied metric",
            "test_input": [
                {
                    "area_code": "ML - Storage",
                    "empty_location_position_count": "15",
                    "total_location_position_count": "50",
                    "aisle_code": "02",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:MLAI02:locations_occupied:value:static",
                    "success": True,
                    "config_name": "aisle_locations_occupied",
                }
            ],
        },
    ],
}
