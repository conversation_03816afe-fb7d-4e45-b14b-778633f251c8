metrics = {
    # "workstations_downtime_start": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "workstation_code": "^.+$",
    #         "workstation_status": "^(CLOSED|PAUSED)$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:workstations:downtime:60m_set:sum",
    #     "params": {
    #         "instance_id": "{workstation_code}",
    #     },
    #     "redis_operation": "cycle_time_start",
    # },    
    # "workstations_downtime_stop": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "workstation_code": "^.+$",
    #         "workstation_status": "^AVAILABLE$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:workstations:downtime:60m_set:sum",
    #     "params": {
    #         "instance_id": "{workstation_code}",
    #         "message_uid": "{message_uid}",
    #     },
    #     "redis_operation": "cycle_time_stop",
    # },
    # # TODO: Do we need these workflow_metrics? (RT)
    # # "workstation_workflow_metric": {
    # #     "metric_type": "node",
    # #     "match_conditions": {
    # #         "workstationPk": "^.+$"
    # #     },
    # #     "graph_operation": "area_node",
    # #     "name_formula": "{tenant}:{facility}:workstations:{workstationPk}:workflow",
    # #     "redis_operation": "mset",
    # # },
    # # "workstation_workflow_status_metric": {
    # #     "metric_type": "node",
    # #     "match_conditions": {
    # #         "workstationPk": "^.+$"
    # #     },
    # #     "graph_operation": "area_node",
    # #     "name_formula": "{tenant}:{facility}:workstations:{workstationPk}:workflowstatus",
    # #     "redis_operation": "mset",
    # # },
    # # "workstation_status_metric": {
    # #     "metric_type": "node",
    # #     "match_conditions": {
    # #         "workstationPk": "^.+$"
    # #     },
    # #     "graph_operation": "area_node",
    # #     "name_formula": "{tenant}:{facility}:workstations:{workstationPk}:workstationstatus",
    # #     "redis_operation": "mset",
    # # },
    # # "workstation_event_time_metric": {
    # #     "metric_type": "node",
    # #     "match_conditions": {
    # #         "workstationPk": "^.+$"
    # #     },
    # #     "graph_operation": "area_node",
    # #     "name_formula": "{tenant}:{facility}:workstations:{workstationPk}:eventTime",
    # #     "redis_operation": "mset",
    # # },
    # # "workstation_msg_sha_metric": {
    # #     "metric_type": "node",
    # #     "match_conditions": {
    # #         "workstationPk": "^.+$"
    # #     },
    # #     "graph_operation": "area_node",
    # #     "name_formula": "{tenant}:{facility}:workstations:{workstationPk}:msg_sha",
    # #     "redis_operation": "mset",
    # # }
}