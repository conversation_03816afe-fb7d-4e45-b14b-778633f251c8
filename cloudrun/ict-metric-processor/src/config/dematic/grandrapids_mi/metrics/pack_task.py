metrics = {
    ## Packing Node Metrics ##
    # "packing_tasks_count": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "event": "complete",
    #         "message_uid": "{message_uid}",
    #         "packingDocuments": "^.+$"
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:packing:tasks:60m_set:count",
    #     "redis_operation": "event_set",
    # },
    # "packing_operator_cycle_time_start": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "event": "active",
    #         "packingDocuments": "^.+$"
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:packing:operator_cycle_time:60m_set:average",
    #     "redis_operation": "cycle_time_start",
    #     "params": {
    #         "instance_id": "{packingDocuments}",
    #     },
    # },
    # "packing_operator_cycle_time_stop": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "event": "packed",
    #         "packingDocuments": "^.+$"
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:packing:operator_cycle_time:60m_set:average",
    #     "redis_operation": "cycle_time_stop",
    #     "params": {
    #         "instance_id": "{packingDocuments}",
    #         "message_uid": "{message_uid}",
    #     },
    # },
    # "packing_task_cycle_time_start": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "event": "released",
    #         "packingDocuments": "^.+$"
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:packing:task_cycle_time:60m_set:average",
    #     "redis_operation": "cycle_time_start",
    #     "params": {
    #         "instance_id": "{packingDocuments}"
    #     },
    # },
    # "packing_task_cycle_time_stop": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "event": "complete",
    #         "packingDocuments": "^.+$"
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:packing:task_cycle_time:60m_set:average",
    #     "redis_operation": "cycle_time_stop",
    #     "params": {
    #         "instance_id": "{packingDocuments}",
    #         "message_uid": "{message_uid}",
    #     },
    # },
    
    # # TODO: figure out how to determine location data for packing area (FactType must include a transportUnitId field)
    # # Packing Area Inbound Edge Config   
    # # "packing_inbound_totes_count": {
    # #     "metric_type": "edge",
    # # },
    # # # packing Outbound Edge Config
    # # "packing_outbound_totes_count": {
    # #     "metric_type": "edge",
    # # },
}