metrics = {
    # Miniload - Faults - used to calculate miniload movements_per_fault
    "miniload_movements_per_fault_denominator": {
        "graph_operation": "area_node",
        "views": ["facility"],
        "or_condition": [
            {
                "area": ".*ML.*",
            },
            {
                "area": ".*Miniload.*",
            },
        ],
        "match_conditions": {
            "equipment_code": "^ML.+$",
        },
        "config_type": "node",
        "node_name": "miniload",
        "metric_type": "movements_per_fault_denominator",
        "time_window": "60m_set",
        "aggregation": "ratio",
        "redis_operation": "event_set",
        "description": "Total movements per fault across all aisles within the Mini-Load buffer in the last hour.",
    },
}
