# TODO: Do we need this?  I don't think it's used (RT)
metrics = {
    ### Multishuttle Active Devices
    "multishuttle_active_devices_lift": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "availability_status": "^AU$",
            "device_id_code": "^MSAI.*E[RL].*LO.*$",
        },
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "active_devices",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Number of active lift devices within the DMS picking buffer.",
    },
    "multishuttle_active_devices_shuttle": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "availability_status": "^AU$",
            "device_id_code": "^MSAI.*LV.*SH.*$",
        },
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "active_devices",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Number of active shuttle devices within the DMS picking buffer.",
    },
    "multishuttle_device_status": {
        "views": ["MSAI{aisle_code}"],
        "config_type": "node",
        "match_conditions": {
            "device_id_code": "^MSAI.*LV.*SH.*$",
        },
        "graph_operation": "area_node",
        "node_name": "{device_id_code}",
        "metric_type": "device_status",
        "redis_params": {
            "value": "{availability_status}",
        },
        "redis_operation": "store_static_value",
        "time_window": "value",
        "aggregation": "static",
    },
    "active_devices": {
        "config_type": "node",
        "label": "Aisle",
        "match_conditions": {
            "aisle_code": "^.+$",
            "or_condition": [
                {"availability_status": "^AU$"},
                {"availability_status": "^Automatic Mode$"},
            ],
        },
        "node_name": "MSAI{aisle_code}",
        "metric_type": "active_devices",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["facility", "MSAI{aisle_code}"],
        "graph_operation": "area_node",
        "redis_operation": "event_set",
        "parent_nodes": ["multishuttle"],
        "description": "Number of active devices per aisle within the DMS Picking Buffer.",
    },
}
