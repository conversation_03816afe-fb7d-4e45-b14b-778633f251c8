metrics = {
    ############## Miniload Components ################
    "miniload_num_miniloads_available": {
        "graph_operation": "area_node",
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {
            "equipment_code": "^ML.*",
        },
        "node_name": "miniload",
        "metric_type": "miniloads_available",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{equipment_code}",
        },
        "description": "Number of available miniloads",
    },
    ############## Miniload Movements #################
    # mini load total OK aisle movements
    "miniload_aisle_total_movements": {
        "config_type": "node",
        "graph_operation": "area_node",
        "match_conditions": {
            "or_condition": [
                {"source_location_code": "^ML.*"},
                {"destination_location_code": "^ML.*"},
            ],
            "movement_status_code": "^OK$",
        },
        "views": ["facility"],
        "node_name": "miniload",
        "metric_type": "aisle_total_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total movements across all aisles within the Mini-Load buffer in the last hour.",
    },
    # mini load total OK storage movements
    "miniload_aisle_storage_movements": {
        "config_type": "node",
        "graph_operation": "area_node",
        "match_conditions": {
            "or_condition": [
                {"source_location_code": "^ML.*"},
                {"destination_location_code": "^ML.*"},
            ],
            "movement_status_code": "^OK$",
            "movement_type_code": "^Storage$",
        },
        "node_name": "miniload",
        "metric_type": "aisle_storage_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["facility"],
        "redis_operation": "event_set",
        "description": "Total storage movements across all aisles within the Mini-Load buffer in the last hour.",
    },
    # mini load total OK retrieval movements
    "miniload_aisle_retrieval_movements": {
        "config_type": "node",
        "graph_operation": "area_node",
        "match_conditions": {
            "or_condition": [
                {"source_location_code": "^ML.*"},
                {"destination_location_code": "^ML.*"},
            ],
            "movement_status_code": "^OK$",
            "movement_type_code": "^Retrieval$",
        },
        "node_name": "miniload",
        "metric_type": "aisle_retrieval_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "views": ["facility"],
        "description": "Total retrieval movements across all aisles within the Mini-Load buffer in the last hour.",
    },
    # mini load total OK bypass movements
    "miniload_aisle_bypass_movements": {
        "config_type": "node",
        "graph_operation": "area_node",
        "match_conditions": {
            "or_condition": [
                {"source_location_code": "^ML.*"},
                {"destination_location_code": "^ML.*"},
            ],
            "movement_status_code": "^OK$",
            "movement_type_code": "^Bypass$",
        },
        "node_name": "miniload",
        "metric_type": "aisle_bypass_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["facility"],
        "redis_operation": "event_set",
        "description": "Total bypass movements across all aisles within the Mini-Load buffer in the last hour.",
    },
    ############# Miniload Rates #############
    "miniload_storage_rate": {
        "graph_operation": "area_node",
        "config_type": "node",
        "match_conditions": {
            "or_condition": [
                {"source_location_code": "^ML.*"},
                {"destination_location_code": "^ML.*"},
            ],
            "movement_status_code": "^OK$",
            "movement_type_code": "^Storage$",
        },
        "metric_units": "/hr",
        "node_name": "miniload",
        "metric_type": "storage_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "views": ["facility"],
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of storage movements across all aisles in the Mini-Load buffer based on the last 15 minutes.",
    },
    "miniload_retrieval_rate": {
        "graph_operation": "area_node",
        "config_type": "node",
        "match_conditions": {
            "or_condition": [
                {"source_location_code": "^ML.*"},
                {"destination_location_code": "^ML.*"},
            ],
            "movement_status_code": "^OK$",
            "movement_type_code": "^Retrieval$",
        },
        "metric_units": "/hr",
        "node_name": "miniload",
        "metric_type": "retrieval_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "views": ["facility"],
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of retrieval movements across all aisles in the Mini-Load buffer based on the last 15 minutes.",
    },
    "miniload_bypass_rate": {
        "graph_operation": "area_node",
        "config_type": "node",
        "match_conditions": {
            "or_condition": [
                {"source_location_code": "^ML.*"},
                {"destination_location_code": "^ML.*"},
            ],
            "movement_status_code": "^OK$",
            "movement_type_code": "^Bypass$",
        },
        "metric_units": "/hr",
        "node_name": "miniload",
        "metric_type": "bypass_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "views": ["facility"],
        "redis_params": {
            "ttl": 900,
        },
        "redis_operation": "event_set",
        "description": "Hourly rate of bypass movements across all aisles in the Mini-Load buffer based on the last 15 minutes.",
    },
    "miniload_movements_per_fault_numerator": {
        "config_type": "node",
        "match_conditions": {
            "or_condition": [
                {"source_location_code": "^ML.*"},
                {"destination_location_code": "^ML.*"},
            ],
            "movement_status_code": "^OK$",
        },
        "graph_operation": "area_node",
        "views": ["facility"],
        "node_name": "miniload",
        "metric_type": "movements_per_fault_numerator",
        "time_window": "60m_set",
        "aggregation": "ratio",
        "redis_operation": "event_set",
        "description": "Total movements per fault across all aisles within the Mini-Load buffer in the last hour.",
    },
}
