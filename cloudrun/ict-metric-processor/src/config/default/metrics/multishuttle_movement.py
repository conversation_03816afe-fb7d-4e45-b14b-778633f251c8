metrics = {
    # NOTE: <PERSON><PERSON> ticket refers to basing this off distinct values in Neo4j but i think we could also store this in Redis along with the rest of the metrics?  If so, can do same approach for lifts and shuttles.
    "multishuttle_num_distinct_aisles": {
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "aisle_code": "^.+$",
        },
        "config_type": "node",
        "node_name": "multishuttle",
        "metric_type": "num_aisles",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{aisle_code}",
        },
        "description": "Number of aisles within the DMS picking buffer",
    },
    "multishuttle_num_distinct_shuttles": {
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "shuttle_code": "^.+$",
        },
        "config_type": "node",
        "node_name": "multishuttle",
        "metric_type": "num_shuttles",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{shuttle_code}",
        },
        "description": "Number of shuttles within the DMS picking buffer",
    },
    "multishuttle_num_distinct_lifts": {
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "lift_code": "^.+$",
        },
        "config_type": "node",
        "node_name": "multishuttle",
        "metric_type": "num_lifts",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{lift_code}",
        },
        "description": "Number of lifts within the DMS picking buffer",
    },
    ### Total Movements
    "multishuttle_total_movements": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {
            "movement_type_code": "^.+$",
        },
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "total_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total movements within the DMS picking buffer in the last hour.",
    },
    ### All Movements - used to calculate movements-per-fault
    "multishuttle_movements_per_fault_numerator": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {
            "movement_type_code": "^(ByPass|Retrieval|Shuffle|Storage|IAT)$",
        },
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "movements_per_fault_numerator",
        "time_window": "60m_set",
        "aggregation": "ratio",
        "redis_operation": "event_set",
        "description": "Total movements per fault within the DMS picking buffer in the last hour.",
    },
    #### Retrieval Rate
    "multishuttle_movement_retrieval_rate": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "movement_type_code": "Retrieval",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "retrieval_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of retrieval movements in the DMS picking buffer based on the last 15 minutes.",
    },
    #### Total Retrieval Movements
    "multishuttle_movement_total_retrieval_movements": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "movement_type_code": "Retrieval",
        },
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "total_retrieval_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total retrieval movements within the DMS picking buffer in the last hour.",
    },
    ### Storage Movement Rate
    "multishuttle_storage_movement_rate": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "movement_type_code": "Storage",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "storage_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of storage movements in the DMS picking buffer based on the last 15 minutes.",
    },
    ### Storage Movements Total
    "multishuttle_total_storage_movements": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "movement_type_code": "Storage",
        },
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "total_storage_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total storage movements within the DMS picking buffer in the last hour.",
    },
    ### Shuffle Movements Total
    "multishuttle_total_shuffle_movements": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "movement_type_code": "Shuffle",
        },
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "total_shuffle_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total shuffle movements within the DMS picking buffer in the last hour.",
    },
    ### Shuffle Movement Rate
    "multishuttle_shuffle_movement_rate": {
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "movement_type_code": "Shuffle",
        },
        "metric_units": "/hr",
        "config_type": "node",
        "node_name": "multishuttle",
        "metric_type": "shuffle_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of shuffle movements in the DMS picking buffer based on the last 15 minutes.",
    },
    ### ByPass Movements
    "multishuttle_total_bypass_movements": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "movement_type_code": "ByPass",
        },
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "total_bypass_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total bypass movements within the DMS picking buffer in the last hour.",
    },
    ### ByPass Movement Rate
    "multishuttle_bypass_movement_rate": {
        "views": ["facility"],
        "graph_operation": "area_node",
        "match_conditions": {
            "movement_type_code": "ByPass",
        },
        "metric_units": "/hr",
        "config_type": "node",
        "node_name": "multishuttle",
        "metric_type": "bypass_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of bypass movements in the DMS picking buffer based on the last 15 minutes.",
    },
    ### Intra Aisle Transfer Movements
    "multishuttle_total_iat_movements": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "movement_type_code": "IAT",
        },
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "total_iat_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total intra aisle transfer movements within the DMS picking buffer in the last hour.",
    },
    "multishuttle_iat_movement_rate": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "movement_type_code": "IAT",
        },
        "graph_operation": "area_node",
        "node_name": "multishuttle",
        "metric_type": "iat_rate",
        "metric_units": "/hr",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "description": "Hourly rate of intra-aisle transfer movements in the DMS picking buffer based on the last 15 minutes.",
    },
    #########################################################
    ### Aisle Module Metrics
    # Aisle - Number of Lifts
    "aisle_num_distinct_lifts": {
        "aggregation": "count",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "Aisle",
        "match_conditions": {
            "aisle_code": "^.+$",
            "lift_code": "^.+$",
        },
        "metric_type": "num_lifts",
        "node_name": "MSAI{aisle_code}",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{lift_code}",
        },
        "time_window": "60m_set",
        "views": ["multishuttle"],
        "description": "Number of lifts within this DMS aisle",
    },
    "aisle_num_distinct_shuttles": {
        "aggregation": "count",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "Aisle",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
        },
        "metric_type": "num_shuttles",
        "node_name": "MSAI{aisle_code}",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{shuttle_code}",
        },
        "time_window": "60m_set",
        "views": ["multishuttle"],
        "description": "Number of shuttles within this DMS aisle",
    },
    # Aisle - Total Movements
    "aisle_total_movements": {
        "config_type": "node",
        "views": ["multishuttle"],
        "label": "Aisle",
        "match_conditions": {
            "movement_type_code": "^Storage|Retrieval|Shuffle|ByPass|IAT$",
        },
        "graph_operation": "area_node",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "total_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total movements within this DMS aisle in the last hour.",
    },
    # Aisle - Total Storage Movements per aisle
    "aisle_total_storage_movements": {
        "graph_operation": "area_node",
        "views": ["multishuttle"],
        "match_conditions": {
            "movement_type_code": "Storage",
        },
        "parent_nodes": ["multishuttle"],
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "total_storage_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total storage movements within this DMS aisle in the last hour.",
    },
    # Aisle - Storage Rate per aisle
    "aisle_storage_rate": {
        "graph_operation": "area_node",
        "views": ["multishuttle"],
        "match_conditions": {
            "movement_type_code": "Storage",
        },
        "parent_nodes": ["multishuttle"],
        "metric_units": "/hr",
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "storage_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of storage movements within this DMS aisle based on the last 15 minutes.",
    },
    # Aisle - Total Retrieval Movements per aisle
    "aisle_movement_total_retrieval_movements": {
        "graph_operation": "area_node",
        "views": ["multishuttle"],
        "match_conditions": {
            "movement_type_code": "Retrieval",
        },
        "parent_nodes": ["multishuttle"],
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "total_retrieval_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total retrieval movements within this DMS aisle in the last hour.",
    },
    # Aisle - Retrieval Rate per aisle
    "aisle_retrieval_rate": {
        "graph_operation": "area_node",
        "views": ["multishuttle"],
        "match_conditions": {
            "movement_type_code": "Retrieval",
        },
        "parent_nodes": ["multishuttle"],
        "metric_units": "/hr",
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "retrieval_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of retrieval movements within this DMS aisle based on the last 15 minutes.",
    },
    # Total Shuffle Movements per aisle
    "aisle_total_shuffle_movements": {
        "config_type": "node",
        "parent_nodes": ["multishuttle"],
        "label": "Aisle",
        "graph_operation": "area_node",
        "views": ["multishuttle"],
        "match_conditions": {
            "movement_type_code": "Shuffle",
        },
        "node_name": "MSAI{aisle_code}",
        "metric_type": "total_shuffle_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total shuffle movements within this DMS aisle in the last hour.",
    },
    # Aisle - Shuffle Rate per aisle
    "aisle_shuffle_rate": {
        "graph_operation": "area_node",
        "views": ["multishuttle"],
        "match_conditions": {
            "movement_type_code": "Shuffle",
        },
        "parent_nodes": ["multishuttle"],
        "metric_units": "/hr",
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "shuffle_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of shuffle movements within this DMS aisle based on the last 15 minutes.",
    },
    # Total ByPass Movements per aisle
    "aisle_total_bypass_movements": {
        "graph_operation": "area_node",
        "views": ["multishuttle"],
        "label": "Aisle",
        "match_conditions": {
            "movement_type_code": "ByPass",
        },
        "parent_nodes": ["multishuttle"],
        "config_type": "node",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "total_bypass_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total bypass movements within this DMS aisle in the last hour.",
    },
    # Aisle - ByPass Rate per aisle
    "aisle_bypass_rate": {
        "graph_operation": "area_node",
        "views": ["multishuttle"],
        "match_conditions": {
            "movement_type_code": "ByPass",
        },
        "parent_nodes": ["multishuttle"],
        "metric_units": "/hr",
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "bypass_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of bypass movements within this DMS aisle based on the last 15 minutes.",
    },
    # Total Intra Aisle Transfer Movements per aisle
    "aisle_total_iat_movements": {
        "graph_operation": "area_node",
        "views": ["multishuttle"],
        "match_conditions": {
            "movement_type_code": "IAT",
        },
        "config_type": "node",
        "parent_nodes": ["multishuttle"],
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "total_iat_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total intra aisle transfer movements within this DMS aisle in the last hour.",
    },
    "aisle_iat_rate": {
        "graph_operation": "area_node",
        "views": ["multishuttle"],
        "match_conditions": {
            "movement_type_code": "IAT",
        },
        "parent_nodes": ["multishuttle"],
        "metric_units": "/hr",
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "iat_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of intra-aisle transfer movements within this DMS aisle based on the last 15 minutes.",
    },
    ### Aisle - Movements - used to calculate movements-per-fault
    "aisle_movements_per_fault_numerator": {
        "aggregation": "ratio",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "Aisle",
        "match_conditions": {
            "movement_type_code": "^Storage|Retrieval|Shuffle|ByPass|IAT$",
        },
        "metric_type": "movements_per_fault_numerator",
        "node_name": "MSAI{aisle_code}",
        "redis_operation": "event_set",
        "time_window": "60m_set",
        "views": ["multishuttle"],
        "description": "Total lift movements per fault within this DMS aisle over the last hour.",
    },
    ###############################################
    # Shuttle Node Metrics inside of Aisle Graph view
    # **Total Movements**
    "shuttle_total_shuttle_movements": {
        "config_type": "node",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
        },
        "node_name": "{shuttle_code}",
        "metric_type": "total_shuttle_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["MSAI{aisle_code}"],
        "graph_operation": "shuttle_node",
        "redis_operation": "event_set",
        "description": "Total movements made by this shuttle in the last hour.",
    },
    # **Storage Movements**
    "shuttle_total_storage_movements": {
        "config_type": "node",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
            "movement_type_code": "^Storage$",
        },
        "node_name": "{shuttle_code}",
        "metric_type": "total_shuttle_storage_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["MSAI{aisle_code}"],
        "graph_operation": "shuttle_node",
        "redis_operation": "event_set",
        "description": "Total storage movements made by this shuttle in the last hour.",
    },
    "shuttle_inventory_totes_storage_movements": {
        "config_type": "node",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
            "movement_type_code": "^Storage$",
            "sku_code": "^.+$",  # Ensures SKU exists
        },
        "node_name": "{shuttle_code}",
        "metric_type": "inventory_totes_shuttle_storage_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["MSAI{aisle_code}"],
        "graph_operation": "shuttle_node",
        "redis_operation": "event_set",
        "description": "Total inventory tote storage movements made by this shuttle in the last hour.",
    },
    "shuttle_empty_totes_storage_movements": {
        "config_type": "node",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
            "movement_type_code": "^Storage$",
            "sku_code": "^(null|)$",  # Matches missing SKU (null or empty)
        },
        "node_name": "{shuttle_code}",
        "metric_type": "empty_totes_shuttle_storage_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["MSAI{aisle_code}"],
        "graph_operation": "shuttle_node",
        "redis_operation": "event_set",
        "description": "Total empty tote storage movements made by this shuttle in the last hour.",
    },
    # **Retrieval Movements**
    "shuttle_total_retrieval_movements": {
        "config_type": "node",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
            "movement_type_code": "^Retrieval$",
        },
        "node_name": "{shuttle_code}",
        "metric_type": "total_shuttle_retrieval_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["MSAI{aisle_code}"],
        "graph_operation": "shuttle_node",
        "redis_operation": "event_set",
        "description": "Total retrieval movements made by this shuttle in the last hour.",
    },
    "shuttle_inventory_totes_retrieval_movements": {
        "config_type": "node",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
            "movement_type_code": "^Retrieval$",
            "sku_code": "^.+$",  # SKU must be present
        },
        "node_name": "{shuttle_code}",
        "metric_type": "inventory_totes_shuttle_retrieval_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["MSAI{aisle_code}"],
        "graph_operation": "shuttle_node",
        "redis_operation": "event_set",
        "description": "Total inventory tote retrieval movements made by this shuttle in the last hour.",
    },
    "shuttle_empty_totes_retrieval_movements": {
        "config_type": "node",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
            "movement_type_code": "^Retrieval$",
            "sku_code": "^(null|)$",  # Empty tote condition
        },
        "node_name": "{shuttle_code}",
        "metric_type": "empty_totes_shuttle_retrieval_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["MSAI{aisle_code}"],
        "graph_operation": "shuttle_node",
        "redis_operation": "event_set",
        "description": "Total empty tote retrieval movements made by this shuttle in the last hour.",
    },
    # **Shuffle Movements**
    "shuttle_total_shuffle_movements": {
        "config_type": "node",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
            "movement_type_code": "^Shuffle$",
        },
        "node_name": "{shuttle_code}",
        "metric_type": "total_shuttle_shuffle_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["MSAI{aisle_code}"],
        "graph_operation": "shuttle_node",
        "redis_operation": "event_set",
        "description": "Total shuffle movements made by this shuttle in the last hour.",
    },
    "shuttle_inventory_totes_shuffle_movements": {
        "config_type": "node",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
            "movement_type_code": "^Shuffle$",
            "sku_code": "^.+$",  # SKU must be present
        },
        "node_name": "{shuttle_code}",
        "metric_type": "inventory_totes_shuttle_shuffle_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["MSAI{aisle_code}"],
        "graph_operation": "shuttle_node",
        "redis_operation": "event_set",
        "description": "Total inventory tote shuffle movements made by this shuttle in the last hour.",
    },
    "shuttle_empty_totes_shuffle_movements": {
        "config_type": "node",
        "match_conditions": {
            "aisle_code": "^.+$",
            "shuttle_code": "^.+$",
            "movement_type_code": "^Shuffle$",
            "sku_code": "^(null|)$",  # Empty tote condition
        },
        "node_name": "{shuttle_code}",
        "metric_type": "empty_totes_shuttle_shuffle_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "views": ["MSAI{aisle_code}"],
        "graph_operation": "shuttle_node",
        "redis_operation": "event_set",
        "description": "Total empty tote shuffle movements made by this shuttle in the last hour.",
    },
    ###########################################
    ### Lift edges to Aisles inside Aisle View.
    "edge_from_lift_to_aisle": {
        "config_type": "complete-edge",
        "graph_operation": "area_edge",
        "inbound_area": "MSAI{aisle_code}",
        "label": "Aisle",
        "match_conditions": {
            "aisle_code": "^.+$",
            "handling_unit_code": "^.+$",
            "lift_code": "^MSAI.*ER.*LO01$",
            "movement_type_code": "^Storage$",
        },
        "outbound_area": "{lift_code}",
        "outbound_node_label": "Lift",
        "redis_operation": "event_set",
        "views": ["MSAI{aisle_code}"],
        "description": "Hourly rate of storage movements from lift to aisle based on the last 15 minutes.",
    },
    ### Lift edges from Aisles inside Aisle View.
    "edge_from_aisle_to_lift": {
        "config_type": "complete-edge",
        "graph_operation": "area_edge",
        "inbound_area": "{lift_code}",
        "label": "Lift",
        "match_conditions": {
            "aisle_code": "^.+$",
            "handling_unit_code": "^.+$",
            "lift_code": "^MSAI.*EL.*LO01$",
            "movement_type_code": "^Retrieval$",
        },
        "outbound_area": "MSAI{aisle_code}",
        "outbound_node_label": "Aisle",
        "redis_operation": "event_set",
        "views": ["MSAI{aisle_code}"],
        "description": "Hourly rate of retrieval movements from aisle to lift based on the last 15 minutes.",
    },
    ###############################################
    # Lift Node Metrics inside of Aisle Graph view
    # Lift - Total Movements
    "lift_total_movements": {
        "views": ["MSAI{aisle_code}"],
        "config_type": "node",
        "label": "Lift",
        "match_conditions": {
            "aisle_code": "^.+$",
            "lift_code": "^.+$",
        },
        "node_name": "{lift_code}",
        "metric_type": "total_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "graph_operation": "area_node",
        "redis_operation": "event_set",
        "description": "Total movements made by this lift in the last hour.",
    },
    # Lift - Storage Movements
    "lift_total_storage_movements": {
        "views": ["MSAI{aisle_code}"],
        "config_type": "node",
        "label": "Lift",
        "match_conditions": {
            "aisle_code": "^.+$",
            "lift_code": "^.+$",
            "movement_type_code": "^Storage$",
        },
        "node_name": "{lift_code}",
        "metric_type": "total_storage_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "graph_operation": "area_node",
        "redis_operation": "event_set",
        "description": "Total storage movements made by this lift in the last hour.",
    },
    # Lift - Retrieval Movements
    "lift_total_retrieval_movements": {
        "views": ["MSAI{aisle_code}"],
        "config_type": "node",
        "label": "Lift",
        "match_conditions": {
            "aisle_code": "^.+$",
            "lift_code": "^.+$",
            "movement_type_code": "^Retrieval$",
        },
        "node_name": "{lift_code}",
        "metric_type": "total_retrieval_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "graph_operation": "area_node",
        "redis_operation": "event_set",
        "description": "Total retrieval movements made by this lift in the last hour.",
    },
    # Lift - Shuffle Movements
    "lift_total_shuffle_movements": {
        "views": ["MSAI{aisle_code}"],
        "config_type": "node",
        "label": "Lift",
        "match_conditions": {
            "aisle_code": "^.+$",
            "lift_code": "^.+$",
            "movement_type_code": "^Shuffle$",
        },
        "node_name": "{lift_code}",
        "metric_type": "total_shuffle_movements",
        "time_window": "60m_set",
        "aggregation": "count",
        "graph_operation": "area_node",
        "redis_operation": "event_set",
        "description": "Total shuffle movements made by this lift in the last hour.",
    },
    # Lift - Retrieval Rate
    "lift_retrieval_rate": {
        "views": ["MSAI{aisle_code}"],
        "config_type": "node",
        "label": "Lift",
        "match_conditions": {
            "aisle_code": "^.+$",
            "lift_code": "^.+$",
            "movement_type_code": "^Retrieval$",
        },
        "node_name": "{lift_code}",
        "metric_type": "retrieval_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "graph_operation": "area_node",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of retrieval movements made by this lift based on the last 15 minutes.",
    },
    # Lift - Storage Rate
    "lift_storage_movement_rate": {
        "views": ["MSAI{aisle_code}"],
        "config_type": "node",
        "label": "Lift",
        "match_conditions": {
            "aisle_code": "^.+$",
            "lift_code": "^.+$",
            "movement_type_code": "^Storage$",
        },
        "node_name": "{lift_code}",
        "metric_type": "storage_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "graph_operation": "area_node",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "description": "Hourly rate of storage movements made by this lift based on the last 15 minutes.",
    },
}
