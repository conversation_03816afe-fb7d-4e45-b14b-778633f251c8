from typing import List, Dict, Any, Optional
from pydantic import BaseModel


class TestCase(BaseModel):
    """
    Defines the structure for an individual test case.

    This class models the expected structure for unit tests that validate 
    metric processor configs. It includes a test description, 
    test input, and expected output.

    Attributes:
        test_description (str): 
            A brief description of the test case's purpose.
        test_input (List[Dict[str, Any]]): 
            A list of dictionaries representing the input data. Each dictionary 
            contains fields that simulate a single fact message, 
            such as `row_hash`, `aisle_code`, `shuttle_code`, and timestamps.
        expected_output (List[Dict[str, Any]]): 
            A list of dictionaries representing the expected results. 
            - **Node metric tests** include resolved metric names.
            - **Edge metric tests** do NOT include resolved metric names, only success flags. 
                (Resolved names in edge metrics rely on calls to external services).

    """

    test_description: str
    test_input: List[Dict[str, Any]]
    expected_output: List[Dict[str, Any]]


class TestSuite(BaseModel):
    """
    Defines a test suite containing multiple test cases.

    Attributes:
        test_suite (str): Name of the test suite.
        test_suite_description (str): Description of the test suite.
        tests (List[TestCase]): A list of test cases.
    """

    test_suite: str
    test_suite_description: str
    tests: List[TestCase]
