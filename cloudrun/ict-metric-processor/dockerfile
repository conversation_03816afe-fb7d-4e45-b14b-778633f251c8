# Use the official Python image, which includes the necessary tools and libraries.
FROM python:3.11-slim-bullseye

# Set the working directory.
WORKDIR /usr/app

# Install Poetry.
RUN pip install --no-cache-dir poetry

# Copy only the Poetry files first for better cache utilization.
COPY pyproject.toml poetry.lock ./

# Configure Poetry to:
# - Install without creating a virtual environment inside the container (`--no-root` installs only dependencies, not the project itself).
RUN poetry config virtualenvs.create false \
    && poetry install --no-interaction --no-ansi --no-root --verbose 

# Copy the local code to the container image.
COPY . /usr/app/cloudrun/ict-metric-processor/

# Change to the directory containing the application.
WORKDIR /usr/app/cloudrun/ict-metric-processor

# Make the entry script executable.
RUN chmod +x ./entry.sh

# Expose the port the app runs on.
ARG PORT=8080
ENV PORT=${PORT}
EXPOSE ${PORT}

# Run the entry script which will start the application.
CMD ["./entry.sh"]
