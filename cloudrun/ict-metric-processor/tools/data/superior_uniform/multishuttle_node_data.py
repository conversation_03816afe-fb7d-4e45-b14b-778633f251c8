from generate_timestamps import update_timestamp

messages = [
    {
        "messageId": "test-multishuttle-multishuttle_movement-1-ignored",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Ignoresthe invetnory metrics: (inventory_totes_shuttle_storage_movements, inventory_totes_shuttle_shuffle_movements, inventory_totes_shuttle_retrieval_movements).",
            "row_hash": "100",
        },
        "data": {
            "aisle_code": "03",
            "destination_location_code": "MSAI03CL01DS13",
            "level_code": "MSAI03EL01LO01",
            "handling_unit_code": "522ZBA",
            "movement_end_timestamp_utc": update_timestamp(800),
            "movement_type_code": "Shuffle",
            "source_location_code": "MSAI03LL01RO11",
            "sku_code": "SK54321XYZ",  # SKU present
            "shuttle_code": "MSAI03LV01SH03",
        },
    },
    {
        "messageId": "test-multishuttle-multishuttle_movement-1A-ignored",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Ignores the invetnory metrics: (inventory_totes_shuttle_storage_movements, inventory_totes_shuttle_shuffle_movements, inventory_totes_shuttle_retrieval_movements).",
            "row_hash": "101",
        },
        "data": {
            "aisle_code": "00",
            "destination_location_code": "MSAI00CL01DS13",
            "level_code": "MSAI00EL01LO01",
            "handling_unit_code": "522ZBA",
            "movement_end_timestamp_utc": update_timestamp(800),
            "movement_type_code": "Shuffle",
            "source_location_code": "MSAI00LL01RO11",
            "sku_code": "SK54321XYZ",  # SKU present
            "shuttle_code": "MSAI00LV01SH03",
        },
    },
    {
        "messageId": "test-multishuttle-multishuttle_movement-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Creates the invetnory metrics: (inventory_totes_shuttle_storage_movements, inventory_totes_shuttle_shuffle_movements, inventory_totes_shuttle_retrieval_movements).",
            "row_hash": "102",
        },
        "data": {
            "aisle_code": "01",
            "destination_location_code": "MSAI01CL01DS13",
            "level_code": "MSAI01EL01LO01",
            "handling_unit_code": "522ZBA",
            "movement_end_timestamp_utc": update_timestamp(800),
            "movement_type_code": "Shuffle",
            "source_location_code": "MSAI01LL01RO11",
            "sku_code": "SK54321XYZ",  # SKU present
            "shuttle_code": "MSAI01LV01SH03",
        },
    },
    {
        "messageId": "test-multishuttle-multishuttle_movement-1A",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Creates the invetnory metrics: (inventory_totes_shuttle_storage_movements, inventory_totes_shuttle_shuffle_movements, inventory_totes_shuttle_retrieval_movements).",
            "row_hash": "103",
        },
        "data": {
            "aisle_code": "02",
            "destination_location_code": "MSAI02CL01DS13",
            "level_code": "MSAI02EL01LO01",
            "handling_unit_code": "522ZBA",
            "movement_end_timestamp_utc": update_timestamp(800),
            "movement_type_code": "Shuffle",
            "source_location_code": "MSAI02LL01RO11",
            "sku_code": "SK54321XYZ",  # SKU present
            "shuttle_code": "MSAI02LV01SH03",
        },
    },
    {
        "messageId": "test-multishuttle-multishuttle_movement-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Creates the invetnory metrics: (inventory_totes_shuttle_storage_movements, inventory_totes_shuttle_shuffle_movements, inventory_totes_shuttle_retrieval_movements).",
            "row_hash": "104",
        },
        "data": {
            "aisle_code": "01",
            "destination_location_code": "MSAI01LL01RO11",
            "movement_end_timestamp_utc": update_timestamp(200),
            "level_code": "",
            "handling_unit_code": "AAY33167",
            "movement_type_code": "Retrieval",
            "source_location_code": "MS011019030111",
            "sku_code": "SK55555XYZ",  # SKU present
            "shuttle_code": "MSAI01LV01SH03",
        },
    },
    {
        "messageId": "test-multishuttle-multishuttle_movement-3",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Creates IAT Movement",
            "row_hash": "105",
        },
        "data": {
            "aisle_code": "01",
            "destination_location_code": "MSAI01CL01DS13",
            "level_code": "MSAI01EL01LO01",
            "handling_unit_code": "522ZBA",
            "movement_end_timestamp_utc": update_timestamp(300),
            "movement_type_code": "IAT",
            "source_location_code": "MSAI01LL01RO11",
            "sku_code": "SK54321XYZ",  # SKU present
            "shuttle_code": "MSAI01LV01SH03",
        },
    },
    {
        "messageId": "test-multishuttle-bin_utilization",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "bin_utilization",
            "description": "Creates the following metrics: (storage_utilization, location_distribution_occupied, location_distribution_available)",
            "row_hash": "106",
        },
        "data": {
            "aisle_code": "01",
            "area_code": "MS - Storage",
            "empty_location_position_count": 2036,
            "event_timestamp_utc": update_timestamp(510),
            "total_location_position_count": 8556,
        },
    },
    {
        "messageId": "test-multishuttle-viz_event_log-1",
        "attributes": {
            "event_type": "viz_event_log",
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "description": "A fault within Multishuttle, creates the movements_per_fault numerator.",
            "row_hash": "107",
        },
        "data": {
            "classification": "manual",
            "area": "DMS1",
            "event_timestamp_utc": update_timestamp(810),
            "fault_duration_milliseconds": "2000",
            "fault_description": "U117 JAM DETECTED",
        },
    },
    {
        "messageId": "test-multishuttle-nok-interval-batch-1",
        "attributes": {
            "description": "Creates NOK interval metrics for dms multishuttle shuttle",
            "event_type": "nok_interval",
            "facility_id": "superioruniform_eudoraar",
            "row_hash": "108",
            "tenant_id": "superior_uniform",
        },
        "data": {
            "fault_start_timestamp_utc": update_timestamp(800),
            "fault_end_timestamp_utc": update_timestamp(810),
            "fault_duration_milliseconds": "10000",
            "equipment_code": "MSAI01LV12SH01", # Corresponds to a shuttle
        }
    },
    {
        "messageId": "test-multishuttle-nok-interval-batch-2",
        "attributes": {
            "description": "Creates NOK interval metrics for dms multishuttle lift",
            "event_type": "nok_interval",
            "facility_id": "superioruniform_eudoraar",
            "row_hash": "109",
            "tenant_id": "superior_uniform",
        },
        "data": {
            "fault_start_timestamp_utc": update_timestamp(800),
            "fault_end_timestamp_utc": update_timestamp(810),
            "fault_duration_milliseconds": "10000",
            "equipment_code": "MSAI01EL01LO01", # Corresponds to a lift
        }
    },
    {
        "messageId": "test-multishuttle-nok-interval-batch-3",
        "attributes": {
            "description": "Creates NOK interval metrics for dms multishuttle lift",
            "event_type": "nok_interval",
            "facility_id": "superioruniform_eudoraar",
            "row_hash": "110",
            "tenant_id": "superior_uniform",
        },
        "data": {
            "fault_start_timestamp_utc": update_timestamp(800),
            "fault_end_timestamp_utc": update_timestamp(810),
            "fault_duration_milliseconds": "10000",
            "equipment_code": "MSAI02EL01LO01", # Corresponds to a lift
        }
    },
    {
        "messageId": "test-multishuttle-bin_utilization",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "bin_utilization",
            "description": "Creates the following metrics: (storage_utilization, location_distribution_occupied, location_distribution_available)",
            "row_hash": "111",
        },
        "data": {
            "aisle_code": "02",
            "area_code": "MS - Storage",
            "empty_location_position_count": 2036,
            "event_timestamp_utc": update_timestamp(510),
            "total_location_position_count": 8556,
        },
    },
    {
        "messageId": "test-multishuttle-multishuttle_movement-1A",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Creates the invetnory metrics: (total_movements, bypass movements).",
            "row_hash": "103",
        },
        "data": {
            "aisle_code": "01",
            "destination_location_code": "MSAI01CL01DS13",
            "level_code": "MSAI01EL01LO01",
            "handling_unit_code": "522ZBA",
            "movement_end_timestamp_utc": update_timestamp(200),
            "movement_type_code": "ByPass",
            "source_location_code": "MSAI01LL01RO11",
            "sku_code": "SK54321XYZ",  # SKU present
            "shuttle_code": "MSAI01LV01SH03",
        }
    },
    {
        "messageId": "test-multishuttle-multishuttle_movement-1A",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Creates the invetnory metrics: (total_movements, bypass movements).",
            "row_hash": "103",
        },
        "data": {
            "aisle_code": "02",
            "destination_location_code": "MSAI02CL01DS13",
            "level_code": "MSAI02EL01LO01",
            "handling_unit_code": "522ZBA",
            "movement_end_timestamp_utc": update_timestamp(200),
            "movement_type_code": "ByPass",
            "source_location_code": "MSAI02LL01RO11",
            "sku_code": "SK54321XYZ",  # SKU present
            "shuttle_code": "MSAI02LV01SH03",
        }
    },
]
