from generate_timestamps import update_timestamp


recent_timestamp = update_timestamp()

messages = [
    {
        "messageId": "receiving-connection_movement1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "connection_movement",
            "description": "Fact data that generates stock time metrics.",
            "row_hash": "1"
        },
        "data": {
            "movement_end_timestamp_utc":update_timestamp(500),
            "destination_location_code":"RECV-INDUCT",
            "transportTaskId":"2400a5f0-c08b-4239-928c-c3cd8f0c8af6",
            "handling_unit_code":"123",
        }
    },
    {
        "messageId": "receiving-connection_movement-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "connection_movement",
            "description": "Fact data that generates stock time metrics.",
            "row_hash": "2"
        },
        "data": {
            "movement_end_timestamp_utc":update_timestamp(50),
            "source_location_code":"RECV-INDUCT",
            "transportTaskId":"2400a5f0-c08b-4239-928c-c3cd8f0c8af6",
            "handling_unit_code":"123",
        },
    }
]
