
messages = [
    {
        "messageId": "test-receiving-transport_task_confirmation-batch-1",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "transport_task_confirmation",
        },
        "data": [
            {
                "destinationArea":"RCV",
                "destination_location_code":"MCF_TC.RCV.0.0.CCRE01NP01",
                "eventDate":"2024-03-20T17:41:53.771743342Z",
                "eventTime":"2024-03-20T17:41:53.771743342Z",
                "facilityName":"MCF_TC",
                "handlingUnitBid":"*********",
                "movementTypeName":"OTHER",
                "simplifiedHandlingUnitTypeName":"CS01",
                "sourceArea":"RCV",
                "source_location_code":"MCF_TC.RCV.0.0.CCRE01IS01",
                "transportTaskEntityKey":"50",
                "transportTaskType":"INBOUND"
            },
            {
                "destinationArea":"RCV",
                "destination_location_code":"MCF_TC.RCV.0.0.CCRE01NP01",
                "eventDate":"2024-03-20T18:15:33.829653363Z",
                "eventTime":"2024-03-20T18:15:33.829653363Z",
                "facilityName":"MCF_TC",
                "handlingUnitBid":"*********",
                "movementTypeName":"OTHER",
                "simplifiedHandlingUnitTypeName":"CS01",
                "sourceArea":"RCV",
                "source_location_code":"MCF_TC.RCV.0.0.CCRE01IS01",
                "transportTaskEntityKey":"57",
                "transportTaskType":"INBOUND"
                
            },
            {
                "destinationArea":"RCV",
                "destination_location_code":"MCF_TC.RCV.0.0.CCRE01NP01",
                "eventDate":"2024-02-08T17:37:34.960890503Z",
                "eventTime":"2024-02-08T17:37:34.960890503Z",
                "facilityName":"MCF_TC",
                "movementTypeName":"OTHER",
                "simplifiedHandlingUnitTypeName":"CS01",
                "sourceArea":"RCV",
                "source_location_code":"MCF_TC.RCV.0.0.CCRE01IS01",
                "transportTaskEntityKey":"9",
                "transportTaskType":"INBOUND"
            },
            {
                "destinationArea":"RCV",
                "destination_location_code":"MCF_TC.RCV.0.0.CCRE01NP01",
                "eventDate":"2024-04-25T17:27:00.904793203Z",
                "eventTime":"2024-02-08T17:37:34.960890503Z",
                "facilityName":"MCF_TC",
                "handlingUnitBid":"*********",
                "movementTypeName":"OTHER",
                "simplifiedHandlingUnitTypeName":"CS01",
                "sourceArea":"RCV",
                "source_location_code":"MCF_TC.RCV.0.0.CCRE01IS01",
                "transportTaskEntityKey":"13",
                "transportTaskType":"INBOUND"
            },
            {
                "destinationArea":"RCV",
                "destination_location_code":"MCF_TC.RCV.0.0.CCRE01NP01",
                "eventDate":"2024-02-07T22:21:17.474174608Z",
                "eventTime":"2024-02-08T17:37:34.960890503Z",
                "facilityName":"MCF_TC",
                "movementTypeName":"OTHER",
                "simplifiedHandlingUnitTypeName":"CS01",
                "sourceArea":"RCV",
                "source_location_code":"MCF_TC.RCV.0.0.CCRE01IS01",
                "transportTaskEntityKey":"3",
                "transportTaskType":"INBOUND"
            },
        ]
    },
    {
        "messageId": "test-receiving-connection_movement-batch-2",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "connection_movement",
        },
        "data": [
            {                
                "start_timestamp_utc":"2024-02-02T14:54:31.606338Z",
                "source_location_code":"MCF_TC.RCV.0.0.CCRE01IS01",
                "load_unit_code":"UF-001-0624",
                "message_uid": "10000001",
            },
            {
                "start_timestamp_utc":"2024-02-02T14:59:32.076469Z",
                "source_location_code":"MCF_TC.RCV.0.0.CCRE01NP01",
                "load_unit_code":"UF-001-0624",
                "message_uid": "10000002",
            }
        ]
    }
]
