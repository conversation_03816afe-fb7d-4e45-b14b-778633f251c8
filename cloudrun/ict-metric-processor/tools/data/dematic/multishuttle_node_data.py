messages = [
    {
        "messageId": "test-multishuttle-connection_movement-data-is-a-single-fact-not-a-batch",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "connection_movement",
        },
        "data": {
            "start_timestamp_utc": "2024-04-09T15:09:27.908816Z",
            "source_location_code": "AI1834KHMPS01",  # Matches AI.*PS
            "transport_request_code": "d320cb9a-8d9a-423d-8844-8e043f5c2a0c",
            "load_unit_code": "C040000834",
        },
    },
    {
        "messageId": "test-multishuttle-connection_movement-data-is-a-string-and-should-fail",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "connection_movement",
        },
        "data": "This should fail, because it's not a dict, or a list of dicts.",
    },
    {
        "messageId": "test-multishuttle-fact-type-does-not-exist-should-fail",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "viz_event_log",
        },
        "data": {
            "start_timestamp_utc": "2024-04-09T15:09:27.908816Z",
            "source_location_code": "AI1834KHMPS01",  # Matches AI.*PS
            "transport_request_code": "d320cb9a-8d9a-423d-8844-8e043f5c2a0c",
            "load_unit_code": "C040000834",
        },
    },
    {
        "messageId": "test-multishuttle-connection_movement-batch-1",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "connection_movement",
        },
        "data": [
            {
                "start_timestamp_utc": "2024-04-09T15:09:27.908816Z",
                "source_location_code": "AI1834KHMPS01",  # Matches AI.*PS
                "transport_request_code": "d320cb9a-8d9a-423d-8844-8e043f5c2a0c",
                "load_unit_code": "C040000834",
            },
            {
                "start_timestamp_utc": "2024-04-09T15:09:27.908816Z",
                "source_location_code": "AI1834KSL01",  # Matches AI.*SL01
                "transport_request_code": "d320cb9a-8d9a-423d-8844-8e043f5c2a0c",
                "load_unit_code": "C040000834",
            },
            {
                "start_timestamp_utc": "2024-04-09T15:09:27.908816Z",
                "destination_location_code": "MSAI1834PS01",  # Matches MSAI.*PS
                "transport_request_code": "d320cb9a-8d9a-423d-8844-8e043f5c2a0c",
                "load_unit_code": "C040000834",
            },
            {
                "start_timestamp_utc": "2024-04-09T15:09:27.908816Z",
                "source_location_code": "AI1834KSL01",  # Matches AI.*SL01
                "transport_request_code": "d320cb9a-8d9a-423d-8844-8e043f5c2a0c",
                "load_unit_code": "C040000834",
            },
            {
                "start_timestamp_utc": "2024-04-09T15:09:27.908816Z",
                "source_location_code": "CCAI1834PS01",  # Matches CCAI.*PS.*
                "transport_request_code": "d320cb9a-8d9a-423d-8844-8e043f5c2a0c",
                "load_unit_code": "C040000834",
            },
            {
                "start_timestamp_utc": "2024-04-09T15:09:27.908816Z",
                "source_location_code": "CCAI1834GR01NP07",  # Matches CCAI.*GR01NP07
                "transport_request_code": "d320cb9a-8d9a-423d-8844-8e043f5c2a0c",
                "load_unit_code": "C040000834",
            },
            {
                "start_timestamp_utc": "2024-04-09T15:09:27.908816Z",
                "source_location_code": "CCAI1834GR01NP07",  # Matches CCAI.*GR01NP07
                "transport_request_code": "d320cb9a-8d9a-423d-8844-8e043f5c2a0c",
                "load_unit_code": "C040000834",
            },
        ]
    },
    {
        "messageId": "test-multishuttle-multishuttle_movement-batch-2",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "multishuttle_movement",
            "description": "Retrieval movement data for multishuttle"
        },
        "data": [
            {"aisle":"2","destination_location_code":"MSAI02CL01DS13","end_timestamp_utc":"2024-10-29T14:22:29.413-04:00","event_code":"OK","level_code":"MSAI02EL01LO01","load_unit_code":"300ZXU","movement_type_code":"Retrieval","project_sku":"82704","project_sku_size":"L","shuttle_id":"","sku_code":"82704L","source_location_code":"MSAI02LL01RO11","start_timestamp_utc":"2024-10-29T14:21:26.274-04:00","transport_request_code":"183700007"},
            {"aisle":"2","destination_location_code":"MSAI02CL01DS13","end_timestamp_utc":"2024-10-29T14:22:19.931-04:00","event_code":"OK","level_code":"MSAI02EL01LO01","load_unit_code":"522ZBA","movement_type_code":"Retrieval","project_sku":"JTN2633NV","project_sku_size":"36W34L","shuttle_id":"","sku_code":"JTN2633NV36W34L","source_location_code":"MSAI02LL01RO11","start_timestamp_utc":"2024-10-29T14:21:47.180-04:00","transport_request_code":"184900007"},
            {"aisle":"1","destination_location_code":"MSAI01LL01RO11","end_timestamp_utc":"2024-10-29T14:01:30.978-04:00","event_code":"OK","level_code":"","load_unit_code":"AAY33167","movement_type_code":"Retrieval","project_sku":"JTN1426BK","project_sku_size":"40","shuttle_id":"MSAI01LV03SH01","sku_code":"JTN1426BK40","source_location_code":"MS011019030111","start_timestamp_utc":"2024-10-29T14:00:55.446-04:00","transport_request_code":"166600007"},
            {"aisle":"1","destination_location_code":"MSAI01LL01RO11","end_timestamp_utc":"2024-10-29T14:01:30.978-04:00","event_code":"OK","level_code":"","load_unit_code":"AAY33167","movement_type_code":"Retrieval","project_sku":"JTN1426BK","project_sku_size":"40","shuttle_id":"MSAI01LV03SH01","sku_code":"JTN1426BK40","source_location_code":"MS011019030111","start_timestamp_utc":"2024-10-29T14:00:55.446-04:00","transport_request_code":"166600007"},
        ]
    },
    {
        "messageId": "test-multishuttle-bin_utilization-batch-3",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "bin_utilization",
            "description": "multishuttle storage location utilization data + location distribution data."
        },
        "data": [
            {
                "aisle_code":"1",
                "area_code":"ML - Storage",
                "empty_location_position_count":"2036",
                "percent_empty_openings":" 23.80",
                "percent_empty_storage_locations":" 23.80",
                "total_location_position_count":"8556",
                "total_openings":"8556",
            },
            {"aisle_code":"7","empty_location_position_count":"0","EmptyOpenings":"0","percent_empty_openings":"  0.00","percent_empty_storage_locations":"  0.00","total_location_position_count":"8556","total_openings":"8556","area_code":"ML - Storage"},
            {"aisle_code":"6","area_code":"ML - Storage","total_openings":"8556","EmptyOpenings":"0","total_location_position_count":"8556","empty_location_position_count":"0","percent_empty_openings":"  0.00","percent_empty_storage_locations":"  0.25",},
            {"aisle_code":"1","area_code":"ML - Storage","total_openings":"8556","EmptyOpenings":"2318","total_location_position_count":"8556","empty_location_position_count":"2318","percent_empty_openings":" 27.09","percent_empty_storage_locations":" 27.09",},
        ],
    },
    {
        "messageId": "test-multishuttle-multishuttle_movement-batch-4",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "multishuttle_movement",
            "description": "Storage and Retrieval movement data for storage rate, storage total, retrival rate, retrieval total."
        },
        "data": [
            {"aisle":"1","destination_location_code":"MSAI01LR01RI12","end_timestamp_utc":"2024-09-04T11:55:51.350-05:00","event_code":"OK","level_code":"MSAI01ER01LO01","load_unit_code":"AOP398","movement_type_code":"Storage","project_sku":"ESA9002CH","project_sku_size":"M","shuttle_id":"","sku_code":"ESA9002CHM","source_location_code":"MSAI01CR01PS12","start_timestamp_utc":"2024-09-04T11:55:12.886-05:00","transport_request_code":"32738"},
            {"aisle":"1","destination_location_code":"MSAI01LL01RO11","end_timestamp_utc":"2024-09-04T11:56:11.647-05:00","event_code":"OK","level_code":"","load_unit_code":"ABH277","movement_type_code":"Retrieval","project_sku":"LWE4002RD","project_sku_size":"M","shuttle_id":"MSAI01LV23SH01","sku_code":"LWE4002RDM","source_location_code":"MS011158230211","start_timestamp_utc":"2024-09-04T11:55:00.417-05:00","transport_request_code":"33007"},
            {"aisle":"2","destination_location_code":"MS021059060211","end_timestamp_utc":"2024-09-04T11:56:23.783-05:00","event_code":"OK","level_code":"","load_unit_code":"AGG507","movement_type_code":"Storage","project_sku":"IH2927BK/RD","project_sku_size":"OSFA","shuttle_id":"MSAI02LV06SH01","sku_code":"IH2927BK/RDOSFA","source_location_code":"MSAI02LR01RI12","start_timestamp_utc":"2024-09-04T11:55:41.428-05:00","transport_request_code":"32613"},
            {"aisle":"1","destination_location_code":"MSAI01LL01RO11","end_timestamp_utc":"2024-09-04T11:56:26.408-05:00","event_code":"OK","level_code":"","load_unit_code":"BAN565","movement_type_code":"Retrieval","project_sku":"LOV6013YW","project_sku_size":"NS","shuttle_id":"MSAI01LV20SH01","sku_code":"LOV6013YWNS","source_location_code":"MS012028200211","start_timestamp_utc":"2024-09-04T11:53:43.078-05:00","transport_request_code":"32976"},
        ],
    },
    {
        "messageId": "test-multishuttle-nok_interval-batch-5",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "nok_interval",
            "description": "Used for movements_per_fault metric.  This event is the fault that causes the currently accumulated number of movements to get saved into redis."
        },
        "data": [
            {
                "classification":"manual",
                "equipment_code":"MSAI02LL01RO12",
                "end_timestamp_utc":"2024-09-04T11:51:39.000-05:00",
                "fault_duration_milliseconds":"2000",
                "start_timestamp_utc":"2024-09-04T11:51:37.000-05:00",
                "status_code":"M:DMSRackOutfeedDevi"
            }
        ],
    },
    {
        "messageId": "test-multishuttle-storage-retrieval-movements-batch-6",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi", 
            "event_type": "multishuttle_movement",
            "description": "Retrieval and Storage event data for multishuttle metrics"
        },
        "data": [
            {
                "aisle":"03",
                "destination_location_id":"MCF_TC.MS01.03.ER01.MSAI03CR01DS10",
                "event_date":"2024-01-30T18:39:58.188938916Z",
                "level":"",
                "lift_id":"",
                "movement_type_code":"Storage",
                "source_location_id":"MCF_TC.MS01.03.EL01.MSAI03CL01PS10",
                "transport_unit_id":"C040000857"
            },
            {
                "aisle":"04",
                "destination_location_id":"MCF_TC.MS01.04.ER01.MSAI04CR01DS10",
                "event_date":"2024-01-30T18:39:58.188938916Z",
                "level":"",
                "lift_id":"",
                "movement_type_code":"Retrieval",
                "source_location_id":"MCF_TC.MS01.03.EL01.MSAI03CL01PS10",
                "transport_unit_id":"C040000857"
            },
            {
                "aisle":"03",
                "destination_location_id":"MCF_TC.MS01.03.ER01.MSAI03CR01DS10",
                "event_date":"2024-01-30T19:39:58.188938916Z",
                "level":"",
                "lift_id":"",
                "movement_type_code":"Storage",
                "source_location_id":"MCF_TC.MS01.03.EL01.MSAI03CL01PS10",
                "transport_unit_id":"C0858858858"
            },
            {
                "aisle":"04",
                "destination_location_id":"MCF_TC.MS01.04.ER01.MSAI04CR01DS10",
                "event_date":"2024-01-30T19:39:58.188938916Z",
                "level":"",
                "lift_id":"",
                "movement_type_code":"Retrieval",
                "source_location_id":"MCF_TC.MS01.03.EL01.MSAI03CL01PS10",
                "transport_unit_id":"C0858858858"
            },
                {
                "aisle":"04",
                "destination_location_id":"MCF_TC.MS01.04.ER01.MSAI04CR01DS10",
                "event_date":"2024-01-30T20:39:58.188938916Z",
                "level":"",
                "lift_id":"",
                "movement_type_code":"Shuffle",
                "source_location_id":"MCF_TC.MS01.03.EL01.MSAI03CL01PS10",
                "transport_unit_id":"C0858858858"
            },
        ],
    },
    {
        "messageId": "test-multishuttle-fault_event-devices-batch-6",
        "attributes": {
            "tenant_id": "dematic",
            "facility_id": "grandrapids_mi",
            "event_type": "fault_event",
            "description": "Data related to status of multishuttle devies."
        },
        "data": [
            ### Lifts ###
            {
                "availability_status":"AU",
                "device_id_code":"MSAI000ER000LO000",
                "event_time_timestamp_utc":"2024-09-04T11:51:39.000-05:00",
            },
            {
                "availability_status":"AU",
                "device_id_code":"MSAI000EL000LO001",
                "event_time_timestamp_utc":"2024-09-04T12:01:39.000-05:00",
            },
            {
                "availability_status":"AU",
                "device_id_code":"MSAI000ER000LO002",
                "event_time_timestamp_utc":"2024-09-04T12:11:39.000-05:00",
            },
            {
                "availability_status":"AU",
                "device_id_code":"MSAI000EL000LO003",
                "event_time_timestamp_utc":"2024-09-04T12:21:39.000-05:00",
            },
            {
                "availability_status":"MA",
                "device_id_code":"MSAI000EL000LO004",
                "event_time_timestamp_utc":"2024-09-04T12:31:39.000-05:00",
            },
            ### Shuttles ###
            {
                "availability_status":"AU",
                "device_id_code":"MSAI000LV000SH001",
                "event_time_timestamp_utc":"2024-09-04T13:00:39.000-05:00",
            },
            {
                "availability_status":"AU",
                "device_id_code":"MSAI000LV000SH002",
                "event_time_timestamp_utc":"2024-09-04T14:00:39.000-05:00",
            },
            {
                "availability_status":"AU",
                "device_id_code":"MSAI000LV000SH003",
                "event_time_timestamp_utc":"2024-09-04T15:00:39.000-05:00",
            },
            {
                "availability_status":"AU",
                "device_id_code":"MSAI000LV000SH004",
                "event_time_timestamp_utc":"2024-09-04T16:00:39.000-05:00",
            },
            {
                "availability_status":"OL",
                "device_id_code":"MSAI000LV000SH001",
                "event_time_timestamp_utc":"2024-09-04T17:00:39.000-05:00",
            },
            {
                "availability_status":"Null",
                "device_id_code":"MSAI000LV000SH002",
                "event_time_timestamp_utc":"2024-09-04T17:00:39.000-05:00",
            },
        ],
    },
]

