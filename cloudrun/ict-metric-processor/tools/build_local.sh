#!/bin/bash

CONTAINER_NAME="ict-metric-processor"
NETWORK_NAME="control_tower_network"

echo "Stopping and removing all containers matching '$CONTAINER_NAME'..."
docker ps -aq --filter "name=$CONTAINER_NAME" | xargs -r docker stop
docker ps -aq --filter "name=$CONTAINER_NAME" | xargs -r docker rm

echo "Removing volumes that are not in use at the moment"
docker volume ls -q | xargs -r docker volume rm

# Create Docker Network
if ! docker network inspect "$NETWORK_NAME" >/dev/null 2>&1; then
    echo "Creating Docker network: $NETWORK_NAME"
    docker network create "$NETWORK_NAME"
else
    echo "Docker network $NETWORK_NAME already exists."
fi

# Start fresh instances of the containers
echo "Starting containers..."
docker compose up -d

# Change to the project root directory
cd "$(dirname "$0")/.." || exit

# Verify mkdocs.yml exists
if [ ! -f "mkdocs.yml" ]; then
    echo "Error: mkdocs.yml not found in $(pwd)"
    exit 1
fi

# Kill any process running on port 8000
PORT=8000
lsof -ti :$PORT | xargs -r kill -9

# Start MkDocs documentation server
echo "Starting MkDocs documentation server..."
mkdocs serve