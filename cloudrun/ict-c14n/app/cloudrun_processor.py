# standard imports
import logging

# custom imports
from c14n_data_processor.processor import DataProcessor


class CanonicalizationProcessor:
    def __init__(
        self,
        tenant,
        facility,
        schema,
        logger=None,
        tz_identifier="UTC",
        allow_unknown=True,
        purge_unknown=False,
    ):
        self.tenant = tenant
        self.facility = facility
        self.schema = schema
        self.tz_identifier = tz_identifier
        self.allow_unknown = allow_unknown
        self.purge_unknown = purge_unknown
        self.logger = logger or logging.getLogger(__name__)

        # Initialize DataProcessor with the schema, validator, tz_identifier, and logger
        self.data_processor = DataProcessor(
            schema=self.schema, logger=self.logger, tz_identifier=self.tz_identifier
        )

    def process_messages(self, messages):
        for item in messages:
            preprocessed_item = self.data_processor.duplicate_w_known_tz(
                item, create_utc=True, create_local=True
            )
            if self.data_processor.validate_item(preprocessed_item):
                processed_item = self.data_processor.validator.normalized(
                    preprocessed_item
                )
                if processed_item is not None:
                    self.data_processor.processed_items.append(processed_item)
                    #self.logger.info(f"Processed item: {processed_item}")
                else:
                    self.logger.error("Processed item is None.")
