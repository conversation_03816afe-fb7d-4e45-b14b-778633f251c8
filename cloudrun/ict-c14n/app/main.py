# standard imports
from flask import Flask, request
from functools import lru_cache
import base64
import json
import os
import importlib.util

# gcp imports
from google.cloud import pubsub_v1
from google.cloud import storage

# custom imports
from .cloudrun_processor import CanonicalizationProcessor
from .logging_config import logger  # Importing custom logger


app = Flask(__name__)  # Define Flask application instance

PROJECT_ID = os.environ.get("PROJECT_ID", "ict-d-etl")

# Initialize Google Cloud Storage client
gcs_client = storage.Client(project=PROJECT_ID)

BUCKET_NAME = os.environ.get("BUCKET_NAME", "ict-d-etl-c14n_data_processor_schemas")
SOURCE_TOPIC_NAME = os.environ.get("SOURCE_TOPIC_NAME", "dev-adi-to-bq")
TARGET_TOPIC_NAME = os.environ.get("TARGET_TOPIC_NAME", "dev-adi-silver")

# Initialize Publisher client
publisher = pubsub_v1.PublisherClient()

# Construct topic strings
source_topic_path = publisher.topic_path(PROJECT_ID, SOURCE_TOPIC_NAME)
target_topic_path = publisher.topic_path(PROJECT_ID, TARGET_TOPIC_NAME)


def load_python_module(file_path, module_name):
    """
    Dynamically loads a Python file as a module and returns the module object.

    Args:
        file_path (str): The file path to the Python file to load.
        module_name (str): The name to assign to the module.

    Returns:
        module: Loaded Python module.
    """
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


@lru_cache(maxsize=100)
def fetch_schema_and_config(tenant, facility):
    """
    Fetch schema.py and configuration.json from GCS for a given tenant and facility, or fall back to default.

    Args:
        tenant (str): The tenant identifier used in the GCS path.
        facility (str): The facility identifier used in the GCS path.

    Returns:
        tuple: A tuple containing the schema dictionary and the time zone identifier.

    Raises:
        FileNotFoundError: If neither the requested nor default files exist.
    """

    def fetch_files(schema_path, config_path):
        bucket = gcs_client.bucket(BUCKET_NAME)
        logger.debug(
            f"Attempting to fetch schema from {schema_path} and config from {config_path}."
        )

        # Fetch schema.py
        schema_blob = bucket.blob(schema_path)
        if schema_blob.exists():
            schema_file_path = "/tmp/schema_temp.py"
            schema_blob.download_to_filename(schema_file_path)
            schema_module = load_python_module(schema_file_path, "dynamic_schema")
            schema_dict = getattr(schema_module, "schema", None)
            logger.debug("Loaded schema from dynamic module")
        else:
            logger.error(f"Schema file not found at {schema_path}.")
            raise FileNotFoundError(f"The schema file at {schema_path} does not exist.")

        # Fetch configuration.json
        config_blob = bucket.blob(config_path)
        if config_blob.exists():
            config_content = config_blob.download_as_text()
            config_data = json.loads(config_content)
            logger.debug(f"Downloaded configuration.json content: {config_data}")
        else:
            logger.error(f"configuration.json not found for {config_path}")
            raise FileNotFoundError(
                f"The configuration file at {config_path} does not exist."
            )

        return schema_dict, config_data["tz_identifier"]

    schema_path = f"{tenant}/{facility}/schema.py"
    config_path = f"{tenant}/{facility}/configuration.json"

    try:
        return fetch_files(schema_path, config_path)
    except FileNotFoundError as e:
        logger.warning(
            f"Files not found for {tenant}/{facility}. Falling back to {tenant}/default."
        )
        try:
            default_schema_path = f"{tenant}/default/schema.py"
            default_config_path = f"{tenant}/default/configuration.json"
            return fetch_files(default_schema_path, default_config_path)
        except FileNotFoundError as default_exception:
            logger.error(
                f"Files not found for {tenant}/default either. Original error: {e}, Default error: {default_exception}"
            )
            raise


def get_c14n_payload(request_json):
    """
    Decode a base64-encoded message and return the canonicalization payload.

    This function extracts and decodes the base64-encoded message data found in the
    request JSON. It returns the decoded data in JSON format.

    Args:
        request_json (dict): The incoming request JSON containing the base64-encoded data.

    Returns:
        dict or None: The decoded message data as a dictionary, or None if an error occurs.
    """
    base64data = request_json["message"]["data"]
    if not base64data:
        logger.error("No data found in the message")
        return None

    try:
        decoded_data = base64.b64decode(base64data).decode("utf-8")
        message_data = json.loads(decoded_data)
        logger.info(f"Decoded message data: {message_data}")
        return message_data
    except (json.JSONDecodeError, ValueError, TypeError) as err:
        logger.error(f"Error processing message data: {err}")
        return None


def publish_c14n_to_target_pubsub(item, attributes):
    """
    Publish a canonicalized item to the target Pub/Sub topic.

    Args:
        item (dict): The canonicalized item to be published.
        attributes (dict): The attributes to include in the Pub/Sub message.

    Returns:
        str or None: The message ID of the published message if successful, or None if publishing failed.
    """
    item_str = json.dumps(item)
    logger.debug(
        f"Publishing item to {target_topic_path} with attributes {attributes}."
    )
    try:
        future = publisher.publish(
            target_topic_path, item_str.encode("utf-8"), **attributes
        )
        result = future.result()
        return result
    except Exception as e:
        logger.error(f"Error publishing to Pub/Sub: {e}")
        return None


@app.route("/", methods=["POST"])
def handler():
    """
    HTTP POST handler for processing incoming Pub/Sub messages.

    This function processes an incoming HTTP POST request containing a Pub/Sub message
    and handles it using the canonicalization processor. The result is then published
    to a target Pub/Sub topic.

    Returns:
        tuple: A tuple containing the response message and HTTP status code.
    """
    request_json = request.get_json(silent=True)
    app.logger.info(f"Incoming request JSON: {request_json}")

    if request_json and "message" in request_json:
        messages = get_c14n_payload(request_json)
        if messages is None:
            return "No c14n Payload", 422

        attributes = request_json["message"].get("attributes", {})

        # Fetch the schema and config
        schema_content, tz_identifier = fetch_schema_and_config(
            attributes["tenant"], attributes["facility"]
        )

        # Initialize and run the CanonicalizationProcessor
        processor = CanonicalizationProcessor(
            schema=schema_content,
            tenant=attributes["tenant"],
            facility=attributes["facility"],
            tz_identifier=tz_identifier,
            logger=logger,
        )
        processor.process_messages(messages)

        for item in processor.data_processor.processed_items:
            result = publish_c14n_to_target_pubsub(item, attributes)
            if result is None:
                processor.logger.error("Error Publishing")
            else:
                processor.logger.info(f"Published to {TARGET_TOPIC_NAME} topic.")

        return "Processed successfully", 200
    else:
        return "No Data", 422


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(os.environ.get("PORT", 8080)))
