import json
import base64
import subprocess

# Define JSON data
data = [
    {
        "endTime": "2024-04-22T16:31:45.486096Z",
        "duration": 158560,
        "eventDate": "2024-04-22T16:31:45.590139313Z",
        "leaveTime": "2024-04-22T16:29:06.925408Z",
        "startTime": "2024-04-22T16:29:06.925408Z",
        "destinationLocationId": "MCF_TC.TRAY.0.0.CCTA01TR01",
    }
]

# Convert JSON to a string and then to bytes
json_bytes = json.dumps(data).encode("utf-8")

# Encode the bytes to a base64 string
base64_encoded_data = base64.b64encode(json_bytes).decode("utf-8")

# Prepare the curl command
curl_command = [
    "curl",
    "-X",
    "POST",
    "http://localhost:8080",
    "-H",
    "Content-Type: application/json",
    "-d",
    json.dumps(
        {
            "message": {
                "data": base64_encoded_data,
                "attributes": {
                    "tenant": "dematic",
                    "facility": "grandrapids_mi",
                },
            }
        }
    ),
]

# Execute the curl command using subprocess
result = subprocess.run(curl_command, capture_output=True, text=True)

# Print the output from curl
print("Status:", result.returncode)
print("Output:", result.stdout)
if result.stderr:
    print("Error:", result.stderr)
