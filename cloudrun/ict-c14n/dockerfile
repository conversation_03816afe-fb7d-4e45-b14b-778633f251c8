# Use the official Python image, which includes the necessary tools and libraries.
FROM python:3.11-slim-bullseye

# Zscaler Certificate. Required for secure connections to private repositories.
COPY /certs/ZscalerRootCertificate-Win-2048-SHA256.crt /usr/local/share/ca-certificates/ZscalerRootCertificate-Win-2048-SHA256.crt
RUN cat /usr/local/share/ca-certificates/ZscalerRootCertificate-Win-2048-SHA256.crt >> /etc/ssl/certs/ca-certificates.crt && \
    apt-get update && \
    apt-get install -y curl ca-certificates && \
    update-ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# Set the working directory.
WORKDIR /usr/app

# Copy the requirements file first, for better cache on rebuilds
COPY requirements.txt .

# Install Python dependencies using a generic command that can be overridden at runtime or during build
# ARG ARTIFACTORY_USER
# ARG ARTIFACTORY_PASSWORD
ARG DEV_BLD_USERNAME
ARG DEV_BLD_ARTIFACTORY_PWD
ENV PIP_EXTRA_INDEX_URL=https://${DEV_BLD_USERNAME}:${DEV_BLD_ARTIFACTORY_PWD}@artifactory.dematic.com/artifactory/api/pypi/ICT-pypi/simple
RUN pip install --no-cache-dir -r requirements.txt --extra-index-url https://pypi.org/simple --trusted-host artifactory.dematic.com

# Copy the local code to the container image.
COPY . /usr/app/cloudrun/ict-c14n/

# Change to the directory containing the application.
WORKDIR /usr/app/cloudrun/ict-c14n

# Make the entry script executable.
RUN chmod +x ./entry.sh

# Expose the port the app runs on.
EXPOSE 8080

# Run the entry script which will start the application.
CMD ["./entry.sh"]
