## Running `ict-c14n` Locally

### Prerequisites

Ensure you have Docker installed on your machine. If you need to install Dock<PERSON>, you can download it from [<PERSON><PERSON>'s official website](https://www.docker.com/products/docker-desktop).

### 1. Set Up Application Default Credentials

Before you begin, you need to configure your Google Cloud application default credentials to allow your application to authenticate with Google Cloud APIs when running locally.

1. **Navigate to your project directory:**
    ```bash
    cd control-tower-etl/cloudrun/ict-c14n
    ```

2. **Set up the credentials** (if not already set):
    - Obtain your credentials JSON file from Google Cloud.
    - Ensure the JSON file is located at `$HOME/.config/gcloud/application_default_credentials.json`. This is the default path where Google Cloud SDK looks for credentials when running applications locally.

### 2. Create a `.env` File

Create a `.env` file in the `ict-c14n` directory to store environment variables that the application will use.

- Open a text editor and create a new file named `.env` in the current directory.
- Add the following lines, replacing `<A00_username>` and `<your_password>` with your actual Artifactory credentials:
    ```
    ARTIFACTORY_USER=<A00_username>
    ARTIFACTORY_PASSWORD=<your_password>
    ```
- Please note that your dematic password should work to grant access to the pypi dematic registry. If this install fails, fetch your pypi password from the pypi at Set Up a Pypi client on the registry.
### 3. Build the Docker Image

Build the Docker image using the provided script. This script assumes you have Docker installed and configured.

1. **Make the build script executable:**
    ```bash
    chmod +x build.sh
    ```

2. **Run the build script:**
    ```bash
    ./local_build.sh
    ```
    This script will build a Docker image named `ict-c14n`. It pulls base images and dependencies.

### 4. Run the Docker Container

Once the image is built, run it locally using Docker:
```bash
docker run -d \
    -v "$HOME/.config/gcloud/application_default_credentials.json":/gcp/creds.json:ro \
    --env GOOGLE_APPLICATION_CREDENTIALS=/gcp/creds.json \
    -p 8080:8080 \
    ict-c14n

This command does the following:

- `-d` runs the container in detached mode (in the background).
- `-v` mounts the application default credentials into the container.
- `--env` sets the environment variable inside the container to point to the credentials file.
- `-p` maps port 8080 of the container to port 8080 on your host, allowing you to access the application via `localhost:8080`.


### 6. Test the Application with `local_curl.py`

After the application is running locally, you can test it by executing the `local_curl.py` script. This script sends a simulated API request to your application to ensure it is handling requests properly.

To run the script, navigate to the directory containing `local_curl.py` and execute it using Python:

```bash
python local_curl.py
```