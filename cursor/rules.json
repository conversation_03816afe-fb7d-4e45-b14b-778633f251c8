{"comment_rules": {"preserve_comments": true, "comment_guidelines": ["Comments should be preserved unless explicitly replaced with more accurate or helpful information", "When updating comments, maintain the original structure and formatting", "Comments should be clear and specific about the conditions they describe", "Comments should reference specific fields or variables when relevant", "Comments should explain the 'why' not just the 'what'", "Comments should be updated when code logic changes", "Comments should be indented to match the code they describe"], "comment_update_rules": ["Only replace comments when providing more accurate or detailed information", "When updating comments, ensure the new comment maintains or improves clarity", "Keep existing comment structure when possible", "Preserve important historical context in comments", "Update comments when fixing bugs or changing logic"]}, "python_formatting_rules": {"follow_pep8": true, "use_black": true, "formatting_guidelines": ["Use 4 spaces for indentation", "Maximum line length of 88 characters (Black standard)", "Use double quotes for strings", "Use single quotes for single-character strings", "Add two blank lines before top-level functions and classes", "Add one blank line before class methods", "Use trailing commas in multi-line structures", "Use parentheses for line continuation", "Use spaces around operators", "Use spaces after commas", "No spaces inside parentheses, brackets, or braces", "Use snake_case for variables and functions", "Use PascalCase for classes", "Use UPPER_CASE for constants"], "import_rules": ["Group imports in the following order: standard library, third-party, local", "Sort imports alphabetically within groups", "Use absolute imports", "One import per line", "Avoid wildcard imports"]}, "python_oop_rules": {"class_design": ["Follow the Single Responsibility Principle - each class should have one primary responsibility", "Use composition over inheritance when possible", "Keep classes focused and cohesive", "Use abstract base classes for defining interfaces", "Implement proper encapsulation using private attributes and methods", "Use properties (@property) for controlled attribute access", "Implement proper __init__ methods with type hints", "Use dataclasses for simple data containers", "Implement proper string representation methods (__str__, __repr__)", "Use class methods for factory methods and alternative constructors"], "inheritance_rules": ["Use inheritance for 'is-a' relationships, not for code reuse", "Keep inheritance hierarchies shallow (prefer composition)", "Use multiple inheritance only when necessary and with clear interfaces", "Implement proper method resolution order (MRO) understanding", "Use super() for parent class method calls", "Document inheritance relationships clearly"], "method_design": ["Keep methods small and focused", "Use descriptive method names that indicate their purpose", "Implement proper error handling in methods", "Use type hints for method parameters and return values", "Document method behavior, parameters, and return values", "Use private methods (prefixed with _) for internal implementation", "Implement proper method chaining where appropriate", "Use class methods for operations that don't need instance state", "Use static methods for utility functions that don't need class or instance state"], "design_patterns": ["Use the Singleton pattern appropriately and document its use", "Implement the Factory pattern for complex object creation", "Use the Strategy pattern for interchangeable algorithms", "Implement the Observer pattern for event handling", "Use the Decorator pattern for adding functionality", "Implement the Command pattern for operation encapsulation", "Use the Template Method pattern for algorithm skeletons", "Implement proper dependency injection"], "best_practices": ["Use type hints consistently throughout the codebase", "Implement proper exception handling and custom exceptions", "Use context managers (__enter__, __exit__) for resource management", "Implement proper comparison methods (__eq__, __lt__, etc.)", "Use slots for memory optimization when appropriate", "Implement proper serialization methods when needed", "Use proper documentation strings for classes and methods", "Implement proper unit tests for classes", "Use proper logging within classes", "Follow the Liskov Substitution Principle"]}}