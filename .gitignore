# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc/istanbul test coverage
.nyc_output

# SonarQube
.scannerwork/

# Editor directories and files
.idea
*.iml
.vscode/**

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Output of cloudfunction building
*.zip

# Webpack
.webpack

# dotenv environment variables file
.env
.env.test
.env.servicePrincipal
.env.yaml
.npmrc
.env.yaml

# Serverless directories
.serverless/
.serverless_plugins/

# Azure Functions artifacts
bin
obj
appsettings.json

#local secrets
localsecrets
*.secrets.yml

#previous config files
*.old

# Operating system files
.DS_Store

gitkey
iothubcerts
deploy-azure
function.json

azure-functions-core-tools

integration.secrets.js

# local Blob storage files
localBlobStorage
/runDev.sh
az-storage
dump.rdb

# ignore generated files by scripts
*.generated.*

# Azurite files
__azurite*.json

# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*
*.terraform.lock.hcl

# Ignore user-specific test tfvars files
infrastructure/Configs/test_*.tfvars

# Airflow files
airflow.cfg
airflow.db
airflow.db-journal
standalone_admin_password.txt
airflow-webserver.pid
webserver_config.py

# Compiled Python files and cache
__pycache__/
venv
.venv/

key.json
.cursorignore

# Metric Config Test Results
cloudrun/ict-metric-processor/test_results

infrastructure/CloudRun/metric_processor_subscription_filter_output.txt