# Helpful docs:
# - Main page: https://docs.gitlab.com/ee/user/project/codeowners/
# - Syntax: https://docs.gitlab.com/ee/user/project/codeowners/reference.html
# - Defining more specific owners for already matched paths:
#     https://docs.gitlab.com/ee/user/project/codeowners/#define-more-specific-owners-for-more-specifically-defined-files-or-directories

# [rule-name][number of required approvers]
# path/to/code/requiring/approvals @user @group

[Guild-Approvals][1]
/infrastructure/                        @dematic/teams/swrd-ict-infra
/airflow/                               @dematic/teams/swrd-ict-etl
/auth0/                                 @dematic/teams/swrd-ict-etl
/cloudfunctions/                        @dematic/teams/swrd-ict-etl
/cloudrun/                              @dematic/teams/swrd-ict-etl
/edp_tableau_views/                     @dematic/teams/swrd-ict-etl
/insights/                              @dematic/teams/swrd-ict-etl
/liquibase/                             @dematic/teams/swrd-ict-etl
/scripts/                               @dematic/teams/swrd-ict-etl

# Optional approval from docs team on all .md files
^[General Documentation]
*.md                                    @dematic/teams/swrd-docs
